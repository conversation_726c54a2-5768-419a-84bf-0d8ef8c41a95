import signal
import os
import hashlib
import shutil
from typing import Dict
import os
import pandas as pd
import json
import xml.etree.ElementTree as ET
import yaml


TIMEOUT_DURATION = 25

def is_file_valid(file_path):
    ext = os.path.splitext(file_path)[1].lower()
    try:
        if ext == '.csv':
            pd.read_csv(file_path)
        elif ext == '.json':
            with open(file_path, 'r') as f:
                json.load(f)
        elif ext == '.xml':
            ET.parse(file_path)
        elif ext == '.yaml' or ext == '.yml':
            with open(file_path, 'r') as f:
                yaml.safe_load(f)
        else:
            return True, None
        return True, None
    except Exception as e:
        return False, str(e)
        
class timeout:
    def __init__(self, seconds=TIMEOUT_DURATION, error_message="Timeout"):
        self.seconds = seconds
        self.error_message = error_message

    def handle_timeout(self, signum, frame):
        raise TimeoutError(self.error_message)

    def __enter__(self):
        signal.signal(signal.SIGALRM, self.handle_timeout)
        signal.alarm(self.seconds)

    def __exit__(self, type, value, traceback):
        signal.alarm(0)


def delete_files_in_folder(folder_path):
    if os.path.exists(folder_path):
        files = os.listdir(folder_path)
        for file in files:
            file_path = os.path.join(folder_path, file)
            if os.path.isfile(file_path):
                os.remove(file_path)
            elif os.path.isdir(file_path):
                shutil.rmtree(file_path)
        
def create_folder_if_not_exists(path):
    if not os.path.exists(path):
        os.makedirs(path)

def calculate_sha256(file_path):
    with open(file_path, 'rb') as f:
        file_data = f.read()
        return hashlib.sha256(file_data).hexdigest()


