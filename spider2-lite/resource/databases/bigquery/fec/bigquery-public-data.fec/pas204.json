{"table_name": "pas204", "table_fullname": "bigquery-public-data.fec.pas204", "column_names": ["cmte_id", "amndt_ind", "rpt_tp", "transaction_pgi", "image_num", "transaction_tp", "entity_tp", "name", "city", "state", "zip_code", "employer", "occupation", "transaction_dt", "transaction_amt", "other_id", "cand_id", "tran_id", "file_num", "memo_cd", "memo_text", "sub_id"], "column_types": ["STRING", "STRING", "STRING", "STRING", "STRING", "STRING", "STRING", "STRING", "STRING", "STRING", "STRING", "STRING", "STRING", "DATE", "FLOAT64", "STRING", "STRING", "STRING", "INT64", "STRING", "STRING", "INT64"], "nested_column_names": ["cmte_id", "amndt_ind", "rpt_tp", "transaction_pgi", "image_num", "transaction_tp", "entity_tp", "name", "city", "state", "zip_code", "employer", "occupation", "transaction_dt", "transaction_amt", "other_id", "cand_id", "tran_id", "file_num", "memo_cd", "memo_text", "sub_id"], "nested_column_types": ["STRING", "STRING", "STRING", "STRING", "STRING", "STRING", "STRING", "STRING", "STRING", "STRING", "STRING", "STRING", "STRING", "DATE", "FLOAT64", "STRING", "STRING", "STRING", "INT64", "STRING", "STRING", "INT64"], "description": ["Filer Identification Number", "Amendment Indicator", "Report Type", "Primary-General Indicator", "Image Number", "Transaction Type", "Entity Type", "Recipient/Payee", "City/Town", "State", "Zip Code", "Employer", "Occupation", "Transaction Date(MMDDYYYY)", "Transaction Amount", "Other Identification Number", "Candidate Identification Number", "Transaction ID", "File Number / Report ID", "Memo Code", "Memo Text", "FEC Record Number"], "sample_rows": [{"cmte_id": "C00053553", "amndt_ind": "A", "rpt_tp": "M10", "transaction_pgi": null, "image_num": "25980400667", "transaction_tp": "24A", "entity_tp": null, "name": "CARRENO MCCUNE & COMPANY", "city": "HOUSTON", "state": "TX", "zip_code": "77007", "employer": null, "occupation": null, "transaction_dt": "2004-09-14", "transaction_amt": 52000.0, "other_id": "H6TX24057", "cand_id": "H6TX24057", "tran_id": "4227989", "file_num": 163500, "memo_cd": null, "memo_text": null, "sub_id": 4020420051053659348}, {"cmte_id": "C90005471", "amndt_ind": "A", "rpt_tp": "Q3", "transaction_pgi": null, "image_num": "23992036098", "transaction_tp": "24A", "entity_tp": "CAN", "name": "BACHURSKI & ASSOCIATES", "city": "WASHINGTON", "state": "DC", "zip_code": "20003", "employer": null, "occupation": null, "transaction_dt": "2003-07-28", "transaction_amt": 1025.0, "other_id": "H4TX22023", "cand_id": "H4TX22023", "tran_id": "57-11", "file_num": 97155, "memo_cd": null, "memo_text": null, "sub_id": 4101420031035561183}, {"cmte_id": "C00350439", "amndt_ind": "A", "rpt_tp": "Q3", "transaction_pgi": null, "image_num": "26960019609", "transaction_tp": "24A", "entity_tp": "CAN", "name": "LIST AMERICA", "city": "WASHINGTON", "state": "DC", "zip_code": "20007", "employer": null, "occupation": null, "transaction_dt": "2004-09-30", "transaction_amt": 921.0, "other_id": "H6LA07131", "cand_id": "H6LA07131", "tran_id": "SA24.5037", "file_num": 208118, "memo_cd": null, "memo_text": null, "sub_id": 4032820061065141320}, {"cmte_id": "C90005471", "amndt_ind": "A", "rpt_tp": "Q3", "transaction_pgi": null, "image_num": "23992036097", "transaction_tp": "24A", "entity_tp": "CAN", "name": "T/C MAILING", "city": "OWINGS", "state": "MD", "zip_code": "20736", "employer": null, "occupation": null, "transaction_dt": "2003-07-28", "transaction_amt": 848.0, "other_id": "H4TX22023", "cand_id": "H4TX22023", "tran_id": "57-10", "file_num": 97155, "memo_cd": null, "memo_text": null, "sub_id": 4101420031035561179}, {"cmte_id": "C90005471", "amndt_ind": "A", "rpt_tp": "Q3", "transaction_pgi": null, "image_num": "23992036096", "transaction_tp": "24A", "entity_tp": "CAN", "name": "T/C MAILING", "city": "OWINGS", "state": "MD", "zip_code": "20736", "employer": null, "occupation": null, "transaction_dt": "2003-07-28", "transaction_amt": 595.0, "other_id": "H4TX22023", "cand_id": "H4TX22023", "tran_id": "57-9", "file_num": 97155, "memo_cd": null, "memo_text": null, "sub_id": 4101420031035561176}]}