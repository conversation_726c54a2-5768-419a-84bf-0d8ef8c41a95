{"table_name": "cbsa_2018_1yr", "table_fullname": "bigquery-public-data.census_bureau_acs.cbsa_2018_1yr", "column_names": ["geo_id", "nonfamily_households", "family_households", "median_year_structure_built", "rent_burden_not_computed", "rent_over_50_percent", "rent_40_to_50_percent", "rent_35_to_40_percent", "rent_30_to_35_percent", "rent_25_to_30_percent", "rent_20_to_25_percent", "rent_15_to_20_percent", "rent_10_to_15_percent", "rent_under_10_percent", "total_pop", "male_pop", "female_pop", "median_age", "white_pop", "black_pop", "asian_pop", "hispanic_pop", "amerindian_pop", "other_race_pop", "two_or_more_races_pop", "not_hispanic_pop", "commuters_by_public_transportation", "households", "median_income", "income_per_capita", "housing_units", "vacant_housing_units", "vacant_housing_units_for_rent", "vacant_housing_units_for_sale", "median_rent", "percent_income_spent_on_rent", "owner_occupied_housing_units", "million_dollar_housing_units", "mortgaged_housing_units", "families_with_young_children", "two_parent_families_with_young_children", "two_parents_in_labor_force_families_with_young_children", "two_parents_father_in_labor_force_families_with_young_children", "two_parents_mother_in_labor_force_families_with_young_children", "two_parents_not_in_labor_force_families_with_young_children", "one_parent_families_with_young_children", "father_one_parent_families_with_young_children", "father_in_labor_force_one_parent_families_with_young_children", "commute_10_14_mins", "commute_15_19_mins", "commute_20_24_mins", "commute_25_29_mins", "commute_30_34_mins", "commute_45_59_mins", "aggregate_travel_time_to_work", "income_less_10000", "income_10000_14999", "income_15000_19999", "income_20000_24999", "income_25000_29999", "income_30000_34999", "income_35000_39999", "income_40000_44999", "income_45000_49999", "income_50000_59999", "income_60000_74999", "income_75000_99999", "income_100000_124999", "income_125000_149999", "income_150000_199999", "income_200000_or_more", "renter_occupied_housing_units_paying_cash_median_gross_rent", "owner_occupied_housing_units_lower_value_quartile", "owner_occupied_housing_units_median_value", "owner_occupied_housing_units_upper_value_quartile", "married_households", "occupied_housing_units", "housing_units_renter_occupied", "dwellings_1_units_detached", "dwellings_1_units_attached", "dwellings_2_units", "dwellings_3_to_4_units", "dwellings_5_to_9_units", "dwellings_10_to_19_units", "dwellings_20_to_49_units", "dwellings_50_or_more_units", "mobile_homes", "housing_built_2005_or_later", "housing_built_2000_to_2004", "housing_built_1939_or_earlier", "male_under_5", "male_5_to_9", "male_10_to_14", "male_15_to_17", "male_18_to_19", "male_20", "male_21", "male_22_to_24", "male_25_to_29", "male_30_to_34", "male_35_to_39", "male_40_to_44", "male_45_to_49", "male_50_to_54", "male_55_to_59", "male_60_61", "male_62_64", "male_65_to_66", "male_67_to_69", "male_70_to_74", "male_75_to_79", "male_80_to_84", "male_85_and_over", "female_under_5", "female_5_to_9", "female_10_to_14", "female_15_to_17", "female_18_to_19", "female_20", "female_21", "female_22_to_24", "female_25_to_29", "female_30_to_34", "female_35_to_39", "female_40_to_44", "female_45_to_49", "female_50_to_54", "female_55_to_59", "female_60_to_61", "female_62_to_64", "female_65_to_66", "female_67_to_69", "female_70_to_74", "female_75_to_79", "female_80_to_84", "female_85_and_over", "white_including_hispanic", "black_including_hispanic", "amerindian_including_hispanic", "asian_including_hispanic", "commute_5_9_mins", "commute_35_39_mins", "commute_40_44_mins", "commute_60_89_mins", "commute_90_more_mins", "households_retirement_income", "armed_forces", "civilian_labor_force", "employed_pop", "unemployed_pop", "not_in_labor_force", "pop_16_over", "pop_in_labor_force", "asian_male_45_54", "asian_male_55_64", "black_male_45_54", "black_male_55_64", "hispanic_male_45_54", "hispanic_male_55_64", "white_male_45_54", "white_male_55_64", "bachelors_degree_2", "bachelors_degree_or_higher_25_64", "children", "children_in_single_female_hh", "commuters_by_bus", "commuters_by_car_truck_van", "commuters_by_carpool", "commuters_by_subway_or_elevated", "commuters_drove_alone", "different_house_year_ago_different_city", "different_house_year_ago_same_city", "employed_agriculture_forestry_fishing_hunting_mining", "employed_arts_entertainment_recreation_accommodation_food", "employed_construction", "employed_education_health_social", "employed_finance_insurance_real_estate", "employed_information", "employed_manufacturing", "employed_other_services_not_public_admin", "employed_public_administration", "employed_retail_trade", "employed_science_management_admin_waste", "employed_transportation_warehousing_utilities", "employed_wholesale_trade", "female_female_households", "four_more_cars", "gini_index", "graduate_professional_degree", "group_quarters", "high_school_including_ged", "households_public_asst_or_food_stamps", "in_grades_1_to_4", "in_grades_5_to_8", "in_grades_9_to_12", "in_school", "in_undergrad_college", "less_than_high_school_graduate", "male_45_64_associates_degree", "male_45_64_bachelors_degree", "male_45_64_graduate_degree", "male_45_64_less_than_9_grade", "male_45_64_grade_9_12", "male_45_64_high_school", "male_45_64_some_college", "male_45_to_64", "male_male_households", "management_business_sci_arts_employed", "no_car", "no_cars", "not_us_citizen_pop", "occupation_management_arts", "occupation_natural_resources_construction_maintenance", "occupation_production_transportation_material", "occupation_sales_office", "occupation_services", "one_car", "two_cars", "three_cars", "pop_25_64", "pop_determined_poverty_status", "population_1_year_and_over", "population_3_years_over", "poverty", "sales_office_employed", "some_college_and_associates_degree", "walked_to_work", "worked_at_home", "workers_16_and_over", "associates_degree", "bachelors_degree", "high_school_diploma", "less_one_year_college", "masters_degree", "one_year_more_college", "pop_25_years_over", "commute_35_44_mins", "commute_60_more_mins", "commute_less_10_mins", "commuters_16_over", "hispanic_any_race", "pop_5_years_over", "speak_only_english_at_home", "speak_spanish_at_home", "speak_spanish_at_home_low_english", "pop_15_and_over", "pop_never_married", "pop_now_married", "pop_separated", "pop_widowed", "pop_divorced", "do_date"], "column_types": ["STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "STRING"], "nested_column_names": ["geo_id", "nonfamily_households", "family_households", "median_year_structure_built", "rent_burden_not_computed", "rent_over_50_percent", "rent_40_to_50_percent", "rent_35_to_40_percent", "rent_30_to_35_percent", "rent_25_to_30_percent", "rent_20_to_25_percent", "rent_15_to_20_percent", "rent_10_to_15_percent", "rent_under_10_percent", "total_pop", "male_pop", "female_pop", "median_age", "white_pop", "black_pop", "asian_pop", "hispanic_pop", "amerindian_pop", "other_race_pop", "two_or_more_races_pop", "not_hispanic_pop", "commuters_by_public_transportation", "households", "median_income", "income_per_capita", "housing_units", "vacant_housing_units", "vacant_housing_units_for_rent", "vacant_housing_units_for_sale", "median_rent", "percent_income_spent_on_rent", "owner_occupied_housing_units", "million_dollar_housing_units", "mortgaged_housing_units", "families_with_young_children", "two_parent_families_with_young_children", "two_parents_in_labor_force_families_with_young_children", "two_parents_father_in_labor_force_families_with_young_children", "two_parents_mother_in_labor_force_families_with_young_children", "two_parents_not_in_labor_force_families_with_young_children", "one_parent_families_with_young_children", "father_one_parent_families_with_young_children", "father_in_labor_force_one_parent_families_with_young_children", "commute_10_14_mins", "commute_15_19_mins", "commute_20_24_mins", "commute_25_29_mins", "commute_30_34_mins", "commute_45_59_mins", "aggregate_travel_time_to_work", "income_less_10000", "income_10000_14999", "income_15000_19999", "income_20000_24999", "income_25000_29999", "income_30000_34999", "income_35000_39999", "income_40000_44999", "income_45000_49999", "income_50000_59999", "income_60000_74999", "income_75000_99999", "income_100000_124999", "income_125000_149999", "income_150000_199999", "income_200000_or_more", "renter_occupied_housing_units_paying_cash_median_gross_rent", "owner_occupied_housing_units_lower_value_quartile", "owner_occupied_housing_units_median_value", "owner_occupied_housing_units_upper_value_quartile", "married_households", "occupied_housing_units", "housing_units_renter_occupied", "dwellings_1_units_detached", "dwellings_1_units_attached", "dwellings_2_units", "dwellings_3_to_4_units", "dwellings_5_to_9_units", "dwellings_10_to_19_units", "dwellings_20_to_49_units", "dwellings_50_or_more_units", "mobile_homes", "housing_built_2005_or_later", "housing_built_2000_to_2004", "housing_built_1939_or_earlier", "male_under_5", "male_5_to_9", "male_10_to_14", "male_15_to_17", "male_18_to_19", "male_20", "male_21", "male_22_to_24", "male_25_to_29", "male_30_to_34", "male_35_to_39", "male_40_to_44", "male_45_to_49", "male_50_to_54", "male_55_to_59", "male_60_61", "male_62_64", "male_65_to_66", "male_67_to_69", "male_70_to_74", "male_75_to_79", "male_80_to_84", "male_85_and_over", "female_under_5", "female_5_to_9", "female_10_to_14", "female_15_to_17", "female_18_to_19", "female_20", "female_21", "female_22_to_24", "female_25_to_29", "female_30_to_34", "female_35_to_39", "female_40_to_44", "female_45_to_49", "female_50_to_54", "female_55_to_59", "female_60_to_61", "female_62_to_64", "female_65_to_66", "female_67_to_69", "female_70_to_74", "female_75_to_79", "female_80_to_84", "female_85_and_over", "white_including_hispanic", "black_including_hispanic", "amerindian_including_hispanic", "asian_including_hispanic", "commute_5_9_mins", "commute_35_39_mins", "commute_40_44_mins", "commute_60_89_mins", "commute_90_more_mins", "households_retirement_income", "armed_forces", "civilian_labor_force", "employed_pop", "unemployed_pop", "not_in_labor_force", "pop_16_over", "pop_in_labor_force", "asian_male_45_54", "asian_male_55_64", "black_male_45_54", "black_male_55_64", "hispanic_male_45_54", "hispanic_male_55_64", "white_male_45_54", "white_male_55_64", "bachelors_degree_2", "bachelors_degree_or_higher_25_64", "children", "children_in_single_female_hh", "commuters_by_bus", "commuters_by_car_truck_van", "commuters_by_carpool", "commuters_by_subway_or_elevated", "commuters_drove_alone", "different_house_year_ago_different_city", "different_house_year_ago_same_city", "employed_agriculture_forestry_fishing_hunting_mining", "employed_arts_entertainment_recreation_accommodation_food", "employed_construction", "employed_education_health_social", "employed_finance_insurance_real_estate", "employed_information", "employed_manufacturing", "employed_other_services_not_public_admin", "employed_public_administration", "employed_retail_trade", "employed_science_management_admin_waste", "employed_transportation_warehousing_utilities", "employed_wholesale_trade", "female_female_households", "four_more_cars", "gini_index", "graduate_professional_degree", "group_quarters", "high_school_including_ged", "households_public_asst_or_food_stamps", "in_grades_1_to_4", "in_grades_5_to_8", "in_grades_9_to_12", "in_school", "in_undergrad_college", "less_than_high_school_graduate", "male_45_64_associates_degree", "male_45_64_bachelors_degree", "male_45_64_graduate_degree", "male_45_64_less_than_9_grade", "male_45_64_grade_9_12", "male_45_64_high_school", "male_45_64_some_college", "male_45_to_64", "male_male_households", "management_business_sci_arts_employed", "no_car", "no_cars", "not_us_citizen_pop", "occupation_management_arts", "occupation_natural_resources_construction_maintenance", "occupation_production_transportation_material", "occupation_sales_office", "occupation_services", "one_car", "two_cars", "three_cars", "pop_25_64", "pop_determined_poverty_status", "population_1_year_and_over", "population_3_years_over", "poverty", "sales_office_employed", "some_college_and_associates_degree", "walked_to_work", "worked_at_home", "workers_16_and_over", "associates_degree", "bachelors_degree", "high_school_diploma", "less_one_year_college", "masters_degree", "one_year_more_college", "pop_25_years_over", "commute_35_44_mins", "commute_60_more_mins", "commute_less_10_mins", "commuters_16_over", "hispanic_any_race", "pop_5_years_over", "speak_only_english_at_home", "speak_spanish_at_home", "speak_spanish_at_home_low_english", "pop_15_and_over", "pop_never_married", "pop_now_married", "pop_separated", "pop_widowed", "pop_divorced", "do_date"], "nested_column_types": ["STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "STRING"], "description": ["Core Based Statistical Area (CBSA) Geoids", "Nonfamily Households. A householder living alone or with nonrelatives only. Unmarried couples households, whether opposite-sex or same-sex, with no relatives of the householder present are tabulated in nonfamily households.", "Family Households. A family consists of a householder and one or more other people living in the same household who are related to the householder by birth, marriage, or adoption. All people in a household who are related to the householder are regarded as members of his or her family. A family household may contain people not related to the householder, but those people are not included as part of the family of the householder in tabulations. Thus, the number of family households is equal to the number of families, but family households may include more members than do families. A household can contain only one family for purposes of tabulations. Not all households contain families since a household may be comprised of a group of unrelated people or of one person living alone -- these are called nonfamily households. Families are classified by type as either a \"married couple family\" or \"other family\" according to the sex of the householder and the presence of relatives. The data on family type are based ...", "Median Year Structure Built. Median Year Structure Built", "Housing units without rent burden computed. Units for which no rent is paid and units occupied by households that reported no income or a net loss comprise this category", "Housing units spending over 50% income on rent. Gross rent over 50 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 40 to 49.9% income on rent. Gross rent from 40.0 to 49.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 35 to 39.9% income on rent. Gross rent from 35.0 to 39.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 30 to 34.9% income on rent. Gross rent from 30.0 to 34.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 25 to 29.9% income on rent. Gross rent from 25.0 to 29.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 20 to 24.9% income on rent. Gross rent from 20.0 to 24.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 15 to 19.9% income on rent. Gross rent from 15.0 to 19.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 10 to 14.9% income on rent. Gross rent from 10.0 to 14.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending less than 10% on rent. Gross rent less than 10 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Total Population. The total number of all people living in a given geographic area.  This is a very useful catch-all denominator when calculating rates.", "Male Population. The number of people within each geography who are male.", "Female Population. The number of people within each geography who are female.", "Median Age. The median age of all people in a given geographic area.", "White Population. The number of people identifying as white, non-Hispanic in each geography.", "Black or African American Population. The number of people identifying as black or African American, non-Hispanic in each geography.", "Asian Population. The number of people identifying as Asian, non-Hispanic in each geography.", "Hispanic Population. The number of people identifying as Hispanic or Latino in each geography.", "American Indian and Alaska Native Population. The number of people identifying as American Indian or Alaska native in each geography.", "Other Race population. The number of people identifying as another race in each geography", "Two or more races population. The number of people identifying as two or more races in each geography", "Population not Hispanic. The number of people not identifying as Hispanic or Latino in each geography.", "Commuters by Public Transportation. The number of workers age 16 years and over within a geographic area who primarily traveled to work by public transportation.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Households. A count of the number of households in each geography. A household consists of one or more people who live in the same dwelling and also share at meals or living accommodation, and may consist of a single family or some other grouping of people. ", "Median Household Income in the past 12 Months. Within a geographic area, the median income received by every household on a regular basis before payments for personal income taxes, social security, union dues, medicare deductions, etc.  It includes income received from wages, salary, commissions, bonuses, and tips; self-employment income from own nonfarm or farm businesses, including proprietorships and partnerships; interest, dividends, net rental income, royalty income, or income from estates and trusts; Social Security or Railroad Retirement income; Supplemental Security Income (SSI); any cash public assistance or welfare payments from the state or local welfare office; retirement, survivor, or disability benefits; and any other sources of income received regularly such as Veterans' (VA) payments, unemployment and/or worker's compensation, child support, and alimony.", "Per Capita Income in the past 12 Months. Per capita income is the mean income computed for every man, woman, and child in a particular group. It is derived by dividing the total income of a particular group by the total population.", "Housing Units. A count of housing units in each geography.  A housing unit is a house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters.", "Vacant Housing Units. The count of vacant housing units in a geographic area. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Vacant Housing Units for Rent. The count of vacant housing units in a geographic area that are for rent. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Vacant Housing Units for Sale. The count of vacant housing units in a geographic area that are for sale. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Median Rent. The median contract rent within a geographic area. The contract rent is the monthly rent agreed to or contracted for, regardless of any furnishings, utilities, fees, meals, or services that may be included. For vacant units, it is the monthly rent asked for the rental unit at the time of interview.", "Percent of Household Income Spent on Rent. Within a geographic area, the median percentage of household income which was spent on gross rent.  Gross rent is the amount of the contract rent plus the estimated average monthly cost of utilities (electricity, gas, water, sewer etc.) and fuels (oil, coal, wood, etc.) if these are paid by the renter.  Household income is the sum of the income of all people 15 years and older living in the household.", "Owner-occupied Housing Units", "Owner-occupied Housing Units valued at $1,000,000 or more.. The count of owner occupied housing units in a geographic area that are valued at $1,000,000 or more.  Value is the respondent's estimate of how much the property (house and lot, mobile home and lot, or condominium unit) would sell for if it were for sale.", "Owner-occupied Housing Units with a Mortgage. The count of housing units within a geographic area that are mortagaged. \"Mortgage\" refers to all forms of debt where the property is pledged as security for repayment of the debt, including deeds of trust, trust deed, contracts to purchase, land contracts, junior mortgages, and home equity loans.", "Families with young children (under 6 years of age)", "Two-parent families with young children (under 6 years of age)", "Two-parent families, both parents in labor force with young children (under 6 years of age)", "Two-parent families, father only in labor force with young children (under 6 years of age)", "Two-parent families, mother only in labor force with young children (under 6 years of age)", "Two-parent families, neither parent in labor force with young children (under 6 years of age)", "One-parent families with young children (under 6 years of age)", "One-parent families, father, with young children (under 6 years of age)", "One-parent families, father in labor force, with young children (under 6 years of age)", "Number of workers with a commute between 10 and 14 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 10 and 14 minutes. ", "Number of workers with a commute between 15 and 19 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 15 and 19 minutes. ", "Number of workers with a commute between 20 and 24 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 20 and 24 minutes.", "Number of workers with a commute between 25 and 29 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 25 and 29 minutes. ", "Number of workers with a commute between 30 and 34 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 30 and 34 minutes. ", "Number of workers with a commute between 45 and 59 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 45 and 59 minutes. ", "Aggregate travel time to work. The total number of minutes every worker in a geographic area over the age of 16 who did not work from home spent spent commuting to work in one day", "Households with income less than $10,000. The number of households in a geographic area whose annual income was less than $10,000.", "Households with income of $10,000 to $14,999. The number of households in a geographic area whose annual income was between $10,000 and $14,999.", "Households with income of $15,000 to $19,999. The number of households in a geographic area whose annual income was between $15,000 and $19,999.", "Households with income of $20,000 to $24,999. The number of households in a geographic area whose annual income was between $20,000 and $24,999.", "Households with income of $25,000 to $29,999. The number of households in a geographic area whose annual income was between $20,000 and $24,999.", "Households with income of $30,000 to $34,999. The number of households in a geographic area whose annual income was between $30,000 and $34,999.", "Households with income of $35,000 to $39,999. The number of households in a geographic area whose annual income was between $35,000 and $39,999.", "Households with income of $40,000 to $44,999. The number of households in a geographic area whose annual income was between $40,000 and $44,999.", "Households with income of $45,000 to $49,999. The number of households in a geographic area whose annual income was between $45,000 and $49,999.", "Households with income of $50,000 to $59,999. The number of households in a geographic area whose annual income was between $50,000 and $59,999.", "Households with income of $60,000 to $74,999. The number of households in a geographic area whose annual income was between $60,000 and $74,999.", "Households with income of $75,000 to $99,999. The number of households in a geographic area whose annual income was between $75,000 and $99,999.", "Households with income of $100,000 to $124,999. The number of households in a geographic area whose annual income was between $100,000 and $124,999.", "Households with income of $125,000 to $149,999. The number of households in a geographic area whose annual income was between $125,000 and $149,999.", "Households with income of $150,000 to $199,999. The number of households in a geographic area whose annual income was between $150,000 and $1999,999.", "Households with income of $200,000 Or More. The number of households in a geographic area whose annual income was more than $200,000.", "Renter-Occupied Housing Units Paying Cash Rent Median Gross Rent", "Owner-Occupied Housing Units Lower Value Quartile", "Owner-Occupied Housing Units Median Value. The middle value (median) in a geographic area owner occupied housing units.", "Owner-Occupied Housing Units Upper Value Quartile", "Married households. People in formal marriages, as well as people in common-law marriages, are included. Does not include same-sex marriages.", "Occupied housing units. A housing unit is classified as occupied if it is the usual place of residence of the person or group of people living in it at the time of enumeration.", "Renter occupied housing units. All occupied units which are not owner occupied, whether they are rented for cash rent or occupied without payment of cash rent, are classified as renter-occupied.", "Single-family (one unit) detached dwellings. This is a 1-unit structure detached from any other house, that is, with open space on all four sides. Such structures are considered detached even if they have an adjoining shed or garage. A one-family house that contains a business is considered detached as long as the building has open space on all four sides. Mobile homes to which one or more permanent rooms have been added or built also are included.", "Single-family (one unit) attached dwellings. This is a 1-unit structure that has one or more walls extending from ground to roof separating it from adjoining structures. In row houses (sometimes called townhouses), double houses, or houses attached to nonresidential structures, each house is a separate, attached structure if the dividing or common wall goes from ground to roof.", "Two-family (two unit) dwellings", "Multifamily dwellings with three to 4 units", "Apartment buildings with 5 to 9 units", "Apartment buildings with 10 to 19 units", "Apartment buildings with 20 to 49 units", "Apartment buildings with 50 or more units", "Mobile homes. A manufactured home is defined as a movable dwelling, 8 feet or more wide and 40 feet or more long, designed to be towed on its own chassis, with transportation gear integral to the unit when it leaves the factory, and without need of a permanent foundation. These homes are built in accordance with the U.S. Department of Housing and Urban Development (HUD) building code.", "Housing units built in 2005 or later. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built in 2005 or later.", "Housing units built between 2000 and 2004. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built from 2000 to 2004.", "Housing units built before 1939. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built in 1939 or earlier.", "Male under 5 years. The male population over the age of five years within the specified area.", "Male age 5 to 9. The male population between the age of five years to nine years within the specified area.", "Male age 10 to 14. The male population between the age of ten years to fourteen years within the specified area.", "Male age 15 to 17. The male population between the age of fifteeen years to seventeen years within the specified area.", "Male age 18 and 19. The male population between the age of eighteen years to nineteen years within the specified area.", "Male age 20. The male population with an age of twenty years within the specified area.", "Male age 21. The male population with an age of twenty-one years within the specified area.", "Male age 22 to 24. The male population between the age of twenty-two years to twenty-four years within the specified area.", "Male age 25 to 29. The male population between the age of twenty-five years to twenty-nine years within the specified area.", "Male age 30 to 34. The male population between the age of thirty years to thirty-four years within the specified area.", "Male age 35 to 39. The male population between the age of thirty-five years to thirty-nine years within the specified area.", "Male age 40 to 44. The male population between the age of fourty years to fourty-four years within the specified area.", "Men age 45 to 49. The male population between the age of fourty-five years to fourty-nine years within the specified area.", "Men age 50 to 54. The male population between the age of fifty years to fifty-four years within the specified area.", "Men age 55 to 59. The male population between the age of fifty-five years to fifty-nine years within the specified area.", "Men age 60 to 61. The male population between the age of sixty years to sixty-one years within the specified area.", "Men age 62 to 64. The male population between the age of sixty-two years to sixty-four years within the specified area.", "Male age 65 to 66. The male population between the age of sixty-five years to sixty-six years within the specified area.", "Male age 67 to 69. The male population between the age of sixty-seven years to sixty-nine years within the specified area.", "Male age 70 to 74. The male population between the age of seventy years to seventy-four years within the specified area.", "Male age 75 to 79. The male population between the age of seventy-five years to seventy-nine years within the specified area.", "Male age 80 to 84. The male population between the age of eighty years to eighty-four years within the specified area.", "Male age 85 and over. The male population of the age of eighty-five years and over within the specified area.", "Female under 5 years. The female population over the age of five years within the specified area.", "Female age 5 to 9. The female population between the age of five years to nine years within the specified area.", "Female age 10 to 14. The female population between the age of ten years to fourteen years within the specified area.", "Female age 15 to 17. The female population between the age of fifteeen years to seventeen years within the specified area.", "Female age 18 and 19. The female population between the age of eighteen years to nineteen years within the specified area.", "Female age 20. The female population with an age of twenty years within the specified area.", "Female age 21. The female population with an age of twenty-one years within the specified area.", "Female age 22 to 24. The female population between the age of twenty-two years to twenty-four years within the specified area.", "Female age 25 to 29. The female population between the age of twenty-five years to twenty-nine years within the specified area.", "Female age 30 to 34. The female population between the age of thirty years to thirty-four years within the specified area.", "Female age 35 to 39. The female population between the age of thirty-five years to thirty-nine years within the specified area.", "Female age 40 to 44. The female population between the age of fourty years to fourty-four years within the specified area.", "Female age 45 to 49. The female population between the age of fourty-five years to fourty-nine years within the specified area.", "Female age 50 to 54. The female population between the age of fifty years to fifty-four years within the specified area.", "Female age 55 to 59. The female population between the age of fifty-five years to fifty-nine years within the specified area.", "Female age 60 and 61. The female population between the age of sixty years to sixty-one years within the specified area.", "Female age 62 to 64. The female population between the age of sixty-two years to sixty-four years within the specified area.", "Female age 65 to 66. The female population between the age of sixty-five years to sixty-six years within the specified area.", "Female age 67 to 69. The female population between the age of sixty-seven years to sixty-nine years within the specified area.", "Female age 70 to 74. The female population between the age of seventy years to seventy-four years within the specified area.", "Female age 75 to 79. The female population between the age of seventy-five years to seventy-nine years within the specified area.", "Female age 80 to 84. The female population between the age of eighty years to eighty-four years within the specified area.", "Female age 85 and over. The female population of the age of eighty-five years and over within the specified area.", "White including Hispanic", "Black including Hispanic", "American Indian including Hispanic", "Asian including Hispanic", "Number of workers with a commute between 5 and 9 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 5 and 9 minutes.", "Number of workers with a commute between 35 and 39 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 35 and 39 minutes. ", "Number of workers with a commute between 40 and 44 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 40 and 44 minutes. ", "Number of workers with a commute between 60 and 89 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 60 and 89 minutes .", "Number of workers with a commute of more than 90 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute more than 90 minutes.", "Households receiving retirement income", "Population in Armed Forces. The number of people in each geography who are members of the U.S. Armed Forces (people on active duty with the United States Army, Air Force, Navy, Marine Corps, or Coast Guard).", "Population in Civilian Labor Force. The number of civilians 16 years and over in each geography who can be classified as either \"employed\" or \"unemployed\" below.", "Employed Population. The number of civilians 16 years old and over in each geography who either (1) were \"at work,\" that is, those who did any work at all during the reference week as paid employees, worked in their own business or profession, worked on their own farm, or worked 15 hours or more as unpaid workers on a family farm or in a family business; or (2) were \"with a job but not at work,\" that is, those who did not work during the reference week but had jobs or businesses from which they were temporarily absent due to illness, bad weather, industrial dispute, vacation, or other personal reasons. Excluded from the employed are people whose only activity consisted of work around the house or unpaid volunteer work for religious, charitable, and similar organizations; also excluded are all institutionalized people and people on active duty in the United States Armed Forces.", "", "Population Not in Labor Force. The number of people in each geography who are 16 years old and over who are not classified as members of the labor force. This category consists mainly of students, homemakers, retired workers, seasonal workers interviewed in an off season who were not looking for work, institutionalized people, and people doing only incidental unpaid family work (less than 15 hours during the reference week).", "Population age 16 and over. The number of people in each geography who are age 16 or over.", "Population in Labor Force. The number of people in each geography who are either in the civilian labor force or are members of the U.S. Armed Forces (people on active duty with the United States Army, Air Force, Navy, Marine Corps, or Coast Guard).", "Asian Men age 45 to 54", "Asian Men age 55 to 64", "Black Men age 45 to 54", "Black Men age 55 to 64", "Hispanic Men age 45 to 54", "Hispanic Men age 55 to 64", "White Men age 45 to 54", "White Men age 55 to 64", "Population who completed a bachelor's degree. From mobility table.", "Population with Bachelors Degree or Higher, Ages 25 to 64. The number of people in each geography who are between the ages of 25 and 64 who have attained a bachelors degree or higher. ", "Children under 18 Years of Age. The number of people within each geography who are under 18 years of age.", "Children under 18 years of age in single female-led household", "Commuters by Bus. The number of workers age 16 years and over within a geographic area who primarily traveled to work by bus.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.  This is a subset of workers who commuted by public transport.", "Commuters by Car, Truck, or Van. The number of workers age 16 years and over within  a geographic area who primarily traveled to work by car, truck or  van.  This is the principal mode of travel or type of conveyance,  by distance rather than time, that the worker usually used to get  from home to work.", "Commuters by Carpool. The number of workers age 16 years and over within a geographic area who primarily traveled to work by carpool.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Commuters by Subway or Elevated. The number of workers age 16 years and over within a geographic area who primarily traveled to work by subway or elevated train.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.  This is a subset of workers who commuted by public transport.", "Commuters who drove alone. The number of workers age 16 years and over within a geographic area who primarily traveled by car driving alone. This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Lived in a different house one year ago in a different city. All people in a geographic area who lived in a different city within the year prior to the survey.", "Lived in a different house one year ago in the same city. All people in a geographic area who lived in the same city but moved to a different unit within the year prior to the survey.", "Workers employed in firms in agriculture, forestry, fishing, hunting, or mining. The Agriculture, Forestry, Fishing and Hunting sector comprises establishments primarily engaged in growing crops, raising animals, harvesting timber, and harvesting fish and other animals from a farm, ranch, or their natural habitats.", "Workers employed in firms in arts, entertainment, recreation, accommodation and food services. The Arts, Entertainment, and Recreation sector includes a wide range of establishments that operate facilities or provide services to meet varied cultural, entertainment, and recreational interests of their patrons. This sector comprises (1) establishments that are involved in producing, promoting, or participating in live performances, events, or exhibits intended for public viewing; (2) establishments that preserve and exhibit objects and sites of historical, cultural, or educational interest; and (3) establishments that operate facilities or provide services that enable patrons to participate in recreational activities or pursue amusement, hobby, and leisure-time interests.", "Workers employed in firms in construction. The Construction sector comprises establishments primarily engaged in the construction of buildings or engineering projects (e.g., highways and utility systems). Construction work done may include new work, additions, alterations, or maintenance and repairs.", "Workers employed in firms in educational services, health care, and social assistance. Outpatient health services, other than hospital care, including: public health administration; research and education; categorical health programs; treatment and immunization clinics; nursing; environmental health activities such as air and water pollution control; ambulance service if provided separately from fire protection services, and other general public health activities such as mosquito abatement. School health services provided by health agencies (rather than school agencies) are included here. Sewage treatment operations are classified under Sewerage.", "Workers employed in firms in finance, insurance, real estate and rental and leasing. The Real Estate and Rental and Leasing sector comprises establishments primarily engaged in renting, leasing, or otherwise allowing the use of tangible or intangible assets, and establishments providing related services. The major portion of this sector comprises establishments that rent, lease, or otherwise allow the use of their own assets by others. The assets may be tangible, as is the case of real estate and equipment, or intangible, as is the case with patents and trademarks.", "Workers employed in firms in information. The Information sector comprises establishments engaged in the following processes: (a) producing and distributing information and cultural products, (b) providing the means to transmit or distribute these products as well as data or communications, and (c) processing data. Included are the publishing industries, the motion picture and sound recording industries; the broadcasting industries, the telecommunications industries; Web search portals, data processing industries, and the information services industries.", "Workers employed in firms in manufacturing. The Manufacturing sector comprises establishments engaged in the mechanical, physical, or chemical transformation of materials, substances, or components into new products.", "Workers employed in firms in other services except public administration. The Other Services (Except Public Administration) sector comprises establishments engaged in providing services not specifically provided for elsewhere in the classification system. Establishments in this sector are primarily engaged in activities such as equipment and machinery repairing, promoting or administering religious activities, grantmaking, advocacy, and providing drycleaning and laundry services, personal care services, death care services, pet care services, photofinishing services, temporary parking services, and dating services. Private households that engage in employing workers on or about the premises in activities primarily concerned with the operation of the household are included in this sector.", "Workers employed in firms in public administration. The Public Administration sector consists of establishments of federal, state, and local government agencies that administer, oversee, and manage public programs and have executive, legislative, or judicial authority over other institutions within a given area. These agencies also set policy, create laws, adjudicate civil and criminal legal cases, provide for public safety and for national defense. In general, government establishments in the public administration sector oversee governmental programs and activities that are not performed by private establishments.", "Workers employed in firms in retail trade. The Retail Trade sector comprises establishments engaged in retailing merchandise, generally without transformation, and rendering services incidental to the sale of merchandise. The retailing process is the final step in the distribution of merchandise; retailers are, therefore, organized to sell merchandise in small quantities to the general public.", "Workers employed in firms in professional scientific, management, administrative and waste management services. The Administrative and Support and Waste Management and Remediation Services sector comprises establishments performing routine support activities for the day-to-day operations of other organizations. The establishments in this sector specialize in one or more of these support activities and provide these services to clients in a variety of industries and, in some cases, to households. Activities performed include office administration, hiring and placing of personnel, document preparation and similar clerical services, solicitation, collection, security and surveillance services, cleaning, and waste disposal services.", "Workers employed in firms in transportation, warehousing, and utilities. The Transportation and Warehousing sector includes industries providing transportation of passengers and cargo, warehousing and storage for goods, scenic and sightseeing transportation, and support activities related to modes of transportation. The modes of transportation are air, rail, water, road, and pipeline.", "Workers employed in firms in wholesale trade. The Wholesale Trade sector comprises establishments engaged in wholesaling merchandise, generally without transformation, and rendering services incidental to the sale of merchandise. The wholesaling process is an intermediate step in the distribution of merchandise. Wholesalers are organized to sell or arrange the purchase or sale of (a) goods for resale (i.e., goods sold to other wholesalers or retailers), (b) capital or durable nonconsumer goods, and (c) raw and intermediate materials and supplies used in production.", "Households with two female partners. An unmarried partner is a person age 15 years and over, who is not related to the householder, who shares living quarters, and who has an intimate relationship with the householder.", "Four car households. The number of households with four or more passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Gini Index. The Gini index, or index of income concentration, is a statistical measure of income inequality ranging from 0 to 1. A measure of 1 indicates perfect inequality, i.e., one household having all the income and rest having none. A measure of 0 indicates perfect equality, i.e., all households having an equal share of income.", "Population who completed a graduate or professional degree", "Population living in group quarters", "Population with high school degree, including GED. The number of people in a geographic area over the age of 25 who attained a high school degree or GED.", "Households on cash public assistance or receiving food stamps (SNAP)", "Students Enrolled in Grades 1 to 4. The total number of people in each geography currently enrolled in grades 1 through 4 inclusive.  This corresponds roughly to elementary school.", "Students Enrolled in Grades 5 to 8. The total number of people in each geography currently enrolled in grades 5 through 8 inclusive.  This corresponds roughly to middle school.", "Students Enrolled in Grades 9 to 12. The total number of people in each geography currently enrolled in grades 9 through 12 inclusive.  This corresponds roughly to high school.", "Students Enrolled in School. The total number of people in each geography currently enrolled at any level of school, from nursery or pre-school to advanced post-graduate education.  Only includes those over the age of 3.", "Students Enrolled as Undergraduate in College. The number of people in a geographic area who are enrolled in college at the undergraduate level. Enrollment refers to being registered or listed as a student in an educational program leading to a college degree. This may be a public school or college, a private school or college.", "Less than high school graduate. The number of people in a geographic area over the age of 25 who have not completed high school or any other advanced degree.", "Men age 45 to 64 who obtained an associate's degree", "Men age 45 to 64 who obtained a bachelor's degree", "Men age 45 to 64 who obtained a graduate or professional degree", "Men age 45 to 64 who attained less than a 9th grade education", "Men age 45 to 64 who attained between 9th and 12th grade, no diploma", "Men age 45 to 64 who completed high school or obtained GED", "Men age 45 to 64 who completed some college, no degree", "Men age 45 to 64 (\"middle aged\"). The male population between the age of fourty-five years to sixty-four years within the specified area.", "Households with two male partners. An unmarried partner is a person age 15 years and over, who is not related to the householder, who shares living quarters, and who has an intimate relationship with the householder.", "Civilian Employed Population in Management, Business, Science, and Arts Occupations. The number of employed civilians 16 years old and over in each geography in the Management, Business, Science, and Arts occupations. Occupation codes are based on the Standard Occupational Classification (SOC), published by the Executive Office of the President, Office of Management and Budget.", "Workers age 16 and over with no vehicle. All people in a geographic area over the age of 16 who do not own a car.", "Car-free households. The number of households without passenger cars, vans, and pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Not a U.S. Citizen Population. The number of people within each geography who indicated that they are not U.S. citizens.", "Workers employed in management business science and arts occupations", "Workers employed in natural resources, construction, and maintenance occupations", "Workers employed in production, transportation, and material moving", "Workers employed in sales and office occupations", "Workers employed in service occupations", "One car households. The number of households with one passenger car, van, , pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Two car households. The number of households with two passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Three car households. The number of households with one passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Population age 25 to 64. The number of people in each geography who are between the ages of 25 and 64. ", "Population for Whom Poverty Status Determined. The number of people in each geography who could be identified as either living in poverty or not.  This should be used as the denominator when calculating poverty rates, as it excludes people for whom it was not possible to determine poverty.", "Population 1 year and over. All people, male and female, child and adult, living in a given geographic area that are 1 year and older.", "Population 3 Years and Over. The total number of people in each geography age 3 years and over.  This denominator is mostly used to calculate rates of school enrollment.", "Income In The Past 12 Months Below Poverty Level. The number of people in a geographic area who are part of a family (which could be just them as an individual) determined to be \"in poverty\" following the Office of Management and Budget's Directive 14. (https://www.census.gov/hhes/povmeas/methodology/ombdir14.html)", "Civilian Employed Population in Sales and Office Occupations. The number of employed civilians 16 years old and over in each geography in the Sales and Office occupations. Occupation codes are based on the Standard Occupational Classification (SOC), published by the Executive Office of the President, Office of Management and Budget.", "Population who completed some college or obtained associate's degree. The number of people in a geographic area over the age of 25 who obtained an associate's degree, and did not complete a more advanced degree.", "Walked to Work. The number of workers age 16 years and over within a geographic area who primarily walked to work.  This would mean that of any way of getting to work, they travelled the most distance walking.", "Worked at Home. The count within a geographical area of workers over the age of 16 who worked at home.", "Workers over the Age of 16. The number of people in each geography who work. Workers include those employed at private for-profit companies, the self-employed, government workers and non-profit employees.", "Population Completed Associate's Degree. The number of people in a geographic area over the age of 25 who obtained a associate's degree, and did not complete a more advanced degree.", "Population Completed Bachelor's Degree. The number of people in a geographic area over the age of 25 who obtained a bachelor's degree, and did not complete a more advanced degree.", "Population Completed High School. The number of people in a geographic area over the age of 25 who completed high school, and did not complete a more advanced degree.", "Population completed less than one year of college, no degree. The number of people in a geographic area over the age of 25 who attended college for less than one year and no further.", "Population Completed Master's Degree. The number of people in a geographic area over the age of 25 who obtained a master's degree, but did not complete a more advanced degree.", "Population completed more than one year of college, no degree. The number of people in a geographic area over the age of 25 who attended college for more than one year but did not obtain a degree", "Population 25 Years and Over. The number of people in a geographic area who are over the age of 25.  This is used mostly as a denominator of educational attainment.", "Number of workers with a commute between 35 and 44 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 35 and 44 minutes. ", "Number of workers with a commute of over 60 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in over 60 minutes.", "Number of workers with less than 10 minute commute. The number of workers in a geographic area over the age of 16 who do not work from home and commute in less than 10 minutes.", "Workers age 16 and over who do not work from home. The number of workers in a geographic area over the age of 16 who do not work from home", "Hispanic of any race", "Population 5 Years and Over. The number of people in a geographic area who are over the age of 5.  This is primarily used as a denominator of measures of language spoken at home.", "Speaks only English at Home. The number of people in a geographic area over age 5 who speak only English at home.", "Speaks Spanish at Home. The number of people in a geographic area over age 5 who speak Spanish at home, possibly in addition to other languages.", "Speaks Spanish at Home, speaks English less than \"very well\"", "Population 15 Years and Over. The number of people in a geographic area who are over the age of 15.  This is used mostly as a denominator of marital status.", "Never Married. The number of people in a geographic area who have never been married.", "Currently married. The number of people in a geographic area who are currently married", "Married but separated. The number of people in a geographic area who are married but separated", "Widowed. The number of people in a geographic area who are widowed", "Divorced. The number of people in a geographic area who are divorced", null], "sample_rows": [{"geo_id": "26900", "nonfamily_households": 278698.0, "family_households": 504647.0, "median_year_structure_built": 1980.0, "rent_burden_not_computed": 16901.0, "rent_over_50_percent": 58986.0, "rent_40_to_50_percent": 25371.0, "rent_35_to_40_percent": 15909.0, "rent_30_to_35_percent": 22199.0, "rent_25_to_30_percent": 32976.0, "rent_20_to_25_percent": 32148.0, "rent_15_to_20_percent": 37167.0, "rent_10_to_15_percent": 25809.0, "rent_under_10_percent": 8908.0, "total_pop": 2048428.0, "male_pop": 999436.0, "female_pop": 1048992.0, "median_age": 36.7, "white_pop": 1467653.0, "black_pop": 314932.0, "asian_pop": 67304.0, "hispanic_pop": 139431.0, "amerindian_pop": 2121.0, "other_race_pop": 8014.0, "two_or_more_races_pop": 47651.0, "not_hispanic_pop": 1908997.0, "commuters_by_public_transportation": 9444.0, "households": 783345.0, "median_income": 61022.0, "income_per_capita": 32723.0, "housing_units": 867509.0, "vacant_housing_units": 84164.0, "vacant_housing_units_for_rent": 21939.0, "vacant_housing_units_for_sale": 5459.0, "median_rent": 735.0, "percent_income_spent_on_rent": 28.9, "owner_occupied_housing_units": 506971.0, "million_dollar_housing_units": 2326.0, "mortgaged_housing_units": 358277.0, "families_with_young_children": 152944.0, "two_parent_families_with_young_children": 97149.0, "two_parents_in_labor_force_families_with_young_children": 65489.0, "two_parents_father_in_labor_force_families_with_young_children": 28098.0, "two_parents_mother_in_labor_force_families_with_young_children": 3334.0, "two_parents_not_in_labor_force_families_with_young_children": 228.0, "one_parent_families_with_young_children": 55795.0, "father_one_parent_families_with_young_children": 10539.0, "father_in_labor_force_one_parent_families_with_young_children": 9404.0, "commute_10_14_mins": 123077.0, "commute_15_19_mins": 143358.0, "commute_20_24_mins": 143158.0, "commute_25_29_mins": 71278.0, "commute_30_34_mins": 153703.0, "commute_45_59_mins": 79288.0, "aggregate_travel_time_to_work": 24290660.0, "income_less_10000": 45546.0, "income_10000_14999": 29924.0, "income_15000_19999": 33438.0, "income_20000_24999": 35444.0, "income_25000_29999": 34335.0, "income_30000_34999": 38120.0, "income_35000_39999": 33173.0, "income_40000_44999": 40388.0, "income_45000_49999": 30714.0, "income_50000_59999": 62343.0, "income_60000_74999": 84598.0, "income_75000_99999": 103053.0, "income_100000_124999": 73593.0, "income_125000_149999": 47801.0, "income_150000_199999": 44734.0, "income_200000_or_more": 46141.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 907.0, "owner_occupied_housing_units_lower_value_quartile": 114700.0, "owner_occupied_housing_units_median_value": 169200.0, "owner_occupied_housing_units_upper_value_quartile": 269800.0, "married_households": 370034.0, "occupied_housing_units": 783345.0, "housing_units_renter_occupied": 276374.0, "dwellings_1_units_detached": 593119.0, "dwellings_1_units_attached": 53233.0, "dwellings_2_units": 11945.0, "dwellings_3_to_4_units": 36334.0, "dwellings_5_to_9_units": 60297.0, "dwellings_10_to_19_units": 44250.0, "dwellings_20_to_49_units": 18286.0, "dwellings_50_or_more_units": 28479.0, "mobile_homes": 21418.0, "housing_built_2005_or_later": 41321.0, "housing_built_2000_to_2004": 27570.0, "housing_built_1939_or_earlier": 39655.0, "male_under_5": 69658.0, "male_5_to_9": 71526.0, "male_10_to_14": 75607.0, "male_15_to_17": 41783.0, "male_18_to_19": 25085.0, "male_20": 10498.0, "male_21": 13370.0, "male_22_to_24": 39934.0, "male_25_to_29": 73126.0, "male_30_to_34": 70109.0, "male_35_to_39": 71641.0, "male_40_to_44": 63792.0, "male_45_to_49": 65456.0, "male_50_to_54": 63840.0, "male_55_to_59": 65895.0, "male_60_61": 25123.0, "male_62_64": 30848.0, "male_65_to_66": 19493.0, "male_67_to_69": 23605.0, "male_70_to_74": 34392.0, "male_75_to_79": 21617.0, "male_80_to_84": 12221.0, "male_85_and_over": 10817.0, "female_under_5": 65466.0, "female_5_to_9": 67469.0, "female_10_to_14": 72595.0, "female_15_to_17": 40961.0, "female_18_to_19": 22970.0, "female_20": 13375.0, "female_21": 10499.0, "female_22_to_24": 40317.0, "female_25_to_29": 77266.0, "female_30_to_34": 73814.0, "female_35_to_39": 74470.0, "female_40_to_44": 63083.0, "female_45_to_49": 68262.0, "female_50_to_54": 67981.0, "female_55_to_59": 68344.0, "female_60_to_61": 25653.0, "female_62_to_64": 37534.0, "female_65_to_66": 22304.0, "female_67_to_69": 30787.0, "female_70_to_74": 36965.0, "female_75_to_79": 28302.0, "female_80_to_84": 19971.0, "female_85_and_over": 20604.0, "white_including_hispanic": 1552540.0, "black_including_hispanic": 318095.0, "amerindian_including_hispanic": 2948.0, "asian_including_hispanic": 67856.0, "commute_5_9_mins": 88106.0, "commute_35_39_mins": 41333.0, "commute_40_44_mins": 44817.0, "commute_60_89_mins": 31532.0, "commute_90_more_mins": 15253.0, "households_retirement_income": 132722.0, "armed_forces": 2038.0, "civilian_labor_force": 1072532.0, "employed_pop": 1024427.0, "unemployed_pop": 48105.0, "not_in_labor_force": 524642.0, "pop_16_over": 1599212.0, "pop_in_labor_force": 1074570.0, "asian_male_45_54": 4866.0, "asian_male_55_64": 2321.0, "black_male_45_54": 15764.0, "black_male_55_64": 14859.0, "hispanic_male_45_54": 8025.0, "hispanic_male_55_64": 4356.0, "white_male_45_54": 99012.0, "white_male_55_64": 99038.0, "bachelors_degree_2": 309149.0, "bachelors_degree_or_higher_25_64": 404176.0, "children": 505065.0, "children_in_single_female_hh": 140514.0, "commuters_by_bus": 9295.0, "commuters_by_car_truck_van": 919340.0, "commuters_by_carpool": 80542.0, "commuters_by_subway_or_elevated": 0.0, "commuters_drove_alone": 838798.0, "different_house_year_ago_different_city": 175100.0, "different_house_year_ago_same_city": 130290.0, "employed_agriculture_forestry_fishing_hunting_mining": 5662.0, "employed_arts_entertainment_recreation_accommodation_food": 87430.0, "employed_construction": 64349.0, "employed_education_health_social": 228417.0, "employed_finance_insurance_real_estate": 76139.0, "employed_information": 15589.0, "employed_manufacturing": 133197.0, "employed_other_services_not_public_admin": 46839.0, "employed_public_administration": 41493.0, "employed_retail_trade": 117609.0, "employed_science_management_admin_waste": 115065.0, "employed_transportation_warehousing_utilities": 64487.0, "employed_wholesale_trade": 28151.0, "female_female_households": 2598.0, "four_more_cars": 53563.0, "gini_index": 0.4672, "graduate_professional_degree": 169279.0, "group_quarters": 36742.0, "high_school_including_ged": 376270.0, "households_public_asst_or_food_stamps": 65847.0, "in_grades_1_to_4": 109132.0, "in_grades_5_to_8": 117524.0, "in_grades_9_to_12": 115582.0, "in_school": 512063.0, "in_undergrad_college": 87200.0, "less_than_high_school_graduate": 142586.0, "male_45_64_associates_degree": 19194.0, "male_45_64_bachelors_degree": 54060.0, "male_45_64_graduate_degree": 31829.0, "male_45_64_less_than_9_grade": 8349.0, "male_45_64_grade_9_12": 19810.0, "male_45_64_high_school": 76377.0, "male_45_64_some_college": 41543.0, "male_45_to_64": 251162.0, "male_male_households": 655.0, "management_business_sci_arts_employed": 406494.0, "no_car": 22623.0, "no_cars": 43956.0, "not_us_citizen_pop": 87384.0, "occupation_management_arts": 406494.0, "occupation_natural_resources_construction_maintenance": 76781.0, "occupation_production_transportation_material": 154464.0, "occupation_sales_office": 232906.0, "occupation_services": 153782.0, "one_car": 257923.0, "two_cars": 312372.0, "three_cars": 115531.0, "pop_25_64": 1086237.0, "pop_determined_poverty_status": 2004301.0, "population_1_year_and_over": 2023083.0, "population_3_years_over": 1964151.0, "poverty": 242598.0, "sales_office_employed": 232906.0, "some_college_and_associates_degree": 370031.0, "walked_to_work": 12971.0, "worked_at_home": 50427.0, "workers_16_and_over": 1006132.0, "associates_degree": 108811.0, "bachelors_degree": 309149.0, "high_school_diploma": 319126.0, "less_one_year_college": 87984.0, "masters_degree": 126050.0, "one_year_more_college": 173236.0, "pop_25_years_over": 1367315.0, "commute_35_44_mins": 86150.0, "commute_60_more_mins": 46785.0, "commute_less_10_mins": 108908.0, "commuters_16_over": 955705.0, "hispanic_any_race": 139431.0, "pop_5_years_over": 1913304.0, "speak_only_english_at_home": 1734121.0, "speak_spanish_at_home": 90272.0, "speak_spanish_at_home_low_english": 34394.0, "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "do_date": "2018"}, {"geo_id": "46060", "nonfamily_households": 156271.0, "family_households": 246052.0, "median_year_structure_built": 1985.0, "rent_burden_not_computed": 10831.0, "rent_over_50_percent": 33320.0, "rent_40_to_50_percent": 12238.0, "rent_35_to_40_percent": 8519.0, "rent_30_to_35_percent": 13537.0, "rent_25_to_30_percent": 19651.0, "rent_20_to_25_percent": 17540.0, "rent_15_to_20_percent": 18624.0, "rent_10_to_15_percent": 11905.0, "rent_under_10_percent": 5524.0, "total_pop": 1039073.0, "male_pop": 510267.0, "female_pop": 528806.0, "median_age": 39.0, "white_pop": 532748.0, "black_pop": 34331.0, "asian_pop": 29403.0, "hispanic_pop": 390183.0, "amerindian_pop": 25461.0, "other_race_pop": 1696.0, "two_or_more_races_pop": 24040.0, "not_hispanic_pop": 648890.0, "commuters_by_public_transportation": 9647.0, "households": 402323.0, "median_income": 53464.0, "income_per_capita": 29410.0, "housing_units": 462778.0, "vacant_housing_units": 60455.0, "vacant_housing_units_for_rent": 10611.0, "vacant_housing_units_for_sale": 5472.0, "median_rent": 758.0, "percent_income_spent_on_rent": 29.3, "owner_occupied_housing_units": 250634.0, "million_dollar_housing_units": 1240.0, "mortgaged_housing_units": 150794.0, "families_with_young_children": 65506.0, "two_parent_families_with_young_children": 38643.0, "two_parents_in_labor_force_families_with_young_children": 20967.0, "two_parents_father_in_labor_force_families_with_young_children": 15071.0, "two_parents_mother_in_labor_force_families_with_young_children": 1751.0, "two_parents_not_in_labor_force_families_with_young_children": 854.0, "one_parent_families_with_young_children": 26863.0, "father_one_parent_families_with_young_children": 6980.0, "father_in_labor_force_one_parent_families_with_young_children": 6224.0, "commute_10_14_mins": 51613.0, "commute_15_19_mins": 73705.0, "commute_20_24_mins": 69235.0, "commute_25_29_mins": 31267.0, "commute_30_34_mins": 72283.0, "commute_45_59_mins": 31052.0, "aggregate_travel_time_to_work": 10776855.0, "income_less_10000": 28808.0, "income_10000_14999": 16852.0, "income_15000_19999": 22087.0, "income_20000_24999": 21072.0, "income_25000_29999": 20776.0, "income_30000_34999": 17528.0, "income_35000_39999": 22701.0, "income_40000_44999": 20716.0, "income_45000_49999": 17826.0, "income_50000_59999": 31506.0, "income_60000_74999": 45509.0, "income_75000_99999": 46922.0, "income_100000_124999": 29568.0, "income_125000_149999": 20361.0, "income_150000_199999": 21024.0, "income_200000_or_more": 19067.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 887.0, "owner_occupied_housing_units_lower_value_quartile": 131000.0, "owner_occupied_housing_units_median_value": 198100.0, "owner_occupied_housing_units_upper_value_quartile": 295500.0, "married_households": 174435.0, "occupied_housing_units": 402323.0, "housing_units_renter_occupied": 151689.0, "dwellings_1_units_detached": 279400.0, "dwellings_1_units_attached": 32246.0, "dwellings_2_units": 8987.0, "dwellings_3_to_4_units": 15135.0, "dwellings_5_to_9_units": 17975.0, "dwellings_10_to_19_units": 24844.0, "dwellings_20_to_49_units": 18559.0, "dwellings_50_or_more_units": 20498.0, "mobile_homes": 43790.0, "housing_built_2005_or_later": 12333.0, "housing_built_2000_to_2004": 12964.0, "housing_built_1939_or_earlier": 11675.0, "male_under_5": 29795.0, "male_5_to_9": 25824.0, "male_10_to_14": 36025.0, "male_15_to_17": 18140.0, "male_18_to_19": 16370.0, "male_20": 7960.0, "male_21": 11123.0, "male_22_to_24": 26646.0, "male_25_to_29": 38244.0, "male_30_to_34": 31218.0, "male_35_to_39": 29694.0, "male_40_to_44": 29541.0, "male_45_to_49": 28034.0, "male_50_to_54": 27582.0, "male_55_to_59": 30300.0, "male_60_61": 13076.0, "male_62_64": 17585.0, "male_65_to_66": 12358.0, "male_67_to_69": 18438.0, "male_70_to_74": 23810.0, "male_75_to_79": 19107.0, "male_80_to_84": 11425.0, "male_85_and_over": 7972.0, "female_under_5": 28517.0, "female_5_to_9": 29267.0, "female_10_to_14": 30008.0, "female_15_to_17": 18718.0, "female_18_to_19": 16162.0, "female_20": 8869.0, "female_21": 10737.0, "female_22_to_24": 24421.0, "female_25_to_29": 34757.0, "female_30_to_34": 29201.0, "female_35_to_39": 29735.0, "female_40_to_44": 29483.0, "female_45_to_49": 29141.0, "female_50_to_54": 28973.0, "female_55_to_59": 33143.0, "female_60_to_61": 14638.0, "female_62_to_64": 20599.0, "female_65_to_66": 15091.0, "female_67_to_69": 19232.0, "female_70_to_74": 28344.0, "female_75_to_79": 22189.0, "female_80_to_84": 14244.0, "female_85_and_over": 13337.0, "white_including_hispanic": 771880.0, "black_including_hispanic": 37940.0, "amerindian_including_hispanic": 45440.0, "asian_including_hispanic": 30481.0, "commute_5_9_mins": 34531.0, "commute_35_39_mins": 14769.0, "commute_40_44_mins": 18145.0, "commute_60_89_mins": 11778.0, "commute_90_more_mins": 9041.0, "households_retirement_income": 98719.0, "armed_forces": 6469.0, "civilian_labor_force": 481650.0, "employed_pop": 454426.0, "unemployed_pop": 27224.0, "not_in_labor_force": 359257.0, "pop_16_over": 847376.0, "pop_in_labor_force": 488119.0, "asian_male_45_54": 1718.0, "asian_male_55_64": 1028.0, "black_male_45_54": 2320.0, "black_male_55_64": 2511.0, "hispanic_male_45_54": 19695.0, "hispanic_male_55_64": 15737.0, "white_male_45_54": 29881.0, "white_male_55_64": 39845.0, "bachelors_degree_2": 129456.0, "bachelors_degree_or_higher_25_64": 146895.0, "children": 216294.0, "children_in_single_female_hh": 60310.0, "commuters_by_bus": 9231.0, "commuters_by_car_truck_van": 391151.0, "commuters_by_carpool": 45268.0, "commuters_by_subway_or_elevated": 0.0, "commuters_drove_alone": 345883.0, "different_house_year_ago_different_city": 107059.0, "different_house_year_ago_same_city": 88415.0, "employed_agriculture_forestry_fishing_hunting_mining": 4515.0, "employed_arts_entertainment_recreation_accommodation_food": 54774.0, "employed_construction": 30497.0, "employed_education_health_social": 111650.0, "employed_finance_insurance_real_estate": 25917.0, "employed_information": 9169.0, "employed_manufacturing": 30269.0, "employed_other_services_not_public_admin": 24596.0, "employed_public_administration": 26166.0, "employed_retail_trade": 52107.0, "employed_science_management_admin_waste": 56301.0, "employed_transportation_warehousing_utilities": 19404.0, "employed_wholesale_trade": 9061.0, "female_female_households": 1097.0, "four_more_cars": 25127.0, "gini_index": 0.4655, "graduate_professional_degree": 91482.0, "group_quarters": 30683.0, "high_school_including_ged": 162971.0, "households_public_asst_or_food_stamps": 52477.0, "in_grades_1_to_4": 46300.0, "in_grades_5_to_8": 54855.0, "in_grades_9_to_12": 51001.0, "in_school": 260232.0, "in_undergrad_college": 74174.0, "less_than_high_school_graduate": 82016.0, "male_45_64_associates_degree": 9438.0, "male_45_64_bachelors_degree": 17737.0, "male_45_64_graduate_degree": 13971.0, "male_45_64_less_than_9_grade": 4779.0, "male_45_64_grade_9_12": 10212.0, "male_45_64_high_school": 28259.0, "male_45_64_some_college": 32181.0, "male_45_to_64": 116577.0, "male_male_households": 923.0, "management_business_sci_arts_employed": 163976.0, "no_car": 14823.0, "no_cars": 31391.0, "not_us_citizen_pop": 68823.0, "occupation_management_arts": 163976.0, "occupation_natural_resources_construction_maintenance": 42817.0, "occupation_production_transportation_material": 43438.0, "occupation_sales_office": 98588.0, "occupation_services": 105607.0, "one_car": 149428.0, "two_cars": 147512.0, "three_cars": 48865.0, "pop_25_64": 494944.0, "pop_determined_poverty_status": 1010866.0, "population_1_year_and_over": 1028540.0, "population_3_years_over": 1002894.0, "poverty": 164204.0, "sales_office_employed": 98588.0, "some_college_and_associates_degree": 234566.0, "walked_to_work": 9573.0, "worked_at_home": 24877.0, "workers_16_and_over": 450472.0, "associates_degree": 59438.0, "bachelors_degree": 129456.0, "high_school_diploma": 135474.0, "less_one_year_college": 49084.0, "masters_degree": 63221.0, "one_year_more_college": 126044.0, "pop_25_years_over": 700491.0, "commute_35_44_mins": 32914.0, "commute_60_more_mins": 20819.0, "commute_less_10_mins": 42707.0, "commuters_16_over": 425595.0, "hispanic_any_race": 390183.0, "pop_5_years_over": 980761.0, "speak_only_english_at_home": 695617.0, "speak_spanish_at_home": 232803.0, "speak_spanish_at_home_low_english": 69797.0, "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "do_date": "2018"}, {"geo_id": "27260", "nonfamily_households": 194004.0, "family_households": 381419.0, "median_year_structure_built": 1989.0, "rent_burden_not_computed": 12944.0, "rent_over_50_percent": 45093.0, "rent_40_to_50_percent": 18215.0, "rent_35_to_40_percent": 14818.0, "rent_30_to_35_percent": 20763.0, "rent_25_to_30_percent": 24418.0, "rent_20_to_25_percent": 27595.0, "rent_15_to_20_percent": 26875.0, "rent_10_to_15_percent": 14085.0, "rent_under_10_percent": 5305.0, "total_pop": 1534701.0, "male_pop": 750768.0, "female_pop": 783933.0, "median_age": 38.8, "white_pop": 958883.0, "black_pop": 320662.0, "asian_pop": 57530.0, "hispanic_pop": 141094.0, "amerindian_pop": 4226.0, "other_race_pop": 5279.0, "two_or_more_races_pop": 46618.0, "not_hispanic_pop": 1393607.0, "commuters_by_public_transportation": 6881.0, "households": 575423.0, "median_income": 60238.0, "income_per_capita": 33478.0, "housing_units": 655462.0, "vacant_housing_units": 80039.0, "vacant_housing_units_for_rent": 12318.0, "vacant_housing_units_for_sale": 5504.0, "median_rent": 916.0, "percent_income_spent_on_rent": 30.1, "owner_occupied_housing_units": 365312.0, "million_dollar_housing_units": 4107.0, "mortgaged_housing_units": 242246.0, "families_with_young_children": 106235.0, "two_parent_families_with_young_children": 64264.0, "two_parents_in_labor_force_families_with_young_children": 39081.0, "two_parents_father_in_labor_force_families_with_young_children": 21916.0, "two_parents_mother_in_labor_force_families_with_young_children": 1474.0, "two_parents_not_in_labor_force_families_with_young_children": 1793.0, "one_parent_families_with_young_children": 41971.0, "father_one_parent_families_with_young_children": 9709.0, "father_in_labor_force_one_parent_families_with_young_children": 8473.0, "commute_10_14_mins": 77246.0, "commute_15_19_mins": 96042.0, "commute_20_24_mins": 111589.0, "commute_25_29_mins": 52434.0, "commute_30_34_mins": 124852.0, "commute_45_59_mins": 65753.0, "aggregate_travel_time_to_work": 18374920.0, "income_less_10000": 33142.0, "income_10000_14999": 22168.0, "income_15000_19999": 25102.0, "income_20000_24999": 29629.0, "income_25000_29999": 25281.0, "income_30000_34999": 27043.0, "income_35000_39999": 27180.0, "income_40000_44999": 24435.0, "income_45000_49999": 23969.0, "income_50000_59999": 48368.0, "income_60000_74999": 63460.0, "income_75000_99999": 74748.0, "income_100000_124999": 49023.0, "income_125000_149999": 31371.0, "income_150000_199999": 32991.0, "income_200000_or_more": 37513.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 1082.0, "owner_occupied_housing_units_lower_value_quartile": 138500.0, "owner_occupied_housing_units_median_value": 217200.0, "owner_occupied_housing_units_upper_value_quartile": 333200.0, "married_households": 267481.0, "occupied_housing_units": 575423.0, "housing_units_renter_occupied": 210111.0, "dwellings_1_units_detached": 413134.0, "dwellings_1_units_attached": 27941.0, "dwellings_2_units": 9217.0, "dwellings_3_to_4_units": 23922.0, "dwellings_5_to_9_units": 33745.0, "dwellings_10_to_19_units": 42434.0, "dwellings_20_to_49_units": 31727.0, "dwellings_50_or_more_units": 24906.0, "mobile_homes": 47799.0, "housing_built_2005_or_later": 40204.0, "housing_built_2000_to_2004": 20800.0, "housing_built_1939_or_earlier": 21579.0, "male_under_5": 47819.0, "male_5_to_9": 47336.0, "male_10_to_14": 53740.0, "male_15_to_17": 28564.0, "male_18_to_19": 20985.0, "male_20": 10091.0, "male_21": 10881.0, "male_22_to_24": 25465.0, "male_25_to_29": 55309.0, "male_30_to_34": 53096.0, "male_35_to_39": 46746.0, "male_40_to_44": 48355.0, "male_45_to_49": 48270.0, "male_50_to_54": 49795.0, "male_55_to_59": 51002.0, "male_60_61": 18137.0, "male_62_64": 26420.0, "male_65_to_66": 17528.0, "male_67_to_69": 22009.0, "male_70_to_74": 30019.0, "male_75_to_79": 20341.0, "male_80_to_84": 10262.0, "male_85_and_over": 8598.0, "female_under_5": 45196.0, "female_5_to_9": 48046.0, "female_10_to_14": 46654.0, "female_15_to_17": 26598.0, "female_18_to_19": 17558.0, "female_20": 10589.0, "female_21": 7212.0, "female_22_to_24": 26563.0, "female_25_to_29": 56167.0, "female_30_to_34": 54789.0, "female_35_to_39": 53185.0, "female_40_to_44": 46271.0, "female_45_to_49": 51161.0, "female_50_to_54": 52076.0, "female_55_to_59": 58209.0, "female_60_to_61": 18618.0, "female_62_to_64": 31005.0, "female_65_to_66": 20630.0, "female_67_to_69": 24146.0, "female_70_to_74": 35528.0, "female_75_to_79": 23190.0, "female_80_to_84": 16406.0, "female_85_and_over": 14136.0, "white_including_hispanic": 1062395.0, "black_including_hispanic": 325384.0, "amerindian_including_hispanic": 4804.0, "asian_including_hispanic": 58444.0, "commute_5_9_mins": 48226.0, "commute_35_39_mins": 23995.0, "commute_40_44_mins": 29943.0, "commute_60_89_mins": 33084.0, "commute_90_more_mins": 9943.0, "households_retirement_income": 113217.0, "armed_forces": 13944.0, "civilian_labor_force": 769560.0, "employed_pop": 727956.0, "unemployed_pop": 41604.0, "not_in_labor_force": 446143.0, "pop_16_over": 1229647.0, "pop_in_labor_force": 783504.0, "asian_male_45_54": 3519.0, "asian_male_55_64": 2468.0, "black_male_45_54": 18486.0, "black_male_55_64": 16769.0, "hispanic_male_45_54": 9616.0, "hispanic_male_55_64": 5656.0, "white_male_45_54": 64508.0, "white_male_55_64": 69283.0, "bachelors_degree_2": 214643.0, "bachelors_degree_or_higher_25_64": 264083.0, "children": 343953.0, "children_in_single_female_hh": 101466.0, "commuters_by_bus": 6540.0, "commuters_by_car_truck_van": 647254.0, "commuters_by_carpool": 61676.0, "commuters_by_subway_or_elevated": 0.0, "commuters_drove_alone": 585578.0, "different_house_year_ago_different_city": 144494.0, "different_house_year_ago_same_city": 121602.0, "employed_agriculture_forestry_fishing_hunting_mining": 4232.0, "employed_arts_entertainment_recreation_accommodation_food": 75727.0, "employed_construction": 50972.0, "employed_education_health_social": 152021.0, "employed_finance_insurance_real_estate": 74216.0, "employed_information": 9582.0, "employed_manufacturing": 38590.0, "employed_other_services_not_public_admin": 40183.0, "employed_public_administration": 34950.0, "employed_retail_trade": 86928.0, "employed_science_management_admin_waste": 95608.0, "employed_transportation_warehousing_utilities": 49101.0, "employed_wholesale_trade": 15846.0, "female_female_households": 1552.0, "four_more_cars": 29316.0, "gini_index": 0.4819, "graduate_professional_degree": 121299.0, "group_quarters": 28817.0, "high_school_including_ged": 295990.0, "households_public_asst_or_food_stamps": 87382.0, "in_grades_1_to_4": 77334.0, "in_grades_5_to_8": 76850.0, "in_grades_9_to_12": 76442.0, "in_school": 369832.0, "in_undergrad_college": 77176.0, "less_than_high_school_graduate": 98837.0, "male_45_64_associates_degree": 15331.0, "male_45_64_bachelors_degree": 39696.0, "male_45_64_graduate_degree": 22560.0, "male_45_64_less_than_9_grade": 6135.0, "male_45_64_grade_9_12": 13305.0, "male_45_64_high_school": 56583.0, "male_45_64_some_college": 40014.0, "male_45_to_64": 193624.0, "male_male_households": 436.0, "management_business_sci_arts_employed": 273910.0, "no_car": 16525.0, "no_cars": 33522.0, "not_us_citizen_pop": 64264.0, "occupation_management_arts": 273910.0, "occupation_natural_resources_construction_maintenance": 64983.0, "occupation_production_transportation_material": 85744.0, "occupation_sales_office": 168575.0, "occupation_services": 134744.0, "one_car": 205857.0, "two_cars": 231393.0, "three_cars": 75335.0, "pop_25_64": 818611.0, "pop_determined_poverty_status": 1505541.0, "population_1_year_and_over": 1518224.0, "population_3_years_over": 1478437.0, "poverty": 183681.0, "sales_office_employed": 168575.0, "some_college_and_associates_degree": 330635.0, "walked_to_work": 11712.0, "worked_at_home": 46554.0, "workers_16_and_over": 729411.0, "associates_degree": 104185.0, "bachelors_degree": 214643.0, "high_school_diploma": 252167.0, "less_one_year_college": 82800.0, "masters_degree": 87492.0, "one_year_more_college": 143650.0, "pop_25_years_over": 1061404.0, "commute_35_44_mins": 53938.0, "commute_60_more_mins": 43027.0, "commute_less_10_mins": 57976.0, "commuters_16_over": 682857.0, "hispanic_any_race": 141094.0, "pop_5_years_over": 1441686.0, "speak_only_english_at_home": 1244084.0, "speak_spanish_at_home": 93622.0, "speak_spanish_at_home_low_english": 37633.0, "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "do_date": "2018"}, {"geo_id": "33340", "nonfamily_households": 242215.0, "family_households": 392183.0, "median_year_structure_built": 1964.0, "rent_burden_not_computed": 12611.0, "rent_over_50_percent": 60835.0, "rent_40_to_50_percent": 22178.0, "rent_35_to_40_percent": 14512.0, "rent_30_to_35_percent": 19416.0, "rent_25_to_30_percent": 26506.0, "rent_20_to_25_percent": 30735.0, "rent_15_to_20_percent": 31943.0, "rent_10_to_15_percent": 23626.0, "rent_under_10_percent": 8973.0, "total_pop": 1576113.0, "male_pop": 768414.0, "female_pop": 807699.0, "median_age": 37.9, "white_pop": 1043305.0, "black_pop": 255331.0, "asian_pop": 60745.0, "hispanic_pop": 171951.0, "amerindian_pop": 4956.0, "other_race_pop": 2755.0, "two_or_more_races_pop": 36512.0, "not_hispanic_pop": 1404162.0, "commuters_by_public_transportation": 20458.0, "households": 634398.0, "median_income": 60643.0, "income_per_capita": 35106.0, "housing_units": 681231.0, "vacant_housing_units": 46833.0, "vacant_housing_units_for_rent": 15651.0, "vacant_housing_units_for_sale": 3002.0, "median_rent": 766.0, "percent_income_spent_on_rent": 29.5, "owner_occupied_housing_units": 383063.0, "million_dollar_housing_units": 2333.0, "mortgaged_housing_units": 254432.0, "families_with_young_children": 113900.0, "two_parent_families_with_young_children": 71885.0, "two_parents_in_labor_force_families_with_young_children": 45136.0, "two_parents_father_in_labor_force_families_with_young_children": 23488.0, "two_parents_mother_in_labor_force_families_with_young_children": 1934.0, "two_parents_not_in_labor_force_families_with_young_children": 1327.0, "one_parent_families_with_young_children": 42015.0, "father_one_parent_families_with_young_children": 9310.0, "father_in_labor_force_one_parent_families_with_young_children": 8362.0, "commute_10_14_mins": 108460.0, "commute_15_19_mins": 131530.0, "commute_20_24_mins": 126628.0, "commute_25_29_mins": 62802.0, "commute_30_34_mins": 107535.0, "commute_45_59_mins": 38694.0, "aggregate_travel_time_to_work": 17238675.0, "income_less_10000": 39600.0, "income_10000_14999": 29851.0, "income_15000_19999": 25663.0, "income_20000_24999": 30961.0, "income_25000_29999": 27466.0, "income_30000_34999": 30319.0, "income_35000_39999": 27424.0, "income_40000_44999": 28359.0, "income_45000_49999": 27588.0, "income_50000_59999": 45891.0, "income_60000_74999": 63167.0, "income_75000_99999": 78576.0, "income_100000_124999": 59991.0, "income_125000_149999": 38530.0, "income_150000_199999": 40570.0, "income_200000_or_more": 40442.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 884.0, "owner_occupied_housing_units_lower_value_quartile": 147200.0, "owner_occupied_housing_units_median_value": 222100.0, "owner_occupied_housing_units_upper_value_quartile": 327800.0, "married_households": 282117.0, "occupied_housing_units": 634398.0, "housing_units_renter_occupied": 251335.0, "dwellings_1_units_detached": 371711.0, "dwellings_1_units_attached": 37640.0, "dwellings_2_units": 77292.0, "dwellings_3_to_4_units": 33769.0, "dwellings_5_to_9_units": 44377.0, "dwellings_10_to_19_units": 25847.0, "dwellings_20_to_49_units": 37979.0, "dwellings_50_or_more_units": 48375.0, "mobile_homes": 4121.0, "housing_built_2005_or_later": 12462.0, "housing_built_2000_to_2004": 9402.0, "housing_built_1939_or_earlier": 43162.0, "male_under_5": 50055.0, "male_5_to_9": 49984.0, "male_10_to_14": 53681.0, "male_15_to_17": 31589.0, "male_18_to_19": 21013.0, "male_20": 9060.0, "male_21": 9426.0, "male_22_to_24": 31039.0, "male_25_to_29": 55246.0, "male_30_to_34": 53506.0, "male_35_to_39": 53672.0, "male_40_to_44": 43529.0, "male_45_to_49": 47160.0, "male_50_to_54": 49470.0, "male_55_to_59": 49782.0, "male_60_61": 22754.0, "male_62_64": 29869.0, "male_65_to_66": 16981.0, "male_67_to_69": 22300.0, "male_70_to_74": 26977.0, "male_75_to_79": 17494.0, "male_80_to_84": 13123.0, "male_85_and_over": 10704.0, "female_under_5": 47627.0, "female_5_to_9": 47729.0, "female_10_to_14": 51982.0, "female_15_to_17": 30199.0, "female_18_to_19": 19983.0, "female_20": 10815.0, "female_21": 8404.0, "female_22_to_24": 30008.0, "female_25_to_29": 56728.0, "female_30_to_34": 54620.0, "female_35_to_39": 54970.0, "female_40_to_44": 46301.0, "female_45_to_49": 48020.0, "female_50_to_54": 52021.0, "female_55_to_59": 51987.0, "female_60_to_61": 23517.0, "female_62_to_64": 34309.0, "female_65_to_66": 17944.0, "female_67_to_69": 23918.0, "female_70_to_74": 33180.0, "female_75_to_79": 24143.0, "female_80_to_84": 16614.0, "female_85_and_over": 22680.0, "white_including_hispanic": 1132203.0, "black_including_hispanic": 258372.0, "amerindian_including_hispanic": 6184.0, "asian_including_hispanic": 61615.0, "commute_5_9_mins": 75830.0, "commute_35_39_mins": 26123.0, "commute_40_44_mins": 25392.0, "commute_60_89_mins": 20007.0, "commute_90_more_mins": 9798.0, "households_retirement_income": 114459.0, "armed_forces": 1256.0, "civilian_labor_force": 826511.0, "employed_pop": 796045.0, "unemployed_pop": 30466.0, "not_in_labor_force": 426304.0, "pop_16_over": 1254071.0, "pop_in_labor_force": 827767.0, "asian_male_45_54": 3006.0, "asian_male_55_64": 2077.0, "black_male_45_54": 12986.0, "black_male_55_64": 11825.0, "hispanic_male_45_54": 8940.0, "hispanic_male_55_64": 5851.0, "white_male_45_54": 69569.0, "white_male_55_64": 81586.0, "bachelors_degree_2": 242429.0, "bachelors_degree_or_higher_25_64": 316044.0, "children": 362846.0, "children_in_single_female_hh": 106049.0, "commuters_by_bus": 20019.0, "commuters_by_car_truck_van": 700694.0, "commuters_by_carpool": 59126.0, "commuters_by_subway_or_elevated": 27.0, "commuters_drove_alone": 641568.0, "different_house_year_ago_different_city": 123983.0, "different_house_year_ago_same_city": 88861.0, "employed_agriculture_forestry_fishing_hunting_mining": 3646.0, "employed_arts_entertainment_recreation_accommodation_food": 67299.0, "employed_construction": 35813.0, "employed_education_health_social": 199346.0, "employed_finance_insurance_real_estate": 55256.0, "employed_information": 14323.0, "employed_manufacturing": 134631.0, "employed_other_services_not_public_admin": 33433.0, "employed_public_administration": 22306.0, "employed_retail_trade": 86193.0, "employed_science_management_admin_waste": 83840.0, "employed_transportation_warehousing_utilities": 39340.0, "employed_wholesale_trade": 20619.0, "female_female_households": 1269.0, "four_more_cars": 28834.0, "gini_index": 0.4806, "graduate_professional_degree": 144121.0, "group_quarters": 30625.0, "high_school_including_ged": 287017.0, "households_public_asst_or_food_stamps": 83904.0, "in_grades_1_to_4": 77968.0, "in_grades_5_to_8": 84746.0, "in_grades_9_to_12": 84113.0, "in_school": 390593.0, "in_undergrad_college": 74877.0, "less_than_high_school_graduate": 90903.0, "male_45_64_associates_degree": 18073.0, "male_45_64_bachelors_degree": 38280.0, "male_45_64_graduate_degree": 27434.0, "male_45_64_less_than_9_grade": 5770.0, "male_45_64_grade_9_12": 14100.0, "male_45_64_high_school": 56515.0, "male_45_64_some_college": 38863.0, "male_45_to_64": 199035.0, "male_male_households": 1297.0, "management_business_sci_arts_employed": 318872.0, "no_car": 23081.0, "no_cars": 57827.0, "not_us_citizen_pop": 64650.0, "occupation_management_arts": 318872.0, "occupation_natural_resources_construction_maintenance": 47972.0, "occupation_production_transportation_material": 123876.0, "occupation_sales_office": 172980.0, "occupation_services": 132345.0, "one_car": 229260.0, "two_cars": 244668.0, "three_cars": 73809.0, "pop_25_64": 827461.0, "pop_determined_poverty_status": 1546059.0, "population_1_year_and_over": 1558089.0, "population_3_years_over": 1518429.0, "poverty": 203363.0, "sales_office_employed": 172980.0, "some_college_and_associates_degree": 309049.0, "walked_to_work": 18260.0, "worked_at_home": 39702.0, "workers_16_and_over": 787638.0, "associates_degree": 94651.0, "bachelors_degree": 242429.0, "high_school_diploma": 254838.0, "less_one_year_college": 59912.0, "masters_degree": 101038.0, "one_year_more_college": 154486.0, "pop_25_years_over": 1073519.0, "commute_35_44_mins": 51515.0, "commute_60_more_mins": 29805.0, "commute_less_10_mins": 90967.0, "commuters_16_over": 747936.0, "hispanic_any_race": 171951.0, "pop_5_years_over": 1478431.0, "speak_only_english_at_home": 1280418.0, "speak_spanish_at_home": 116501.0, "speak_spanish_at_home_low_english": 40033.0, "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "do_date": "2018"}, {"geo_id": "18140", "nonfamily_households": 297098.0, "family_households": 504707.0, "median_year_structure_built": 1980.0, "rent_burden_not_computed": 16749.0, "rent_over_50_percent": 59242.0, "rent_40_to_50_percent": 25091.0, "rent_35_to_40_percent": 15175.0, "rent_30_to_35_percent": 24314.0, "rent_25_to_30_percent": 33637.0, "rent_20_to_25_percent": 42384.0, "rent_15_to_20_percent": 44746.0, "rent_10_to_15_percent": 33954.0, "rent_under_10_percent": 10415.0, "total_pop": 2106541.0, "male_pop": 1036528.0, "female_pop": 1070013.0, "median_age": 36.1, "white_pop": 1520089.0, "black_pop": 326562.0, "asian_pop": 93612.0, "hispanic_pop": 90759.0, "amerindian_pop": 2095.0, "other_race_pop": 4687.0, "two_or_more_races_pop": 68593.0, "not_hispanic_pop": 2015782.0, "commuters_by_public_transportation": 17364.0, "households": 801805.0, "median_income": 64052.0, "income_per_capita": 33830.0, "housing_units": 869414.0, "vacant_housing_units": 67609.0, "vacant_housing_units_for_rent": 15179.0, "vacant_housing_units_for_sale": 3781.0, "median_rent": 774.0, "percent_income_spent_on_rent": 26.9, "owner_occupied_housing_units": 496098.0, "million_dollar_housing_units": 2648.0, "mortgaged_housing_units": 345029.0, "families_with_young_children": 158419.0, "two_parent_families_with_young_children": 106255.0, "two_parents_in_labor_force_families_with_young_children": 68438.0, "two_parents_father_in_labor_force_families_with_young_children": 33812.0, "two_parents_mother_in_labor_force_families_with_young_children": 3299.0, "two_parents_not_in_labor_force_families_with_young_children": 706.0, "one_parent_families_with_young_children": 52164.0, "father_one_parent_families_with_young_children": 12480.0, "father_in_labor_force_one_parent_families_with_young_children": 10364.0, "commute_10_14_mins": 130407.0, "commute_15_19_mins": 169081.0, "commute_20_24_mins": 178185.0, "commute_25_29_mins": 89377.0, "commute_30_34_mins": 144870.0, "commute_45_59_mins": 60472.0, "aggregate_travel_time_to_work": 23619925.0, "income_less_10000": 44976.0, "income_10000_14999": 28193.0, "income_15000_19999": 30279.0, "income_20000_24999": 34129.0, "income_25000_29999": 34609.0, "income_30000_34999": 36141.0, "income_35000_39999": 36744.0, "income_40000_44999": 34120.0, "income_45000_49999": 32058.0, "income_50000_59999": 62837.0, "income_60000_74999": 84507.0, "income_75000_99999": 107957.0, "income_100000_124999": 78789.0, "income_125000_149999": 50665.0, "income_150000_199999": 53671.0, "income_200000_or_more": 52130.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 947.0, "owner_occupied_housing_units_lower_value_quartile": 127700.0, "owner_occupied_housing_units_median_value": 196700.0, "owner_occupied_housing_units_upper_value_quartile": 297000.0, "married_households": 365588.0, "occupied_housing_units": 801805.0, "housing_units_renter_occupied": 305707.0, "dwellings_1_units_detached": 536169.0, "dwellings_1_units_attached": 62150.0, "dwellings_2_units": 23777.0, "dwellings_3_to_4_units": 53559.0, "dwellings_5_to_9_units": 72253.0, "dwellings_10_to_19_units": 43285.0, "dwellings_20_to_49_units": 26699.0, "dwellings_50_or_more_units": 31465.0, "mobile_homes": 19942.0, "housing_built_2005_or_later": 32501.0, "housing_built_2000_to_2004": 26563.0, "housing_built_1939_or_earlier": 33139.0, "male_under_5": 71695.0, "male_5_to_9": 67548.0, "male_10_to_14": 72550.0, "male_15_to_17": 40931.0, "male_18_to_19": 31289.0, "male_20": 12663.0, "male_21": 16377.0, "male_22_to_24": 37877.0, "male_25_to_29": 85649.0, "male_30_to_34": 80192.0, "male_35_to_39": 75126.0, "male_40_to_44": 65502.0, "male_45_to_49": 68886.0, "male_50_to_54": 66301.0, "male_55_to_59": 62010.0, "male_60_61": 27441.0, "male_62_64": 33090.0, "male_65_to_66": 19335.0, "male_67_to_69": 24819.0, "male_70_to_74": 32610.0, "male_75_to_79": 21184.0, "male_80_to_84": 12495.0, "male_85_and_over": 10958.0, "female_under_5": 68706.0, "female_5_to_9": 66712.0, "female_10_to_14": 66898.0, "female_15_to_17": 39952.0, "female_18_to_19": 29054.0, "female_20": 14694.0, "female_21": 13345.0, "female_22_to_24": 39103.0, "female_25_to_29": 85598.0, "female_30_to_34": 79677.0, "female_35_to_39": 73839.0, "female_40_to_44": 68569.0, "female_45_to_49": 68092.0, "female_50_to_54": 65772.0, "female_55_to_59": 68537.0, "female_60_to_61": 26130.0, "female_62_to_64": 37774.0, "female_65_to_66": 21478.0, "female_67_to_69": 30057.0, "female_70_to_74": 39681.0, "female_75_to_79": 26262.0, "female_80_to_84": 20246.0, "female_85_and_over": 19837.0, "white_including_hispanic": 1572126.0, "black_including_hispanic": 330617.0, "amerindian_including_hispanic": 4300.0, "asian_including_hispanic": 94387.0, "commute_5_9_mins": 81520.0, "commute_35_39_mins": 34405.0, "commute_40_44_mins": 35917.0, "commute_60_89_mins": 32216.0, "commute_90_more_mins": 12142.0, "households_retirement_income": 143625.0, "armed_forces": 1140.0, "civilian_labor_force": 1114965.0, "employed_pop": 1068522.0, "unemployed_pop": 46443.0, "not_in_labor_force": 550221.0, "pop_16_over": 1666326.0, "pop_in_labor_force": 1116105.0, "asian_male_45_54": 5373.0, "asian_male_55_64": 3296.0, "black_male_45_54": 19550.0, "black_male_55_64": 16053.0, "hispanic_male_45_54": 4607.0, "hispanic_male_55_64": 2250.0, "white_male_45_54": 102201.0, "white_male_55_64": 99113.0, "bachelors_degree_2": 336606.0, "bachelors_degree_or_higher_25_64": 453732.0, "children": 494992.0, "children_in_single_female_hh": 129897.0, "commuters_by_bus": 17123.0, "commuters_by_car_truck_van": 939148.0, "commuters_by_carpool": 85959.0, "commuters_by_subway_or_elevated": 80.0, "commuters_drove_alone": 853189.0, "different_house_year_ago_different_city": 195425.0, "different_house_year_ago_same_city": 128511.0, "employed_agriculture_forestry_fishing_hunting_mining": 5679.0, "employed_arts_entertainment_recreation_accommodation_food": 96723.0, "employed_construction": 53941.0, "employed_education_health_social": 257270.0, "employed_finance_insurance_real_estate": 103012.0, "employed_information": 19660.0, "employed_manufacturing": 92262.0, "employed_other_services_not_public_admin": 43883.0, "employed_public_administration": 44756.0, "employed_retail_trade": 127344.0, "employed_science_management_admin_waste": 125011.0, "employed_transportation_warehousing_utilities": 67829.0, "employed_wholesale_trade": 31152.0, "female_female_households": 2023.0, "four_more_cars": 53496.0, "gini_index": 0.455, "graduate_professional_degree": 197029.0, "group_quarters": 52015.0, "high_school_including_ged": 388102.0, "households_public_asst_or_food_stamps": 83363.0, "in_grades_1_to_4": 107898.0, "in_grades_5_to_8": 109843.0, "in_grades_9_to_12": 110095.0, "in_school": 530393.0, "in_undergrad_college": 111162.0, "less_than_high_school_graduate": 112159.0, "male_45_64_associates_degree": 16910.0, "male_45_64_bachelors_degree": 58694.0, "male_45_64_graduate_degree": 31842.0, "male_45_64_less_than_9_grade": 5944.0, "male_45_64_grade_9_12": 16507.0, "male_45_64_high_school": 78799.0, "male_45_64_some_college": 49032.0, "male_45_to_64": 257728.0, "male_male_households": 1914.0, "management_business_sci_arts_employed": 456517.0, "no_car": 24820.0, "no_cars": 50375.0, "not_us_citizen_pop": 94286.0, "occupation_management_arts": 456517.0, "occupation_natural_resources_construction_maintenance": 63624.0, "occupation_production_transportation_material": 145796.0, "occupation_sales_office": 234361.0, "occupation_services": 168224.0, "one_car": 267590.0, "two_cars": 317932.0, "three_cars": 112412.0, "pop_25_64": 1138185.0, "pop_determined_poverty_status": 2051647.0, "population_1_year_and_over": 2079665.0, "population_3_years_over": 2024722.0, "poverty": 264071.0, "sales_office_employed": 234361.0, "some_college_and_associates_degree": 383251.0, "walked_to_work": 23241.0, "worked_at_home": 57693.0, "workers_16_and_over": 1049446.0, "associates_degree": 106068.0, "bachelors_degree": 336606.0, "high_school_diploma": 341859.0, "less_one_year_college": 92099.0, "masters_degree": 147147.0, "one_year_more_college": 185084.0, "pop_25_years_over": 1417147.0, "commute_35_44_mins": 70322.0, "commute_60_more_mins": 44358.0, "commute_less_10_mins": 104681.0, "commuters_16_over": 991753.0, "hispanic_any_race": 90759.0, "pop_5_years_over": 1966140.0, "speak_only_english_at_home": 1755508.0, "speak_spanish_at_home": 53629.0, "speak_spanish_at_home_low_english": 22796.0, "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "do_date": "2018"}]}