{"table_name": "puma_2018_5yr", "table_fullname": "bigquery-public-data.census_bureau_acs.puma_2018_5yr", "column_names": ["geo_id", "do_date", "total_pop", "households", "male_pop", "female_pop", "median_age", "male_under_5", "male_5_to_9", "male_10_to_14", "male_15_to_17", "male_18_to_19", "male_20", "male_21", "male_22_to_24", "male_25_to_29", "male_30_to_34", "male_35_to_39", "male_40_to_44", "male_45_to_49", "male_50_to_54", "male_55_to_59", "male_60_to_61", "male_62_to_64", "male_65_to_66", "male_67_to_69", "male_70_to_74", "male_75_to_79", "male_80_to_84", "male_85_and_over", "female_under_5", "female_5_to_9", "female_10_to_14", "female_15_to_17", "female_18_to_19", "female_20", "female_21", "female_22_to_24", "female_25_to_29", "female_30_to_34", "female_35_to_39", "female_40_to_44", "female_45_to_49", "female_50_to_54", "female_55_to_59", "female_60_to_61", "female_62_to_64", "female_65_to_66", "female_67_to_69", "female_70_to_74", "female_75_to_79", "female_80_to_84", "female_85_and_over", "white_pop", "population_1_year_and_over", "population_3_years_over", "pop_5_years_over", "pop_15_and_over", "pop_16_over", "pop_25_years_over", "pop_25_64", "pop_never_married", "pop_now_married", "pop_separated", "pop_widowed", "pop_divorced", "not_us_citizen_pop", "black_pop", "asian_pop", "hispanic_pop", "amerindian_pop", "other_race_pop", "two_or_more_races_pop", "hispanic_any_race", "not_hispanic_pop", "asian_male_45_54", "asian_male_55_64", "black_male_45_54", "black_male_55_64", "hispanic_male_45_54", "hispanic_male_55_64", "white_male_45_54", "white_male_55_64", "median_income", "income_per_capita", "income_less_10000", "income_10000_14999", "income_15000_19999", "income_20000_24999", "income_25000_29999", "income_30000_34999", "income_35000_39999", "income_40000_44999", "income_45000_49999", "income_50000_59999", "income_60000_74999", "income_75000_99999", "income_100000_124999", "income_125000_149999", "income_150000_199999", "income_200000_or_more", "pop_determined_poverty_status", "poverty", "gini_index", "housing_units", "renter_occupied_housing_units_paying_cash_median_gross_rent", "owner_occupied_housing_units_lower_value_quartile", "owner_occupied_housing_units_median_value", "owner_occupied_housing_units_upper_value_quartile", "occupied_housing_units", "housing_units_renter_occupied", "vacant_housing_units", "vacant_housing_units_for_rent", "vacant_housing_units_for_sale", "dwellings_1_units_detached", "dwellings_1_units_attached", "dwellings_2_units", "dwellings_3_to_4_units", "dwellings_5_to_9_units", "dwellings_10_to_19_units", "dwellings_20_to_49_units", "dwellings_50_or_more_units", "mobile_homes", "housing_built_2005_or_later", "housing_built_2000_to_2004", "housing_built_1939_or_earlier", "median_year_structure_built", "married_households", "nonfamily_households", "family_households", "households_public_asst_or_food_stamps", "male_male_households", "female_female_households", "children", "children_in_single_female_hh", "median_rent", "percent_income_spent_on_rent", "rent_burden_not_computed", "rent_over_50_percent", "rent_40_to_50_percent", "rent_35_to_40_percent", "rent_30_to_35_percent", "rent_25_to_30_percent", "rent_20_to_25_percent", "rent_15_to_20_percent", "rent_10_to_15_percent", "rent_under_10_percent", "owner_occupied_housing_units", "million_dollar_housing_units", "mortgaged_housing_units", "different_house_year_ago_different_city", "different_house_year_ago_same_city", "families_with_young_children", "two_parent_families_with_young_children", "two_parents_in_labor_force_families_with_young_children", "two_parents_father_in_labor_force_families_with_young_children", "two_parents_mother_in_labor_force_families_with_young_children", "two_parents_not_in_labor_force_families_with_young_children", "one_parent_families_with_young_children", "father_one_parent_families_with_young_children", "father_in_labor_force_one_parent_families_with_young_children", "commute_less_10_mins", "commute_10_14_mins", "commute_15_19_mins", "commute_20_24_mins", "commute_25_29_mins", "commute_30_34_mins", "commute_35_44_mins", "commute_60_more_mins", "commute_45_59_mins", "commuters_16_over", "walked_to_work", "worked_at_home", "no_car", "no_cars", "one_car", "two_cars", "three_cars", "four_more_cars", "aggregate_travel_time_to_work", "commuters_by_public_transportation", "commuters_by_bus", "commuters_by_car_truck_van", "commuters_by_carpool", "commuters_by_subway_or_elevated", "commuters_drove_alone", "group_quarters", "associates_degree", "bachelors_degree", "high_school_diploma", "less_one_year_college", "masters_degree", "one_year_more_college", "less_than_high_school_graduate", "high_school_including_ged", "bachelors_degree_2", "bachelors_degree_or_higher_25_64", "graduate_professional_degree", "some_college_and_associates_degree", "male_45_64_associates_degree", "male_45_64_bachelors_degree", "male_45_64_graduate_degree", "male_45_64_less_than_9_grade", "male_45_64_grade_9_12", "male_45_64_high_school", "male_45_64_some_college", "male_45_to_64", "employed_pop", "unemployed_pop", "pop_in_labor_force", "not_in_labor_force", "workers_16_and_over", "armed_forces", "civilian_labor_force", "employed_agriculture_forestry_fishing_hunting_mining", "employed_arts_entertainment_recreation_accommodation_food", "employed_construction", "employed_education_health_social", "employed_finance_insurance_real_estate", "employed_information", "employed_manufacturing", "employed_other_services_not_public_admin", "employed_public_administration", "employed_retail_trade", "employed_science_management_admin_waste", "employed_transportation_warehousing_utilities", "employed_wholesale_trade", "occupation_management_arts", "occupation_natural_resources_construction_maintenance", "occupation_production_transportation_material", "occupation_sales_office", "occupation_services", "management_business_sci_arts_employed", "sales_office_employed", "in_grades_1_to_4", "in_grades_5_to_8", "in_grades_9_to_12", "in_school", "in_undergrad_college", "speak_only_english_at_home", "speak_spanish_at_home", "speak_spanish_at_home_low_english"], "column_types": ["STRING", "DATE", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64"], "nested_column_names": ["geo_id", "do_date", "total_pop", "households", "male_pop", "female_pop", "median_age", "male_under_5", "male_5_to_9", "male_10_to_14", "male_15_to_17", "male_18_to_19", "male_20", "male_21", "male_22_to_24", "male_25_to_29", "male_30_to_34", "male_35_to_39", "male_40_to_44", "male_45_to_49", "male_50_to_54", "male_55_to_59", "male_60_to_61", "male_62_to_64", "male_65_to_66", "male_67_to_69", "male_70_to_74", "male_75_to_79", "male_80_to_84", "male_85_and_over", "female_under_5", "female_5_to_9", "female_10_to_14", "female_15_to_17", "female_18_to_19", "female_20", "female_21", "female_22_to_24", "female_25_to_29", "female_30_to_34", "female_35_to_39", "female_40_to_44", "female_45_to_49", "female_50_to_54", "female_55_to_59", "female_60_to_61", "female_62_to_64", "female_65_to_66", "female_67_to_69", "female_70_to_74", "female_75_to_79", "female_80_to_84", "female_85_and_over", "white_pop", "population_1_year_and_over", "population_3_years_over", "pop_5_years_over", "pop_15_and_over", "pop_16_over", "pop_25_years_over", "pop_25_64", "pop_never_married", "pop_now_married", "pop_separated", "pop_widowed", "pop_divorced", "not_us_citizen_pop", "black_pop", "asian_pop", "hispanic_pop", "amerindian_pop", "other_race_pop", "two_or_more_races_pop", "hispanic_any_race", "not_hispanic_pop", "asian_male_45_54", "asian_male_55_64", "black_male_45_54", "black_male_55_64", "hispanic_male_45_54", "hispanic_male_55_64", "white_male_45_54", "white_male_55_64", "median_income", "income_per_capita", "income_less_10000", "income_10000_14999", "income_15000_19999", "income_20000_24999", "income_25000_29999", "income_30000_34999", "income_35000_39999", "income_40000_44999", "income_45000_49999", "income_50000_59999", "income_60000_74999", "income_75000_99999", "income_100000_124999", "income_125000_149999", "income_150000_199999", "income_200000_or_more", "pop_determined_poverty_status", "poverty", "gini_index", "housing_units", "renter_occupied_housing_units_paying_cash_median_gross_rent", "owner_occupied_housing_units_lower_value_quartile", "owner_occupied_housing_units_median_value", "owner_occupied_housing_units_upper_value_quartile", "occupied_housing_units", "housing_units_renter_occupied", "vacant_housing_units", "vacant_housing_units_for_rent", "vacant_housing_units_for_sale", "dwellings_1_units_detached", "dwellings_1_units_attached", "dwellings_2_units", "dwellings_3_to_4_units", "dwellings_5_to_9_units", "dwellings_10_to_19_units", "dwellings_20_to_49_units", "dwellings_50_or_more_units", "mobile_homes", "housing_built_2005_or_later", "housing_built_2000_to_2004", "housing_built_1939_or_earlier", "median_year_structure_built", "married_households", "nonfamily_households", "family_households", "households_public_asst_or_food_stamps", "male_male_households", "female_female_households", "children", "children_in_single_female_hh", "median_rent", "percent_income_spent_on_rent", "rent_burden_not_computed", "rent_over_50_percent", "rent_40_to_50_percent", "rent_35_to_40_percent", "rent_30_to_35_percent", "rent_25_to_30_percent", "rent_20_to_25_percent", "rent_15_to_20_percent", "rent_10_to_15_percent", "rent_under_10_percent", "owner_occupied_housing_units", "million_dollar_housing_units", "mortgaged_housing_units", "different_house_year_ago_different_city", "different_house_year_ago_same_city", "families_with_young_children", "two_parent_families_with_young_children", "two_parents_in_labor_force_families_with_young_children", "two_parents_father_in_labor_force_families_with_young_children", "two_parents_mother_in_labor_force_families_with_young_children", "two_parents_not_in_labor_force_families_with_young_children", "one_parent_families_with_young_children", "father_one_parent_families_with_young_children", "father_in_labor_force_one_parent_families_with_young_children", "commute_less_10_mins", "commute_10_14_mins", "commute_15_19_mins", "commute_20_24_mins", "commute_25_29_mins", "commute_30_34_mins", "commute_35_44_mins", "commute_60_more_mins", "commute_45_59_mins", "commuters_16_over", "walked_to_work", "worked_at_home", "no_car", "no_cars", "one_car", "two_cars", "three_cars", "four_more_cars", "aggregate_travel_time_to_work", "commuters_by_public_transportation", "commuters_by_bus", "commuters_by_car_truck_van", "commuters_by_carpool", "commuters_by_subway_or_elevated", "commuters_drove_alone", "group_quarters", "associates_degree", "bachelors_degree", "high_school_diploma", "less_one_year_college", "masters_degree", "one_year_more_college", "less_than_high_school_graduate", "high_school_including_ged", "bachelors_degree_2", "bachelors_degree_or_higher_25_64", "graduate_professional_degree", "some_college_and_associates_degree", "male_45_64_associates_degree", "male_45_64_bachelors_degree", "male_45_64_graduate_degree", "male_45_64_less_than_9_grade", "male_45_64_grade_9_12", "male_45_64_high_school", "male_45_64_some_college", "male_45_to_64", "employed_pop", "unemployed_pop", "pop_in_labor_force", "not_in_labor_force", "workers_16_and_over", "armed_forces", "civilian_labor_force", "employed_agriculture_forestry_fishing_hunting_mining", "employed_arts_entertainment_recreation_accommodation_food", "employed_construction", "employed_education_health_social", "employed_finance_insurance_real_estate", "employed_information", "employed_manufacturing", "employed_other_services_not_public_admin", "employed_public_administration", "employed_retail_trade", "employed_science_management_admin_waste", "employed_transportation_warehousing_utilities", "employed_wholesale_trade", "occupation_management_arts", "occupation_natural_resources_construction_maintenance", "occupation_production_transportation_material", "occupation_sales_office", "occupation_services", "management_business_sci_arts_employed", "sales_office_employed", "in_grades_1_to_4", "in_grades_5_to_8", "in_grades_9_to_12", "in_school", "in_undergrad_college", "speak_only_english_at_home", "speak_spanish_at_home", "speak_spanish_at_home_low_english"], "nested_column_types": ["STRING", "DATE", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64"], "description": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], "sample_rows": [{"geo_id": "3604002", "do_date": "2014-01-01", "total_pop": 129976.0, "households": 46453.0, "male_pop": 64524.0, "female_pop": 65452.0, "median_age": 30.5, "male_under_5": 3965.0, "male_5_to_9": 3851.0, "male_10_to_14": 3643.0, "male_15_to_17": 2248.0, "male_18_to_19": 1254.0, "male_20": 741.0, "male_21": 975.0, "male_22_to_24": 5127.0, "male_25_to_29": 10579.0, "male_30_to_34": 7460.0, "male_35_to_39": 5139.0, "male_40_to_44": 4040.0, "male_45_to_49": 3108.0, "male_50_to_54": 3197.0, "male_55_to_59": 2768.0, "male_60_to_61": 876.0, "male_62_to_64": 917.0, "male_65_to_66": 688.0, "male_67_to_69": 932.0, "male_70_to_74": 1248.0, "male_75_to_79": 838.0, "male_80_to_84": 700.0, "male_85_and_over": 230.0, "female_under_5": 3907.0, "female_5_to_9": 3627.0, "female_10_to_14": 3401.0, "female_15_to_17": 2061.0, "female_18_to_19": 1365.0, "female_20": 930.0, "female_21": 860.0, "female_22_to_24": 5344.0, "female_25_to_29": 9056.0, "female_30_to_34": 6443.0, "female_35_to_39": 4799.0, "female_40_to_44": 3688.0, "female_45_to_49": 3274.0, "female_50_to_54": 3515.0, "female_55_to_59": 3144.0, "female_60_to_61": 1327.0, "female_62_to_64": 1469.0, "female_65_to_66": 858.0, "female_67_to_69": 1405.0, "female_70_to_74": 1914.0, "female_75_to_79": 1172.0, "female_80_to_84": 1069.0, "female_85_and_over": 824.0, "white_pop": 24616.0, "population_1_year_and_over": 128525.0, "population_3_years_over": 125194.0, "pop_5_years_over": 122104.0, "pop_15_and_over": NaN, "pop_16_over": 106390.0, "pop_25_years_over": 86677.0, "pop_25_64": 74799.0, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "not_us_citizen_pop": 22944.0, "black_pop": 23856.0, "asian_pop": 6666.0, "hispanic_pop": 71908.0, "amerindian_pop": 205.0, "other_race_pop": 687.0, "two_or_more_races_pop": 2034.0, "hispanic_any_race": 71908.0, "not_hispanic_pop": 58068.0, "asian_male_45_54": 313.0, "asian_male_55_64": 225.0, "black_male_45_54": 1857.0, "black_male_55_64": 2016.0, "hispanic_male_45_54": 3662.0, "hispanic_male_55_64": 2489.0, "white_male_45_54": 719.0, "white_male_55_64": 186.0, "median_income": 47569.0, "income_per_capita": 25076.0, "income_less_10000": 5608.0, "income_10000_14999": 3890.0, "income_15000_19999": 2734.0, "income_20000_24999": 2505.0, "income_25000_29999": 2118.0, "income_30000_34999": 1945.0, "income_35000_39999": 1554.0, "income_40000_44999": 2040.0, "income_45000_49999": 1702.0, "income_50000_59999": 2730.0, "income_60000_74999": 3999.0, "income_75000_99999": 4863.0, "income_100000_124999": 3654.0, "income_125000_149999": 2532.0, "income_150000_199999": 2637.0, "income_200000_or_more": 1942.0, "pop_determined_poverty_status": 129068.0, "poverty": 35895.0, "gini_index": 0.501, "housing_units": 50440.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 1414.0, "owner_occupied_housing_units_lower_value_quartile": 421700.0, "owner_occupied_housing_units_median_value": 643600.0, "owner_occupied_housing_units_upper_value_quartile": 926600.0, "occupied_housing_units": 46453.0, "housing_units_renter_occupied": 40326.0, "vacant_housing_units": 3987.0, "vacant_housing_units_for_rent": 1600.0, "vacant_housing_units_for_sale": 174.0, "dwellings_1_units_detached": 701.0, "dwellings_1_units_attached": 1892.0, "dwellings_2_units": 7555.0, "dwellings_3_to_4_units": 11263.0, "dwellings_5_to_9_units": 16567.0, "dwellings_10_to_19_units": 3391.0, "dwellings_20_to_49_units": 3403.0, "dwellings_50_or_more_units": 5478.0, "mobile_homes": 61.0, "housing_built_2005_or_later": 452.0, "housing_built_2000_to_2004": 971.0, "housing_built_1939_or_earlier": 2434.0, "median_year_structure_built": 0.0, "married_households": 10985.0, "nonfamily_households": 22131.0, "family_households": 24322.0, "households_public_asst_or_food_stamps": 15112.0, "male_male_households": 310.0, "female_female_households": 94.0, "children": 26703.0, "children_in_single_female_hh": 13136.0, "median_rent": 1296.0, "percent_income_spent_on_rent": 33.7, "rent_burden_not_computed": 1858.0, "rent_over_50_percent": 12238.0, "rent_40_to_50_percent": 3350.0, "rent_35_to_40_percent": 2559.0, "rent_30_to_35_percent": 4118.0, "rent_25_to_30_percent": 4427.0, "rent_20_to_25_percent": 4065.0, "rent_15_to_20_percent": 3457.0, "rent_10_to_15_percent": 2708.0, "rent_under_10_percent": 1546.0, "owner_occupied_housing_units": 6127.0, "million_dollar_housing_units": 1005.0, "mortgaged_housing_units": 4065.0, "different_house_year_ago_different_city": 3481.0, "different_house_year_ago_same_city": 11267.0, "families_with_young_children": 8472.0, "two_parent_families_with_young_children": 3350.0, "two_parents_in_labor_force_families_with_young_children": 1851.0, "two_parents_father_in_labor_force_families_with_young_children": 1227.0, "two_parents_mother_in_labor_force_families_with_young_children": 171.0, "two_parents_not_in_labor_force_families_with_young_children": 101.0, "one_parent_families_with_young_children": 5122.0, "father_one_parent_families_with_young_children": 1033.0, "father_in_labor_force_one_parent_families_with_young_children": 862.0, "commute_less_10_mins": 1902.0, "commute_10_14_mins": 2696.0, "commute_15_19_mins": 2445.0, "commute_20_24_mins": 3699.0, "commute_25_29_mins": 2270.0, "commute_30_34_mins": 9600.0, "commute_35_44_mins": 9840.0, "commute_60_more_mins": 14115.0, "commute_45_59_mins": 15239.0, "commuters_16_over": 61806.0, "walked_to_work": 4681.0, "worked_at_home": 2997.0, "no_car": 41174.0, "no_cars": 32309.0, "one_car": 11454.0, "two_cars": 2117.0, "three_cars": 429.0, "four_more_cars": 144.0, "aggregate_travel_time_to_work": 2553650.0, "commuters_by_public_transportation": 45368.0, "commuters_by_bus": 2686.0, "commuters_by_car_truck_van": 9141.0, "commuters_by_carpool": 1433.0, "commuters_by_subway_or_elevated": 42331.0, "commuters_drove_alone": 7708.0, "group_quarters": 2429.0, "associates_degree": 4512.0, "bachelors_degree": 18807.0, "high_school_diploma": 16764.0, "less_one_year_college": 2498.0, "masters_degree": 5392.0, "one_year_more_college": 9948.0, "less_than_high_school_graduate": 23677.0, "high_school_including_ged": 20923.0, "bachelors_degree_2": 18807.0, "bachelors_degree_or_higher_25_64": 24264.0, "graduate_professional_degree": 6312.0, "some_college_and_associates_degree": 16958.0, "male_45_64_associates_degree": 407.0, "male_45_64_bachelors_degree": 783.0, "male_45_64_graduate_degree": 440.0, "male_45_64_less_than_9_grade": 1493.0, "male_45_64_grade_9_12": 1926.0, "male_45_64_high_school": 4188.0, "male_45_64_some_college": 1629.0, "male_45_to_64": 10866.0, "employed_pop": 66190.0, "unemployed_pop": 5967.0, "pop_in_labor_force": 72157.0, "not_in_labor_force": 34233.0, "workers_16_and_over": 64803.0, "armed_forces": 0.0, "civilian_labor_force": 72157.0, "employed_agriculture_forestry_fishing_hunting_mining": 226.0, "employed_arts_entertainment_recreation_accommodation_food": 10359.0, "employed_construction": 4691.0, "employed_education_health_social": 13078.0, "employed_finance_insurance_real_estate": 3046.0, "employed_information": 4109.0, "employed_manufacturing": 2754.0, "employed_other_services_not_public_admin": 3676.0, "employed_public_administration": 1645.0, "employed_retail_trade": 7952.0, "employed_science_management_admin_waste": 9950.0, "employed_transportation_warehousing_utilities": 3314.0, "employed_wholesale_trade": 1390.0, "occupation_management_arts": 23436.0, "occupation_natural_resources_construction_maintenance": 5620.0, "occupation_production_transportation_material": 7028.0, "occupation_sales_office": 13445.0, "occupation_services": 16661.0, "management_business_sci_arts_employed": 23436.0, "sales_office_employed": 13445.0, "in_grades_1_to_4": 6148.0, "in_grades_5_to_8": 5858.0, "in_grades_9_to_12": 6064.0, "in_school": 31265.0, "in_undergrad_college": 7732.0, "speak_only_english_at_home": 54414.0, "speak_spanish_at_home": 57732.0, "speak_spanish_at_home_low_english": 26299.0}, {"geo_id": "2701406", "do_date": "2014-01-01", "total_pop": 133986.0, "households": 52028.0, "male_pop": 67216.0, "female_pop": 66770.0, "median_age": 33.7, "male_under_5": 6318.0, "male_5_to_9": 4898.0, "male_10_to_14": 3877.0, "male_15_to_17": 2082.0, "male_18_to_19": 1946.0, "male_20": 878.0, "male_21": 663.0, "male_22_to_24": 2847.0, "male_25_to_29": 6259.0, "male_30_to_34": 6890.0, "male_35_to_39": 5916.0, "male_40_to_44": 4424.0, "male_45_to_49": 4004.0, "male_50_to_54": 3575.0, "male_55_to_59": 3236.0, "male_60_to_61": 1421.0, "male_62_to_64": 2096.0, "male_65_to_66": 1212.0, "male_67_to_69": 1371.0, "male_70_to_74": 1495.0, "male_75_to_79": 843.0, "male_80_to_84": 514.0, "male_85_and_over": 451.0, "female_under_5": 5273.0, "female_5_to_9": 4517.0, "female_10_to_14": 3717.0, "female_15_to_17": 2006.0, "female_18_to_19": 1881.0, "female_20": 693.0, "female_21": 694.0, "female_22_to_24": 2506.0, "female_25_to_29": 5954.0, "female_30_to_34": 7012.0, "female_35_to_39": 5594.0, "female_40_to_44": 4249.0, "female_45_to_49": 4099.0, "female_50_to_54": 3747.0, "female_55_to_59": 3798.0, "female_60_to_61": 1368.0, "female_62_to_64": 1960.0, "female_65_to_66": 1372.0, "female_67_to_69": 1463.0, "female_70_to_74": 1814.0, "female_75_to_79": 1163.0, "female_80_to_84": 724.0, "female_85_and_over": 1166.0, "white_pop": 73312.0, "population_1_year_and_over": 131590.0, "population_3_years_over": 126649.0, "pop_5_years_over": 122395.0, "pop_15_and_over": NaN, "pop_16_over": 104100.0, "pop_25_years_over": 89190.0, "pop_25_64": 75602.0, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "not_us_citizen_pop": 15677.0, "black_pop": 29165.0, "asian_pop": 4104.0, "hispanic_pop": 19686.0, "amerindian_pop": 2345.0, "other_race_pop": 392.0, "two_or_more_races_pop": 4940.0, "hispanic_any_race": 19686.0, "not_hispanic_pop": 114300.0, "asian_male_45_54": 213.0, "asian_male_55_64": 125.0, "black_male_45_54": 1488.0, "black_male_55_64": 1144.0, "hispanic_male_45_54": 836.0, "hispanic_male_55_64": 414.0, "white_male_45_54": 4740.0, "white_male_55_64": 4929.0, "median_income": 59456.0, "income_per_capita": 31081.0, "income_less_10000": 3792.0, "income_10000_14999": 3496.0, "income_15000_19999": 2363.0, "income_20000_24999": 2451.0, "income_25000_29999": 2288.0, "income_30000_34999": 2042.0, "income_35000_39999": 1968.0, "income_40000_44999": 2129.0, "income_45000_49999": 2104.0, "income_50000_59999": 3556.0, "income_60000_74999": 5463.0, "income_75000_99999": 6257.0, "income_100000_124999": 4609.0, "income_125000_149999": 3564.0, "income_150000_199999": 3251.0, "income_200000_or_more": 2695.0, "pop_determined_poverty_status": 130365.0, "poverty": 25277.0, "gini_index": 0.4603, "housing_units": 54446.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 909.0, "owner_occupied_housing_units_lower_value_quartile": 168700.0, "owner_occupied_housing_units_median_value": 220500.0, "owner_occupied_housing_units_upper_value_quartile": 284700.0, "occupied_housing_units": 52028.0, "housing_units_renter_occupied": 21943.0, "vacant_housing_units": 2418.0, "vacant_housing_units_for_rent": 551.0, "vacant_housing_units_for_sale": 313.0, "dwellings_1_units_detached": 29541.0, "dwellings_1_units_attached": 1912.0, "dwellings_2_units": 5150.0, "dwellings_3_to_4_units": 2728.0, "dwellings_5_to_9_units": 1521.0, "dwellings_10_to_19_units": 2544.0, "dwellings_20_to_49_units": 3346.0, "dwellings_50_or_more_units": 7466.0, "mobile_homes": 206.0, "housing_built_2005_or_later": 493.0, "housing_built_2000_to_2004": 941.0, "housing_built_1939_or_earlier": 4201.0, "median_year_structure_built": 0.0, "married_households": 19041.0, "nonfamily_households": 24596.0, "family_households": 27432.0, "households_public_asst_or_food_stamps": 9418.0, "male_male_households": 279.0, "female_female_households": 375.0, "children": 32688.0, "children_in_single_female_hh": 9930.0, "median_rent": 825.0, "percent_income_spent_on_rent": 30.0, "rent_burden_not_computed": 988.0, "rent_over_50_percent": 5594.0, "rent_40_to_50_percent": 1629.0, "rent_35_to_40_percent": 1262.0, "rent_30_to_35_percent": 1984.0, "rent_25_to_30_percent": 2784.0, "rent_20_to_25_percent": 2698.0, "rent_15_to_20_percent": 2694.0, "rent_10_to_15_percent": 1501.0, "rent_under_10_percent": 809.0, "owner_occupied_housing_units": 30085.0, "million_dollar_housing_units": 44.0, "mortgaged_housing_units": 22610.0, "different_house_year_ago_different_city": 10134.0, "different_house_year_ago_same_city": 12981.0, "families_with_young_children": 12873.0, "two_parent_families_with_young_children": 8929.0, "two_parents_in_labor_force_families_with_young_children": 6275.0, "two_parents_father_in_labor_force_families_with_young_children": 2190.0, "two_parents_mother_in_labor_force_families_with_young_children": 424.0, "two_parents_not_in_labor_force_families_with_young_children": 40.0, "one_parent_families_with_young_children": 3944.0, "father_one_parent_families_with_young_children": 791.0, "father_in_labor_force_one_parent_families_with_young_children": 737.0, "commute_less_10_mins": 6073.0, "commute_10_14_mins": 8641.0, "commute_15_19_mins": 12364.0, "commute_20_24_mins": 13705.0, "commute_25_29_mins": 6688.0, "commute_30_34_mins": 10814.0, "commute_35_44_mins": 3760.0, "commute_60_more_mins": 2870.0, "commute_45_59_mins": 2818.0, "commuters_16_over": 67733.0, "walked_to_work": 3210.0, "worked_at_home": 3511.0, "no_car": 5356.0, "no_cars": 8582.0, "one_car": 19757.0, "two_cars": 18246.0, "three_cars": 4096.0, "four_more_cars": 1347.0, "aggregate_travel_time_to_work": 1566680.0, "commuters_by_public_transportation": 9363.0, "commuters_by_bus": 7024.0, "commuters_by_car_truck_van": 50666.0, "commuters_by_carpool": 6281.0, "commuters_by_subway_or_elevated": 1651.0, "commuters_drove_alone": 44385.0, "group_quarters": 4123.0, "associates_degree": 6058.0, "bachelors_degree": 24210.0, "high_school_diploma": 12712.0, "less_one_year_college": 4189.0, "masters_degree": 11686.0, "one_year_more_college": 11012.0, "less_than_high_school_graduate": 12903.0, "high_school_including_ged": 15084.0, "bachelors_degree_2": 24210.0, "bachelors_degree_or_higher_25_64": 35099.0, "graduate_professional_degree": 15734.0, "some_college_and_associates_degree": 21259.0, "male_45_64_associates_degree": 825.0, "male_45_64_bachelors_degree": 3443.0, "male_45_64_graduate_degree": 2180.0, "male_45_64_less_than_9_grade": 1109.0, "male_45_64_grade_9_12": 789.0, "male_45_64_high_school": 3024.0, "male_45_64_some_college": 2962.0, "male_45_to_64": 14332.0, "employed_pop": 72610.0, "unemployed_pop": 4375.0, "pop_in_labor_force": 77012.0, "not_in_labor_force": 27088.0, "workers_16_and_over": 71244.0, "armed_forces": 27.0, "civilian_labor_force": 76985.0, "employed_agriculture_forestry_fishing_hunting_mining": 263.0, "employed_arts_entertainment_recreation_accommodation_food": 9256.0, "employed_construction": 2784.0, "employed_education_health_social": 20594.0, "employed_finance_insurance_real_estate": 4850.0, "employed_information": 2036.0, "employed_manufacturing": 5486.0, "employed_other_services_not_public_admin": 3703.0, "employed_public_administration": 2485.0, "employed_retail_trade": 6736.0, "employed_science_management_admin_waste": 9821.0, "employed_transportation_warehousing_utilities": 3072.0, "employed_wholesale_trade": 1524.0, "occupation_management_arts": 34530.0, "occupation_natural_resources_construction_maintenance": 3538.0, "occupation_production_transportation_material": 7412.0, "occupation_sales_office": 12949.0, "occupation_services": 14181.0, "management_business_sci_arts_employed": 34530.0, "sales_office_employed": 12949.0, "in_grades_1_to_4": 7606.0, "in_grades_5_to_8": 6188.0, "in_grades_9_to_12": 6112.0, "in_school": 36125.0, "in_undergrad_college": 8539.0, "speak_only_english_at_home": 85767.0, "speak_spanish_at_home": 15863.0, "speak_spanish_at_home_low_english": 8863.0}, {"geo_id": "1703526", "do_date": "2014-01-01", "total_pop": 161182.0, "households": 52493.0, "male_pop": 81133.0, "female_pop": 80049.0, "median_age": 33.9, "male_under_5": 5909.0, "male_5_to_9": 6134.0, "male_10_to_14": 5833.0, "male_15_to_17": 3430.0, "male_18_to_19": 1893.0, "male_20": 1096.0, "male_21": 1043.0, "male_22_to_24": 3726.0, "male_25_to_29": 7441.0, "male_30_to_34": 6326.0, "male_35_to_39": 6061.0, "male_40_to_44": 5435.0, "male_45_to_49": 4796.0, "male_50_to_54": 5098.0, "male_55_to_59": 4268.0, "male_60_to_61": 1470.0, "male_62_to_64": 2289.0, "male_65_to_66": 1642.0, "male_67_to_69": 1634.0, "male_70_to_74": 2196.0, "male_75_to_79": 1802.0, "male_80_to_84": 842.0, "male_85_and_over": 769.0, "female_under_5": 5322.0, "female_5_to_9": 5342.0, "female_10_to_14": 5491.0, "female_15_to_17": 3081.0, "female_18_to_19": 2157.0, "female_20": 1122.0, "female_21": 1140.0, "female_22_to_24": 3856.0, "female_25_to_29": 6604.0, "female_30_to_34": 6329.0, "female_35_to_39": 5229.0, "female_40_to_44": 5394.0, "female_45_to_49": 4857.0, "female_50_to_54": 5086.0, "female_55_to_59": 3929.0, "female_60_to_61": 2117.0, "female_62_to_64": 2276.0, "female_65_to_66": 1473.0, "female_67_to_69": 2062.0, "female_70_to_74": 2689.0, "female_75_to_79": 1933.0, "female_80_to_84": 1173.0, "female_85_and_over": 1387.0, "white_pop": 25561.0, "population_1_year_and_over": 159211.0, "population_3_years_over": 154595.0, "pop_5_years_over": 149951.0, "pop_15_and_over": NaN, "pop_16_over": 125083.0, "pop_25_years_over": 104607.0, "pop_25_64": 85005.0, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "not_us_citizen_pop": 35488.0, "black_pop": 12373.0, "asian_pop": 32350.0, "hispanic_pop": 89461.0, "amerindian_pop": 96.0, "other_race_pop": 66.0, "two_or_more_races_pop": 1275.0, "hispanic_any_race": 89461.0, "not_hispanic_pop": 71721.0, "asian_male_45_54": 2400.0, "asian_male_55_64": 2054.0, "black_male_45_54": 636.0, "black_male_55_64": 530.0, "hispanic_male_45_54": 5088.0, "hispanic_male_55_64": 3352.0, "white_male_45_54": 1687.0, "white_male_55_64": 2104.0, "median_income": 40453.0, "income_per_capita": 19312.0, "income_less_10000": 4764.0, "income_10000_14999": 3154.0, "income_15000_19999": 3675.0, "income_20000_24999": 4328.0, "income_25000_29999": 3504.0, "income_30000_34999": 3398.0, "income_35000_39999": 3123.0, "income_40000_44999": 3030.0, "income_45000_49999": 2230.0, "income_50000_59999": 4020.0, "income_60000_74999": 4810.0, "income_75000_99999": 4903.0, "income_100000_124999": 2953.0, "income_125000_149999": 1746.0, "income_150000_199999": 1730.0, "income_200000_or_more": 1125.0, "pop_determined_poverty_status": 160540.0, "poverty": 37984.0, "gini_index": 0.4556, "housing_units": 59417.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 861.0, "owner_occupied_housing_units_lower_value_quartile": 145500.0, "owner_occupied_housing_units_median_value": 211100.0, "owner_occupied_housing_units_upper_value_quartile": 293400.0, "occupied_housing_units": 52493.0, "housing_units_renter_occupied": 28745.0, "vacant_housing_units": 6924.0, "vacant_housing_units_for_rent": 1892.0, "vacant_housing_units_for_sale": 785.0, "dwellings_1_units_detached": 15777.0, "dwellings_1_units_attached": 1937.0, "dwellings_2_units": 20337.0, "dwellings_3_to_4_units": 13629.0, "dwellings_5_to_9_units": 4218.0, "dwellings_10_to_19_units": 649.0, "dwellings_20_to_49_units": 729.0, "dwellings_50_or_more_units": 2041.0, "mobile_homes": 84.0, "housing_built_2005_or_later": 93.0, "housing_built_2000_to_2004": 542.0, "housing_built_1939_or_earlier": 4229.0, "median_year_structure_built": 0.0, "married_households": 22474.0, "nonfamily_households": 16761.0, "family_households": 35732.0, "households_public_asst_or_food_stamps": 13830.0, "male_male_households": 42.0, "female_female_households": 58.0, "children": 40542.0, "children_in_single_female_hh": 11196.0, "median_rent": 685.0, "percent_income_spent_on_rent": 30.7, "rent_burden_not_computed": 1711.0, "rent_over_50_percent": 6676.0, "rent_40_to_50_percent": 2929.0, "rent_35_to_40_percent": 1902.0, "rent_30_to_35_percent": 2313.0, "rent_25_to_30_percent": 3112.0, "rent_20_to_25_percent": 3375.0, "rent_15_to_20_percent": 3406.0, "rent_10_to_15_percent": 2268.0, "rent_under_10_percent": 1053.0, "owner_occupied_housing_units": 23748.0, "million_dollar_housing_units": 20.0, "mortgaged_housing_units": 13657.0, "different_house_year_ago_different_city": 2774.0, "different_house_year_ago_same_city": 9603.0, "families_with_young_children": 12911.0, "two_parent_families_with_young_children": 7226.0, "two_parents_in_labor_force_families_with_young_children": 4133.0, "two_parents_father_in_labor_force_families_with_young_children": 2777.0, "two_parents_mother_in_labor_force_families_with_young_children": 197.0, "two_parents_not_in_labor_force_families_with_young_children": 119.0, "one_parent_families_with_young_children": 5685.0, "father_one_parent_families_with_young_children": 1486.0, "father_in_labor_force_one_parent_families_with_young_children": 1425.0, "commute_less_10_mins": 3792.0, "commute_10_14_mins": 5552.0, "commute_15_19_mins": 7649.0, "commute_20_24_mins": 9490.0, "commute_25_29_mins": 3042.0, "commute_30_34_mins": 15611.0, "commute_35_44_mins": 6039.0, "commute_60_more_mins": 9113.0, "commute_45_59_mins": 7316.0, "commuters_16_over": 67604.0, "walked_to_work": 4016.0, "worked_at_home": 1406.0, "no_car": 7835.0, "no_cars": 11995.0, "one_car": 22619.0, "two_cars": 13009.0, "three_cars": 3568.0, "four_more_cars": 1302.0, "aggregate_travel_time_to_work": 2172650.0, "commuters_by_public_transportation": 15664.0, "commuters_by_bus": 9393.0, "commuters_by_car_truck_van": 46298.0, "commuters_by_carpool": 11022.0, "commuters_by_subway_or_elevated": 5899.0, "commuters_drove_alone": 35276.0, "group_quarters": 521.0, "associates_degree": 4827.0, "bachelors_degree": 12115.0, "high_school_diploma": 32480.0, "less_one_year_college": 3426.0, "masters_degree": 4373.0, "one_year_more_college": 11155.0, "less_than_high_school_graduate": 30761.0, "high_school_including_ged": 36372.0, "bachelors_degree_2": 12115.0, "bachelors_degree_or_higher_25_64": 16779.0, "graduate_professional_degree": 5951.0, "some_college_and_associates_degree": 19408.0, "male_45_64_associates_degree": 507.0, "male_45_64_bachelors_degree": 1490.0, "male_45_64_graduate_degree": 835.0, "male_45_64_less_than_9_grade": 4236.0, "male_45_64_grade_9_12": 2112.0, "male_45_64_high_school": 6310.0, "male_45_64_some_college": 2431.0, "male_45_to_64": 17921.0, "employed_pop": 70490.0, "unemployed_pop": 8908.0, "pop_in_labor_force": 79408.0, "not_in_labor_force": 45675.0, "workers_16_and_over": 69010.0, "armed_forces": 10.0, "civilian_labor_force": 79398.0, "employed_agriculture_forestry_fishing_hunting_mining": 132.0, "employed_arts_entertainment_recreation_accommodation_food": 13188.0, "employed_construction": 4567.0, "employed_education_health_social": 12163.0, "employed_finance_insurance_real_estate": 3352.0, "employed_information": 886.0, "employed_manufacturing": 8904.0, "employed_other_services_not_public_admin": 4388.0, "employed_public_administration": 1887.0, "employed_retail_trade": 6819.0, "employed_science_management_admin_waste": 7793.0, "employed_transportation_warehousing_utilities": 3953.0, "employed_wholesale_trade": 2458.0, "occupation_management_arts": 16125.0, "occupation_natural_resources_construction_maintenance": 6819.0, "occupation_production_transportation_material": 14584.0, "occupation_sales_office": 13988.0, "occupation_services": 18974.0, "management_business_sci_arts_employed": 16125.0, "sales_office_employed": 13988.0, "in_grades_1_to_4": 9122.0, "in_grades_5_to_8": 9392.0, "in_grades_9_to_12": 8696.0, "in_school": 43295.0, "in_undergrad_college": 8671.0, "speak_only_english_at_home": 46945.0, "speak_spanish_at_home": 71348.0, "speak_spanish_at_home_low_english": 30725.0}, {"geo_id": "3604110", "do_date": "2014-01-01", "total_pop": 183687.0, "households": 61913.0, "male_pop": 89962.0, "female_pop": 93725.0, "median_age": 37.2, "male_under_5": 5848.0, "male_5_to_9": 6138.0, "male_10_to_14": 4920.0, "male_15_to_17": 3504.0, "male_18_to_19": 1813.0, "male_20": 1252.0, "male_21": 1146.0, "male_22_to_24": 3500.0, "male_25_to_29": 7317.0, "male_30_to_34": 7591.0, "male_35_to_39": 7302.0, "male_40_to_44": 6202.0, "male_45_to_49": 6363.0, "male_50_to_54": 6674.0, "male_55_to_59": 6200.0, "male_60_to_61": 1922.0, "male_62_to_64": 2908.0, "male_65_to_66": 1490.0, "male_67_to_69": 1783.0, "male_70_to_74": 2249.0, "male_75_to_79": 1700.0, "male_80_to_84": 979.0, "male_85_and_over": 1161.0, "female_under_5": 5792.0, "female_5_to_9": 5145.0, "female_10_to_14": 6038.0, "female_15_to_17": 2922.0, "female_18_to_19": 1648.0, "female_20": 951.0, "female_21": 947.0, "female_22_to_24": 3452.0, "female_25_to_29": 7158.0, "female_30_to_34": 7698.0, "female_35_to_39": 8001.0, "female_40_to_44": 5697.0, "female_45_to_49": 6811.0, "female_50_to_54": 6116.0, "female_55_to_59": 6204.0, "female_60_to_61": 2513.0, "female_62_to_64": 3174.0, "female_65_to_66": 1903.0, "female_67_to_69": 2080.0, "female_70_to_74": 2645.0, "female_75_to_79": 2648.0, "female_80_to_84": 1984.0, "female_85_and_over": 2198.0, "white_pop": 95044.0, "population_1_year_and_over": 181585.0, "population_3_years_over": 176774.0, "pop_5_years_over": 172047.0, "pop_15_and_over": NaN, "pop_16_over": 147573.0, "pop_25_years_over": 128671.0, "pop_25_64": 105851.0, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "not_us_citizen_pop": 31721.0, "black_pop": 2568.0, "asian_pop": 15545.0, "hispanic_pop": 67451.0, "amerindian_pop": 85.0, "other_race_pop": 571.0, "two_or_more_races_pop": 2423.0, "hispanic_any_race": 67451.0, "not_hispanic_pop": 116236.0, "asian_male_45_54": 934.0, "asian_male_55_64": 997.0, "black_male_45_54": 270.0, "black_male_55_64": 223.0, "hispanic_male_45_54": 4672.0, "hispanic_male_55_64": 2883.0, "white_male_45_54": 7076.0, "white_male_55_64": 6916.0, "median_income": 73644.0, "income_per_capita": 31398.0, "income_less_10000": 3234.0, "income_10000_14999": 2140.0, "income_15000_19999": 2277.0, "income_20000_24999": 2186.0, "income_25000_29999": 2402.0, "income_30000_34999": 2292.0, "income_35000_39999": 2168.0, "income_40000_44999": 2527.0, "income_45000_49999": 2010.0, "income_50000_59999": 4455.0, "income_60000_74999": 5938.0, "income_75000_99999": 9169.0, "income_100000_124999": 6962.0, "income_125000_149999": 5156.0, "income_150000_199999": 5269.0, "income_200000_or_more": 3728.0, "pop_determined_poverty_status": 182680.0, "poverty": 18925.0, "gini_index": 0.4232, "housing_units": 67537.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 1501.0, "owner_occupied_housing_units_lower_value_quartile": 475800.0, "owner_occupied_housing_units_median_value": 610500.0, "owner_occupied_housing_units_upper_value_quartile": 739700.0, "occupied_housing_units": 61913.0, "housing_units_renter_occupied": 35797.0, "vacant_housing_units": 5624.0, "vacant_housing_units_for_rent": 1375.0, "vacant_housing_units_for_sale": 124.0, "dwellings_1_units_detached": 5192.0, "dwellings_1_units_attached": 9822.0, "dwellings_2_units": 24513.0, "dwellings_3_to_4_units": 12993.0, "dwellings_5_to_9_units": 9324.0, "dwellings_10_to_19_units": 1518.0, "dwellings_20_to_49_units": 2396.0, "dwellings_50_or_more_units": 1687.0, "mobile_homes": 54.0, "housing_built_2005_or_later": 81.0, "housing_built_2000_to_2004": 462.0, "housing_built_1939_or_earlier": 8908.0, "median_year_structure_built": 0.0, "married_households": 29826.0, "nonfamily_households": 18424.0, "family_households": 43489.0, "households_public_asst_or_food_stamps": 5170.0, "male_male_households": 131.0, "female_female_households": 41.0, "children": 40307.0, "children_in_single_female_hh": 9302.0, "median_rent": 1387.0, "percent_income_spent_on_rent": 29.0, "rent_burden_not_computed": 2034.0, "rent_over_50_percent": 8386.0, "rent_40_to_50_percent": 2887.0, "rent_35_to_40_percent": 2289.0, "rent_30_to_35_percent": 2633.0, "rent_25_to_30_percent": 3383.0, "rent_20_to_25_percent": 4375.0, "rent_15_to_20_percent": 4632.0, "rent_10_to_15_percent": 3642.0, "rent_under_10_percent": 1536.0, "owner_occupied_housing_units": 26116.0, "million_dollar_housing_units": 1003.0, "mortgaged_housing_units": 14790.0, "different_house_year_ago_different_city": 1923.0, "different_house_year_ago_same_city": 9895.0, "families_with_young_children": 13785.0, "two_parent_families_with_young_children": 9176.0, "two_parents_in_labor_force_families_with_young_children": 5153.0, "two_parents_father_in_labor_force_families_with_young_children": 3677.0, "two_parents_mother_in_labor_force_families_with_young_children": 192.0, "two_parents_not_in_labor_force_families_with_young_children": 154.0, "one_parent_families_with_young_children": 4609.0, "father_one_parent_families_with_young_children": 1311.0, "father_in_labor_force_one_parent_families_with_young_children": 1150.0, "commute_less_10_mins": 2210.0, "commute_10_14_mins": 3634.0, "commute_15_19_mins": 5745.0, "commute_20_24_mins": 5503.0, "commute_25_29_mins": 2356.0, "commute_30_34_mins": 17554.0, "commute_35_44_mins": 7888.0, "commute_60_more_mins": 23051.0, "commute_45_59_mins": 20954.0, "commuters_16_over": 88895.0, "walked_to_work": 5946.0, "worked_at_home": 2115.0, "no_car": 25150.0, "no_cars": 20553.0, "one_car": 26195.0, "two_cars": 11910.0, "three_cars": 2531.0, "four_more_cars": 724.0, "aggregate_travel_time_to_work": 3763940.0, "commuters_by_public_transportation": 48480.0, "commuters_by_bus": 8535.0, "commuters_by_car_truck_van": 33260.0, "commuters_by_carpool": 4774.0, "commuters_by_subway_or_elevated": 39036.0, "commuters_drove_alone": 28486.0, "group_quarters": 780.0, "associates_degree": 9952.0, "bachelors_degree": 23831.0, "high_school_diploma": 36398.0, "less_one_year_college": 9318.0, "masters_degree": 10615.0, "one_year_more_college": 12130.0, "less_than_high_school_graduate": 19874.0, "high_school_including_ged": 40812.0, "bachelors_degree_2": 23831.0, "bachelors_degree_or_higher_25_64": 33601.0, "graduate_professional_degree": 12754.0, "some_college_and_associates_degree": 31400.0, "male_45_64_associates_degree": 2125.0, "male_45_64_bachelors_degree": 3593.0, "male_45_64_graduate_degree": 1833.0, "male_45_64_less_than_9_grade": 2060.0, "male_45_64_grade_9_12": 1683.0, "male_45_64_high_school": 8631.0, "male_45_64_some_college": 4142.0, "male_45_to_64": 24067.0, "employed_pop": 92645.0, "unemployed_pop": 4889.0, "pop_in_labor_force": 97589.0, "not_in_labor_force": 49984.0, "workers_16_and_over": 91010.0, "armed_forces": 55.0, "civilian_labor_force": 97534.0, "employed_agriculture_forestry_fishing_hunting_mining": 124.0, "employed_arts_entertainment_recreation_accommodation_food": 10469.0, "employed_construction": 9950.0, "employed_education_health_social": 21115.0, "employed_finance_insurance_real_estate": 7562.0, "employed_information": 2200.0, "employed_manufacturing": 4408.0, "employed_other_services_not_public_admin": 5757.0, "employed_public_administration": 3478.0, "employed_retail_trade": 7778.0, "employed_science_management_admin_waste": 10622.0, "employed_transportation_warehousing_utilities": 6822.0, "employed_wholesale_trade": 2360.0, "occupation_management_arts": 29573.0, "occupation_natural_resources_construction_maintenance": 10436.0, "occupation_production_transportation_material": 10660.0, "occupation_sales_office": 19952.0, "occupation_services": 22024.0, "management_business_sci_arts_employed": 29573.0, "sales_office_employed": 19952.0, "in_grades_1_to_4": 8889.0, "in_grades_5_to_8": 8724.0, "in_grades_9_to_12": 8616.0, "in_school": 43199.0, "in_undergrad_college": 9417.0, "speak_only_english_at_home": 75840.0, "speak_spanish_at_home": 46155.0, "speak_spanish_at_home_low_english": 17964.0}, {"geo_id": "2503302", "do_date": "2014-01-01", "total_pop": 160086.0, "households": 73784.0, "male_pop": 79790.0, "female_pop": 80296.0, "median_age": 33.8, "male_under_5": 4281.0, "male_5_to_9": 2682.0, "male_10_to_14": 2825.0, "male_15_to_17": 1426.0, "male_18_to_19": 2379.0, "male_20": 958.0, "male_21": 1177.0, "male_22_to_24": 3790.0, "male_25_to_29": 11783.0, "male_30_to_34": 10631.0, "male_35_to_39": 5995.0, "male_40_to_44": 5440.0, "male_45_to_49": 4793.0, "male_50_to_54": 4724.0, "male_55_to_59": 4962.0, "male_60_to_61": 1384.0, "male_62_to_64": 1942.0, "male_65_to_66": 1350.0, "male_67_to_69": 1590.0, "male_70_to_74": 2181.0, "male_75_to_79": 1635.0, "male_80_to_84": 979.0, "male_85_and_over": 883.0, "female_under_5": 3798.0, "female_5_to_9": 3106.0, "female_10_to_14": 2492.0, "female_15_to_17": 1288.0, "female_18_to_19": 2589.0, "female_20": 1113.0, "female_21": 975.0, "female_22_to_24": 3560.0, "female_25_to_29": 12348.0, "female_30_to_34": 10459.0, "female_35_to_39": 6085.0, "female_40_to_44": 4536.0, "female_45_to_49": 4548.0, "female_50_to_54": 4405.0, "female_55_to_59": 4477.0, "female_60_to_61": 1781.0, "female_62_to_64": 2212.0, "female_65_to_66": 1518.0, "female_67_to_69": 1797.0, "female_70_to_74": 2314.0, "female_75_to_79": 1878.0, "female_80_to_84": 1303.0, "female_85_and_over": 1714.0, "white_pop": 90363.0, "population_1_year_and_over": 158387.0, "population_3_years_over": 154907.0, "pop_5_years_over": 152007.0, "pop_15_and_over": NaN, "pop_16_over": 140038.0, "pop_25_years_over": 121647.0, "pop_25_64": 102505.0, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "not_us_citizen_pop": 30018.0, "black_pop": 8629.0, "asian_pop": 18293.0, "hispanic_pop": 38196.0, "amerindian_pop": 147.0, "other_race_pop": 461.0, "two_or_more_races_pop": 3877.0, "hispanic_any_race": 38196.0, "not_hispanic_pop": 121890.0, "asian_male_45_54": 861.0, "asian_male_55_64": 999.0, "black_male_45_54": 452.0, "black_male_55_64": 642.0, "hispanic_male_45_54": 2413.0, "hispanic_male_55_64": 1140.0, "white_male_45_54": 5561.0, "white_male_55_64": 5401.0, "median_income": 82695.0, "income_per_capita": 67099.0, "income_less_10000": 6270.0, "income_10000_14999": 4438.0, "income_15000_19999": 2730.0, "income_20000_24999": 2522.0, "income_25000_29999": 2182.0, "income_30000_34999": 1889.0, "income_35000_39999": 1797.0, "income_40000_44999": 1899.0, "income_45000_49999": 1810.0, "income_50000_59999": 4142.0, "income_60000_74999": 5078.0, "income_75000_99999": 6121.0, "income_100000_124999": 6208.0, "income_125000_149999": 4789.0, "income_150000_199999": 7426.0, "income_200000_or_more": 14483.0, "pop_determined_poverty_status": 154010.0, "poverty": 26338.0, "gini_index": 0.5756, "housing_units": 82695.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 1738.0, "owner_occupied_housing_units_lower_value_quartile": 457700.0, "owner_occupied_housing_units_median_value": 709200.0, "owner_occupied_housing_units_upper_value_quartile": 1174000.0, "occupied_housing_units": 73784.0, "housing_units_renter_occupied": 49220.0, "vacant_housing_units": 8911.0, "vacant_housing_units_for_rent": 1529.0, "vacant_housing_units_for_sale": 411.0, "dwellings_1_units_detached": 2617.0, "dwellings_1_units_attached": 4321.0, "dwellings_2_units": 5855.0, "dwellings_3_to_4_units": 19695.0, "dwellings_5_to_9_units": 14252.0, "dwellings_10_to_19_units": 6166.0, "dwellings_20_to_49_units": 6720.0, "dwellings_50_or_more_units": 22822.0, "mobile_homes": 181.0, "housing_built_2005_or_later": 2197.0, "housing_built_2000_to_2004": 1596.0, "housing_built_1939_or_earlier": 2868.0, "median_year_structure_built": 0.0, "married_households": 21263.0, "nonfamily_households": 43117.0, "family_households": 30667.0, "households_public_asst_or_food_stamps": 10057.0, "male_male_households": 549.0, "female_female_households": 75.0, "children": 21898.0, "children_in_single_female_hh": 6425.0, "median_rent": 1621.0, "percent_income_spent_on_rent": 28.5, "rent_burden_not_computed": 2212.0, "rent_over_50_percent": 10513.0, "rent_40_to_50_percent": 3157.0, "rent_35_to_40_percent": 3021.0, "rent_30_to_35_percent": 5044.0, "rent_25_to_30_percent": 5716.0, "rent_20_to_25_percent": 5988.0, "rent_15_to_20_percent": 6072.0, "rent_10_to_15_percent": 4164.0, "rent_under_10_percent": 3333.0, "owner_occupied_housing_units": 24564.0, "million_dollar_housing_units": 3601.0, "mortgaged_housing_units": 17065.0, "different_house_year_ago_different_city": 16738.0, "different_house_year_ago_same_city": 16664.0, "families_with_young_children": 9112.0, "two_parent_families_with_young_children": 5823.0, "two_parents_in_labor_force_families_with_young_children": 4021.0, "two_parents_father_in_labor_force_families_with_young_children": 1503.0, "two_parents_mother_in_labor_force_families_with_young_children": 203.0, "two_parents_not_in_labor_force_families_with_young_children": 96.0, "one_parent_families_with_young_children": 3289.0, "father_one_parent_families_with_young_children": 1132.0, "father_in_labor_force_one_parent_families_with_young_children": 1064.0, "commute_less_10_mins": 8229.0, "commute_10_14_mins": 8711.0, "commute_15_19_mins": 12583.0, "commute_20_24_mins": 13618.0, "commute_25_29_mins": 6856.0, "commute_30_34_mins": 16658.0, "commute_35_44_mins": 8015.0, "commute_60_more_mins": 7305.0, "commute_45_59_mins": 7544.0, "commuters_16_over": 89519.0, "walked_to_work": 26075.0, "worked_at_home": 3851.0, "no_car": 30272.0, "no_cars": 31222.0, "one_car": 31939.0, "two_cars": 9145.0, "three_cars": 1300.0, "four_more_cars": 178.0, "aggregate_travel_time_to_work": 2439435.0, "commuters_by_public_transportation": 30990.0, "commuters_by_bus": 5716.0, "commuters_by_car_truck_van": 28491.0, "commuters_by_carpool": 3889.0, "commuters_by_subway_or_elevated": 23818.0, "commuters_drove_alone": 24602.0, "group_quarters": 7928.0, "associates_degree": 3472.0, "bachelors_degree": 36212.0, "high_school_diploma": 16248.0, "less_one_year_college": 2531.0, "masters_degree": 21074.0, "one_year_more_college": 7431.0, "less_than_high_school_graduate": 17699.0, "high_school_including_ged": 18241.0, "bachelors_degree_2": 36212.0, "bachelors_degree_or_higher_25_64": 63984.0, "graduate_professional_degree": 36061.0, "some_college_and_associates_degree": 13434.0, "male_45_64_associates_degree": 615.0, "male_45_64_bachelors_degree": 3735.0, "male_45_64_graduate_degree": 5195.0, "male_45_64_less_than_9_grade": 2169.0, "male_45_64_grade_9_12": 862.0, "male_45_64_high_school": 3527.0, "male_45_64_some_college": 1702.0, "male_45_to_64": 17805.0, "employed_pop": 95229.0, "unemployed_pop": 4862.0, "pop_in_labor_force": 100302.0, "not_in_labor_force": 39736.0, "workers_16_and_over": 93370.0, "armed_forces": 211.0, "civilian_labor_force": 100091.0, "employed_agriculture_forestry_fishing_hunting_mining": 57.0, "employed_arts_entertainment_recreation_accommodation_food": 12258.0, "employed_construction": 3618.0, "employed_education_health_social": 21573.0, "employed_finance_insurance_real_estate": 13450.0, "employed_information": 2297.0, "employed_manufacturing": 5012.0, "employed_other_services_not_public_admin": 3184.0, "employed_public_administration": 2484.0, "employed_retail_trade": 5935.0, "employed_science_management_admin_waste": 21174.0, "employed_transportation_warehousing_utilities": 2513.0, "employed_wholesale_trade": 1674.0, "occupation_management_arts": 54052.0, "occupation_natural_resources_construction_maintenance": 3512.0, "occupation_production_transportation_material": 4558.0, "occupation_sales_office": 15588.0, "occupation_services": 17519.0, "management_business_sci_arts_employed": 54052.0, "sales_office_employed": 15588.0, "in_grades_1_to_4": 4630.0, "in_grades_5_to_8": 4186.0, "in_grades_9_to_12": 3620.0, "in_school": 33062.0, "in_undergrad_college": 10543.0, "speak_only_english_at_home": 91413.0, "speak_spanish_at_home": 33324.0, "speak_spanish_at_home_low_english": 18806.0}]}