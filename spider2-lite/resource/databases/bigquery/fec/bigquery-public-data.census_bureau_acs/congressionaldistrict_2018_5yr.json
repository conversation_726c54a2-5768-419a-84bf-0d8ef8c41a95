{"table_name": "congressionaldistrict_2018_5yr", "table_fullname": "bigquery-public-data.census_bureau_acs.congressionaldistrict_2018_5yr", "column_names": ["geo_id", "do_date", "total_pop", "households", "male_pop", "female_pop", "median_age", "male_under_5", "male_5_to_9", "male_10_to_14", "male_15_to_17", "male_18_to_19", "male_20", "male_21", "male_22_to_24", "male_25_to_29", "male_30_to_34", "male_35_to_39", "male_40_to_44", "male_45_to_49", "male_50_to_54", "male_55_to_59", "male_60_to_61", "male_62_to_64", "male_65_to_66", "male_67_to_69", "male_70_to_74", "male_75_to_79", "male_80_to_84", "male_85_and_over", "female_under_5", "female_5_to_9", "female_10_to_14", "female_15_to_17", "female_18_to_19", "female_20", "female_21", "female_22_to_24", "female_25_to_29", "female_30_to_34", "female_35_to_39", "female_40_to_44", "female_45_to_49", "female_50_to_54", "female_55_to_59", "female_60_to_61", "female_62_to_64", "female_65_to_66", "female_67_to_69", "female_70_to_74", "female_75_to_79", "female_80_to_84", "female_85_and_over", "white_pop", "population_1_year_and_over", "population_3_years_over", "pop_5_years_over", "pop_15_and_over", "pop_16_over", "pop_25_years_over", "pop_25_64", "pop_never_married", "pop_now_married", "pop_separated", "pop_widowed", "pop_divorced", "not_us_citizen_pop", "black_pop", "asian_pop", "hispanic_pop", "amerindian_pop", "other_race_pop", "two_or_more_races_pop", "hispanic_any_race", "not_hispanic_pop", "asian_male_45_54", "asian_male_55_64", "black_male_45_54", "black_male_55_64", "hispanic_male_45_54", "hispanic_male_55_64", "white_male_45_54", "white_male_55_64", "median_income", "income_per_capita", "income_less_10000", "income_10000_14999", "income_15000_19999", "income_20000_24999", "income_25000_29999", "income_30000_34999", "income_35000_39999", "income_40000_44999", "income_45000_49999", "income_50000_59999", "income_60000_74999", "income_75000_99999", "income_100000_124999", "income_125000_149999", "income_150000_199999", "income_200000_or_more", "pop_determined_poverty_status", "poverty", "gini_index", "housing_units", "renter_occupied_housing_units_paying_cash_median_gross_rent", "owner_occupied_housing_units_lower_value_quartile", "owner_occupied_housing_units_median_value", "owner_occupied_housing_units_upper_value_quartile", "occupied_housing_units", "housing_units_renter_occupied", "vacant_housing_units", "vacant_housing_units_for_rent", "vacant_housing_units_for_sale", "dwellings_1_units_detached", "dwellings_1_units_attached", "dwellings_2_units", "dwellings_3_to_4_units", "dwellings_5_to_9_units", "dwellings_10_to_19_units", "dwellings_20_to_49_units", "dwellings_50_or_more_units", "mobile_homes", "housing_built_2005_or_later", "housing_built_2000_to_2004", "housing_built_1939_or_earlier", "median_year_structure_built", "married_households", "nonfamily_households", "family_households", "households_public_asst_or_food_stamps", "male_male_households", "female_female_households", "children", "children_in_single_female_hh", "median_rent", "percent_income_spent_on_rent", "rent_burden_not_computed", "rent_over_50_percent", "rent_40_to_50_percent", "rent_35_to_40_percent", "rent_30_to_35_percent", "rent_25_to_30_percent", "rent_20_to_25_percent", "rent_15_to_20_percent", "rent_10_to_15_percent", "rent_under_10_percent", "owner_occupied_housing_units", "million_dollar_housing_units", "mortgaged_housing_units", "different_house_year_ago_different_city", "different_house_year_ago_same_city", "families_with_young_children", "two_parent_families_with_young_children", "two_parents_in_labor_force_families_with_young_children", "two_parents_father_in_labor_force_families_with_young_children", "two_parents_mother_in_labor_force_families_with_young_children", "two_parents_not_in_labor_force_families_with_young_children", "one_parent_families_with_young_children", "father_one_parent_families_with_young_children", "father_in_labor_force_one_parent_families_with_young_children", "commute_less_10_mins", "commute_10_14_mins", "commute_15_19_mins", "commute_20_24_mins", "commute_25_29_mins", "commute_30_34_mins", "commute_35_44_mins", "commute_60_more_mins", "commute_45_59_mins", "commuters_16_over", "walked_to_work", "worked_at_home", "no_car", "no_cars", "one_car", "two_cars", "three_cars", "four_more_cars", "aggregate_travel_time_to_work", "commuters_by_public_transportation", "commuters_by_bus", "commuters_by_car_truck_van", "commuters_by_carpool", "commuters_by_subway_or_elevated", "commuters_drove_alone", "group_quarters", "associates_degree", "bachelors_degree", "high_school_diploma", "less_one_year_college", "masters_degree", "one_year_more_college", "less_than_high_school_graduate", "high_school_including_ged", "bachelors_degree_2", "bachelors_degree_or_higher_25_64", "graduate_professional_degree", "some_college_and_associates_degree", "male_45_64_associates_degree", "male_45_64_bachelors_degree", "male_45_64_graduate_degree", "male_45_64_less_than_9_grade", "male_45_64_grade_9_12", "male_45_64_high_school", "male_45_64_some_college", "male_45_to_64", "employed_pop", "unemployed_pop", "pop_in_labor_force", "not_in_labor_force", "workers_16_and_over", "armed_forces", "civilian_labor_force", "employed_agriculture_forestry_fishing_hunting_mining", "employed_arts_entertainment_recreation_accommodation_food", "employed_construction", "employed_education_health_social", "employed_finance_insurance_real_estate", "employed_information", "employed_manufacturing", "employed_other_services_not_public_admin", "employed_public_administration", "employed_retail_trade", "employed_science_management_admin_waste", "employed_transportation_warehousing_utilities", "employed_wholesale_trade", "occupation_management_arts", "occupation_natural_resources_construction_maintenance", "occupation_production_transportation_material", "occupation_sales_office", "occupation_services", "management_business_sci_arts_employed", "sales_office_employed", "in_grades_1_to_4", "in_grades_5_to_8", "in_grades_9_to_12", "in_school", "in_undergrad_college", "speak_only_english_at_home", "speak_spanish_at_home", "speak_spanish_at_home_low_english"], "column_types": ["STRING", "DATE", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64"], "nested_column_names": ["geo_id", "do_date", "total_pop", "households", "male_pop", "female_pop", "median_age", "male_under_5", "male_5_to_9", "male_10_to_14", "male_15_to_17", "male_18_to_19", "male_20", "male_21", "male_22_to_24", "male_25_to_29", "male_30_to_34", "male_35_to_39", "male_40_to_44", "male_45_to_49", "male_50_to_54", "male_55_to_59", "male_60_to_61", "male_62_to_64", "male_65_to_66", "male_67_to_69", "male_70_to_74", "male_75_to_79", "male_80_to_84", "male_85_and_over", "female_under_5", "female_5_to_9", "female_10_to_14", "female_15_to_17", "female_18_to_19", "female_20", "female_21", "female_22_to_24", "female_25_to_29", "female_30_to_34", "female_35_to_39", "female_40_to_44", "female_45_to_49", "female_50_to_54", "female_55_to_59", "female_60_to_61", "female_62_to_64", "female_65_to_66", "female_67_to_69", "female_70_to_74", "female_75_to_79", "female_80_to_84", "female_85_and_over", "white_pop", "population_1_year_and_over", "population_3_years_over", "pop_5_years_over", "pop_15_and_over", "pop_16_over", "pop_25_years_over", "pop_25_64", "pop_never_married", "pop_now_married", "pop_separated", "pop_widowed", "pop_divorced", "not_us_citizen_pop", "black_pop", "asian_pop", "hispanic_pop", "amerindian_pop", "other_race_pop", "two_or_more_races_pop", "hispanic_any_race", "not_hispanic_pop", "asian_male_45_54", "asian_male_55_64", "black_male_45_54", "black_male_55_64", "hispanic_male_45_54", "hispanic_male_55_64", "white_male_45_54", "white_male_55_64", "median_income", "income_per_capita", "income_less_10000", "income_10000_14999", "income_15000_19999", "income_20000_24999", "income_25000_29999", "income_30000_34999", "income_35000_39999", "income_40000_44999", "income_45000_49999", "income_50000_59999", "income_60000_74999", "income_75000_99999", "income_100000_124999", "income_125000_149999", "income_150000_199999", "income_200000_or_more", "pop_determined_poverty_status", "poverty", "gini_index", "housing_units", "renter_occupied_housing_units_paying_cash_median_gross_rent", "owner_occupied_housing_units_lower_value_quartile", "owner_occupied_housing_units_median_value", "owner_occupied_housing_units_upper_value_quartile", "occupied_housing_units", "housing_units_renter_occupied", "vacant_housing_units", "vacant_housing_units_for_rent", "vacant_housing_units_for_sale", "dwellings_1_units_detached", "dwellings_1_units_attached", "dwellings_2_units", "dwellings_3_to_4_units", "dwellings_5_to_9_units", "dwellings_10_to_19_units", "dwellings_20_to_49_units", "dwellings_50_or_more_units", "mobile_homes", "housing_built_2005_or_later", "housing_built_2000_to_2004", "housing_built_1939_or_earlier", "median_year_structure_built", "married_households", "nonfamily_households", "family_households", "households_public_asst_or_food_stamps", "male_male_households", "female_female_households", "children", "children_in_single_female_hh", "median_rent", "percent_income_spent_on_rent", "rent_burden_not_computed", "rent_over_50_percent", "rent_40_to_50_percent", "rent_35_to_40_percent", "rent_30_to_35_percent", "rent_25_to_30_percent", "rent_20_to_25_percent", "rent_15_to_20_percent", "rent_10_to_15_percent", "rent_under_10_percent", "owner_occupied_housing_units", "million_dollar_housing_units", "mortgaged_housing_units", "different_house_year_ago_different_city", "different_house_year_ago_same_city", "families_with_young_children", "two_parent_families_with_young_children", "two_parents_in_labor_force_families_with_young_children", "two_parents_father_in_labor_force_families_with_young_children", "two_parents_mother_in_labor_force_families_with_young_children", "two_parents_not_in_labor_force_families_with_young_children", "one_parent_families_with_young_children", "father_one_parent_families_with_young_children", "father_in_labor_force_one_parent_families_with_young_children", "commute_less_10_mins", "commute_10_14_mins", "commute_15_19_mins", "commute_20_24_mins", "commute_25_29_mins", "commute_30_34_mins", "commute_35_44_mins", "commute_60_more_mins", "commute_45_59_mins", "commuters_16_over", "walked_to_work", "worked_at_home", "no_car", "no_cars", "one_car", "two_cars", "three_cars", "four_more_cars", "aggregate_travel_time_to_work", "commuters_by_public_transportation", "commuters_by_bus", "commuters_by_car_truck_van", "commuters_by_carpool", "commuters_by_subway_or_elevated", "commuters_drove_alone", "group_quarters", "associates_degree", "bachelors_degree", "high_school_diploma", "less_one_year_college", "masters_degree", "one_year_more_college", "less_than_high_school_graduate", "high_school_including_ged", "bachelors_degree_2", "bachelors_degree_or_higher_25_64", "graduate_professional_degree", "some_college_and_associates_degree", "male_45_64_associates_degree", "male_45_64_bachelors_degree", "male_45_64_graduate_degree", "male_45_64_less_than_9_grade", "male_45_64_grade_9_12", "male_45_64_high_school", "male_45_64_some_college", "male_45_to_64", "employed_pop", "unemployed_pop", "pop_in_labor_force", "not_in_labor_force", "workers_16_and_over", "armed_forces", "civilian_labor_force", "employed_agriculture_forestry_fishing_hunting_mining", "employed_arts_entertainment_recreation_accommodation_food", "employed_construction", "employed_education_health_social", "employed_finance_insurance_real_estate", "employed_information", "employed_manufacturing", "employed_other_services_not_public_admin", "employed_public_administration", "employed_retail_trade", "employed_science_management_admin_waste", "employed_transportation_warehousing_utilities", "employed_wholesale_trade", "occupation_management_arts", "occupation_natural_resources_construction_maintenance", "occupation_production_transportation_material", "occupation_sales_office", "occupation_services", "management_business_sci_arts_employed", "sales_office_employed", "in_grades_1_to_4", "in_grades_5_to_8", "in_grades_9_to_12", "in_school", "in_undergrad_college", "speak_only_english_at_home", "speak_spanish_at_home", "speak_spanish_at_home_low_english"], "nested_column_types": ["STRING", "DATE", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64"], "description": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], "sample_rows": [{"geo_id": "09ZZ", "do_date": "2014-01-01", "total_pop": 0.0, "households": 0.0, "male_pop": 0.0, "female_pop": 0.0, "median_age": NaN, "male_under_5": 0.0, "male_5_to_9": 0.0, "male_10_to_14": 0.0, "male_15_to_17": 0.0, "male_18_to_19": 0.0, "male_20": 0.0, "male_21": 0.0, "male_22_to_24": 0.0, "male_25_to_29": 0.0, "male_30_to_34": 0.0, "male_35_to_39": 0.0, "male_40_to_44": 0.0, "male_45_to_49": 0.0, "male_50_to_54": 0.0, "male_55_to_59": 0.0, "male_60_to_61": 0.0, "male_62_to_64": 0.0, "male_65_to_66": 0.0, "male_67_to_69": 0.0, "male_70_to_74": 0.0, "male_75_to_79": 0.0, "male_80_to_84": 0.0, "male_85_and_over": 0.0, "female_under_5": 0.0, "female_5_to_9": 0.0, "female_10_to_14": 0.0, "female_15_to_17": 0.0, "female_18_to_19": 0.0, "female_20": 0.0, "female_21": 0.0, "female_22_to_24": 0.0, "female_25_to_29": 0.0, "female_30_to_34": 0.0, "female_35_to_39": 0.0, "female_40_to_44": 0.0, "female_45_to_49": 0.0, "female_50_to_54": 0.0, "female_55_to_59": 0.0, "female_60_to_61": 0.0, "female_62_to_64": 0.0, "female_65_to_66": 0.0, "female_67_to_69": 0.0, "female_70_to_74": 0.0, "female_75_to_79": 0.0, "female_80_to_84": 0.0, "female_85_and_over": 0.0, "white_pop": 0.0, "population_1_year_and_over": 0.0, "population_3_years_over": 0.0, "pop_5_years_over": 0.0, "pop_15_and_over": NaN, "pop_16_over": 0.0, "pop_25_years_over": 0.0, "pop_25_64": 0.0, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "not_us_citizen_pop": 0.0, "black_pop": 0.0, "asian_pop": 0.0, "hispanic_pop": 0.0, "amerindian_pop": 0.0, "other_race_pop": 0.0, "two_or_more_races_pop": 0.0, "hispanic_any_race": 0.0, "not_hispanic_pop": 0.0, "asian_male_45_54": 0.0, "asian_male_55_64": 0.0, "black_male_45_54": 0.0, "black_male_55_64": 0.0, "hispanic_male_45_54": 0.0, "hispanic_male_55_64": 0.0, "white_male_45_54": 0.0, "white_male_55_64": 0.0, "median_income": NaN, "income_per_capita": NaN, "income_less_10000": 0.0, "income_10000_14999": 0.0, "income_15000_19999": 0.0, "income_20000_24999": 0.0, "income_25000_29999": 0.0, "income_30000_34999": 0.0, "income_35000_39999": 0.0, "income_40000_44999": 0.0, "income_45000_49999": 0.0, "income_50000_59999": 0.0, "income_60000_74999": 0.0, "income_75000_99999": 0.0, "income_100000_124999": 0.0, "income_125000_149999": 0.0, "income_150000_199999": 0.0, "income_200000_or_more": 0.0, "pop_determined_poverty_status": 0.0, "poverty": 0.0, "gini_index": NaN, "housing_units": 0.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": NaN, "owner_occupied_housing_units_lower_value_quartile": NaN, "owner_occupied_housing_units_median_value": NaN, "owner_occupied_housing_units_upper_value_quartile": NaN, "occupied_housing_units": 0.0, "housing_units_renter_occupied": 0.0, "vacant_housing_units": 0.0, "vacant_housing_units_for_rent": 0.0, "vacant_housing_units_for_sale": 0.0, "dwellings_1_units_detached": 0.0, "dwellings_1_units_attached": 0.0, "dwellings_2_units": 0.0, "dwellings_3_to_4_units": 0.0, "dwellings_5_to_9_units": 0.0, "dwellings_10_to_19_units": 0.0, "dwellings_20_to_49_units": 0.0, "dwellings_50_or_more_units": 0.0, "mobile_homes": 0.0, "housing_built_2005_or_later": 0.0, "housing_built_2000_to_2004": 0.0, "housing_built_1939_or_earlier": 0.0, "median_year_structure_built": NaN, "married_households": 0.0, "nonfamily_households": 0.0, "family_households": 0.0, "households_public_asst_or_food_stamps": 0.0, "male_male_households": 0.0, "female_female_households": 0.0, "children": 0.0, "children_in_single_female_hh": 0.0, "median_rent": NaN, "percent_income_spent_on_rent": NaN, "rent_burden_not_computed": 0.0, "rent_over_50_percent": 0.0, "rent_40_to_50_percent": 0.0, "rent_35_to_40_percent": 0.0, "rent_30_to_35_percent": 0.0, "rent_25_to_30_percent": 0.0, "rent_20_to_25_percent": 0.0, "rent_15_to_20_percent": 0.0, "rent_10_to_15_percent": 0.0, "rent_under_10_percent": 0.0, "owner_occupied_housing_units": 0.0, "million_dollar_housing_units": 0.0, "mortgaged_housing_units": 0.0, "different_house_year_ago_different_city": 0.0, "different_house_year_ago_same_city": 0.0, "families_with_young_children": 0.0, "two_parent_families_with_young_children": 0.0, "two_parents_in_labor_force_families_with_young_children": 0.0, "two_parents_father_in_labor_force_families_with_young_children": 0.0, "two_parents_mother_in_labor_force_families_with_young_children": 0.0, "two_parents_not_in_labor_force_families_with_young_children": 0.0, "one_parent_families_with_young_children": 0.0, "father_one_parent_families_with_young_children": 0.0, "father_in_labor_force_one_parent_families_with_young_children": 0.0, "commute_less_10_mins": 0.0, "commute_10_14_mins": 0.0, "commute_15_19_mins": 0.0, "commute_20_24_mins": 0.0, "commute_25_29_mins": 0.0, "commute_30_34_mins": 0.0, "commute_35_44_mins": 0.0, "commute_60_more_mins": 0.0, "commute_45_59_mins": 0.0, "commuters_16_over": 0.0, "walked_to_work": 0.0, "worked_at_home": 0.0, "no_car": 0.0, "no_cars": 0.0, "one_car": 0.0, "two_cars": 0.0, "three_cars": 0.0, "four_more_cars": 0.0, "aggregate_travel_time_to_work": NaN, "commuters_by_public_transportation": 0.0, "commuters_by_bus": 0.0, "commuters_by_car_truck_van": 0.0, "commuters_by_carpool": 0.0, "commuters_by_subway_or_elevated": 0.0, "commuters_drove_alone": 0.0, "group_quarters": 0.0, "associates_degree": 0.0, "bachelors_degree": 0.0, "high_school_diploma": 0.0, "less_one_year_college": 0.0, "masters_degree": 0.0, "one_year_more_college": 0.0, "less_than_high_school_graduate": 0.0, "high_school_including_ged": 0.0, "bachelors_degree_2": 0.0, "bachelors_degree_or_higher_25_64": 0.0, "graduate_professional_degree": 0.0, "some_college_and_associates_degree": 0.0, "male_45_64_associates_degree": 0.0, "male_45_64_bachelors_degree": 0.0, "male_45_64_graduate_degree": 0.0, "male_45_64_less_than_9_grade": 0.0, "male_45_64_grade_9_12": 0.0, "male_45_64_high_school": 0.0, "male_45_64_some_college": 0.0, "male_45_to_64": 0.0, "employed_pop": 0.0, "unemployed_pop": 0.0, "pop_in_labor_force": 0.0, "not_in_labor_force": 0.0, "workers_16_and_over": 0.0, "armed_forces": 0.0, "civilian_labor_force": 0.0, "employed_agriculture_forestry_fishing_hunting_mining": 0.0, "employed_arts_entertainment_recreation_accommodation_food": 0.0, "employed_construction": 0.0, "employed_education_health_social": 0.0, "employed_finance_insurance_real_estate": 0.0, "employed_information": 0.0, "employed_manufacturing": 0.0, "employed_other_services_not_public_admin": 0.0, "employed_public_administration": 0.0, "employed_retail_trade": 0.0, "employed_science_management_admin_waste": 0.0, "employed_transportation_warehousing_utilities": 0.0, "employed_wholesale_trade": 0.0, "occupation_management_arts": 0.0, "occupation_natural_resources_construction_maintenance": 0.0, "occupation_production_transportation_material": 0.0, "occupation_sales_office": 0.0, "occupation_services": 0.0, "management_business_sci_arts_employed": 0.0, "sales_office_employed": 0.0, "in_grades_1_to_4": 0.0, "in_grades_5_to_8": 0.0, "in_grades_9_to_12": 0.0, "in_school": 0.0, "in_undergrad_college": 0.0, "speak_only_english_at_home": 0.0, "speak_spanish_at_home": 0.0, "speak_spanish_at_home_low_english": 0.0}, {"geo_id": "26ZZ", "do_date": "2014-01-01", "total_pop": 0.0, "households": 0.0, "male_pop": 0.0, "female_pop": 0.0, "median_age": NaN, "male_under_5": 0.0, "male_5_to_9": 0.0, "male_10_to_14": 0.0, "male_15_to_17": 0.0, "male_18_to_19": 0.0, "male_20": 0.0, "male_21": 0.0, "male_22_to_24": 0.0, "male_25_to_29": 0.0, "male_30_to_34": 0.0, "male_35_to_39": 0.0, "male_40_to_44": 0.0, "male_45_to_49": 0.0, "male_50_to_54": 0.0, "male_55_to_59": 0.0, "male_60_to_61": 0.0, "male_62_to_64": 0.0, "male_65_to_66": 0.0, "male_67_to_69": 0.0, "male_70_to_74": 0.0, "male_75_to_79": 0.0, "male_80_to_84": 0.0, "male_85_and_over": 0.0, "female_under_5": 0.0, "female_5_to_9": 0.0, "female_10_to_14": 0.0, "female_15_to_17": 0.0, "female_18_to_19": 0.0, "female_20": 0.0, "female_21": 0.0, "female_22_to_24": 0.0, "female_25_to_29": 0.0, "female_30_to_34": 0.0, "female_35_to_39": 0.0, "female_40_to_44": 0.0, "female_45_to_49": 0.0, "female_50_to_54": 0.0, "female_55_to_59": 0.0, "female_60_to_61": 0.0, "female_62_to_64": 0.0, "female_65_to_66": 0.0, "female_67_to_69": 0.0, "female_70_to_74": 0.0, "female_75_to_79": 0.0, "female_80_to_84": 0.0, "female_85_and_over": 0.0, "white_pop": 0.0, "population_1_year_and_over": 0.0, "population_3_years_over": 0.0, "pop_5_years_over": 0.0, "pop_15_and_over": NaN, "pop_16_over": 0.0, "pop_25_years_over": 0.0, "pop_25_64": 0.0, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "not_us_citizen_pop": 0.0, "black_pop": 0.0, "asian_pop": 0.0, "hispanic_pop": 0.0, "amerindian_pop": 0.0, "other_race_pop": 0.0, "two_or_more_races_pop": 0.0, "hispanic_any_race": 0.0, "not_hispanic_pop": 0.0, "asian_male_45_54": 0.0, "asian_male_55_64": 0.0, "black_male_45_54": 0.0, "black_male_55_64": 0.0, "hispanic_male_45_54": 0.0, "hispanic_male_55_64": 0.0, "white_male_45_54": 0.0, "white_male_55_64": 0.0, "median_income": NaN, "income_per_capita": NaN, "income_less_10000": 0.0, "income_10000_14999": 0.0, "income_15000_19999": 0.0, "income_20000_24999": 0.0, "income_25000_29999": 0.0, "income_30000_34999": 0.0, "income_35000_39999": 0.0, "income_40000_44999": 0.0, "income_45000_49999": 0.0, "income_50000_59999": 0.0, "income_60000_74999": 0.0, "income_75000_99999": 0.0, "income_100000_124999": 0.0, "income_125000_149999": 0.0, "income_150000_199999": 0.0, "income_200000_or_more": 0.0, "pop_determined_poverty_status": 0.0, "poverty": 0.0, "gini_index": NaN, "housing_units": 0.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": NaN, "owner_occupied_housing_units_lower_value_quartile": NaN, "owner_occupied_housing_units_median_value": NaN, "owner_occupied_housing_units_upper_value_quartile": NaN, "occupied_housing_units": 0.0, "housing_units_renter_occupied": 0.0, "vacant_housing_units": 0.0, "vacant_housing_units_for_rent": 0.0, "vacant_housing_units_for_sale": 0.0, "dwellings_1_units_detached": 0.0, "dwellings_1_units_attached": 0.0, "dwellings_2_units": 0.0, "dwellings_3_to_4_units": 0.0, "dwellings_5_to_9_units": 0.0, "dwellings_10_to_19_units": 0.0, "dwellings_20_to_49_units": 0.0, "dwellings_50_or_more_units": 0.0, "mobile_homes": 0.0, "housing_built_2005_or_later": 0.0, "housing_built_2000_to_2004": 0.0, "housing_built_1939_or_earlier": 0.0, "median_year_structure_built": NaN, "married_households": 0.0, "nonfamily_households": 0.0, "family_households": 0.0, "households_public_asst_or_food_stamps": 0.0, "male_male_households": 0.0, "female_female_households": 0.0, "children": 0.0, "children_in_single_female_hh": 0.0, "median_rent": NaN, "percent_income_spent_on_rent": NaN, "rent_burden_not_computed": 0.0, "rent_over_50_percent": 0.0, "rent_40_to_50_percent": 0.0, "rent_35_to_40_percent": 0.0, "rent_30_to_35_percent": 0.0, "rent_25_to_30_percent": 0.0, "rent_20_to_25_percent": 0.0, "rent_15_to_20_percent": 0.0, "rent_10_to_15_percent": 0.0, "rent_under_10_percent": 0.0, "owner_occupied_housing_units": 0.0, "million_dollar_housing_units": 0.0, "mortgaged_housing_units": 0.0, "different_house_year_ago_different_city": 0.0, "different_house_year_ago_same_city": 0.0, "families_with_young_children": 0.0, "two_parent_families_with_young_children": 0.0, "two_parents_in_labor_force_families_with_young_children": 0.0, "two_parents_father_in_labor_force_families_with_young_children": 0.0, "two_parents_mother_in_labor_force_families_with_young_children": 0.0, "two_parents_not_in_labor_force_families_with_young_children": 0.0, "one_parent_families_with_young_children": 0.0, "father_one_parent_families_with_young_children": 0.0, "father_in_labor_force_one_parent_families_with_young_children": 0.0, "commute_less_10_mins": 0.0, "commute_10_14_mins": 0.0, "commute_15_19_mins": 0.0, "commute_20_24_mins": 0.0, "commute_25_29_mins": 0.0, "commute_30_34_mins": 0.0, "commute_35_44_mins": 0.0, "commute_60_more_mins": 0.0, "commute_45_59_mins": 0.0, "commuters_16_over": 0.0, "walked_to_work": 0.0, "worked_at_home": 0.0, "no_car": 0.0, "no_cars": 0.0, "one_car": 0.0, "two_cars": 0.0, "three_cars": 0.0, "four_more_cars": 0.0, "aggregate_travel_time_to_work": NaN, "commuters_by_public_transportation": 0.0, "commuters_by_bus": 0.0, "commuters_by_car_truck_van": 0.0, "commuters_by_carpool": 0.0, "commuters_by_subway_or_elevated": 0.0, "commuters_drove_alone": 0.0, "group_quarters": 0.0, "associates_degree": 0.0, "bachelors_degree": 0.0, "high_school_diploma": 0.0, "less_one_year_college": 0.0, "masters_degree": 0.0, "one_year_more_college": 0.0, "less_than_high_school_graduate": 0.0, "high_school_including_ged": 0.0, "bachelors_degree_2": 0.0, "bachelors_degree_or_higher_25_64": 0.0, "graduate_professional_degree": 0.0, "some_college_and_associates_degree": 0.0, "male_45_64_associates_degree": 0.0, "male_45_64_bachelors_degree": 0.0, "male_45_64_graduate_degree": 0.0, "male_45_64_less_than_9_grade": 0.0, "male_45_64_grade_9_12": 0.0, "male_45_64_high_school": 0.0, "male_45_64_some_college": 0.0, "male_45_to_64": 0.0, "employed_pop": 0.0, "unemployed_pop": 0.0, "pop_in_labor_force": 0.0, "not_in_labor_force": 0.0, "workers_16_and_over": 0.0, "armed_forces": 0.0, "civilian_labor_force": 0.0, "employed_agriculture_forestry_fishing_hunting_mining": 0.0, "employed_arts_entertainment_recreation_accommodation_food": 0.0, "employed_construction": 0.0, "employed_education_health_social": 0.0, "employed_finance_insurance_real_estate": 0.0, "employed_information": 0.0, "employed_manufacturing": 0.0, "employed_other_services_not_public_admin": 0.0, "employed_public_administration": 0.0, "employed_retail_trade": 0.0, "employed_science_management_admin_waste": 0.0, "employed_transportation_warehousing_utilities": 0.0, "employed_wholesale_trade": 0.0, "occupation_management_arts": 0.0, "occupation_natural_resources_construction_maintenance": 0.0, "occupation_production_transportation_material": 0.0, "occupation_sales_office": 0.0, "occupation_services": 0.0, "management_business_sci_arts_employed": 0.0, "sales_office_employed": 0.0, "in_grades_1_to_4": 0.0, "in_grades_5_to_8": 0.0, "in_grades_9_to_12": 0.0, "in_school": 0.0, "in_undergrad_college": 0.0, "speak_only_english_at_home": 0.0, "speak_spanish_at_home": 0.0, "speak_spanish_at_home_low_english": 0.0}, {"geo_id": "17ZZ", "do_date": "2014-01-01", "total_pop": 0.0, "households": 0.0, "male_pop": 0.0, "female_pop": 0.0, "median_age": NaN, "male_under_5": 0.0, "male_5_to_9": 0.0, "male_10_to_14": 0.0, "male_15_to_17": 0.0, "male_18_to_19": 0.0, "male_20": 0.0, "male_21": 0.0, "male_22_to_24": 0.0, "male_25_to_29": 0.0, "male_30_to_34": 0.0, "male_35_to_39": 0.0, "male_40_to_44": 0.0, "male_45_to_49": 0.0, "male_50_to_54": 0.0, "male_55_to_59": 0.0, "male_60_to_61": 0.0, "male_62_to_64": 0.0, "male_65_to_66": 0.0, "male_67_to_69": 0.0, "male_70_to_74": 0.0, "male_75_to_79": 0.0, "male_80_to_84": 0.0, "male_85_and_over": 0.0, "female_under_5": 0.0, "female_5_to_9": 0.0, "female_10_to_14": 0.0, "female_15_to_17": 0.0, "female_18_to_19": 0.0, "female_20": 0.0, "female_21": 0.0, "female_22_to_24": 0.0, "female_25_to_29": 0.0, "female_30_to_34": 0.0, "female_35_to_39": 0.0, "female_40_to_44": 0.0, "female_45_to_49": 0.0, "female_50_to_54": 0.0, "female_55_to_59": 0.0, "female_60_to_61": 0.0, "female_62_to_64": 0.0, "female_65_to_66": 0.0, "female_67_to_69": 0.0, "female_70_to_74": 0.0, "female_75_to_79": 0.0, "female_80_to_84": 0.0, "female_85_and_over": 0.0, "white_pop": 0.0, "population_1_year_and_over": 0.0, "population_3_years_over": 0.0, "pop_5_years_over": 0.0, "pop_15_and_over": NaN, "pop_16_over": 0.0, "pop_25_years_over": 0.0, "pop_25_64": 0.0, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "not_us_citizen_pop": 0.0, "black_pop": 0.0, "asian_pop": 0.0, "hispanic_pop": 0.0, "amerindian_pop": 0.0, "other_race_pop": 0.0, "two_or_more_races_pop": 0.0, "hispanic_any_race": 0.0, "not_hispanic_pop": 0.0, "asian_male_45_54": 0.0, "asian_male_55_64": 0.0, "black_male_45_54": 0.0, "black_male_55_64": 0.0, "hispanic_male_45_54": 0.0, "hispanic_male_55_64": 0.0, "white_male_45_54": 0.0, "white_male_55_64": 0.0, "median_income": NaN, "income_per_capita": NaN, "income_less_10000": 0.0, "income_10000_14999": 0.0, "income_15000_19999": 0.0, "income_20000_24999": 0.0, "income_25000_29999": 0.0, "income_30000_34999": 0.0, "income_35000_39999": 0.0, "income_40000_44999": 0.0, "income_45000_49999": 0.0, "income_50000_59999": 0.0, "income_60000_74999": 0.0, "income_75000_99999": 0.0, "income_100000_124999": 0.0, "income_125000_149999": 0.0, "income_150000_199999": 0.0, "income_200000_or_more": 0.0, "pop_determined_poverty_status": 0.0, "poverty": 0.0, "gini_index": NaN, "housing_units": 0.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": NaN, "owner_occupied_housing_units_lower_value_quartile": NaN, "owner_occupied_housing_units_median_value": NaN, "owner_occupied_housing_units_upper_value_quartile": NaN, "occupied_housing_units": 0.0, "housing_units_renter_occupied": 0.0, "vacant_housing_units": 0.0, "vacant_housing_units_for_rent": 0.0, "vacant_housing_units_for_sale": 0.0, "dwellings_1_units_detached": 0.0, "dwellings_1_units_attached": 0.0, "dwellings_2_units": 0.0, "dwellings_3_to_4_units": 0.0, "dwellings_5_to_9_units": 0.0, "dwellings_10_to_19_units": 0.0, "dwellings_20_to_49_units": 0.0, "dwellings_50_or_more_units": 0.0, "mobile_homes": 0.0, "housing_built_2005_or_later": 0.0, "housing_built_2000_to_2004": 0.0, "housing_built_1939_or_earlier": 0.0, "median_year_structure_built": NaN, "married_households": 0.0, "nonfamily_households": 0.0, "family_households": 0.0, "households_public_asst_or_food_stamps": 0.0, "male_male_households": 0.0, "female_female_households": 0.0, "children": 0.0, "children_in_single_female_hh": 0.0, "median_rent": NaN, "percent_income_spent_on_rent": NaN, "rent_burden_not_computed": 0.0, "rent_over_50_percent": 0.0, "rent_40_to_50_percent": 0.0, "rent_35_to_40_percent": 0.0, "rent_30_to_35_percent": 0.0, "rent_25_to_30_percent": 0.0, "rent_20_to_25_percent": 0.0, "rent_15_to_20_percent": 0.0, "rent_10_to_15_percent": 0.0, "rent_under_10_percent": 0.0, "owner_occupied_housing_units": 0.0, "million_dollar_housing_units": 0.0, "mortgaged_housing_units": 0.0, "different_house_year_ago_different_city": 0.0, "different_house_year_ago_same_city": 0.0, "families_with_young_children": 0.0, "two_parent_families_with_young_children": 0.0, "two_parents_in_labor_force_families_with_young_children": 0.0, "two_parents_father_in_labor_force_families_with_young_children": 0.0, "two_parents_mother_in_labor_force_families_with_young_children": 0.0, "two_parents_not_in_labor_force_families_with_young_children": 0.0, "one_parent_families_with_young_children": 0.0, "father_one_parent_families_with_young_children": 0.0, "father_in_labor_force_one_parent_families_with_young_children": 0.0, "commute_less_10_mins": 0.0, "commute_10_14_mins": 0.0, "commute_15_19_mins": 0.0, "commute_20_24_mins": 0.0, "commute_25_29_mins": 0.0, "commute_30_34_mins": 0.0, "commute_35_44_mins": 0.0, "commute_60_more_mins": 0.0, "commute_45_59_mins": 0.0, "commuters_16_over": 0.0, "walked_to_work": 0.0, "worked_at_home": 0.0, "no_car": 0.0, "no_cars": 0.0, "one_car": 0.0, "two_cars": 0.0, "three_cars": 0.0, "four_more_cars": 0.0, "aggregate_travel_time_to_work": NaN, "commuters_by_public_transportation": 0.0, "commuters_by_bus": 0.0, "commuters_by_car_truck_van": 0.0, "commuters_by_carpool": 0.0, "commuters_by_subway_or_elevated": 0.0, "commuters_drove_alone": 0.0, "group_quarters": 0.0, "associates_degree": 0.0, "bachelors_degree": 0.0, "high_school_diploma": 0.0, "less_one_year_college": 0.0, "masters_degree": 0.0, "one_year_more_college": 0.0, "less_than_high_school_graduate": 0.0, "high_school_including_ged": 0.0, "bachelors_degree_2": 0.0, "bachelors_degree_or_higher_25_64": 0.0, "graduate_professional_degree": 0.0, "some_college_and_associates_degree": 0.0, "male_45_64_associates_degree": 0.0, "male_45_64_bachelors_degree": 0.0, "male_45_64_graduate_degree": 0.0, "male_45_64_less_than_9_grade": 0.0, "male_45_64_grade_9_12": 0.0, "male_45_64_high_school": 0.0, "male_45_64_some_college": 0.0, "male_45_to_64": 0.0, "employed_pop": 0.0, "unemployed_pop": 0.0, "pop_in_labor_force": 0.0, "not_in_labor_force": 0.0, "workers_16_and_over": 0.0, "armed_forces": 0.0, "civilian_labor_force": 0.0, "employed_agriculture_forestry_fishing_hunting_mining": 0.0, "employed_arts_entertainment_recreation_accommodation_food": 0.0, "employed_construction": 0.0, "employed_education_health_social": 0.0, "employed_finance_insurance_real_estate": 0.0, "employed_information": 0.0, "employed_manufacturing": 0.0, "employed_other_services_not_public_admin": 0.0, "employed_public_administration": 0.0, "employed_retail_trade": 0.0, "employed_science_management_admin_waste": 0.0, "employed_transportation_warehousing_utilities": 0.0, "employed_wholesale_trade": 0.0, "occupation_management_arts": 0.0, "occupation_natural_resources_construction_maintenance": 0.0, "occupation_production_transportation_material": 0.0, "occupation_sales_office": 0.0, "occupation_services": 0.0, "management_business_sci_arts_employed": 0.0, "sales_office_employed": 0.0, "in_grades_1_to_4": 0.0, "in_grades_5_to_8": 0.0, "in_grades_9_to_12": 0.0, "in_school": 0.0, "in_undergrad_college": 0.0, "speak_only_english_at_home": 0.0, "speak_spanish_at_home": 0.0, "speak_spanish_at_home_low_english": 0.0}, {"geo_id": "4203", "do_date": "2014-01-01", "total_pop": 722979.0, "households": 294473.0, "male_pop": 334718.0, "female_pop": 388261.0, "median_age": 33.9, "male_under_5": 21846.0, "male_5_to_9": 17891.0, "male_10_to_14": 18655.0, "male_15_to_17": 10141.0, "male_18_to_19": 11921.0, "male_20": 5739.0, "male_21": 6089.0, "male_22_to_24": 18384.0, "male_25_to_29": 39971.0, "male_30_to_34": 30924.0, "male_35_to_39": 21764.0, "male_40_to_44": 17446.0, "male_45_to_49": 17984.0, "male_50_to_54": 19939.0, "male_55_to_59": 19782.0, "male_60_to_61": 7311.0, "male_62_to_64": 9648.0, "male_65_to_66": 6201.0, "male_67_to_69": 7838.0, "male_70_to_74": 9816.0, "male_75_to_79": 7004.0, "male_80_to_84": 4321.0, "male_85_and_over": 4103.0, "female_under_5": 21470.0, "female_5_to_9": 17629.0, "female_10_to_14": 17159.0, "female_15_to_17": 10261.0, "female_18_to_19": 12815.0, "female_20": 6422.0, "female_21": 7638.0, "female_22_to_24": 19467.0, "female_25_to_29": 46371.0, "female_30_to_34": 33234.0, "female_35_to_39": 24585.0, "female_40_to_44": 19595.0, "female_45_to_49": 20873.0, "female_50_to_54": 22192.0, "female_55_to_59": 23493.0, "female_60_to_61": 9706.0, "female_62_to_64": 13237.0, "female_65_to_66": 8291.0, "female_67_to_69": 11097.0, "female_70_to_74": 13830.0, "female_75_to_79": 10254.0, "female_80_to_84": 8691.0, "female_85_and_over": 9951.0, "white_pop": 216411.0, "population_1_year_and_over": 714272.0, "population_3_years_over": 696784.0, "pop_5_years_over": 679663.0, "pop_15_and_over": NaN, "pop_16_over": 601946.0, "pop_25_years_over": 499452.0, "pop_25_64": 398055.0, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "not_us_citizen_pop": 35015.0, "black_pop": 408344.0, "asian_pop": 42260.0, "hispanic_pop": 33468.0, "amerindian_pop": 1676.0, "other_race_pop": 2159.0, "two_or_more_races_pop": 18393.0, "hispanic_any_race": 33468.0, "not_hispanic_pop": 689511.0, "asian_male_45_54": 1831.0, "asian_male_55_64": 1295.0, "black_male_45_54": 23260.0, "black_male_55_64": 23482.0, "hispanic_male_45_54": 1333.0, "hispanic_male_55_64": 747.0, "white_male_45_54": 10990.0, "white_male_55_64": 10807.0, "median_income": 45084.0, "income_per_capita": 31290.0, "income_less_10000": 41241.0, "income_10000_14999": 19745.0, "income_15000_19999": 18118.0, "income_20000_24999": 15379.0, "income_25000_29999": 14198.0, "income_30000_34999": 13606.0, "income_35000_39999": 11948.0, "income_40000_44999": 12808.0, "income_45000_49999": 10856.0, "income_50000_59999": 21190.0, "income_60000_74999": 23812.0, "income_75000_99999": 28176.0, "income_100000_124999": 18922.0, "income_125000_149999": 13182.0, "income_150000_199999": 14082.0, "income_200000_or_more": 17210.0, "pop_determined_poverty_status": 693319.0, "poverty": 171372.0, "gini_index": 0.5452, "housing_units": 349649.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 1062.0, "owner_occupied_housing_units_lower_value_quartile": 91100.0, "owner_occupied_housing_units_median_value": 166400.0, "owner_occupied_housing_units_upper_value_quartile": 306200.0, "occupied_housing_units": 294473.0, "housing_units_renter_occupied": 152372.0, "vacant_housing_units": 55176.0, "vacant_housing_units_for_rent": 13482.0, "vacant_housing_units_for_sale": 3658.0, "dwellings_1_units_detached": 22001.0, "dwellings_1_units_attached": 186911.0, "dwellings_2_units": 24846.0, "dwellings_3_to_4_units": 28013.0, "dwellings_5_to_9_units": 16704.0, "dwellings_10_to_19_units": 7820.0, "dwellings_20_to_49_units": 13414.0, "dwellings_50_or_more_units": 49283.0, "mobile_homes": 515.0, "housing_built_2005_or_later": 4083.0, "housing_built_2000_to_2004": 5455.0, "housing_built_1939_or_earlier": 38952.0, "median_year_structure_built": 0.0, "married_households": 69044.0, "nonfamily_households": 157354.0, "family_households": 137119.0, "households_public_asst_or_food_stamps": 68855.0, "male_male_households": 1018.0, "female_female_households": 987.0, "children": 135052.0, "children_in_single_female_hh": 73969.0, "median_rent": 880.0, "percent_income_spent_on_rent": 31.4, "rent_burden_not_computed": 13433.0, "rent_over_50_percent": 41990.0, "rent_40_to_50_percent": 11070.0, "rent_35_to_40_percent": 7988.0, "rent_30_to_35_percent": 11799.0, "rent_25_to_30_percent": 16187.0, "rent_20_to_25_percent": 15797.0, "rent_15_to_20_percent": 17260.0, "rent_10_to_15_percent": 11404.0, "rent_under_10_percent": 5444.0, "owner_occupied_housing_units": 142101.0, "million_dollar_housing_units": 1946.0, "mortgaged_housing_units": 82346.0, "different_house_year_ago_different_city": 40989.0, "different_house_year_ago_same_city": 73017.0, "families_with_young_children": 48491.0, "two_parent_families_with_young_children": 19846.0, "two_parents_in_labor_force_families_with_young_children": 14083.0, "two_parents_father_in_labor_force_families_with_young_children": 4338.0, "two_parents_mother_in_labor_force_families_with_young_children": 1014.0, "two_parents_not_in_labor_force_families_with_young_children": 411.0, "one_parent_families_with_young_children": 28645.0, "father_one_parent_families_with_young_children": 3912.0, "father_in_labor_force_one_parent_families_with_young_children": 3133.0, "commute_less_10_mins": 18728.0, "commute_10_14_mins": 25584.0, "commute_15_19_mins": 36018.0, "commute_20_24_mins": 44543.0, "commute_25_29_mins": 20369.0, "commute_30_34_mins": 56503.0, "commute_35_44_mins": 28023.0, "commute_60_more_mins": 40969.0, "commute_45_59_mins": 34936.0, "commuters_16_over": 305673.0, "walked_to_work": 42071.0, "worked_at_home": 15956.0, "no_car": 74661.0, "no_cars": 107440.0, "one_car": 127574.0, "two_cars": 48160.0, "three_cars": 8721.0, "four_more_cars": 2578.0, "aggregate_travel_time_to_work": 9897740.0, "commuters_by_public_transportation": 93486.0, "commuters_by_bus": 63089.0, "commuters_by_car_truck_van": 154721.0, "commuters_by_carpool": 18574.0, "commuters_by_subway_or_elevated": 17360.0, "commuters_drove_alone": 136147.0, "group_quarters": 34050.0, "associates_degree": 25537.0, "bachelors_degree": 100950.0, "high_school_diploma": 122092.0, "less_one_year_college": 21299.0, "masters_degree": 55837.0, "one_year_more_college": 63522.0, "less_than_high_school_graduate": 59645.0, "high_school_including_ged": 139110.0, "bachelors_degree_2": 100950.0, "bachelors_degree_or_higher_25_64": 164768.0, "graduate_professional_degree": 89389.0, "some_college_and_associates_degree": 110358.0, "male_45_64_associates_degree": 3629.0, "male_45_64_bachelors_degree": 10090.0, "male_45_64_graduate_degree": 10053.0, "male_45_64_less_than_9_grade": 2188.0, "male_45_64_grade_9_12": 8623.0, "male_45_64_high_school": 26511.0, "male_45_64_some_college": 13570.0, "male_45_to_64": 74664.0, "employed_pop": 329006.0, "unemployed_pop": 34463.0, "pop_in_labor_force": 363647.0, "not_in_labor_force": 238299.0, "workers_16_and_over": 321629.0, "armed_forces": 178.0, "civilian_labor_force": 363469.0, "employed_agriculture_forestry_fishing_hunting_mining": 836.0, "employed_arts_entertainment_recreation_accommodation_food": 32490.0, "employed_construction": 7536.0, "employed_education_health_social": 112889.0, "employed_finance_insurance_real_estate": 22794.0, "employed_information": 8066.0, "employed_manufacturing": 15423.0, "employed_other_services_not_public_admin": 13387.0, "employed_public_administration": 18739.0, "employed_retail_trade": 27923.0, "employed_science_management_admin_waste": 47027.0, "employed_transportation_warehousing_utilities": 17213.0, "employed_wholesale_trade": 4683.0, "occupation_management_arts": 157175.0, "occupation_natural_resources_construction_maintenance": 10788.0, "occupation_production_transportation_material": 28679.0, "occupation_sales_office": 64590.0, "occupation_services": 67774.0, "management_business_sci_arts_employed": 157175.0, "sales_office_employed": 64590.0, "in_grades_1_to_4": 28089.0, "in_grades_5_to_8": 28740.0, "in_grades_9_to_12": 28224.0, "in_school": 191467.0, "in_undergrad_college": 59736.0, "speak_only_english_at_home": 600020.0, "speak_spanish_at_home": 22765.0, "speak_spanish_at_home_low_english": 7646.0}, {"geo_id": "3607", "do_date": "2014-01-01", "total_pop": 734208.0, "households": 254629.0, "male_pop": 361368.0, "female_pop": 372840.0, "median_age": 33.6, "male_under_5": 28124.0, "male_5_to_9": 24296.0, "male_10_to_14": 22190.0, "male_15_to_17": 12488.0, "male_18_to_19": 7432.0, "male_20": 3812.0, "male_21": 4247.0, "male_22_to_24": 16905.0, "male_25_to_29": 39556.0, "male_30_to_34": 37315.0, "male_35_to_39": 30150.0, "male_40_to_44": 24431.0, "male_45_to_49": 22210.0, "male_50_to_54": 20789.0, "male_55_to_59": 18417.0, "male_60_to_61": 6731.0, "male_62_to_64": 8718.0, "male_65_to_66": 5471.0, "male_67_to_69": 6175.0, "male_70_to_74": 8313.0, "male_75_to_79": 5801.0, "male_80_to_84": 4501.0, "male_85_and_over": 3296.0, "female_under_5": 27160.0, "female_5_to_9": 22747.0, "female_10_to_14": 20566.0, "female_15_to_17": 12267.0, "female_18_to_19": 7252.0, "female_20": 4349.0, "female_21": 4469.0, "female_22_to_24": 18133.0, "female_25_to_29": 37852.0, "female_30_to_34": 34276.0, "female_35_to_39": 29681.0, "female_40_to_44": 23322.0, "female_45_to_49": 22643.0, "female_50_to_54": 21704.0, "female_55_to_59": 20032.0, "female_60_to_61": 8742.0, "female_62_to_64": 10088.0, "female_65_to_66": 6523.0, "female_67_to_69": 7719.0, "female_70_to_74": 11187.0, "female_75_to_79": 8259.0, "female_80_to_84": 6735.0, "female_85_and_over": 7134.0, "white_pop": 227596.0, "population_1_year_and_over": 723221.0, "population_3_years_over": 700502.0, "pop_5_years_over": 678924.0, "pop_15_and_over": NaN, "pop_16_over": 581193.0, "pop_25_years_over": 497771.0, "pop_25_64": 416657.0, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "not_us_citizen_pop": 132404.0, "black_pop": 60207.0, "asian_pop": 131516.0, "hispanic_pop": 295651.0, "amerindian_pop": 1051.0, "other_race_pop": 3395.0, "two_or_more_races_pop": 14410.0, "hispanic_any_race": 295651.0, "not_hispanic_pop": 438557.0, "asian_male_45_54": 8845.0, "asian_male_55_64": 8069.0, "black_male_45_54": 5526.0, "black_male_55_64": 4357.0, "hispanic_male_45_54": 17973.0, "hispanic_male_55_64": 12498.0, "white_male_45_54": 11195.0, "white_male_55_64": 9394.0, "median_income": 55497.0, "income_per_capita": 32740.0, "income_less_10000": 24186.0, "income_10000_14999": 19025.0, "income_15000_19999": 14132.0, "income_20000_24999": 12967.0, "income_25000_29999": 10590.0, "income_30000_34999": 10459.0, "income_35000_39999": 9008.0, "income_40000_44999": 9248.0, "income_45000_49999": 8497.0, "income_50000_59999": 15183.0, "income_60000_74999": 19856.0, "income_75000_99999": 26330.0, "income_100000_124999": 19581.0, "income_125000_149999": 13401.0, "income_150000_199999": 17194.0, "income_200000_or_more": 24972.0, "pop_determined_poverty_status": 725043.0, "poverty": 176549.0, "gini_index": 0.5538, "housing_units": 276299.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 1383.0, "owner_occupied_housing_units_lower_value_quartile": 463800.0, "owner_occupied_housing_units_median_value": 693000.0, "owner_occupied_housing_units_upper_value_quartile": 1108700.0, "occupied_housing_units": 254629.0, "housing_units_renter_occupied": 196575.0, "vacant_housing_units": 21670.0, "vacant_housing_units_for_rent": 5665.0, "vacant_housing_units_for_sale": 1124.0, "dwellings_1_units_detached": 8495.0, "dwellings_1_units_attached": 12884.0, "dwellings_2_units": 44448.0, "dwellings_3_to_4_units": 49913.0, "dwellings_5_to_9_units": 47489.0, "dwellings_10_to_19_units": 23918.0, "dwellings_20_to_49_units": 38435.0, "dwellings_50_or_more_units": 50159.0, "mobile_homes": 352.0, "housing_built_2005_or_later": 1794.0, "housing_built_2000_to_2004": 4788.0, "housing_built_1939_or_earlier": 22487.0, "median_year_structure_built": 0.0, "married_households": 98844.0, "nonfamily_households": 97804.0, "family_households": 156825.0, "households_public_asst_or_food_stamps": 67390.0, "male_male_households": 931.0, "female_female_households": 433.0, "children": 169838.0, "children_in_single_female_hh": 47342.0, "median_rent": 1272.0, "percent_income_spent_on_rent": 31.4, "rent_burden_not_computed": 8690.0, "rent_over_50_percent": 52190.0, "rent_40_to_50_percent": 16130.0, "rent_35_to_40_percent": 12586.0, "rent_30_to_35_percent": 18161.0, "rent_25_to_30_percent": 20213.0, "rent_20_to_25_percent": 22240.0, "rent_15_to_20_percent": 20967.0, "rent_10_to_15_percent": 15476.0, "rent_under_10_percent": 9922.0, "owner_occupied_housing_units": 58054.0, "million_dollar_housing_units": 7945.0, "mortgaged_housing_units": 36007.0, "different_house_year_ago_different_city": 15070.0, "different_house_year_ago_same_city": 54759.0, "families_with_young_children": 62867.0, "two_parent_families_with_young_children": 42890.0, "two_parents_in_labor_force_families_with_young_children": 23190.0, "two_parents_father_in_labor_force_families_with_young_children": 16586.0, "two_parents_mother_in_labor_force_families_with_young_children": 2445.0, "two_parents_not_in_labor_force_families_with_young_children": 669.0, "one_parent_families_with_young_children": 19977.0, "father_one_parent_families_with_young_children": 3987.0, "father_in_labor_force_one_parent_families_with_young_children": 3401.0, "commute_less_10_mins": 12315.0, "commute_10_14_mins": 17224.0, "commute_15_19_mins": 22025.0, "commute_20_24_mins": 24455.0, "commute_25_29_mins": 12768.0, "commute_30_34_mins": 61011.0, "commute_35_44_mins": 42457.0, "commute_60_more_mins": 68098.0, "commute_45_59_mins": 64975.0, "commuters_16_over": 325328.0, "walked_to_work": 44597.0, "worked_at_home": 16063.0, "no_car": 193392.0, "no_cars": 164227.0, "one_car": 73680.0, "two_cars": 13723.0, "three_cars": 2375.0, "four_more_cars": 624.0, "aggregate_travel_time_to_work": 12736965.0, "commuters_by_public_transportation": 213069.0, "commuters_by_bus": 18371.0, "commuters_by_car_truck_van": 55433.0, "commuters_by_carpool": 12625.0, "commuters_by_subway_or_elevated": 191253.0, "commuters_drove_alone": 42808.0, "group_quarters": 13030.0, "associates_degree": 21884.0, "bachelors_degree": 103800.0, "high_school_diploma": 99206.0, "less_one_year_college": 15066.0, "masters_degree": 46406.0, "one_year_more_college": 41194.0, "less_than_high_school_graduate": 135603.0, "high_school_including_ged": 116021.0, "bachelors_degree_2": 103800.0, "bachelors_degree_or_higher_25_64": 155002.0, "graduate_professional_degree": 64203.0, "some_college_and_associates_degree": 78144.0, "male_45_64_associates_degree": 3724.0, "male_45_64_bachelors_degree": 10324.0, "male_45_64_graduate_degree": 7926.0, "male_45_64_less_than_9_grade": 12873.0, "male_45_64_grade_9_12": 10943.0, "male_45_64_high_school": 21675.0, "male_45_64_some_college": 9400.0, "male_45_to_64": 76865.0, "employed_pop": 349854.0, "unemployed_pop": 24148.0, "pop_in_labor_force": 374042.0, "not_in_labor_force": 207151.0, "workers_16_and_over": 341391.0, "armed_forces": 40.0, "civilian_labor_force": 374002.0, "employed_agriculture_forestry_fishing_hunting_mining": 551.0, "employed_arts_entertainment_recreation_accommodation_food": 50349.0, "employed_construction": 20257.0, "employed_education_health_social": 73436.0, "employed_finance_insurance_real_estate": 26413.0, "employed_information": 18438.0, "employed_manufacturing": 16288.0, "employed_other_services_not_public_admin": 19528.0, "employed_public_administration": 9304.0, "employed_retail_trade": 35221.0, "employed_science_management_admin_waste": 54779.0, "employed_transportation_warehousing_utilities": 17612.0, "employed_wholesale_trade": 7678.0, "occupation_management_arts": 140305.0, "occupation_natural_resources_construction_maintenance": 22975.0, "occupation_production_transportation_material": 35657.0, "occupation_sales_office": 69959.0, "occupation_services": 80958.0, "management_business_sci_arts_employed": 140305.0, "sales_office_employed": 69959.0, "in_grades_1_to_4": 37924.0, "in_grades_5_to_8": 34176.0, "in_grades_9_to_12": 35119.0, "in_school": 178333.0, "in_undergrad_college": 34942.0, "speak_only_english_at_home": 265821.0, "speak_spanish_at_home": 230538.0, "speak_spanish_at_home_low_english": 102870.0}]}