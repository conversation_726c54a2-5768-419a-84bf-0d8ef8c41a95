{"table_name": "congressionaldistrict_2015_5yr", "table_fullname": "bigquery-public-data.census_bureau_acs.congressionaldistrict_2015_5yr", "column_names": ["geo_id", "nonfamily_households", "family_households", "median_year_structure_built", "rent_burden_not_computed", "rent_over_50_percent", "rent_40_to_50_percent", "rent_35_to_40_percent", "rent_30_to_35_percent", "rent_25_to_30_percent", "rent_20_to_25_percent", "rent_15_to_20_percent", "rent_10_to_15_percent", "rent_under_10_percent", "total_pop", "male_pop", "female_pop", "median_age", "white_pop", "black_pop", "asian_pop", "hispanic_pop", "amerindian_pop", "other_race_pop", "two_or_more_races_pop", "not_hispanic_pop", "commuters_by_public_transportation", "households", "median_income", "income_per_capita", "housing_units", "vacant_housing_units", "vacant_housing_units_for_rent", "vacant_housing_units_for_sale", "median_rent", "percent_income_spent_on_rent", "owner_occupied_housing_units", "million_dollar_housing_units", "mortgaged_housing_units", "families_with_young_children", "two_parent_families_with_young_children", "two_parents_in_labor_force_families_with_young_children", "two_parents_father_in_labor_force_families_with_young_children", "two_parents_mother_in_labor_force_families_with_young_children", "two_parents_not_in_labor_force_families_with_young_children", "one_parent_families_with_young_children", "father_one_parent_families_with_young_children", "father_in_labor_force_one_parent_families_with_young_children", "commute_10_14_mins", "commute_15_19_mins", "commute_20_24_mins", "commute_25_29_mins", "commute_30_34_mins", "commute_45_59_mins", "aggregate_travel_time_to_work", "income_less_10000", "income_10000_14999", "income_15000_19999", "income_20000_24999", "income_25000_29999", "income_30000_34999", "income_35000_39999", "income_40000_44999", "income_45000_49999", "income_50000_59999", "income_60000_74999", "income_75000_99999", "income_100000_124999", "income_125000_149999", "income_150000_199999", "income_200000_or_more", "renter_occupied_housing_units_paying_cash_median_gross_rent", "owner_occupied_housing_units_lower_value_quartile", "owner_occupied_housing_units_median_value", "owner_occupied_housing_units_upper_value_quartile", "married_households", "occupied_housing_units", "housing_units_renter_occupied", "dwellings_1_units_detached", "dwellings_1_units_attached", "dwellings_2_units", "dwellings_3_to_4_units", "dwellings_5_to_9_units", "dwellings_10_to_19_units", "dwellings_20_to_49_units", "dwellings_50_or_more_units", "mobile_homes", "housing_built_2005_or_later", "housing_built_2000_to_2004", "housing_built_1939_or_earlier", "male_under_5", "male_5_to_9", "male_10_to_14", "male_15_to_17", "male_18_to_19", "male_20", "male_21", "male_22_to_24", "male_25_to_29", "male_30_to_34", "male_35_to_39", "male_40_to_44", "male_45_to_49", "male_50_to_54", "male_55_to_59", "male_60_61", "male_62_64", "male_65_to_66", "male_67_to_69", "male_70_to_74", "male_75_to_79", "male_80_to_84", "male_85_and_over", "female_under_5", "female_5_to_9", "female_10_to_14", "female_15_to_17", "female_18_to_19", "female_20", "female_21", "female_22_to_24", "female_25_to_29", "female_30_to_34", "female_35_to_39", "female_40_to_44", "female_45_to_49", "female_50_to_54", "female_55_to_59", "female_60_to_61", "female_62_to_64", "female_65_to_66", "female_67_to_69", "female_70_to_74", "female_75_to_79", "female_80_to_84", "female_85_and_over", "white_including_hispanic", "black_including_hispanic", "amerindian_including_hispanic", "asian_including_hispanic", "commute_5_9_mins", "commute_35_39_mins", "commute_40_44_mins", "commute_60_89_mins", "commute_90_more_mins", "households_retirement_income", "armed_forces", "civilian_labor_force", "employed_pop", "unemployed_pop", "not_in_labor_force", "pop_16_over", "pop_in_labor_force", "asian_male_45_54", "asian_male_55_64", "black_male_45_54", "black_male_55_64", "hispanic_male_45_54", "hispanic_male_55_64", "white_male_45_54", "white_male_55_64", "bachelors_degree_2", "bachelors_degree_or_higher_25_64", "children", "children_in_single_female_hh", "commuters_by_bus", "commuters_by_car_truck_van", "commuters_by_carpool", "commuters_by_subway_or_elevated", "commuters_drove_alone", "different_house_year_ago_different_city", "different_house_year_ago_same_city", "employed_agriculture_forestry_fishing_hunting_mining", "employed_arts_entertainment_recreation_accommodation_food", "employed_construction", "employed_education_health_social", "employed_finance_insurance_real_estate", "employed_information", "employed_manufacturing", "employed_other_services_not_public_admin", "employed_public_administration", "employed_retail_trade", "employed_science_management_admin_waste", "employed_transportation_warehousing_utilities", "employed_wholesale_trade", "female_female_households", "four_more_cars", "gini_index", "graduate_professional_degree", "group_quarters", "high_school_including_ged", "households_public_asst_or_food_stamps", "in_grades_1_to_4", "in_grades_5_to_8", "in_grades_9_to_12", "in_school", "in_undergrad_college", "less_than_high_school_graduate", "male_45_64_associates_degree", "male_45_64_bachelors_degree", "male_45_64_graduate_degree", "male_45_64_less_than_9_grade", "male_45_64_grade_9_12", "male_45_64_high_school", "male_45_64_some_college", "male_45_to_64", "male_male_households", "management_business_sci_arts_employed", "no_car", "no_cars", "not_us_citizen_pop", "occupation_management_arts", "occupation_natural_resources_construction_maintenance", "occupation_production_transportation_material", "occupation_sales_office", "occupation_services", "one_car", "two_cars", "three_cars", "pop_25_64", "pop_determined_poverty_status", "population_1_year_and_over", "population_3_years_over", "poverty", "sales_office_employed", "some_college_and_associates_degree", "walked_to_work", "worked_at_home", "workers_16_and_over", "associates_degree", "bachelors_degree", "high_school_diploma", "less_one_year_college", "masters_degree", "one_year_more_college", "pop_25_years_over", "commute_35_44_mins", "commute_60_more_mins", "commute_less_10_mins", "commuters_16_over", "hispanic_any_race", "pop_5_years_over", "speak_only_english_at_home", "speak_spanish_at_home", "speak_spanish_at_home_low_english", "do_date", "pop_15_and_over", "pop_never_married", "pop_now_married", "pop_separated", "pop_widowed", "pop_divorced"], "column_types": ["STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64"], "nested_column_names": ["geo_id", "nonfamily_households", "family_households", "median_year_structure_built", "rent_burden_not_computed", "rent_over_50_percent", "rent_40_to_50_percent", "rent_35_to_40_percent", "rent_30_to_35_percent", "rent_25_to_30_percent", "rent_20_to_25_percent", "rent_15_to_20_percent", "rent_10_to_15_percent", "rent_under_10_percent", "total_pop", "male_pop", "female_pop", "median_age", "white_pop", "black_pop", "asian_pop", "hispanic_pop", "amerindian_pop", "other_race_pop", "two_or_more_races_pop", "not_hispanic_pop", "commuters_by_public_transportation", "households", "median_income", "income_per_capita", "housing_units", "vacant_housing_units", "vacant_housing_units_for_rent", "vacant_housing_units_for_sale", "median_rent", "percent_income_spent_on_rent", "owner_occupied_housing_units", "million_dollar_housing_units", "mortgaged_housing_units", "families_with_young_children", "two_parent_families_with_young_children", "two_parents_in_labor_force_families_with_young_children", "two_parents_father_in_labor_force_families_with_young_children", "two_parents_mother_in_labor_force_families_with_young_children", "two_parents_not_in_labor_force_families_with_young_children", "one_parent_families_with_young_children", "father_one_parent_families_with_young_children", "father_in_labor_force_one_parent_families_with_young_children", "commute_10_14_mins", "commute_15_19_mins", "commute_20_24_mins", "commute_25_29_mins", "commute_30_34_mins", "commute_45_59_mins", "aggregate_travel_time_to_work", "income_less_10000", "income_10000_14999", "income_15000_19999", "income_20000_24999", "income_25000_29999", "income_30000_34999", "income_35000_39999", "income_40000_44999", "income_45000_49999", "income_50000_59999", "income_60000_74999", "income_75000_99999", "income_100000_124999", "income_125000_149999", "income_150000_199999", "income_200000_or_more", "renter_occupied_housing_units_paying_cash_median_gross_rent", "owner_occupied_housing_units_lower_value_quartile", "owner_occupied_housing_units_median_value", "owner_occupied_housing_units_upper_value_quartile", "married_households", "occupied_housing_units", "housing_units_renter_occupied", "dwellings_1_units_detached", "dwellings_1_units_attached", "dwellings_2_units", "dwellings_3_to_4_units", "dwellings_5_to_9_units", "dwellings_10_to_19_units", "dwellings_20_to_49_units", "dwellings_50_or_more_units", "mobile_homes", "housing_built_2005_or_later", "housing_built_2000_to_2004", "housing_built_1939_or_earlier", "male_under_5", "male_5_to_9", "male_10_to_14", "male_15_to_17", "male_18_to_19", "male_20", "male_21", "male_22_to_24", "male_25_to_29", "male_30_to_34", "male_35_to_39", "male_40_to_44", "male_45_to_49", "male_50_to_54", "male_55_to_59", "male_60_61", "male_62_64", "male_65_to_66", "male_67_to_69", "male_70_to_74", "male_75_to_79", "male_80_to_84", "male_85_and_over", "female_under_5", "female_5_to_9", "female_10_to_14", "female_15_to_17", "female_18_to_19", "female_20", "female_21", "female_22_to_24", "female_25_to_29", "female_30_to_34", "female_35_to_39", "female_40_to_44", "female_45_to_49", "female_50_to_54", "female_55_to_59", "female_60_to_61", "female_62_to_64", "female_65_to_66", "female_67_to_69", "female_70_to_74", "female_75_to_79", "female_80_to_84", "female_85_and_over", "white_including_hispanic", "black_including_hispanic", "amerindian_including_hispanic", "asian_including_hispanic", "commute_5_9_mins", "commute_35_39_mins", "commute_40_44_mins", "commute_60_89_mins", "commute_90_more_mins", "households_retirement_income", "armed_forces", "civilian_labor_force", "employed_pop", "unemployed_pop", "not_in_labor_force", "pop_16_over", "pop_in_labor_force", "asian_male_45_54", "asian_male_55_64", "black_male_45_54", "black_male_55_64", "hispanic_male_45_54", "hispanic_male_55_64", "white_male_45_54", "white_male_55_64", "bachelors_degree_2", "bachelors_degree_or_higher_25_64", "children", "children_in_single_female_hh", "commuters_by_bus", "commuters_by_car_truck_van", "commuters_by_carpool", "commuters_by_subway_or_elevated", "commuters_drove_alone", "different_house_year_ago_different_city", "different_house_year_ago_same_city", "employed_agriculture_forestry_fishing_hunting_mining", "employed_arts_entertainment_recreation_accommodation_food", "employed_construction", "employed_education_health_social", "employed_finance_insurance_real_estate", "employed_information", "employed_manufacturing", "employed_other_services_not_public_admin", "employed_public_administration", "employed_retail_trade", "employed_science_management_admin_waste", "employed_transportation_warehousing_utilities", "employed_wholesale_trade", "female_female_households", "four_more_cars", "gini_index", "graduate_professional_degree", "group_quarters", "high_school_including_ged", "households_public_asst_or_food_stamps", "in_grades_1_to_4", "in_grades_5_to_8", "in_grades_9_to_12", "in_school", "in_undergrad_college", "less_than_high_school_graduate", "male_45_64_associates_degree", "male_45_64_bachelors_degree", "male_45_64_graduate_degree", "male_45_64_less_than_9_grade", "male_45_64_grade_9_12", "male_45_64_high_school", "male_45_64_some_college", "male_45_to_64", "male_male_households", "management_business_sci_arts_employed", "no_car", "no_cars", "not_us_citizen_pop", "occupation_management_arts", "occupation_natural_resources_construction_maintenance", "occupation_production_transportation_material", "occupation_sales_office", "occupation_services", "one_car", "two_cars", "three_cars", "pop_25_64", "pop_determined_poverty_status", "population_1_year_and_over", "population_3_years_over", "poverty", "sales_office_employed", "some_college_and_associates_degree", "walked_to_work", "worked_at_home", "workers_16_and_over", "associates_degree", "bachelors_degree", "high_school_diploma", "less_one_year_college", "masters_degree", "one_year_more_college", "pop_25_years_over", "commute_35_44_mins", "commute_60_more_mins", "commute_less_10_mins", "commuters_16_over", "hispanic_any_race", "pop_5_years_over", "speak_only_english_at_home", "speak_spanish_at_home", "speak_spanish_at_home_low_english", "do_date", "pop_15_and_over", "pop_never_married", "pop_now_married", "pop_separated", "pop_widowed", "pop_divorced"], "nested_column_types": ["STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64"], "description": ["US Congressional Districts Geoids", "Nonfamily Households. A householder living alone or with nonrelatives only. Unmarried couples households, whether opposite-sex or same-sex, with no relatives of the householder present are tabulated in nonfamily households.", "Family Households. A family consists of a householder and one or more other people living in the same household who are related to the householder by birth, marriage, or adoption. All people in a household who are related to the householder are regarded as members of his or her family. A family household may contain people not related to the householder, but those people are not included as part of the family of the householder in tabulations. Thus, the number of family households is equal to the number of families, but family households may include more members than do families. A household can contain only one family for purposes of tabulations. Not all households contain families since a household may be comprised of a group of unrelated people or of one person living alone -- these are called nonfamily households. Families are classified by type as either a \"married couple family\" or \"other family\" according to the sex of the householder and the presence of relatives. The data on family type are based ...", "Median Year Structure Built. Median Year Structure Built", "Housing units without rent burden computed. Units for which no rent is paid and units occupied by households that reported no income or a net loss comprise this category", "Housing units spending over 50% income on rent. Gross rent over 50 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 40 to 49.9% income on rent. Gross rent from 40.0 to 49.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 35 to 39.9% income on rent. Gross rent from 35.0 to 39.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 30 to 34.9% income on rent. Gross rent from 30.0 to 34.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 25 to 29.9% income on rent. Gross rent from 25.0 to 29.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 20 to 24.9% income on rent. Gross rent from 20.0 to 24.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 15 to 19.9% income on rent. Gross rent from 15.0 to 19.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 10 to 14.9% income on rent. Gross rent from 10.0 to 14.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending less than 10% on rent. Gross rent less than 10 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Total Population. The total number of all people living in a given geographic area.  This is a very useful catch-all denominator when calculating rates.", "Male Population. The number of people within each geography who are male.", "Female Population. The number of people within each geography who are female.", "Median Age. The median age of all people in a given geographic area.", "White Population. The number of people identifying as white, non-Hispanic in each geography.", "Black or African American Population. The number of people identifying as black or African American, non-Hispanic in each geography.", "Asian Population. The number of people identifying as Asian, non-Hispanic in each geography.", "Hispanic Population. The number of people identifying as Hispanic or Latino in each geography.", "American Indian and Alaska Native Population. The number of people identifying as American Indian or Alaska native in each geography.", "Other Race population. The number of people identifying as another race in each geography", "Two or more races population. The number of people identifying as two or more races in each geography", "Population not Hispanic. The number of people not identifying as Hispanic or Latino in each geography.", "Commuters by Public Transportation. The number of workers age 16 years and over within a geographic area who primarily traveled to work by public transportation.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Households. A count of the number of households in each geography. A household consists of one or more people who live in the same dwelling and also share at meals or living accommodation, and may consist of a single family or some other grouping of people. ", "Median Household Income in the past 12 Months. Within a geographic area, the median income received by every household on a regular basis before payments for personal income taxes, social security, union dues, medicare deductions, etc.  It includes income received from wages, salary, commissions, bonuses, and tips; self-employment income from own nonfarm or farm businesses, including proprietorships and partnerships; interest, dividends, net rental income, royalty income, or income from estates and trusts; Social Security or Railroad Retirement income; Supplemental Security Income (SSI); any cash public assistance or welfare payments from the state or local welfare office; retirement, survivor, or disability benefits; and any other sources of income received regularly such as Veterans' (VA) payments, unemployment and/or worker's compensation, child support, and alimony.", "Per Capita Income in the past 12 Months. Per capita income is the mean income computed for every man, woman, and child in a particular group. It is derived by dividing the total income of a particular group by the total population.", "Housing Units. A count of housing units in each geography.  A housing unit is a house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters.", "Vacant Housing Units. The count of vacant housing units in a geographic area. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Vacant Housing Units for Rent. The count of vacant housing units in a geographic area that are for rent. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Vacant Housing Units for Sale. The count of vacant housing units in a geographic area that are for sale. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Median Rent. The median contract rent within a geographic area. The contract rent is the monthly rent agreed to or contracted for, regardless of any furnishings, utilities, fees, meals, or services that may be included. For vacant units, it is the monthly rent asked for the rental unit at the time of interview.", "Percent of Household Income Spent on Rent. Within a geographic area, the median percentage of household income which was spent on gross rent.  Gross rent is the amount of the contract rent plus the estimated average monthly cost of utilities (electricity, gas, water, sewer etc.) and fuels (oil, coal, wood, etc.) if these are paid by the renter.  Household income is the sum of the income of all people 15 years and older living in the household.", "Owner-occupied Housing Units", "Owner-occupied Housing Units valued at $1,000,000 or more.. The count of owner occupied housing units in a geographic area that are valued at $1,000,000 or more.  Value is the respondent's estimate of how much the property (house and lot, mobile home and lot, or condominium unit) would sell for if it were for sale.", "Owner-occupied Housing Units with a Mortgage. The count of housing units within a geographic area that are mortagaged. \"Mortgage\" refers to all forms of debt where the property is pledged as security for repayment of the debt, including deeds of trust, trust deed, contracts to purchase, land contracts, junior mortgages, and home equity loans.", "Families with young children (under 6 years of age)", "Two-parent families with young children (under 6 years of age)", "Two-parent families, both parents in labor force with young children (under 6 years of age)", "Two-parent families, father only in labor force with young children (under 6 years of age)", "Two-parent families, mother only in labor force with young children (under 6 years of age)", "Two-parent families, neither parent in labor force with young children (under 6 years of age)", "One-parent families with young children (under 6 years of age)", "One-parent families, father, with young children (under 6 years of age)", "One-parent families, father in labor force, with young children (under 6 years of age)", "Number of workers with a commute between 10 and 14 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 10 and 14 minutes. ", "Number of workers with a commute between 15 and 19 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 15 and 19 minutes. ", "Number of workers with a commute between 20 and 24 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 20 and 24 minutes.", "Number of workers with a commute between 25 and 29 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 25 and 29 minutes. ", "Number of workers with a commute between 30 and 34 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 30 and 34 minutes. ", "Number of workers with a commute between 45 and 59 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 45 and 59 minutes. ", "Aggregate travel time to work. The total number of minutes every worker in a geographic area over the age of 16 who did not work from home spent spent commuting to work in one day", "Households with income less than $10,000. The number of households in a geographic area whose annual income was less than $10,000.", "Households with income of $10,000 to $14,999. The number of households in a geographic area whose annual income was between $10,000 and $14,999.", "Households with income of $15,000 to $19,999. The number of households in a geographic area whose annual income was between $15,000 and $19,999.", "Households with income of $20,000 to $24,999. The number of households in a geographic area whose annual income was between $20,000 and $24,999.", "Households with income of $25,000 to $29,999. The number of households in a geographic area whose annual income was between $20,000 and $24,999.", "Households with income of $30,000 to $34,999. The number of households in a geographic area whose annual income was between $30,000 and $34,999.", "Households with income of $35,000 to $39,999. The number of households in a geographic area whose annual income was between $35,000 and $39,999.", "Households with income of $40,000 to $44,999. The number of households in a geographic area whose annual income was between $40,000 and $44,999.", "Households with income of $45,000 to $49,999. The number of households in a geographic area whose annual income was between $45,000 and $49,999.", "Households with income of $50,000 to $59,999. The number of households in a geographic area whose annual income was between $50,000 and $59,999.", "Households with income of $60,000 to $74,999. The number of households in a geographic area whose annual income was between $60,000 and $74,999.", "Households with income of $75,000 to $99,999. The number of households in a geographic area whose annual income was between $75,000 and $99,999.", "Households with income of $100,000 to $124,999. The number of households in a geographic area whose annual income was between $100,000 and $124,999.", "Households with income of $125,000 to $149,999. The number of households in a geographic area whose annual income was between $125,000 and $149,999.", "Households with income of $150,000 to $199,999. The number of households in a geographic area whose annual income was between $150,000 and $1999,999.", "Households with income of $200,000 Or More. The number of households in a geographic area whose annual income was more than $200,000.", "Renter-Occupied Housing Units Paying Cash Rent Median Gross Rent", "Owner-Occupied Housing Units Lower Value Quartile", "Owner-Occupied Housing Units Median Value. The middle value (median) in a geographic area owner occupied housing units.", "Owner-Occupied Housing Units Upper Value Quartile", "Married households. People in formal marriages, as well as people in common-law marriages, are included. Does not include same-sex marriages.", "Occupied housing units. A housing unit is classified as occupied if it is the usual place of residence of the person or group of people living in it at the time of enumeration.", "Renter occupied housing units. All occupied units which are not owner occupied, whether they are rented for cash rent or occupied without payment of cash rent, are classified as renter-occupied.", "Single-family (one unit) detached dwellings. This is a 1-unit structure detached from any other house, that is, with open space on all four sides. Such structures are considered detached even if they have an adjoining shed or garage. A one-family house that contains a business is considered detached as long as the building has open space on all four sides. Mobile homes to which one or more permanent rooms have been added or built also are included.", "Single-family (one unit) attached dwellings. This is a 1-unit structure that has one or more walls extending from ground to roof separating it from adjoining structures. In row houses (sometimes called townhouses), double houses, or houses attached to nonresidential structures, each house is a separate, attached structure if the dividing or common wall goes from ground to roof.", "Two-family (two unit) dwellings", "Multifamily dwellings with three to 4 units", "Apartment buildings with 5 to 9 units", "Apartment buildings with 10 to 19 units", "Apartment buildings with 20 to 49 units", "Apartment buildings with 50 or more units", "Mobile homes. A manufactured home is defined as a movable dwelling, 8 feet or more wide and 40 feet or more long, designed to be towed on its own chassis, with transportation gear integral to the unit when it leaves the factory, and without need of a permanent foundation. These homes are built in accordance with the U.S. Department of Housing and Urban Development (HUD) building code.", "Housing units built in 2005 or later. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built in 2005 or later.", "Housing units built between 2000 and 2004. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built from 2000 to 2004.", "Housing units built before 1939. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built in 1939 or earlier.", "Male under 5 years. The male population over the age of five years within the specified area.", "Male age 5 to 9. The male population between the age of five years to nine years within the specified area.", "Male age 10 to 14. The male population between the age of ten years to fourteen years within the specified area.", "Male age 15 to 17. The male population between the age of fifteeen years to seventeen years within the specified area.", "Male age 18 and 19. The male population between the age of eighteen years to nineteen years within the specified area.", "Male age 20. The male population with an age of twenty years within the specified area.", "Male age 21. The male population with an age of twenty-one years within the specified area.", "Male age 22 to 24. The male population between the age of twenty-two years to twenty-four years within the specified area.", "Male age 25 to 29. The male population between the age of twenty-five years to twenty-nine years within the specified area.", "Male age 30 to 34. The male population between the age of thirty years to thirty-four years within the specified area.", "Male age 35 to 39. The male population between the age of thirty-five years to thirty-nine years within the specified area.", "Male age 40 to 44. The male population between the age of fourty years to fourty-four years within the specified area.", "Men age 45 to 49. The male population between the age of fourty-five years to fourty-nine years within the specified area.", "Men age 50 to 54. The male population between the age of fifty years to fifty-four years within the specified area.", "Men age 55 to 59. The male population between the age of fifty-five years to fifty-nine years within the specified area.", "Men age 60 to 61. The male population between the age of sixty years to sixty-one years within the specified area.", "Men age 62 to 64. The male population between the age of sixty-two years to sixty-four years within the specified area.", "Male age 65 to 66. The male population between the age of sixty-five years to sixty-six years within the specified area.", "Male age 67 to 69. The male population between the age of sixty-seven years to sixty-nine years within the specified area.", "Male age 70 to 74. The male population between the age of seventy years to seventy-four years within the specified area.", "Male age 75 to 79. The male population between the age of seventy-five years to seventy-nine years within the specified area.", "Male age 80 to 84. The male population between the age of eighty years to eighty-four years within the specified area.", "Male age 85 and over. The male population of the age of eighty-five years and over within the specified area.", "Female under 5 years. The female population over the age of five years within the specified area.", "Female age 5 to 9. The female population between the age of five years to nine years within the specified area.", "Female age 10 to 14. The female population between the age of ten years to fourteen years within the specified area.", "Female age 15 to 17. The female population between the age of fifteeen years to seventeen years within the specified area.", "Female age 18 and 19. The female population between the age of eighteen years to nineteen years within the specified area.", "Female age 20. The female population with an age of twenty years within the specified area.", "Female age 21. The female population with an age of twenty-one years within the specified area.", "Female age 22 to 24. The female population between the age of twenty-two years to twenty-four years within the specified area.", "Female age 25 to 29. The female population between the age of twenty-five years to twenty-nine years within the specified area.", "Female age 30 to 34. The female population between the age of thirty years to thirty-four years within the specified area.", "Female age 35 to 39. The female population between the age of thirty-five years to thirty-nine years within the specified area.", "Female age 40 to 44. The female population between the age of fourty years to fourty-four years within the specified area.", "Female age 45 to 49. The female population between the age of fourty-five years to fourty-nine years within the specified area.", "Female age 50 to 54. The female population between the age of fifty years to fifty-four years within the specified area.", "Female age 55 to 59. The female population between the age of fifty-five years to fifty-nine years within the specified area.", "Female age 60 and 61. The female population between the age of sixty years to sixty-one years within the specified area.", "Female age 62 to 64. The female population between the age of sixty-two years to sixty-four years within the specified area.", "Female age 65 to 66. The female population between the age of sixty-five years to sixty-six years within the specified area.", "Female age 67 to 69. The female population between the age of sixty-seven years to sixty-nine years within the specified area.", "Female age 70 to 74. The female population between the age of seventy years to seventy-four years within the specified area.", "Female age 75 to 79. The female population between the age of seventy-five years to seventy-nine years within the specified area.", "Female age 80 to 84. The female population between the age of eighty years to eighty-four years within the specified area.", "Female age 85 and over. The female population of the age of eighty-five years and over within the specified area.", "White including Hispanic", "Black including Hispanic", "American Indian including Hispanic", "Asian including Hispanic", "Number of workers with a commute between 5 and 9 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 5 and 9 minutes.", "Number of workers with a commute between 35 and 39 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 35 and 39 minutes. ", "Number of workers with a commute between 40 and 44 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 40 and 44 minutes. ", "Number of workers with a commute between 60 and 89 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 60 and 89 minutes .", "Number of workers with a commute of more than 90 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute more than 90 minutes.", "Households receiving retirement income", "Population in Armed Forces. The number of people in each geography who are members of the U.S. Armed Forces (people on active duty with the United States Army, Air Force, Navy, Marine Corps, or Coast Guard).", "Population in Civilian Labor Force. The number of civilians 16 years and over in each geography who can be classified as either \"employed\" or \"unemployed\" below.", "Employed Population. The number of civilians 16 years old and over in each geography who either (1) were \"at work,\" that is, those who did any work at all during the reference week as paid employees, worked in their own business or profession, worked on their own farm, or worked 15 hours or more as unpaid workers on a family farm or in a family business; or (2) were \"with a job but not at work,\" that is, those who did not work during the reference week but had jobs or businesses from which they were temporarily absent due to illness, bad weather, industrial dispute, vacation, or other personal reasons. Excluded from the employed are people whose only activity consisted of work around the house or unpaid volunteer work for religious, charitable, and similar organizations; also excluded are all institutionalized people and people on active duty in the United States Armed Forces.", "", "Population Not in Labor Force. The number of people in each geography who are 16 years old and over who are not classified as members of the labor force. This category consists mainly of students, homemakers, retired workers, seasonal workers interviewed in an off season who were not looking for work, institutionalized people, and people doing only incidental unpaid family work (less than 15 hours during the reference week).", "Population age 16 and over. The number of people in each geography who are age 16 or over.", "Population in Labor Force. The number of people in each geography who are either in the civilian labor force or are members of the U.S. Armed Forces (people on active duty with the United States Army, Air Force, Navy, Marine Corps, or Coast Guard).", "Asian Men age 45 to 54", "Asian Men age 55 to 64", "Black Men age 45 to 54", "Black Men age 55 to 64", "Hispanic Men age 45 to 54", "Hispanic Men age 55 to 64", "White Men age 45 to 54", "White Men age 55 to 64", "Population who completed a bachelor's degree. From mobility table.", "Population with Bachelors Degree or Higher, Ages 25 to 64. The number of people in each geography who are between the ages of 25 and 64 who have attained a bachelors degree or higher. ", "Children under 18 Years of Age. The number of people within each geography who are under 18 years of age.", "Children under 18 years of age in single female-led household", "Commuters by Bus. The number of workers age 16 years and over within a geographic area who primarily traveled to work by bus.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.  This is a subset of workers who commuted by public transport.", "Commuters by Car, Truck, or Van. The number of workers age 16 years and over within  a geographic area who primarily traveled to work by car, truck or  van.  This is the principal mode of travel or type of conveyance,  by distance rather than time, that the worker usually used to get  from home to work.", "Commuters by Carpool. The number of workers age 16 years and over within a geographic area who primarily traveled to work by carpool.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Commuters by Subway or Elevated. The number of workers age 16 years and over within a geographic area who primarily traveled to work by subway or elevated train.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.  This is a subset of workers who commuted by public transport.", "Commuters who drove alone. The number of workers age 16 years and over within a geographic area who primarily traveled by car driving alone. This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Lived in a different house one year ago in a different city. All people in a geographic area who lived in a different city within the year prior to the survey.", "Lived in a different house one year ago in the same city. All people in a geographic area who lived in the same city but moved to a different unit within the year prior to the survey.", "Workers employed in firms in agriculture, forestry, fishing, hunting, or mining. The Agriculture, Forestry, Fishing and Hunting sector comprises establishments primarily engaged in growing crops, raising animals, harvesting timber, and harvesting fish and other animals from a farm, ranch, or their natural habitats.", "Workers employed in firms in arts, entertainment, recreation, accommodation and food services. The Arts, Entertainment, and Recreation sector includes a wide range of establishments that operate facilities or provide services to meet varied cultural, entertainment, and recreational interests of their patrons. This sector comprises (1) establishments that are involved in producing, promoting, or participating in live performances, events, or exhibits intended for public viewing; (2) establishments that preserve and exhibit objects and sites of historical, cultural, or educational interest; and (3) establishments that operate facilities or provide services that enable patrons to participate in recreational activities or pursue amusement, hobby, and leisure-time interests.", "Workers employed in firms in construction. The Construction sector comprises establishments primarily engaged in the construction of buildings or engineering projects (e.g., highways and utility systems). Construction work done may include new work, additions, alterations, or maintenance and repairs.", "Workers employed in firms in educational services, health care, and social assistance. Outpatient health services, other than hospital care, including: public health administration; research and education; categorical health programs; treatment and immunization clinics; nursing; environmental health activities such as air and water pollution control; ambulance service if provided separately from fire protection services, and other general public health activities such as mosquito abatement. School health services provided by health agencies (rather than school agencies) are included here. Sewage treatment operations are classified under Sewerage.", "Workers employed in firms in finance, insurance, real estate and rental and leasing. The Real Estate and Rental and Leasing sector comprises establishments primarily engaged in renting, leasing, or otherwise allowing the use of tangible or intangible assets, and establishments providing related services. The major portion of this sector comprises establishments that rent, lease, or otherwise allow the use of their own assets by others. The assets may be tangible, as is the case of real estate and equipment, or intangible, as is the case with patents and trademarks.", "Workers employed in firms in information. The Information sector comprises establishments engaged in the following processes: (a) producing and distributing information and cultural products, (b) providing the means to transmit or distribute these products as well as data or communications, and (c) processing data. Included are the publishing industries, the motion picture and sound recording industries; the broadcasting industries, the telecommunications industries; Web search portals, data processing industries, and the information services industries.", "Workers employed in firms in manufacturing. The Manufacturing sector comprises establishments engaged in the mechanical, physical, or chemical transformation of materials, substances, or components into new products.", "Workers employed in firms in other services except public administration. The Other Services (Except Public Administration) sector comprises establishments engaged in providing services not specifically provided for elsewhere in the classification system. Establishments in this sector are primarily engaged in activities such as equipment and machinery repairing, promoting or administering religious activities, grantmaking, advocacy, and providing drycleaning and laundry services, personal care services, death care services, pet care services, photofinishing services, temporary parking services, and dating services. Private households that engage in employing workers on or about the premises in activities primarily concerned with the operation of the household are included in this sector.", "Workers employed in firms in public administration. The Public Administration sector consists of establishments of federal, state, and local government agencies that administer, oversee, and manage public programs and have executive, legislative, or judicial authority over other institutions within a given area. These agencies also set policy, create laws, adjudicate civil and criminal legal cases, provide for public safety and for national defense. In general, government establishments in the public administration sector oversee governmental programs and activities that are not performed by private establishments.", "Workers employed in firms in retail trade. The Retail Trade sector comprises establishments engaged in retailing merchandise, generally without transformation, and rendering services incidental to the sale of merchandise. The retailing process is the final step in the distribution of merchandise; retailers are, therefore, organized to sell merchandise in small quantities to the general public.", "Workers employed in firms in professional scientific, management, administrative and waste management services. The Administrative and Support and Waste Management and Remediation Services sector comprises establishments performing routine support activities for the day-to-day operations of other organizations. The establishments in this sector specialize in one or more of these support activities and provide these services to clients in a variety of industries and, in some cases, to households. Activities performed include office administration, hiring and placing of personnel, document preparation and similar clerical services, solicitation, collection, security and surveillance services, cleaning, and waste disposal services.", "Workers employed in firms in transportation, warehousing, and utilities. The Transportation and Warehousing sector includes industries providing transportation of passengers and cargo, warehousing and storage for goods, scenic and sightseeing transportation, and support activities related to modes of transportation. The modes of transportation are air, rail, water, road, and pipeline.", "Workers employed in firms in wholesale trade. The Wholesale Trade sector comprises establishments engaged in wholesaling merchandise, generally without transformation, and rendering services incidental to the sale of merchandise. The wholesaling process is an intermediate step in the distribution of merchandise. Wholesalers are organized to sell or arrange the purchase or sale of (a) goods for resale (i.e., goods sold to other wholesalers or retailers), (b) capital or durable nonconsumer goods, and (c) raw and intermediate materials and supplies used in production.", "Households with two female partners. An unmarried partner is a person age 15 years and over, who is not related to the householder, who shares living quarters, and who has an intimate relationship with the householder.", "Four car households. The number of households with four or more passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Gini Index. The Gini index, or index of income concentration, is a statistical measure of income inequality ranging from 0 to 1. A measure of 1 indicates perfect inequality, i.e., one household having all the income and rest having none. A measure of 0 indicates perfect equality, i.e., all households having an equal share of income.", "Population who completed a graduate or professional degree", "Population living in group quarters", "Population with high school degree, including GED. The number of people in a geographic area over the age of 25 who attained a high school degree or GED.", "Households on cash public assistance or receiving food stamps (SNAP)", "Students Enrolled in Grades 1 to 4. The total number of people in each geography currently enrolled in grades 1 through 4 inclusive.  This corresponds roughly to elementary school.", "Students Enrolled in Grades 5 to 8. The total number of people in each geography currently enrolled in grades 5 through 8 inclusive.  This corresponds roughly to middle school.", "Students Enrolled in Grades 9 to 12. The total number of people in each geography currently enrolled in grades 9 through 12 inclusive.  This corresponds roughly to high school.", "Students Enrolled in School. The total number of people in each geography currently enrolled at any level of school, from nursery or pre-school to advanced post-graduate education.  Only includes those over the age of 3.", "Students Enrolled as Undergraduate in College. The number of people in a geographic area who are enrolled in college at the undergraduate level. Enrollment refers to being registered or listed as a student in an educational program leading to a college degree. This may be a public school or college, a private school or college.", "Less than high school graduate. The number of people in a geographic area over the age of 25 who have not completed high school or any other advanced degree.", "Men age 45 to 64 who obtained an associate's degree", "Men age 45 to 64 who obtained a bachelor's degree", "Men age 45 to 64 who obtained a graduate or professional degree", "Men age 45 to 64 who attained less than a 9th grade education", "Men age 45 to 64 who attained between 9th and 12th grade, no diploma", "Men age 45 to 64 who completed high school or obtained GED", "Men age 45 to 64 who completed some college, no degree", "Men age 45 to 64 (\"middle aged\"). The male population between the age of fourty-five years to sixty-four years within the specified area.", "Households with two male partners. An unmarried partner is a person age 15 years and over, who is not related to the householder, who shares living quarters, and who has an intimate relationship with the householder.", "Civilian Employed Population in Management, Business, Science, and Arts Occupations. The number of employed civilians 16 years old and over in each geography in the Management, Business, Science, and Arts occupations. Occupation codes are based on the Standard Occupational Classification (SOC), published by the Executive Office of the President, Office of Management and Budget.", "Workers age 16 and over with no vehicle. All people in a geographic area over the age of 16 who do not own a car.", "Car-free households. The number of households without passenger cars, vans, and pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Not a U.S. Citizen Population. The number of people within each geography who indicated that they are not U.S. citizens.", "Workers employed in management business science and arts occupations", "Workers employed in natural resources, construction, and maintenance occupations", "Workers employed in production, transportation, and material moving", "Workers employed in sales and office occupations", "Workers employed in service occupations", "One car households. The number of households with one passenger car, van, , pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Two car households. The number of households with two passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Three car households. The number of households with one passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Population age 25 to 64. The number of people in each geography who are between the ages of 25 and 64. ", "Population for Whom Poverty Status Determined. The number of people in each geography who could be identified as either living in poverty or not.  This should be used as the denominator when calculating poverty rates, as it excludes people for whom it was not possible to determine poverty.", "Population 1 year and over. All people, male and female, child and adult, living in a given geographic area that are 1 year and older.", "Population 3 Years and Over. The total number of people in each geography age 3 years and over.  This denominator is mostly used to calculate rates of school enrollment.", "Income In The Past 12 Months Below Poverty Level. The number of people in a geographic area who are part of a family (which could be just them as an individual) determined to be \"in poverty\" following the Office of Management and Budget's Directive 14. (https://www.census.gov/hhes/povmeas/methodology/ombdir14.html)", "Civilian Employed Population in Sales and Office Occupations. The number of employed civilians 16 years old and over in each geography in the Sales and Office occupations. Occupation codes are based on the Standard Occupational Classification (SOC), published by the Executive Office of the President, Office of Management and Budget.", "Population who completed some college or obtained associate's degree. The number of people in a geographic area over the age of 25 who obtained an associate's degree, and did not complete a more advanced degree.", "Walked to Work. The number of workers age 16 years and over within a geographic area who primarily walked to work.  This would mean that of any way of getting to work, they travelled the most distance walking.", "Worked at Home. The count within a geographical area of workers over the age of 16 who worked at home.", "Workers over the Age of 16. The number of people in each geography who work. Workers include those employed at private for-profit companies, the self-employed, government workers and non-profit employees.", "Population Completed Associate's Degree. The number of people in a geographic area over the age of 25 who obtained a associate's degree, and did not complete a more advanced degree.", "Population Completed Bachelor's Degree. The number of people in a geographic area over the age of 25 who obtained a bachelor's degree, and did not complete a more advanced degree.", "Population Completed High School. The number of people in a geographic area over the age of 25 who completed high school, and did not complete a more advanced degree.", "Population completed less than one year of college, no degree. The number of people in a geographic area over the age of 25 who attended college for less than one year and no further.", "Population Completed Master's Degree. The number of people in a geographic area over the age of 25 who obtained a master's degree, but did not complete a more advanced degree.", "Population completed more than one year of college, no degree. The number of people in a geographic area over the age of 25 who attended college for more than one year but did not obtain a degree", "Population 25 Years and Over. The number of people in a geographic area who are over the age of 25.  This is used mostly as a denominator of educational attainment.", "Number of workers with a commute between 35 and 44 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 35 and 44 minutes. ", "Number of workers with a commute of over 60 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in over 60 minutes.", "Number of workers with less than 10 minute commute. The number of workers in a geographic area over the age of 16 who do not work from home and commute in less than 10 minutes.", "Workers age 16 and over who do not work from home. The number of workers in a geographic area over the age of 16 who do not work from home", "Hispanic of any race", "Population 5 Years and Over. The number of people in a geographic area who are over the age of 5.  This is primarily used as a denominator of measures of language spoken at home.", "Speaks only English at Home. The number of people in a geographic area over age 5 who speak only English at home.", "Speaks Spanish at Home. The number of people in a geographic area over age 5 who speak Spanish at home, possibly in addition to other languages.", "Speaks Spanish at Home, speaks English less than \"very well\"", null, "Population 15 Years and Over. The number of people in a geographic area who are over the age of 15.  This is used mostly as a denominator of marital status.", "Never Married. The number of people in a geographic area who have never been married.", "Currently married. The number of people in a geographic area who are currently married", "Married but separated. The number of people in a geographic area who are married but separated", "Widowed. The number of people in a geographic area who are widowed", "Divorced. The number of people in a geographic area who are divorced"], "sample_rows": [{"geo_id": "5302", "nonfamily_households": 99145.0, "family_households": 171418.0, "median_year_structure_built": 1984.0, "rent_burden_not_computed": 5245.0, "rent_over_50_percent": 26520.0, "rent_40_to_50_percent": 9993.0, "rent_35_to_40_percent": 7467.0, "rent_30_to_35_percent": 10155.0, "rent_25_to_30_percent": 13556.0, "rent_20_to_25_percent": 13734.0, "rent_15_to_20_percent": 12171.0, "rent_10_to_15_percent": 6770.0, "rent_under_10_percent": 2668.0, "total_pop": 696458.0, "male_pop": 345768.0, "female_pop": 350690.0, "median_age": 37.6, "white_pop": 506836.0, "black_pop": 17959.0, "asian_pop": 56020.0, "hispanic_pop": 71875.0, "amerindian_pop": 8137.0, "other_race_pop": 671.0, "two_or_more_races_pop": 31192.0, "not_hispanic_pop": 624583.0, "commuters_by_public_transportation": 17001.0, "households": 270563.0, "median_income": 59665.0, "income_per_capita": 29873.0, "housing_units": 302129.0, "vacant_housing_units": 31566.0, "vacant_housing_units_for_rent": 4741.0, "vacant_housing_units_for_sale": 2526.0, "median_rent": 923.0, "percent_income_spent_on_rent": 31.3, "owner_occupied_housing_units": 162284.0, "million_dollar_housing_units": 1608.0, "mortgaged_housing_units": 114800.0, "families_with_young_children": 48721.0, "two_parent_families_with_young_children": 33608.0, "two_parents_in_labor_force_families_with_young_children": 18223.0, "two_parents_father_in_labor_force_families_with_young_children": 14091.0, "two_parents_mother_in_labor_force_families_with_young_children": 853.0, "two_parents_not_in_labor_force_families_with_young_children": 441.0, "one_parent_families_with_young_children": 15113.0, "father_one_parent_families_with_young_children": 3749.0, "father_in_labor_force_one_parent_families_with_young_children": 3530.0, "commute_10_14_mins": 48271.0, "commute_15_19_mins": 51677.0, "commute_20_24_mins": 41854.0, "commute_25_29_mins": 17935.0, "commute_30_34_mins": 37334.0, "commute_45_59_mins": 23961.0, "aggregate_travel_time_to_work": 8395975.0, "income_less_10000": 16998.0, "income_10000_14999": 11469.0, "income_15000_19999": 11243.0, "income_20000_24999": 13189.0, "income_25000_29999": 11466.0, "income_30000_34999": 13019.0, "income_35000_39999": 12307.0, "income_40000_44999": 12763.0, "income_45000_49999": 10997.0, "income_50000_59999": 22428.0, "income_60000_74999": 29595.0, "income_75000_99999": 39381.0, "income_100000_124999": 25869.0, "income_125000_149999": 15447.0, "income_150000_199999": 14929.0, "income_200000_or_more": 9463.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 1063.0, "owner_occupied_housing_units_lower_value_quartile": 201100.0, "owner_occupied_housing_units_median_value": 277100.0, "owner_occupied_housing_units_upper_value_quartile": 382500.0, "married_households": 129953.0, "occupied_housing_units": 270563.0, "housing_units_renter_occupied": 108279.0, "dwellings_1_units_detached": 190837.0, "dwellings_1_units_attached": 12313.0, "dwellings_2_units": 9164.0, "dwellings_3_to_4_units": 12190.0, "dwellings_5_to_9_units": 15723.0, "dwellings_10_to_19_units": 18949.0, "dwellings_20_to_49_units": 13665.0, "dwellings_50_or_more_units": 12758.0, "mobile_homes": 16151.0, "housing_built_2005_or_later": 382.0, "housing_built_2000_to_2004": 5544.0, "housing_built_1939_or_earlier": 9656.0, "male_under_5": 21345.0, "male_5_to_9": 20661.0, "male_10_to_14": 20997.0, "male_15_to_17": 12238.0, "male_18_to_19": 9195.0, "male_20": 5753.0, "male_21": 6222.0, "male_22_to_24": 17465.0, "male_25_to_29": 27268.0, "male_30_to_34": 24780.0, "male_35_to_39": 21604.0, "male_40_to_44": 22468.0, "male_45_to_49": 22868.0, "male_50_to_54": 24769.0, "male_55_to_59": 23153.0, "male_60_61": 9029.0, "male_62_64": 11940.0, "male_65_to_66": 7212.0, "male_67_to_69": 9272.0, "male_70_to_74": 10600.0, "male_75_to_79": 7314.0, "male_80_to_84": 5016.0, "male_85_and_over": 4599.0, "female_under_5": 20441.0, "female_5_to_9": 19929.0, "female_10_to_14": 20816.0, "female_15_to_17": 12457.0, "female_18_to_19": 8996.0, "female_20": 5000.0, "female_21": 7111.0, "female_22_to_24": 15704.0, "female_25_to_29": 24534.0, "female_30_to_34": 23776.0, "female_35_to_39": 21033.0, "female_40_to_44": 22087.0, "female_45_to_49": 22584.0, "female_50_to_54": 24534.0, "female_55_to_59": 24365.0, "female_60_to_61": 9215.0, "female_62_to_64": 13414.0, "female_65_to_66": 7919.0, "female_67_to_69": 9862.0, "female_70_to_74": 12218.0, "female_75_to_79": 8396.0, "female_80_to_84": 7205.0, "female_85_and_over": 9094.0, "white_including_hispanic": 553271.0, "black_including_hispanic": 19061.0, "amerindian_including_hispanic": 9128.0, "asian_including_hispanic": 56459.0, "commute_5_9_mins": 31220.0, "commute_35_39_mins": 8714.0, "commute_40_44_mins": 11727.0, "commute_60_89_mins": 22474.0, "commute_90_more_mins": 10576.0, "households_retirement_income": 50170.0, "armed_forces": 8492.0, "civilian_labor_force": 361494.0, "employed_pop": 331634.0, "unemployed_pop": 29860.0, "not_in_labor_force": 194191.0, "pop_16_over": 564177.0, "pop_in_labor_force": 369986.0, "asian_male_45_54": 3621.0, "asian_male_55_64": 3020.0, "black_male_45_54": 1717.0, "black_male_55_64": 940.0, "hispanic_male_45_54": 3116.0, "hispanic_male_55_64": 1694.0, "white_male_45_54": 37318.0, "white_male_55_64": 37194.0, "bachelors_degree_2": 94598.0, "bachelors_degree_or_higher_25_64": 109047.0, "children": 148884.0, "children_in_single_female_hh": 33294.0, "commuters_by_bus": 16092.0, "commuters_by_car_truck_van": 280093.0, "commuters_by_carpool": 35365.0, "commuters_by_subway_or_elevated": 48.0, "commuters_drove_alone": 244728.0, "different_house_year_ago_different_city": 92151.0, "different_house_year_ago_same_city": 33044.0, "employed_agriculture_forestry_fishing_hunting_mining": 3851.0, "employed_arts_entertainment_recreation_accommodation_food": 33491.0, "employed_construction": 21574.0, "employed_education_health_social": 69901.0, "employed_finance_insurance_real_estate": 18026.0, "employed_information": 6530.0, "employed_manufacturing": 49079.0, "employed_other_services_not_public_admin": 16340.0, "employed_public_administration": 14811.0, "employed_retail_trade": 43050.0, "employed_science_management_admin_waste": 33025.0, "employed_transportation_warehousing_utilities": 13891.0, "employed_wholesale_trade": 8065.0, "female_female_households": 745.0, "four_more_cars": 21191.0, "gini_index": 0.4264, "graduate_professional_degree": 44900.0, "group_quarters": 13307.0, "high_school_including_ged": 113544.0, "households_public_asst_or_food_stamps": 40328.0, "in_grades_1_to_4": 30631.0, "in_grades_5_to_8": 33675.0, "in_grades_9_to_12": 33026.0, "in_school": 167378.0, "in_undergrad_college": 45617.0, "less_than_high_school_graduate": 39586.0, "male_45_64_associates_degree": 9446.0, "male_45_64_bachelors_degree": 18253.0, "male_45_64_graduate_degree": 10188.0, "male_45_64_less_than_9_grade": 2220.0, "male_45_64_grade_9_12": 4323.0, "male_45_64_high_school": 22503.0, "male_45_64_some_college": 24826.0, "male_45_to_64": 91759.0, "male_male_households": 430.0, "management_business_sci_arts_employed": 114940.0, "no_car": 7756.0, "no_cars": 17674.0, "not_us_citizen_pop": 46510.0, "occupation_management_arts": 114940.0, "occupation_natural_resources_construction_maintenance": 34915.0, "occupation_production_transportation_material": 39281.0, "occupation_sales_office": 78626.0, "occupation_services": 63872.0, "one_car": 82567.0, "two_cars": 104224.0, "three_cars": 44907.0, "pop_25_64": 373421.0, "pop_determined_poverty_status": 682920.0, "population_1_year_and_over": 689188.0, "population_3_years_over": 672261.0, "poverty": 89480.0, "sales_office_employed": 78626.0, "some_college_and_associates_degree": 179500.0, "walked_to_work": 10390.0, "worked_at_home": 18003.0, "workers_16_and_over": 332900.0, "associates_degree": 50213.0, "bachelors_degree": 94598.0, "high_school_diploma": 93982.0, "less_one_year_college": 41063.0, "masters_degree": 32416.0, "one_year_more_college": 88224.0, "pop_25_years_over": 472128.0, "commute_35_44_mins": 20441.0, "commute_60_more_mins": 33050.0, "commute_less_10_mins": 40374.0, "commuters_16_over": 314897.0, "hispanic_any_race": 71875.0, "pop_5_years_over": 654672.0, "speak_only_english_at_home": 538612.0, "speak_spanish_at_home": 41691.0, "speak_spanish_at_home_low_english": 15915.0, "do_date": "20112015", "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN}, {"geo_id": "4834", "nonfamily_households": 47426.0, "family_households": 163849.0, "median_year_structure_built": 1984.0, "rent_burden_not_computed": 10125.0, "rent_over_50_percent": 15237.0, "rent_40_to_50_percent": 6336.0, "rent_35_to_40_percent": 3870.0, "rent_30_to_35_percent": 4823.0, "rent_25_to_30_percent": 6294.0, "rent_20_to_25_percent": 6911.0, "rent_15_to_20_percent": 6116.0, "rent_10_to_15_percent": 5576.0, "rent_under_10_percent": 2842.0, "total_pop": 717149.0, "male_pop": 354400.0, "female_pop": 362749.0, "median_age": 32.0, "white_pop": 103032.0, "black_pop": 9359.0, "asian_pop": 4289.0, "hispanic_pop": 597895.0, "amerindian_pop": 541.0, "other_race_pop": 280.0, "two_or_more_races_pop": 1673.0, "not_hispanic_pop": 119254.0, "commuters_by_public_transportation": 1104.0, "households": 211275.0, "median_income": 35196.0, "income_per_capita": 16117.0, "housing_units": 255036.0, "vacant_housing_units": 43761.0, "vacant_housing_units_for_rent": 6433.0, "vacant_housing_units_for_sale": 1996.0, "median_rent": 497.0, "percent_income_spent_on_rent": 31.3, "owner_occupied_housing_units": 143145.0, "million_dollar_housing_units": 302.0, "mortgaged_housing_units": 57145.0, "families_with_young_children": 68987.0, "two_parent_families_with_young_children": 37848.0, "two_parents_in_labor_force_families_with_young_children": 15910.0, "two_parents_father_in_labor_force_families_with_young_children": 18630.0, "two_parents_mother_in_labor_force_families_with_young_children": 1465.0, "two_parents_not_in_labor_force_families_with_young_children": 1843.0, "one_parent_families_with_young_children": 31139.0, "father_one_parent_families_with_young_children": 5023.0, "father_in_labor_force_one_parent_families_with_young_children": 4075.0, "commute_10_14_mins": 41399.0, "commute_15_19_mins": 50663.0, "commute_20_24_mins": 36617.0, "commute_25_29_mins": 12769.0, "commute_30_34_mins": 33144.0, "commute_45_59_mins": 9700.0, "aggregate_travel_time_to_work": 5166010.0, "income_less_10000": 29228.0, "income_10000_14999": 17967.0, "income_15000_19999": 16444.0, "income_20000_24999": 15528.0, "income_25000_29999": 13283.0, "income_30000_34999": 12709.0, "income_35000_39999": 10800.0, "income_40000_44999": 9157.0, "income_45000_49999": 8254.0, "income_50000_59999": 15019.0, "income_60000_74999": 18519.0, "income_75000_99999": 18098.0, "income_100000_124999": 10936.0, "income_125000_149999": 6228.0, "income_150000_199999": 4872.0, "income_200000_or_more": 4233.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 654.0, "owner_occupied_housing_units_lower_value_quartile": 43600.0, "owner_occupied_housing_units_median_value": 76000.0, "owner_occupied_housing_units_upper_value_quartile": 122600.0, "married_households": 110190.0, "occupied_housing_units": 211275.0, "housing_units_renter_occupied": 68130.0, "dwellings_1_units_detached": 170029.0, "dwellings_1_units_attached": 4641.0, "dwellings_2_units": 8690.0, "dwellings_3_to_4_units": 11240.0, "dwellings_5_to_9_units": 9133.0, "dwellings_10_to_19_units": 5631.0, "dwellings_20_to_49_units": 4147.0, "dwellings_50_or_more_units": 5524.0, "mobile_homes": 34955.0, "housing_built_2005_or_later": 578.0, "housing_built_2000_to_2004": 5781.0, "housing_built_1939_or_earlier": 10354.0, "male_under_5": 30614.0, "male_5_to_9": 31106.0, "male_10_to_14": 30765.0, "male_15_to_17": 17870.0, "male_18_to_19": 12281.0, "male_20": 6341.0, "male_21": 5820.0, "male_22_to_24": 16393.0, "male_25_to_29": 23660.0, "male_30_to_34": 22765.0, "male_35_to_39": 20858.0, "male_40_to_44": 21675.0, "male_45_to_49": 20120.0, "male_50_to_54": 19933.0, "male_55_to_59": 17916.0, "male_60_61": 6596.0, "male_62_64": 9194.0, "male_65_to_66": 6802.0, "male_67_to_69": 7330.0, "male_70_to_74": 9027.0, "male_75_to_79": 7976.0, "male_80_to_84": 5385.0, "male_85_and_over": 3973.0, "female_under_5": 29289.0, "female_5_to_9": 29903.0, "female_10_to_14": 30567.0, "female_15_to_17": 16927.0, "female_18_to_19": 10895.0, "female_20": 5904.0, "female_21": 5276.0, "female_22_to_24": 14240.0, "female_25_to_29": 22197.0, "female_30_to_34": 22584.0, "female_35_to_39": 22600.0, "female_40_to_44": 22462.0, "female_45_to_49": 20730.0, "female_50_to_54": 20691.0, "female_55_to_59": 20171.0, "female_60_to_61": 7252.0, "female_62_to_64": 10134.0, "female_65_to_66": 6773.0, "female_67_to_69": 9140.0, "female_70_to_74": 11239.0, "female_75_to_79": 9450.0, "female_80_to_84": 7804.0, "female_85_and_over": 6521.0, "white_including_hispanic": 651290.0, "black_including_hispanic": 10724.0, "amerindian_including_hispanic": 1718.0, "asian_including_hispanic": 4375.0, "commute_5_9_mins": 31391.0, "commute_35_39_mins": 4497.0, "commute_40_44_mins": 4829.0, "commute_60_89_mins": 7439.0, "commute_90_more_mins": 3667.0, "households_retirement_income": 28264.0, "armed_forces": 630.0, "civilian_labor_force": 283448.0, "employed_pop": 256316.0, "unemployed_pop": 27132.0, "not_in_labor_force": 238493.0, "pop_16_over": 522571.0, "pop_in_labor_force": 284078.0, "asian_male_45_54": 611.0, "asian_male_55_64": 211.0, "black_male_45_54": 1382.0, "black_male_55_64": 679.0, "hispanic_male_45_54": 31333.0, "hispanic_male_55_64": 25095.0, "white_male_45_54": 6721.0, "white_male_55_64": 7682.0, "bachelors_degree_2": 43969.0, "bachelors_degree_or_higher_25_64": 51246.0, "children": 217041.0, "children_in_single_female_hh": 70498.0, "commuters_by_bus": 1055.0, "commuters_by_car_truck_van": 235502.0, "commuters_by_carpool": 27962.0, "commuters_by_subway_or_elevated": 9.0, "commuters_drove_alone": 207540.0, "different_house_year_ago_different_city": 42994.0, "different_house_year_ago_same_city": 32001.0, "employed_agriculture_forestry_fishing_hunting_mining": 14918.0, "employed_arts_entertainment_recreation_accommodation_food": 20897.0, "employed_construction": 18035.0, "employed_education_health_social": 75909.0, "employed_finance_insurance_real_estate": 10489.0, "employed_information": 3499.0, "employed_manufacturing": 14194.0, "employed_other_services_not_public_admin": 14732.0, "employed_public_administration": 14339.0, "employed_retail_trade": 31424.0, "employed_science_management_admin_waste": 18840.0, "employed_transportation_warehousing_utilities": 12562.0, "employed_wholesale_trade": 6478.0, "female_female_households": 331.0, "four_more_cars": 11969.0, "gini_index": 0.4953, "graduate_professional_degree": 18409.0, "group_quarters": 19317.0, "high_school_including_ged": 112449.0, "households_public_asst_or_food_stamps": 56367.0, "in_grades_1_to_4": 50425.0, "in_grades_5_to_8": 48966.0, "in_grades_9_to_12": 47470.0, "in_school": 210978.0, "in_undergrad_college": 33268.0, "less_than_high_school_graduate": 145436.0, "male_45_64_associates_degree": 3905.0, "male_45_64_bachelors_degree": 6878.0, "male_45_64_graduate_degree": 3790.0, "male_45_64_less_than_9_grade": 15407.0, "male_45_64_grade_9_12": 11325.0, "male_45_64_high_school": 19661.0, "male_45_64_some_college": 12793.0, "male_45_to_64": 73759.0, "male_male_households": 128.0, "management_business_sci_arts_employed": 68378.0, "no_car": 6503.0, "no_cars": 16569.0, "not_us_citizen_pop": 101054.0, "occupation_management_arts": 68378.0, "occupation_natural_resources_construction_maintenance": 33298.0, "occupation_production_transportation_material": 31159.0, "occupation_sales_office": 60925.0, "occupation_services": 62556.0, "one_car": 78898.0, "two_cars": 74892.0, "three_cars": 28947.0, "pop_25_64": 331538.0, "pop_determined_poverty_status": 696559.0, "population_1_year_and_over": 706176.0, "population_3_years_over": 682835.0, "poverty": 219155.0, "sales_office_employed": 60925.0, "some_college_and_associates_degree": 102695.0, "walked_to_work": 4858.0, "worked_at_home": 6485.0, "workers_16_and_over": 252459.0, "associates_degree": 23464.0, "bachelors_degree": 43969.0, "high_school_diploma": 88820.0, "less_one_year_college": 20114.0, "masters_degree": 13562.0, "one_year_more_college": 59117.0, "pop_25_years_over": 422958.0, "commute_35_44_mins": 9326.0, "commute_60_more_mins": 11106.0, "commute_less_10_mins": 41250.0, "commuters_16_over": 245974.0, "hispanic_any_race": 597895.0, "pop_5_years_over": 657246.0, "speak_only_english_at_home": 213306.0, "speak_spanish_at_home": 437910.0, "speak_spanish_at_home_low_english": 161170.0, "do_date": "20112015", "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN}, {"geo_id": "0103", "nonfamily_households": 88218.0, "family_households": 181252.0, "median_year_structure_built": 1984.0, "rent_burden_not_computed": 11219.0, "rent_over_50_percent": 20361.0, "rent_40_to_50_percent": 6696.0, "rent_35_to_40_percent": 5163.0, "rent_30_to_35_percent": 6267.0, "rent_25_to_30_percent": 7798.0, "rent_20_to_25_percent": 8670.0, "rent_15_to_20_percent": 8205.0, "rent_10_to_15_percent": 7372.0, "rent_under_10_percent": 3020.0, "total_pop": 697296.0, "male_pop": 340235.0, "female_pop": 357061.0, "median_age": 38.0, "white_pop": 478639.0, "black_pop": 175297.0, "asian_pop": 9830.0, "hispanic_pop": 20345.0, "amerindian_pop": 1885.0, "other_race_pop": 478.0, "two_or_more_races_pop": 10575.0, "not_hispanic_pop": 676951.0, "commuters_by_public_transportation": 1308.0, "households": 269470.0, "median_income": 41695.0, "income_per_capita": 22643.0, "housing_units": 321161.0, "vacant_housing_units": 51691.0, "vacant_housing_units_for_rent": 5834.0, "vacant_housing_units_for_sale": 5535.0, "median_rent": 484.0, "percent_income_spent_on_rent": 31.4, "owner_occupied_housing_units": 184699.0, "million_dollar_housing_units": 505.0, "mortgaged_housing_units": 104058.0, "families_with_young_children": 47384.0, "two_parent_families_with_young_children": 27017.0, "two_parents_in_labor_force_families_with_young_children": 15826.0, "two_parents_father_in_labor_force_families_with_young_children": 9594.0, "two_parents_mother_in_labor_force_families_with_young_children": 1053.0, "two_parents_not_in_labor_force_families_with_young_children": 544.0, "one_parent_families_with_young_children": 20367.0, "father_one_parent_families_with_young_children": 3335.0, "father_in_labor_force_one_parent_families_with_young_children": 2997.0, "commute_10_14_mins": 40820.0, "commute_15_19_mins": 47670.0, "commute_20_24_mins": 40175.0, "commute_25_29_mins": 17637.0, "commute_30_34_mins": 40213.0, "commute_45_59_mins": 21734.0, "aggregate_travel_time_to_work": 6846625.0, "income_less_10000": 29278.0, "income_10000_14999": 20075.0, "income_15000_19999": 17540.0, "income_20000_24999": 17665.0, "income_25000_29999": 16332.0, "income_30000_34999": 15617.0, "income_35000_39999": 13009.0, "income_40000_44999": 13323.0, "income_45000_49999": 11507.0, "income_50000_59999": 22003.0, "income_60000_74999": 25121.0, "income_75000_99999": 29216.0, "income_100000_124999": 16927.0, "income_125000_149999": 8927.0, "income_150000_199999": 7546.0, "income_200000_or_more": 5384.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 691.0, "owner_occupied_housing_units_lower_value_quartile": 64000.0, "owner_occupied_housing_units_median_value": 118100.0, "owner_occupied_housing_units_upper_value_quartile": 195800.0, "married_households": 128476.0, "occupied_housing_units": 269470.0, "housing_units_renter_occupied": 84771.0, "dwellings_1_units_detached": 211795.0, "dwellings_1_units_attached": 3679.0, "dwellings_2_units": 9322.0, "dwellings_3_to_4_units": 8386.0, "dwellings_5_to_9_units": 11298.0, "dwellings_10_to_19_units": 8053.0, "dwellings_20_to_49_units": 4229.0, "dwellings_50_or_more_units": 3075.0, "mobile_homes": 60979.0, "housing_built_2005_or_later": 375.0, "housing_built_2000_to_2004": 9608.0, "housing_built_1939_or_earlier": 14204.0, "male_under_5": 21115.0, "male_5_to_9": 21897.0, "male_10_to_14": 23251.0, "male_15_to_17": 13875.0, "male_18_to_19": 10736.0, "male_20": 6915.0, "male_21": 7083.0, "male_22_to_24": 15445.0, "male_25_to_29": 22182.0, "male_30_to_34": 21265.0, "male_35_to_39": 19605.0, "male_40_to_44": 23885.0, "male_45_to_49": 22645.0, "male_50_to_54": 23408.0, "male_55_to_59": 22293.0, "male_60_61": 8666.0, "male_62_64": 11857.0, "male_65_to_66": 7172.0, "male_67_to_69": 9490.0, "male_70_to_74": 11409.0, "male_75_to_79": 7667.0, "male_80_to_84": 5056.0, "male_85_and_over": 3318.0, "female_under_5": 20353.0, "female_5_to_9": 21217.0, "female_10_to_14": 21588.0, "female_15_to_17": 13272.0, "female_18_to_19": 10593.0, "female_20": 6184.0, "female_21": 6096.0, "female_22_to_24": 16842.0, "female_25_to_29": 22763.0, "female_30_to_34": 21845.0, "female_35_to_39": 21451.0, "female_40_to_44": 23251.0, "female_45_to_49": 23052.0, "female_50_to_54": 25147.0, "female_55_to_59": 23766.0, "female_60_to_61": 9784.0, "female_62_to_64": 12552.0, "female_65_to_66": 8108.0, "female_67_to_69": 10418.0, "female_70_to_74": 13624.0, "female_75_to_79": 10644.0, "female_80_to_84": 7380.0, "female_85_and_over": 7131.0, "white_including_hispanic": 490025.0, "black_including_hispanic": 176581.0, "amerindian_including_hispanic": 2007.0, "asian_including_hispanic": 9986.0, "commute_5_9_mins": 26047.0, "commute_35_39_mins": 7723.0, "commute_40_44_mins": 8715.0, "commute_60_89_mins": 12434.0, "commute_90_more_mins": 5414.0, "households_retirement_income": 55016.0, "armed_forces": 3154.0, "civilian_labor_force": 320078.0, "employed_pop": 288988.0, "unemployed_pop": 31090.0, "not_in_labor_force": 235101.0, "pop_16_over": 558333.0, "pop_in_labor_force": 323232.0, "asian_male_45_54": 616.0, "asian_male_55_64": 206.0, "black_male_45_54": 11276.0, "black_male_55_64": 9774.0, "hispanic_male_45_54": 747.0, "hispanic_male_55_64": 677.0, "white_male_45_54": 32870.0, "white_male_55_64": 31583.0, "bachelors_degree_2": 57560.0, "bachelors_degree_or_higher_25_64": 78978.0, "children": 156568.0, "children_in_single_female_hh": 49379.0, "commuters_by_bus": 1143.0, "commuters_by_car_truck_van": 269409.0, "commuters_by_carpool": 28074.0, "commuters_by_subway_or_elevated": 88.0, "commuters_drove_alone": 241335.0, "different_house_year_ago_different_city": 79043.0, "different_house_year_ago_same_city": 31765.0, "employed_agriculture_forestry_fishing_hunting_mining": 3623.0, "employed_arts_entertainment_recreation_accommodation_food": 23494.0, "employed_construction": 18668.0, "employed_education_health_social": 67306.0, "employed_finance_insurance_real_estate": 15589.0, "employed_information": 4603.0, "employed_manufacturing": 48605.0, "employed_other_services_not_public_admin": 13296.0, "employed_public_administration": 18039.0, "employed_retail_trade": 33935.0, "employed_science_management_admin_waste": 22409.0, "employed_transportation_warehousing_utilities": 13435.0, "employed_wholesale_trade": 5986.0, "female_female_households": 429.0, "four_more_cars": 22184.0, "gini_index": 0.4689, "graduate_professional_degree": 38192.0, "group_quarters": 19323.0, "high_school_including_ged": 147063.0, "households_public_asst_or_food_stamps": 46875.0, "in_grades_1_to_4": 34295.0, "in_grades_5_to_8": 36790.0, "in_grades_9_to_12": 34872.0, "in_school": 183983.0, "in_undergrad_college": 49746.0, "less_than_high_school_graduate": 80142.0, "male_45_64_associates_degree": 6351.0, "male_45_64_bachelors_degree": 9840.0, "male_45_64_graduate_degree": 7392.0, "male_45_64_less_than_9_grade": 5113.0, "male_45_64_grade_9_12": 11019.0, "male_45_64_high_school": 30241.0, "male_45_64_some_college": 18913.0, "male_45_to_64": 88869.0, "male_male_households": 291.0, "management_business_sci_arts_employed": 92407.0, "no_car": 5629.0, "no_cars": 16781.0, "not_us_citizen_pop": 13660.0, "occupation_management_arts": 92407.0, "occupation_natural_resources_construction_maintenance": 29980.0, "occupation_production_transportation_material": 52212.0, "occupation_sales_office": 68039.0, "occupation_services": 46350.0, "one_car": 85060.0, "two_cars": 99565.0, "three_cars": 45880.0, "pop_25_64": 359417.0, "pop_determined_poverty_status": 676760.0, "population_1_year_and_over": 689690.0, "population_3_years_over": 673572.0, "poverty": 138126.0, "sales_office_employed": 68039.0, "some_college_and_associates_degree": 137877.0, "walked_to_work": 3800.0, "worked_at_home": 8346.0, "workers_16_and_over": 285597.0, "associates_degree": 35381.0, "bachelors_degree": 57560.0, "high_school_diploma": 119560.0, "less_one_year_college": 30650.0, "masters_degree": 26670.0, "one_year_more_college": 71846.0, "pop_25_years_over": 460834.0, "commute_35_44_mins": 16438.0, "commute_60_more_mins": 17848.0, "commute_less_10_mins": 34716.0, "commuters_16_over": 277251.0, "hispanic_any_race": 20345.0, "pop_5_years_over": 655828.0, "speak_only_english_at_home": 627540.0, "speak_spanish_at_home": 14987.0, "speak_spanish_at_home_low_english": 6158.0, "do_date": "20112015", "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN}, {"geo_id": "0502", "nonfamily_households": 102473.0, "family_households": 185762.0, "median_year_structure_built": 1984.0, "rent_burden_not_computed": 9508.0, "rent_over_50_percent": 22317.0, "rent_40_to_50_percent": 8487.0, "rent_35_to_40_percent": 5876.0, "rent_30_to_35_percent": 8061.0, "rent_25_to_30_percent": 10788.0, "rent_20_to_25_percent": 11480.0, "rent_15_to_20_percent": 12508.0, "rent_10_to_15_percent": 8506.0, "rent_under_10_percent": 3361.0, "total_pop": 750711.0, "male_pop": 364317.0, "female_pop": 386394.0, "median_age": 36.5, "white_pop": 520469.0, "black_pop": 164815.0, "asian_pop": 11086.0, "hispanic_pop": 37309.0, "amerindian_pop": 2061.0, "other_race_pop": 633.0, "two_or_more_races_pop": 14133.0, "not_hispanic_pop": 713402.0, "commuters_by_public_transportation": 2156.0, "households": 288235.0, "median_income": 47206.0, "income_per_capita": 25989.0, "housing_units": 332677.0, "vacant_housing_units": 44442.0, "vacant_housing_units_for_rent": 15950.0, "vacant_housing_units_for_sale": 6143.0, "median_rent": 585.0, "percent_income_spent_on_rent": 29.6, "owner_occupied_housing_units": 187343.0, "million_dollar_housing_units": 810.0, "mortgaged_housing_units": 118496.0, "families_with_young_children": 57251.0, "two_parent_families_with_young_children": 35388.0, "two_parents_in_labor_force_families_with_young_children": 20986.0, "two_parents_father_in_labor_force_families_with_young_children": 13098.0, "two_parents_mother_in_labor_force_families_with_young_children": 936.0, "two_parents_not_in_labor_force_families_with_young_children": 368.0, "one_parent_families_with_young_children": 21863.0, "father_one_parent_families_with_young_children": 4478.0, "father_in_labor_force_one_parent_families_with_young_children": 3773.0, "commute_10_14_mins": 53672.0, "commute_15_19_mins": 56264.0, "commute_20_24_mins": 58460.0, "commute_25_29_mins": 19776.0, "commute_30_34_mins": 43134.0, "commute_45_59_mins": 18621.0, "aggregate_travel_time_to_work": 7357295.0, "income_less_10000": 23583.0, "income_10000_14999": 17055.0, "income_15000_19999": 16777.0, "income_20000_24999": 18800.0, "income_25000_29999": 15925.0, "income_30000_34999": 16302.0, "income_35000_39999": 14829.0, "income_40000_44999": 15020.0, "income_45000_49999": 12652.0, "income_50000_59999": 23858.0, "income_60000_74999": 28051.0, "income_75000_99999": 32977.0, "income_100000_124999": 20995.0, "income_125000_149999": 11496.0, "income_150000_199999": 10953.0, "income_200000_or_more": 8962.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 756.0, "owner_occupied_housing_units_lower_value_quartile": 82600.0, "owner_occupied_housing_units_median_value": 135800.0, "owner_occupied_housing_units_upper_value_quartile": 207500.0, "married_households": 134159.0, "occupied_housing_units": 288235.0, "housing_units_renter_occupied": 100892.0, "dwellings_1_units_detached": 223179.0, "dwellings_1_units_attached": 4567.0, "dwellings_2_units": 9086.0, "dwellings_3_to_4_units": 9390.0, "dwellings_5_to_9_units": 14005.0, "dwellings_10_to_19_units": 17064.0, "dwellings_20_to_49_units": 5454.0, "dwellings_50_or_more_units": 13016.0, "mobile_homes": 36472.0, "housing_built_2005_or_later": 446.0, "housing_built_2000_to_2004": 9775.0, "housing_built_1939_or_earlier": 11489.0, "male_under_5": 25713.0, "male_5_to_9": 26034.0, "male_10_to_14": 24258.0, "male_15_to_17": 14895.0, "male_18_to_19": 10066.0, "male_20": 5816.0, "male_21": 5568.0, "male_22_to_24": 16383.0, "male_25_to_29": 26488.0, "male_30_to_34": 25802.0, "male_35_to_39": 24235.0, "male_40_to_44": 22800.0, "male_45_to_49": 23130.0, "male_50_to_54": 24458.0, "male_55_to_59": 22810.0, "male_60_61": 8158.0, "male_62_64": 12365.0, "male_65_to_66": 7445.0, "male_67_to_69": 9221.0, "male_70_to_74": 11312.0, "male_75_to_79": 8020.0, "male_80_to_84": 5156.0, "male_85_and_over": 4184.0, "female_under_5": 24307.0, "female_5_to_9": 25009.0, "female_10_to_14": 23670.0, "female_15_to_17": 14183.0, "female_18_to_19": 9620.0, "female_20": 5366.0, "female_21": 6016.0, "female_22_to_24": 17228.0, "female_25_to_29": 27678.0, "female_30_to_34": 26562.0, "female_35_to_39": 24511.0, "female_40_to_44": 24298.0, "female_45_to_49": 24215.0, "female_50_to_54": 26496.0, "female_55_to_59": 26312.0, "female_60_to_61": 8908.0, "female_62_to_64": 13088.0, "female_65_to_66": 8267.0, "female_67_to_69": 9780.0, "female_70_to_74": 14405.0, "female_75_to_79": 9723.0, "female_80_to_84": 8182.0, "female_85_and_over": 8570.0, "white_including_hispanic": 547745.0, "black_including_hispanic": 166045.0, "amerindian_including_hispanic": 2273.0, "asian_including_hispanic": 11248.0, "commute_5_9_mins": 35745.0, "commute_35_39_mins": 7238.0, "commute_40_44_mins": 8839.0, "commute_60_89_mins": 10934.0, "commute_90_more_mins": 4591.0, "households_retirement_income": 51476.0, "armed_forces": 2755.0, "civilian_labor_force": 367062.0, "employed_pop": 341187.0, "unemployed_pop": 25875.0, "not_in_labor_force": 222003.0, "pop_16_over": 591820.0, "pop_in_labor_force": 369817.0, "asian_male_45_54": 664.0, "asian_male_55_64": 352.0, "black_male_45_54": 8979.0, "black_male_55_64": 7939.0, "hispanic_male_45_54": 1698.0, "hispanic_male_55_64": 880.0, "white_male_45_54": 35846.0, "white_male_55_64": 33557.0, "bachelors_degree_2": 88333.0, "bachelors_degree_or_higher_25_64": 114797.0, "children": 178069.0, "children_in_single_female_hh": 53482.0, "commuters_by_bus": 2126.0, "commuters_by_car_truck_van": 317746.0, "commuters_by_carpool": 33033.0, "commuters_by_subway_or_elevated": 16.0, "commuters_drove_alone": 284713.0, "different_house_year_ago_different_city": 71701.0, "different_house_year_ago_same_city": 43676.0, "employed_agriculture_forestry_fishing_hunting_mining": 5822.0, "employed_arts_entertainment_recreation_accommodation_food": 28431.0, "employed_construction": 22034.0, "employed_education_health_social": 89788.0, "employed_finance_insurance_real_estate": 21968.0, "employed_information": 9639.0, "employed_manufacturing": 25822.0, "employed_other_services_not_public_admin": 17262.0, "employed_public_administration": 21232.0, "employed_retail_trade": 43982.0, "employed_science_management_admin_waste": 29251.0, "employed_transportation_warehousing_utilities": 17696.0, "employed_wholesale_trade": 8260.0, "female_female_households": 685.0, "four_more_cars": 15155.0, "gini_index": 0.466, "graduate_professional_degree": 50746.0, "group_quarters": 16519.0, "high_school_including_ged": 152534.0, "households_public_asst_or_food_stamps": 37274.0, "in_grades_1_to_4": 39755.0, "in_grades_5_to_8": 37191.0, "in_grades_9_to_12": 37543.0, "in_school": 195968.0, "in_undergrad_college": 47589.0, "less_than_high_school_graduate": 56838.0, "male_45_64_associates_degree": 5332.0, "male_45_64_bachelors_degree": 14714.0, "male_45_64_graduate_degree": 8912.0, "male_45_64_less_than_9_grade": 2889.0, "male_45_64_grade_9_12": 6929.0, "male_45_64_high_school": 32260.0, "male_45_64_some_college": 19885.0, "male_45_to_64": 90921.0, "male_male_households": 476.0, "management_business_sci_arts_employed": 125310.0, "no_car": 7377.0, "no_cars": 17569.0, "not_us_citizen_pop": 18943.0, "occupation_management_arts": 125310.0, "occupation_natural_resources_construction_maintenance": 32073.0, "occupation_production_transportation_material": 38984.0, "occupation_sales_office": 87880.0, "occupation_services": 56940.0, "one_car": 101260.0, "two_cars": 113253.0, "three_cars": 40998.0, "pop_25_64": 392314.0, "pop_determined_poverty_status": 734406.0, "population_1_year_and_over": 741828.0, "population_3_years_over": 721895.0, "poverty": 119934.0, "sales_office_employed": 87880.0, "some_college_and_associates_degree": 148128.0, "walked_to_work": 4435.0, "worked_at_home": 9925.0, "workers_16_and_over": 337943.0, "associates_degree": 32871.0, "bachelors_degree": 88333.0, "high_school_diploma": 128594.0, "less_one_year_college": 30346.0, "masters_degree": 33725.0, "one_year_more_college": 84911.0, "pop_25_years_over": 496579.0, "commute_35_44_mins": 16077.0, "commute_60_more_mins": 15525.0, "commute_less_10_mins": 46489.0, "commuters_16_over": 328018.0, "hispanic_any_race": 37309.0, "pop_5_years_over": 700691.0, "speak_only_english_at_home": 656463.0, "speak_spanish_at_home": 28112.0, "speak_spanish_at_home_low_english": 11586.0, "do_date": "20112015", "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN}, {"geo_id": "4101", "nonfamily_households": 103164.0, "family_households": 199020.0, "median_year_structure_built": 1984.0, "rent_burden_not_computed": 4911.0, "rent_over_50_percent": 27316.0, "rent_40_to_50_percent": 9918.0, "rent_35_to_40_percent": 7904.0, "rent_30_to_35_percent": 10447.0, "rent_25_to_30_percent": 12965.0, "rent_20_to_25_percent": 14909.0, "rent_15_to_20_percent": 15446.0, "rent_10_to_15_percent": 9776.0, "rent_under_10_percent": 3134.0, "total_pop": 797650.0, "male_pop": 393987.0, "female_pop": 403663.0, "median_age": 37.3, "white_pop": 579823.0, "black_pop": 12239.0, "asian_pop": 55987.0, "hispanic_pop": 113356.0, "amerindian_pop": 3993.0, "other_race_pop": 843.0, "two_or_more_races_pop": 28720.0, "not_hispanic_pop": 684294.0, "commuters_by_public_transportation": 20497.0, "households": 302184.0, "median_income": 63770.0, "income_per_capita": 32479.0, "housing_units": 326216.0, "vacant_housing_units": 24032.0, "vacant_housing_units_for_rent": 4980.0, "vacant_housing_units_for_sale": 3063.0, "median_rent": 883.0, "percent_income_spent_on_rent": 29.9, "owner_occupied_housing_units": 185458.0, "million_dollar_housing_units": 1624.0, "mortgaged_housing_units": 136301.0, "families_with_young_children": 58281.0, "two_parent_families_with_young_children": 44377.0, "two_parents_in_labor_force_families_with_young_children": 25285.0, "two_parents_father_in_labor_force_families_with_young_children": 17399.0, "two_parents_mother_in_labor_force_families_with_young_children": 1379.0, "two_parents_not_in_labor_force_families_with_young_children": 314.0, "one_parent_families_with_young_children": 13904.0, "father_one_parent_families_with_young_children": 3332.0, "father_in_labor_force_one_parent_families_with_young_children": 2956.0, "commute_10_14_mins": 52887.0, "commute_15_19_mins": 56335.0, "commute_20_24_mins": 52516.0, "commute_25_29_mins": 23403.0, "commute_30_34_mins": 45414.0, "commute_45_59_mins": 29187.0, "aggregate_travel_time_to_work": 8798000.0, "income_less_10000": 15475.0, "income_10000_14999": 12115.0, "income_15000_19999": 12372.0, "income_20000_24999": 14180.0, "income_25000_29999": 13040.0, "income_30000_34999": 13593.0, "income_35000_39999": 13275.0, "income_40000_44999": 13767.0, "income_45000_49999": 11718.0, "income_50000_59999": 23488.0, "income_60000_74999": 30566.0, "income_75000_99999": 42792.0, "income_100000_124999": 30175.0, "income_125000_149999": 18528.0, "income_150000_199999": 18595.0, "income_200000_or_more": 18505.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 1021.0, "owner_occupied_housing_units_lower_value_quartile": 198800.0, "owner_occupied_housing_units_median_value": 278300.0, "owner_occupied_housing_units_upper_value_quartile": 393100.0, "married_households": 156666.0, "occupied_housing_units": 302184.0, "housing_units_renter_occupied": 116726.0, "dwellings_1_units_detached": 195780.0, "dwellings_1_units_attached": 20853.0, "dwellings_2_units": 7749.0, "dwellings_3_to_4_units": 14188.0, "dwellings_5_to_9_units": 18398.0, "dwellings_10_to_19_units": 17445.0, "dwellings_20_to_49_units": 14112.0, "dwellings_50_or_more_units": 23818.0, "mobile_homes": 13572.0, "housing_built_2005_or_later": 513.0, "housing_built_2000_to_2004": 4748.0, "housing_built_1939_or_earlier": 11891.0, "male_under_5": 25446.0, "male_5_to_9": 26506.0, "male_10_to_14": 27962.0, "male_15_to_17": 16027.0, "male_18_to_19": 9487.0, "male_20": 4738.0, "male_21": 5150.0, "male_22_to_24": 13888.0, "male_25_to_29": 28375.0, "male_30_to_34": 30770.0, "male_35_to_39": 28522.0, "male_40_to_44": 30066.0, "male_45_to_49": 27594.0, "male_50_to_54": 27136.0, "male_55_to_59": 25644.0, "male_60_61": 9512.0, "male_62_64": 12606.0, "male_65_to_66": 8104.0, "male_67_to_69": 9224.0, "male_70_to_74": 10829.0, "male_75_to_79": 7171.0, "male_80_to_84": 4952.0, "male_85_and_over": 4278.0, "female_under_5": 24415.0, "female_5_to_9": 25910.0, "female_10_to_14": 26112.0, "female_15_to_17": 15416.0, "female_18_to_19": 9238.0, "female_20": 4465.0, "female_21": 4615.0, "female_22_to_24": 14474.0, "female_25_to_29": 28371.0, "female_30_to_34": 29917.0, "female_35_to_39": 28457.0, "female_40_to_44": 28888.0, "female_45_to_49": 26982.0, "female_50_to_54": 27891.0, "female_55_to_59": 27415.0, "female_60_to_61": 9943.0, "female_62_to_64": 14532.0, "female_65_to_66": 7706.0, "female_67_to_69": 11169.0, "female_70_to_74": 12471.0, "female_75_to_79": 8987.0, "female_80_to_84": 6466.0, "female_85_and_over": 9823.0, "white_including_hispanic": 646336.0, "black_including_hispanic": 12863.0, "amerindian_including_hispanic": 6441.0, "asian_including_hispanic": 56667.0, "commute_5_9_mins": 38398.0, "commute_35_39_mins": 10561.0, "commute_40_44_mins": 15326.0, "commute_60_89_mins": 17251.0, "commute_90_more_mins": 6051.0, "households_retirement_income": 49231.0, "armed_forces": 826.0, "civilian_labor_force": 421094.0, "employed_pop": 387852.0, "unemployed_pop": 33242.0, "not_in_labor_force": 209221.0, "pop_16_over": 631141.0, "pop_in_labor_force": 421920.0, "asian_male_45_54": 3906.0, "asian_male_55_64": 2337.0, "black_male_45_54": 1125.0, "black_male_55_64": 600.0, "hispanic_male_45_54": 5903.0, "hispanic_male_55_64": 2695.0, "white_male_45_54": 42523.0, "white_male_55_64": 41013.0, "bachelors_degree_2": 131163.0, "bachelors_degree_or_higher_25_64": 175834.0, "children": 187794.0, "children_in_single_female_hh": 35895.0, "commuters_by_bus": 12991.0, "commuters_by_car_truck_van": 315666.0, "commuters_by_carpool": 39600.0, "commuters_by_subway_or_elevated": 2794.0, "commuters_drove_alone": 276066.0, "different_house_year_ago_different_city": 101530.0, "different_house_year_ago_same_city": 31908.0, "employed_agriculture_forestry_fishing_hunting_mining": 7795.0, "employed_arts_entertainment_recreation_accommodation_food": 33985.0, "employed_construction": 19403.0, "employed_education_health_social": 78018.0, "employed_finance_insurance_real_estate": 27751.0, "employed_information": 8026.0, "employed_manufacturing": 64782.0, "employed_other_services_not_public_admin": 16556.0, "employed_public_administration": 13217.0, "employed_retail_trade": 42683.0, "employed_science_management_admin_waste": 49163.0, "employed_transportation_warehousing_utilities": 14081.0, "employed_wholesale_trade": 12392.0, "female_female_households": 590.0, "four_more_cars": 16823.0, "gini_index": 0.4468, "graduate_professional_degree": 78307.0, "group_quarters": 14243.0, "high_school_including_ged": 112404.0, "households_public_asst_or_food_stamps": 44912.0, "in_grades_1_to_4": 41379.0, "in_grades_5_to_8": 42270.0, "in_grades_9_to_12": 42338.0, "in_school": 199876.0, "in_undergrad_college": 40347.0, "less_than_high_school_graduate": 49885.0, "male_45_64_associates_degree": 9041.0, "male_45_64_bachelors_degree": 24359.0, "male_45_64_graduate_degree": 15711.0, "male_45_64_less_than_9_grade": 3710.0, "male_45_64_grade_9_12": 4806.0, "male_45_64_high_school": 20117.0, "male_45_64_some_college": 24748.0, "male_45_to_64": 102492.0, "male_male_households": 841.0, "management_business_sci_arts_employed": 165468.0, "no_car": 12450.0, "no_cars": 22221.0, "not_us_citizen_pop": 65462.0, "occupation_management_arts": 165468.0, "occupation_natural_resources_construction_maintenance": 29720.0, "occupation_production_transportation_material": 40761.0, "occupation_sales_office": 89187.0, "occupation_services": 62716.0, "one_car": 98173.0, "two_cars": 122190.0, "three_cars": 42777.0, "pop_25_64": 442621.0, "pop_determined_poverty_status": 784547.0, "population_1_year_and_over": 788371.0, "population_3_years_over": 769356.0, "poverty": 98696.0, "sales_office_employed": 89187.0, "some_college_and_associates_degree": 172042.0, "walked_to_work": 14488.0, "worked_at_home": 21954.0, "workers_16_and_over": 380742.0, "associates_degree": 44944.0, "bachelors_degree": 131163.0, "high_school_diploma": 92645.0, "less_one_year_college": 40248.0, "masters_degree": 55226.0, "one_year_more_college": 86850.0, "pop_25_years_over": 543801.0, "commute_35_44_mins": 25887.0, "commute_60_more_mins": 23302.0, "commute_less_10_mins": 49857.0, "commuters_16_over": 358788.0, "hispanic_any_race": 113356.0, "pop_5_years_over": 747789.0, "speak_only_english_at_home": 598776.0, "speak_spanish_at_home": 82226.0, "speak_spanish_at_home_low_english": 35572.0, "do_date": "20112015", "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN}]}