{"table_name": "congressionaldistrict_2016_1yr", "table_fullname": "bigquery-public-data.census_bureau_acs.congressionaldistrict_2016_1yr", "column_names": ["geo_id", "nonfamily_households", "family_households", "median_year_structure_built", "rent_burden_not_computed", "rent_over_50_percent", "rent_40_to_50_percent", "rent_35_to_40_percent", "rent_30_to_35_percent", "rent_25_to_30_percent", "rent_20_to_25_percent", "rent_15_to_20_percent", "rent_10_to_15_percent", "rent_under_10_percent", "total_pop", "male_pop", "female_pop", "median_age", "white_pop", "black_pop", "asian_pop", "hispanic_pop", "amerindian_pop", "other_race_pop", "two_or_more_races_pop", "not_hispanic_pop", "commuters_by_public_transportation", "households", "median_income", "income_per_capita", "housing_units", "vacant_housing_units", "vacant_housing_units_for_rent", "vacant_housing_units_for_sale", "median_rent", "percent_income_spent_on_rent", "owner_occupied_housing_units", "million_dollar_housing_units", "mortgaged_housing_units", "families_with_young_children", "two_parent_families_with_young_children", "two_parents_in_labor_force_families_with_young_children", "two_parents_father_in_labor_force_families_with_young_children", "two_parents_mother_in_labor_force_families_with_young_children", "two_parents_not_in_labor_force_families_with_young_children", "one_parent_families_with_young_children", "father_one_parent_families_with_young_children", "father_in_labor_force_one_parent_families_with_young_children", "commute_10_14_mins", "commute_15_19_mins", "commute_20_24_mins", "commute_25_29_mins", "commute_30_34_mins", "commute_45_59_mins", "aggregate_travel_time_to_work", "income_less_10000", "income_10000_14999", "income_15000_19999", "income_20000_24999", "income_25000_29999", "income_30000_34999", "income_35000_39999", "income_40000_44999", "income_45000_49999", "income_50000_59999", "income_60000_74999", "income_75000_99999", "income_100000_124999", "income_125000_149999", "income_150000_199999", "income_200000_or_more", "renter_occupied_housing_units_paying_cash_median_gross_rent", "owner_occupied_housing_units_lower_value_quartile", "owner_occupied_housing_units_median_value", "owner_occupied_housing_units_upper_value_quartile", "married_households", "occupied_housing_units", "housing_units_renter_occupied", "dwellings_1_units_detached", "dwellings_1_units_attached", "dwellings_2_units", "dwellings_3_to_4_units", "dwellings_5_to_9_units", "dwellings_10_to_19_units", "dwellings_20_to_49_units", "dwellings_50_or_more_units", "mobile_homes", "housing_built_2005_or_later", "housing_built_2000_to_2004", "housing_built_1939_or_earlier", "male_under_5", "male_5_to_9", "male_10_to_14", "male_15_to_17", "male_18_to_19", "male_20", "male_21", "male_22_to_24", "male_25_to_29", "male_30_to_34", "male_35_to_39", "male_40_to_44", "male_45_to_49", "male_50_to_54", "male_55_to_59", "male_60_61", "male_62_64", "male_65_to_66", "male_67_to_69", "male_70_to_74", "male_75_to_79", "male_80_to_84", "male_85_and_over", "female_under_5", "female_5_to_9", "female_10_to_14", "female_15_to_17", "female_18_to_19", "female_20", "female_21", "female_22_to_24", "female_25_to_29", "female_30_to_34", "female_35_to_39", "female_40_to_44", "female_45_to_49", "female_50_to_54", "female_55_to_59", "female_60_to_61", "female_62_to_64", "female_65_to_66", "female_67_to_69", "female_70_to_74", "female_75_to_79", "female_80_to_84", "female_85_and_over", "white_including_hispanic", "black_including_hispanic", "amerindian_including_hispanic", "asian_including_hispanic", "commute_5_9_mins", "commute_35_39_mins", "commute_40_44_mins", "commute_60_89_mins", "commute_90_more_mins", "households_retirement_income", "armed_forces", "civilian_labor_force", "employed_pop", "unemployed_pop", "not_in_labor_force", "pop_16_over", "pop_in_labor_force", "asian_male_45_54", "asian_male_55_64", "black_male_45_54", "black_male_55_64", "hispanic_male_45_54", "hispanic_male_55_64", "white_male_45_54", "white_male_55_64", "bachelors_degree_2", "bachelors_degree_or_higher_25_64", "children", "children_in_single_female_hh", "commuters_by_bus", "commuters_by_car_truck_van", "commuters_by_carpool", "commuters_by_subway_or_elevated", "commuters_drove_alone", "different_house_year_ago_different_city", "different_house_year_ago_same_city", "employed_agriculture_forestry_fishing_hunting_mining", "employed_arts_entertainment_recreation_accommodation_food", "employed_construction", "employed_education_health_social", "employed_finance_insurance_real_estate", "employed_information", "employed_manufacturing", "employed_other_services_not_public_admin", "employed_public_administration", "employed_retail_trade", "employed_science_management_admin_waste", "employed_transportation_warehousing_utilities", "employed_wholesale_trade", "female_female_households", "four_more_cars", "gini_index", "graduate_professional_degree", "group_quarters", "high_school_including_ged", "households_public_asst_or_food_stamps", "in_grades_1_to_4", "in_grades_5_to_8", "in_grades_9_to_12", "in_school", "in_undergrad_college", "less_than_high_school_graduate", "male_45_64_associates_degree", "male_45_64_bachelors_degree", "male_45_64_graduate_degree", "male_45_64_less_than_9_grade", "male_45_64_grade_9_12", "male_45_64_high_school", "male_45_64_some_college", "male_45_to_64", "male_male_households", "management_business_sci_arts_employed", "no_car", "no_cars", "not_us_citizen_pop", "occupation_management_arts", "occupation_natural_resources_construction_maintenance", "occupation_production_transportation_material", "occupation_sales_office", "occupation_services", "one_car", "two_cars", "three_cars", "pop_25_64", "pop_determined_poverty_status", "population_1_year_and_over", "population_3_years_over", "poverty", "sales_office_employed", "some_college_and_associates_degree", "walked_to_work", "worked_at_home", "workers_16_and_over", "associates_degree", "bachelors_degree", "high_school_diploma", "less_one_year_college", "masters_degree", "one_year_more_college", "pop_25_years_over", "commute_35_44_mins", "commute_60_more_mins", "commute_less_10_mins", "commuters_16_over", "hispanic_any_race", "pop_5_years_over", "speak_only_english_at_home", "speak_spanish_at_home", "speak_spanish_at_home_low_english", "do_date", "pop_15_and_over", "pop_never_married", "pop_now_married", "pop_separated", "pop_widowed", "pop_divorced"], "column_types": ["STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64"], "nested_column_names": ["geo_id", "nonfamily_households", "family_households", "median_year_structure_built", "rent_burden_not_computed", "rent_over_50_percent", "rent_40_to_50_percent", "rent_35_to_40_percent", "rent_30_to_35_percent", "rent_25_to_30_percent", "rent_20_to_25_percent", "rent_15_to_20_percent", "rent_10_to_15_percent", "rent_under_10_percent", "total_pop", "male_pop", "female_pop", "median_age", "white_pop", "black_pop", "asian_pop", "hispanic_pop", "amerindian_pop", "other_race_pop", "two_or_more_races_pop", "not_hispanic_pop", "commuters_by_public_transportation", "households", "median_income", "income_per_capita", "housing_units", "vacant_housing_units", "vacant_housing_units_for_rent", "vacant_housing_units_for_sale", "median_rent", "percent_income_spent_on_rent", "owner_occupied_housing_units", "million_dollar_housing_units", "mortgaged_housing_units", "families_with_young_children", "two_parent_families_with_young_children", "two_parents_in_labor_force_families_with_young_children", "two_parents_father_in_labor_force_families_with_young_children", "two_parents_mother_in_labor_force_families_with_young_children", "two_parents_not_in_labor_force_families_with_young_children", "one_parent_families_with_young_children", "father_one_parent_families_with_young_children", "father_in_labor_force_one_parent_families_with_young_children", "commute_10_14_mins", "commute_15_19_mins", "commute_20_24_mins", "commute_25_29_mins", "commute_30_34_mins", "commute_45_59_mins", "aggregate_travel_time_to_work", "income_less_10000", "income_10000_14999", "income_15000_19999", "income_20000_24999", "income_25000_29999", "income_30000_34999", "income_35000_39999", "income_40000_44999", "income_45000_49999", "income_50000_59999", "income_60000_74999", "income_75000_99999", "income_100000_124999", "income_125000_149999", "income_150000_199999", "income_200000_or_more", "renter_occupied_housing_units_paying_cash_median_gross_rent", "owner_occupied_housing_units_lower_value_quartile", "owner_occupied_housing_units_median_value", "owner_occupied_housing_units_upper_value_quartile", "married_households", "occupied_housing_units", "housing_units_renter_occupied", "dwellings_1_units_detached", "dwellings_1_units_attached", "dwellings_2_units", "dwellings_3_to_4_units", "dwellings_5_to_9_units", "dwellings_10_to_19_units", "dwellings_20_to_49_units", "dwellings_50_or_more_units", "mobile_homes", "housing_built_2005_or_later", "housing_built_2000_to_2004", "housing_built_1939_or_earlier", "male_under_5", "male_5_to_9", "male_10_to_14", "male_15_to_17", "male_18_to_19", "male_20", "male_21", "male_22_to_24", "male_25_to_29", "male_30_to_34", "male_35_to_39", "male_40_to_44", "male_45_to_49", "male_50_to_54", "male_55_to_59", "male_60_61", "male_62_64", "male_65_to_66", "male_67_to_69", "male_70_to_74", "male_75_to_79", "male_80_to_84", "male_85_and_over", "female_under_5", "female_5_to_9", "female_10_to_14", "female_15_to_17", "female_18_to_19", "female_20", "female_21", "female_22_to_24", "female_25_to_29", "female_30_to_34", "female_35_to_39", "female_40_to_44", "female_45_to_49", "female_50_to_54", "female_55_to_59", "female_60_to_61", "female_62_to_64", "female_65_to_66", "female_67_to_69", "female_70_to_74", "female_75_to_79", "female_80_to_84", "female_85_and_over", "white_including_hispanic", "black_including_hispanic", "amerindian_including_hispanic", "asian_including_hispanic", "commute_5_9_mins", "commute_35_39_mins", "commute_40_44_mins", "commute_60_89_mins", "commute_90_more_mins", "households_retirement_income", "armed_forces", "civilian_labor_force", "employed_pop", "unemployed_pop", "not_in_labor_force", "pop_16_over", "pop_in_labor_force", "asian_male_45_54", "asian_male_55_64", "black_male_45_54", "black_male_55_64", "hispanic_male_45_54", "hispanic_male_55_64", "white_male_45_54", "white_male_55_64", "bachelors_degree_2", "bachelors_degree_or_higher_25_64", "children", "children_in_single_female_hh", "commuters_by_bus", "commuters_by_car_truck_van", "commuters_by_carpool", "commuters_by_subway_or_elevated", "commuters_drove_alone", "different_house_year_ago_different_city", "different_house_year_ago_same_city", "employed_agriculture_forestry_fishing_hunting_mining", "employed_arts_entertainment_recreation_accommodation_food", "employed_construction", "employed_education_health_social", "employed_finance_insurance_real_estate", "employed_information", "employed_manufacturing", "employed_other_services_not_public_admin", "employed_public_administration", "employed_retail_trade", "employed_science_management_admin_waste", "employed_transportation_warehousing_utilities", "employed_wholesale_trade", "female_female_households", "four_more_cars", "gini_index", "graduate_professional_degree", "group_quarters", "high_school_including_ged", "households_public_asst_or_food_stamps", "in_grades_1_to_4", "in_grades_5_to_8", "in_grades_9_to_12", "in_school", "in_undergrad_college", "less_than_high_school_graduate", "male_45_64_associates_degree", "male_45_64_bachelors_degree", "male_45_64_graduate_degree", "male_45_64_less_than_9_grade", "male_45_64_grade_9_12", "male_45_64_high_school", "male_45_64_some_college", "male_45_to_64", "male_male_households", "management_business_sci_arts_employed", "no_car", "no_cars", "not_us_citizen_pop", "occupation_management_arts", "occupation_natural_resources_construction_maintenance", "occupation_production_transportation_material", "occupation_sales_office", "occupation_services", "one_car", "two_cars", "three_cars", "pop_25_64", "pop_determined_poverty_status", "population_1_year_and_over", "population_3_years_over", "poverty", "sales_office_employed", "some_college_and_associates_degree", "walked_to_work", "worked_at_home", "workers_16_and_over", "associates_degree", "bachelors_degree", "high_school_diploma", "less_one_year_college", "masters_degree", "one_year_more_college", "pop_25_years_over", "commute_35_44_mins", "commute_60_more_mins", "commute_less_10_mins", "commuters_16_over", "hispanic_any_race", "pop_5_years_over", "speak_only_english_at_home", "speak_spanish_at_home", "speak_spanish_at_home_low_english", "do_date", "pop_15_and_over", "pop_never_married", "pop_now_married", "pop_separated", "pop_widowed", "pop_divorced"], "nested_column_types": ["STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64"], "description": ["US Congressional Districts Geoids", "Nonfamily Households. A householder living alone or with nonrelatives only. Unmarried couples households, whether opposite-sex or same-sex, with no relatives of the householder present are tabulated in nonfamily households.", "Family Households. A family consists of a householder and one or more other people living in the same household who are related to the householder by birth, marriage, or adoption. All people in a household who are related to the householder are regarded as members of his or her family. A family household may contain people not related to the householder, but those people are not included as part of the family of the householder in tabulations. Thus, the number of family households is equal to the number of families, but family households may include more members than do families. A household can contain only one family for purposes of tabulations. Not all households contain families since a household may be comprised of a group of unrelated people or of one person living alone -- these are called nonfamily households. Families are classified by type as either a \"married couple family\" or \"other family\" according to the sex of the householder and the presence of relatives. The data on family type are based ...", "Median Year Structure Built. Median Year Structure Built", "Housing units without rent burden computed. Units for which no rent is paid and units occupied by households that reported no income or a net loss comprise this category", "Housing units spending over 50% income on rent. Gross rent over 50 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 40 to 49.9% income on rent. Gross rent from 40.0 to 49.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 35 to 39.9% income on rent. Gross rent from 35.0 to 39.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 30 to 34.9% income on rent. Gross rent from 30.0 to 34.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 25 to 29.9% income on rent. Gross rent from 25.0 to 29.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 20 to 24.9% income on rent. Gross rent from 20.0 to 24.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 15 to 19.9% income on rent. Gross rent from 15.0 to 19.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 10 to 14.9% income on rent. Gross rent from 10.0 to 14.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending less than 10% on rent. Gross rent less than 10 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Total Population. The total number of all people living in a given geographic area.  This is a very useful catch-all denominator when calculating rates.", "Male Population. The number of people within each geography who are male.", "Female Population. The number of people within each geography who are female.", "Median Age. The median age of all people in a given geographic area.", "White Population. The number of people identifying as white, non-Hispanic in each geography.", "Black or African American Population. The number of people identifying as black or African American, non-Hispanic in each geography.", "Asian Population. The number of people identifying as Asian, non-Hispanic in each geography.", "Hispanic Population. The number of people identifying as Hispanic or Latino in each geography.", "American Indian and Alaska Native Population. The number of people identifying as American Indian or Alaska native in each geography.", "Other Race population. The number of people identifying as another race in each geography", "Two or more races population. The number of people identifying as two or more races in each geography", "Population not Hispanic. The number of people not identifying as Hispanic or Latino in each geography.", "Commuters by Public Transportation. The number of workers age 16 years and over within a geographic area who primarily traveled to work by public transportation.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Households. A count of the number of households in each geography. A household consists of one or more people who live in the same dwelling and also share at meals or living accommodation, and may consist of a single family or some other grouping of people. ", "Median Household Income in the past 12 Months. Within a geographic area, the median income received by every household on a regular basis before payments for personal income taxes, social security, union dues, medicare deductions, etc.  It includes income received from wages, salary, commissions, bonuses, and tips; self-employment income from own nonfarm or farm businesses, including proprietorships and partnerships; interest, dividends, net rental income, royalty income, or income from estates and trusts; Social Security or Railroad Retirement income; Supplemental Security Income (SSI); any cash public assistance or welfare payments from the state or local welfare office; retirement, survivor, or disability benefits; and any other sources of income received regularly such as Veterans' (VA) payments, unemployment and/or worker's compensation, child support, and alimony.", "Per Capita Income in the past 12 Months. Per capita income is the mean income computed for every man, woman, and child in a particular group. It is derived by dividing the total income of a particular group by the total population.", "Housing Units. A count of housing units in each geography.  A housing unit is a house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters.", "Vacant Housing Units. The count of vacant housing units in a geographic area. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Vacant Housing Units for Rent. The count of vacant housing units in a geographic area that are for rent. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Vacant Housing Units for Sale. The count of vacant housing units in a geographic area that are for sale. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Median Rent. The median contract rent within a geographic area. The contract rent is the monthly rent agreed to or contracted for, regardless of any furnishings, utilities, fees, meals, or services that may be included. For vacant units, it is the monthly rent asked for the rental unit at the time of interview.", "Percent of Household Income Spent on Rent. Within a geographic area, the median percentage of household income which was spent on gross rent.  Gross rent is the amount of the contract rent plus the estimated average monthly cost of utilities (electricity, gas, water, sewer etc.) and fuels (oil, coal, wood, etc.) if these are paid by the renter.  Household income is the sum of the income of all people 15 years and older living in the household.", "Owner-occupied Housing Units", "Owner-occupied Housing Units valued at $1,000,000 or more.. The count of owner occupied housing units in a geographic area that are valued at $1,000,000 or more.  Value is the respondent's estimate of how much the property (house and lot, mobile home and lot, or condominium unit) would sell for if it were for sale.", "Owner-occupied Housing Units with a Mortgage. The count of housing units within a geographic area that are mortagaged. \"Mortgage\" refers to all forms of debt where the property is pledged as security for repayment of the debt, including deeds of trust, trust deed, contracts to purchase, land contracts, junior mortgages, and home equity loans.", "Families with young children (under 6 years of age)", "Two-parent families with young children (under 6 years of age)", "Two-parent families, both parents in labor force with young children (under 6 years of age)", "Two-parent families, father only in labor force with young children (under 6 years of age)", "Two-parent families, mother only in labor force with young children (under 6 years of age)", "Two-parent families, neither parent in labor force with young children (under 6 years of age)", "One-parent families with young children (under 6 years of age)", "One-parent families, father, with young children (under 6 years of age)", "One-parent families, father in labor force, with young children (under 6 years of age)", "Number of workers with a commute between 10 and 14 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 10 and 14 minutes. ", "Number of workers with a commute between 15 and 19 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 15 and 19 minutes. ", "Number of workers with a commute between 20 and 24 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 20 and 24 minutes.", "Number of workers with a commute between 25 and 29 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 25 and 29 minutes. ", "Number of workers with a commute between 30 and 34 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 30 and 34 minutes. ", "Number of workers with a commute between 45 and 59 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 45 and 59 minutes. ", "Aggregate travel time to work. The total number of minutes every worker in a geographic area over the age of 16 who did not work from home spent spent commuting to work in one day", "Households with income less than $10,000. The number of households in a geographic area whose annual income was less than $10,000.", "Households with income of $10,000 to $14,999. The number of households in a geographic area whose annual income was between $10,000 and $14,999.", "Households with income of $15,000 to $19,999. The number of households in a geographic area whose annual income was between $15,000 and $19,999.", "Households with income of $20,000 to $24,999. The number of households in a geographic area whose annual income was between $20,000 and $24,999.", "Households with income of $25,000 to $29,999. The number of households in a geographic area whose annual income was between $20,000 and $24,999.", "Households with income of $30,000 to $34,999. The number of households in a geographic area whose annual income was between $30,000 and $34,999.", "Households with income of $35,000 to $39,999. The number of households in a geographic area whose annual income was between $35,000 and $39,999.", "Households with income of $40,000 to $44,999. The number of households in a geographic area whose annual income was between $40,000 and $44,999.", "Households with income of $45,000 to $49,999. The number of households in a geographic area whose annual income was between $45,000 and $49,999.", "Households with income of $50,000 to $59,999. The number of households in a geographic area whose annual income was between $50,000 and $59,999.", "Households with income of $60,000 to $74,999. The number of households in a geographic area whose annual income was between $60,000 and $74,999.", "Households with income of $75,000 to $99,999. The number of households in a geographic area whose annual income was between $75,000 and $99,999.", "Households with income of $100,000 to $124,999. The number of households in a geographic area whose annual income was between $100,000 and $124,999.", "Households with income of $125,000 to $149,999. The number of households in a geographic area whose annual income was between $125,000 and $149,999.", "Households with income of $150,000 to $199,999. The number of households in a geographic area whose annual income was between $150,000 and $1999,999.", "Households with income of $200,000 Or More. The number of households in a geographic area whose annual income was more than $200,000.", "Renter-Occupied Housing Units Paying Cash Rent Median Gross Rent", "Owner-Occupied Housing Units Lower Value Quartile", "Owner-Occupied Housing Units Median Value. The middle value (median) in a geographic area owner occupied housing units.", "Owner-Occupied Housing Units Upper Value Quartile", "Married households. People in formal marriages, as well as people in common-law marriages, are included. Does not include same-sex marriages.", "Occupied housing units. A housing unit is classified as occupied if it is the usual place of residence of the person or group of people living in it at the time of enumeration.", "Renter occupied housing units. All occupied units which are not owner occupied, whether they are rented for cash rent or occupied without payment of cash rent, are classified as renter-occupied.", "Single-family (one unit) detached dwellings. This is a 1-unit structure detached from any other house, that is, with open space on all four sides. Such structures are considered detached even if they have an adjoining shed or garage. A one-family house that contains a business is considered detached as long as the building has open space on all four sides. Mobile homes to which one or more permanent rooms have been added or built also are included.", "Single-family (one unit) attached dwellings. This is a 1-unit structure that has one or more walls extending from ground to roof separating it from adjoining structures. In row houses (sometimes called townhouses), double houses, or houses attached to nonresidential structures, each house is a separate, attached structure if the dividing or common wall goes from ground to roof.", "Two-family (two unit) dwellings", "Multifamily dwellings with three to 4 units", "Apartment buildings with 5 to 9 units", "Apartment buildings with 10 to 19 units", "Apartment buildings with 20 to 49 units", "Apartment buildings with 50 or more units", "Mobile homes. A manufactured home is defined as a movable dwelling, 8 feet or more wide and 40 feet or more long, designed to be towed on its own chassis, with transportation gear integral to the unit when it leaves the factory, and without need of a permanent foundation. These homes are built in accordance with the U.S. Department of Housing and Urban Development (HUD) building code.", "Housing units built in 2005 or later. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built in 2005 or later.", "Housing units built between 2000 and 2004. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built from 2000 to 2004.", "Housing units built before 1939. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built in 1939 or earlier.", "Male under 5 years. The male population over the age of five years within the specified area.", "Male age 5 to 9. The male population between the age of five years to nine years within the specified area.", "Male age 10 to 14. The male population between the age of ten years to fourteen years within the specified area.", "Male age 15 to 17. The male population between the age of fifteeen years to seventeen years within the specified area.", "Male age 18 and 19. The male population between the age of eighteen years to nineteen years within the specified area.", "Male age 20. The male population with an age of twenty years within the specified area.", "Male age 21. The male population with an age of twenty-one years within the specified area.", "Male age 22 to 24. The male population between the age of twenty-two years to twenty-four years within the specified area.", "Male age 25 to 29. The male population between the age of twenty-five years to twenty-nine years within the specified area.", "Male age 30 to 34. The male population between the age of thirty years to thirty-four years within the specified area.", "Male age 35 to 39. The male population between the age of thirty-five years to thirty-nine years within the specified area.", "Male age 40 to 44. The male population between the age of fourty years to fourty-four years within the specified area.", "Men age 45 to 49. The male population between the age of fourty-five years to fourty-nine years within the specified area.", "Men age 50 to 54. The male population between the age of fifty years to fifty-four years within the specified area.", "Men age 55 to 59. The male population between the age of fifty-five years to fifty-nine years within the specified area.", "Men age 60 to 61. The male population between the age of sixty years to sixty-one years within the specified area.", "Men age 62 to 64. The male population between the age of sixty-two years to sixty-four years within the specified area.", "Male age 65 to 66. The male population between the age of sixty-five years to sixty-six years within the specified area.", "Male age 67 to 69. The male population between the age of sixty-seven years to sixty-nine years within the specified area.", "Male age 70 to 74. The male population between the age of seventy years to seventy-four years within the specified area.", "Male age 75 to 79. The male population between the age of seventy-five years to seventy-nine years within the specified area.", "Male age 80 to 84. The male population between the age of eighty years to eighty-four years within the specified area.", "Male age 85 and over. The male population of the age of eighty-five years and over within the specified area.", "Female under 5 years. The female population over the age of five years within the specified area.", "Female age 5 to 9. The female population between the age of five years to nine years within the specified area.", "Female age 10 to 14. The female population between the age of ten years to fourteen years within the specified area.", "Female age 15 to 17. The female population between the age of fifteeen years to seventeen years within the specified area.", "Female age 18 and 19. The female population between the age of eighteen years to nineteen years within the specified area.", "Female age 20. The female population with an age of twenty years within the specified area.", "Female age 21. The female population with an age of twenty-one years within the specified area.", "Female age 22 to 24. The female population between the age of twenty-two years to twenty-four years within the specified area.", "Female age 25 to 29. The female population between the age of twenty-five years to twenty-nine years within the specified area.", "Female age 30 to 34. The female population between the age of thirty years to thirty-four years within the specified area.", "Female age 35 to 39. The female population between the age of thirty-five years to thirty-nine years within the specified area.", "Female age 40 to 44. The female population between the age of fourty years to fourty-four years within the specified area.", "Female age 45 to 49. The female population between the age of fourty-five years to fourty-nine years within the specified area.", "Female age 50 to 54. The female population between the age of fifty years to fifty-four years within the specified area.", "Female age 55 to 59. The female population between the age of fifty-five years to fifty-nine years within the specified area.", "Female age 60 and 61. The female population between the age of sixty years to sixty-one years within the specified area.", "Female age 62 to 64. The female population between the age of sixty-two years to sixty-four years within the specified area.", "Female age 65 to 66. The female population between the age of sixty-five years to sixty-six years within the specified area.", "Female age 67 to 69. The female population between the age of sixty-seven years to sixty-nine years within the specified area.", "Female age 70 to 74. The female population between the age of seventy years to seventy-four years within the specified area.", "Female age 75 to 79. The female population between the age of seventy-five years to seventy-nine years within the specified area.", "Female age 80 to 84. The female population between the age of eighty years to eighty-four years within the specified area.", "Female age 85 and over. The female population of the age of eighty-five years and over within the specified area.", "White including Hispanic", "Black including Hispanic", "American Indian including Hispanic", "Asian including Hispanic", "Number of workers with a commute between 5 and 9 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 5 and 9 minutes.", "Number of workers with a commute between 35 and 39 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 35 and 39 minutes. ", "Number of workers with a commute between 40 and 44 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 40 and 44 minutes. ", "Number of workers with a commute between 60 and 89 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 60 and 89 minutes .", "Number of workers with a commute of more than 90 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute more than 90 minutes.", "Households receiving retirement income", "Population in Armed Forces. The number of people in each geography who are members of the U.S. Armed Forces (people on active duty with the United States Army, Air Force, Navy, Marine Corps, or Coast Guard).", "Population in Civilian Labor Force. The number of civilians 16 years and over in each geography who can be classified as either \"employed\" or \"unemployed\" below.", "Employed Population. The number of civilians 16 years old and over in each geography who either (1) were \"at work,\" that is, those who did any work at all during the reference week as paid employees, worked in their own business or profession, worked on their own farm, or worked 15 hours or more as unpaid workers on a family farm or in a family business; or (2) were \"with a job but not at work,\" that is, those who did not work during the reference week but had jobs or businesses from which they were temporarily absent due to illness, bad weather, industrial dispute, vacation, or other personal reasons. Excluded from the employed are people whose only activity consisted of work around the house or unpaid volunteer work for religious, charitable, and similar organizations; also excluded are all institutionalized people and people on active duty in the United States Armed Forces.", "", "Population Not in Labor Force. The number of people in each geography who are 16 years old and over who are not classified as members of the labor force. This category consists mainly of students, homemakers, retired workers, seasonal workers interviewed in an off season who were not looking for work, institutionalized people, and people doing only incidental unpaid family work (less than 15 hours during the reference week).", "Population age 16 and over. The number of people in each geography who are age 16 or over.", "Population in Labor Force. The number of people in each geography who are either in the civilian labor force or are members of the U.S. Armed Forces (people on active duty with the United States Army, Air Force, Navy, Marine Corps, or Coast Guard).", "Asian Men age 45 to 54", "Asian Men age 55 to 64", "Black Men age 45 to 54", "Black Men age 55 to 64", "Hispanic Men age 45 to 54", "Hispanic Men age 55 to 64", "White Men age 45 to 54", "White Men age 55 to 64", "Population who completed a bachelor's degree. From mobility table.", "Population with Bachelors Degree or Higher, Ages 25 to 64. The number of people in each geography who are between the ages of 25 and 64 who have attained a bachelors degree or higher. ", "Children under 18 Years of Age. The number of people within each geography who are under 18 years of age.", "Children under 18 years of age in single female-led household", "Commuters by Bus. The number of workers age 16 years and over within a geographic area who primarily traveled to work by bus.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.  This is a subset of workers who commuted by public transport.", "Commuters by Car, Truck, or Van. The number of workers age 16 years and over within  a geographic area who primarily traveled to work by car, truck or  van.  This is the principal mode of travel or type of conveyance,  by distance rather than time, that the worker usually used to get  from home to work.", "Commuters by Carpool. The number of workers age 16 years and over within a geographic area who primarily traveled to work by carpool.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Commuters by Subway or Elevated. The number of workers age 16 years and over within a geographic area who primarily traveled to work by subway or elevated train.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.  This is a subset of workers who commuted by public transport.", "Commuters who drove alone. The number of workers age 16 years and over within a geographic area who primarily traveled by car driving alone. This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Lived in a different house one year ago in a different city. All people in a geographic area who lived in a different city within the year prior to the survey.", "Lived in a different house one year ago in the same city. All people in a geographic area who lived in the same city but moved to a different unit within the year prior to the survey.", "Workers employed in firms in agriculture, forestry, fishing, hunting, or mining. The Agriculture, Forestry, Fishing and Hunting sector comprises establishments primarily engaged in growing crops, raising animals, harvesting timber, and harvesting fish and other animals from a farm, ranch, or their natural habitats.", "Workers employed in firms in arts, entertainment, recreation, accommodation and food services. The Arts, Entertainment, and Recreation sector includes a wide range of establishments that operate facilities or provide services to meet varied cultural, entertainment, and recreational interests of their patrons. This sector comprises (1) establishments that are involved in producing, promoting, or participating in live performances, events, or exhibits intended for public viewing; (2) establishments that preserve and exhibit objects and sites of historical, cultural, or educational interest; and (3) establishments that operate facilities or provide services that enable patrons to participate in recreational activities or pursue amusement, hobby, and leisure-time interests.", "Workers employed in firms in construction. The Construction sector comprises establishments primarily engaged in the construction of buildings or engineering projects (e.g., highways and utility systems). Construction work done may include new work, additions, alterations, or maintenance and repairs.", "Workers employed in firms in educational services, health care, and social assistance. Outpatient health services, other than hospital care, including: public health administration; research and education; categorical health programs; treatment and immunization clinics; nursing; environmental health activities such as air and water pollution control; ambulance service if provided separately from fire protection services, and other general public health activities such as mosquito abatement. School health services provided by health agencies (rather than school agencies) are included here. Sewage treatment operations are classified under Sewerage.", "Workers employed in firms in finance, insurance, real estate and rental and leasing. The Real Estate and Rental and Leasing sector comprises establishments primarily engaged in renting, leasing, or otherwise allowing the use of tangible or intangible assets, and establishments providing related services. The major portion of this sector comprises establishments that rent, lease, or otherwise allow the use of their own assets by others. The assets may be tangible, as is the case of real estate and equipment, or intangible, as is the case with patents and trademarks.", "Workers employed in firms in information. The Information sector comprises establishments engaged in the following processes: (a) producing and distributing information and cultural products, (b) providing the means to transmit or distribute these products as well as data or communications, and (c) processing data. Included are the publishing industries, the motion picture and sound recording industries; the broadcasting industries, the telecommunications industries; Web search portals, data processing industries, and the information services industries.", "Workers employed in firms in manufacturing. The Manufacturing sector comprises establishments engaged in the mechanical, physical, or chemical transformation of materials, substances, or components into new products.", "Workers employed in firms in other services except public administration. The Other Services (Except Public Administration) sector comprises establishments engaged in providing services not specifically provided for elsewhere in the classification system. Establishments in this sector are primarily engaged in activities such as equipment and machinery repairing, promoting or administering religious activities, grantmaking, advocacy, and providing drycleaning and laundry services, personal care services, death care services, pet care services, photofinishing services, temporary parking services, and dating services. Private households that engage in employing workers on or about the premises in activities primarily concerned with the operation of the household are included in this sector.", "Workers employed in firms in public administration. The Public Administration sector consists of establishments of federal, state, and local government agencies that administer, oversee, and manage public programs and have executive, legislative, or judicial authority over other institutions within a given area. These agencies also set policy, create laws, adjudicate civil and criminal legal cases, provide for public safety and for national defense. In general, government establishments in the public administration sector oversee governmental programs and activities that are not performed by private establishments.", "Workers employed in firms in retail trade. The Retail Trade sector comprises establishments engaged in retailing merchandise, generally without transformation, and rendering services incidental to the sale of merchandise. The retailing process is the final step in the distribution of merchandise; retailers are, therefore, organized to sell merchandise in small quantities to the general public.", "Workers employed in firms in professional scientific, management, administrative and waste management services. The Administrative and Support and Waste Management and Remediation Services sector comprises establishments performing routine support activities for the day-to-day operations of other organizations. The establishments in this sector specialize in one or more of these support activities and provide these services to clients in a variety of industries and, in some cases, to households. Activities performed include office administration, hiring and placing of personnel, document preparation and similar clerical services, solicitation, collection, security and surveillance services, cleaning, and waste disposal services.", "Workers employed in firms in transportation, warehousing, and utilities. The Transportation and Warehousing sector includes industries providing transportation of passengers and cargo, warehousing and storage for goods, scenic and sightseeing transportation, and support activities related to modes of transportation. The modes of transportation are air, rail, water, road, and pipeline.", "Workers employed in firms in wholesale trade. The Wholesale Trade sector comprises establishments engaged in wholesaling merchandise, generally without transformation, and rendering services incidental to the sale of merchandise. The wholesaling process is an intermediate step in the distribution of merchandise. Wholesalers are organized to sell or arrange the purchase or sale of (a) goods for resale (i.e., goods sold to other wholesalers or retailers), (b) capital or durable nonconsumer goods, and (c) raw and intermediate materials and supplies used in production.", "Households with two female partners. An unmarried partner is a person age 15 years and over, who is not related to the householder, who shares living quarters, and who has an intimate relationship with the householder.", "Four car households. The number of households with four or more passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Gini Index. The Gini index, or index of income concentration, is a statistical measure of income inequality ranging from 0 to 1. A measure of 1 indicates perfect inequality, i.e., one household having all the income and rest having none. A measure of 0 indicates perfect equality, i.e., all households having an equal share of income.", "Population who completed a graduate or professional degree", "Population living in group quarters", "Population with high school degree, including GED. The number of people in a geographic area over the age of 25 who attained a high school degree or GED.", "Households on cash public assistance or receiving food stamps (SNAP)", "Students Enrolled in Grades 1 to 4. The total number of people in each geography currently enrolled in grades 1 through 4 inclusive.  This corresponds roughly to elementary school.", "Students Enrolled in Grades 5 to 8. The total number of people in each geography currently enrolled in grades 5 through 8 inclusive.  This corresponds roughly to middle school.", "Students Enrolled in Grades 9 to 12. The total number of people in each geography currently enrolled in grades 9 through 12 inclusive.  This corresponds roughly to high school.", "Students Enrolled in School. The total number of people in each geography currently enrolled at any level of school, from nursery or pre-school to advanced post-graduate education.  Only includes those over the age of 3.", "Students Enrolled as Undergraduate in College. The number of people in a geographic area who are enrolled in college at the undergraduate level. Enrollment refers to being registered or listed as a student in an educational program leading to a college degree. This may be a public school or college, a private school or college.", "Less than high school graduate. The number of people in a geographic area over the age of 25 who have not completed high school or any other advanced degree.", "Men age 45 to 64 who obtained an associate's degree", "Men age 45 to 64 who obtained a bachelor's degree", "Men age 45 to 64 who obtained a graduate or professional degree", "Men age 45 to 64 who attained less than a 9th grade education", "Men age 45 to 64 who attained between 9th and 12th grade, no diploma", "Men age 45 to 64 who completed high school or obtained GED", "Men age 45 to 64 who completed some college, no degree", "Men age 45 to 64 (\"middle aged\"). The male population between the age of fourty-five years to sixty-four years within the specified area.", "Households with two male partners. An unmarried partner is a person age 15 years and over, who is not related to the householder, who shares living quarters, and who has an intimate relationship with the householder.", "Civilian Employed Population in Management, Business, Science, and Arts Occupations. The number of employed civilians 16 years old and over in each geography in the Management, Business, Science, and Arts occupations. Occupation codes are based on the Standard Occupational Classification (SOC), published by the Executive Office of the President, Office of Management and Budget.", "Workers age 16 and over with no vehicle. All people in a geographic area over the age of 16 who do not own a car.", "Car-free households. The number of households without passenger cars, vans, and pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Not a U.S. Citizen Population. The number of people within each geography who indicated that they are not U.S. citizens.", "Workers employed in management business science and arts occupations", "Workers employed in natural resources, construction, and maintenance occupations", "Workers employed in production, transportation, and material moving", "Workers employed in sales and office occupations", "Workers employed in service occupations", "One car households. The number of households with one passenger car, van, , pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Two car households. The number of households with two passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Three car households. The number of households with one passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Population age 25 to 64. The number of people in each geography who are between the ages of 25 and 64. ", "Population for Whom Poverty Status Determined. The number of people in each geography who could be identified as either living in poverty or not.  This should be used as the denominator when calculating poverty rates, as it excludes people for whom it was not possible to determine poverty.", "Population 1 year and over. All people, male and female, child and adult, living in a given geographic area that are 1 year and older.", "Population 3 Years and Over. The total number of people in each geography age 3 years and over.  This denominator is mostly used to calculate rates of school enrollment.", "Income In The Past 12 Months Below Poverty Level. The number of people in a geographic area who are part of a family (which could be just them as an individual) determined to be \"in poverty\" following the Office of Management and Budget's Directive 14. (https://www.census.gov/hhes/povmeas/methodology/ombdir14.html)", "Civilian Employed Population in Sales and Office Occupations. The number of employed civilians 16 years old and over in each geography in the Sales and Office occupations. Occupation codes are based on the Standard Occupational Classification (SOC), published by the Executive Office of the President, Office of Management and Budget.", "Population who completed some college or obtained associate's degree. The number of people in a geographic area over the age of 25 who obtained an associate's degree, and did not complete a more advanced degree.", "Walked to Work. The number of workers age 16 years and over within a geographic area who primarily walked to work.  This would mean that of any way of getting to work, they travelled the most distance walking.", "Worked at Home. The count within a geographical area of workers over the age of 16 who worked at home.", "Workers over the Age of 16. The number of people in each geography who work. Workers include those employed at private for-profit companies, the self-employed, government workers and non-profit employees.", "Population Completed Associate's Degree. The number of people in a geographic area over the age of 25 who obtained a associate's degree, and did not complete a more advanced degree.", "Population Completed Bachelor's Degree. The number of people in a geographic area over the age of 25 who obtained a bachelor's degree, and did not complete a more advanced degree.", "Population Completed High School. The number of people in a geographic area over the age of 25 who completed high school, and did not complete a more advanced degree.", "Population completed less than one year of college, no degree. The number of people in a geographic area over the age of 25 who attended college for less than one year and no further.", "Population Completed Master's Degree. The number of people in a geographic area over the age of 25 who obtained a master's degree, but did not complete a more advanced degree.", "Population completed more than one year of college, no degree. The number of people in a geographic area over the age of 25 who attended college for more than one year but did not obtain a degree", "Population 25 Years and Over. The number of people in a geographic area who are over the age of 25.  This is used mostly as a denominator of educational attainment.", "Number of workers with a commute between 35 and 44 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 35 and 44 minutes. ", "Number of workers with a commute of over 60 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in over 60 minutes.", "Number of workers with less than 10 minute commute. The number of workers in a geographic area over the age of 16 who do not work from home and commute in less than 10 minutes.", "Workers age 16 and over who do not work from home. The number of workers in a geographic area over the age of 16 who do not work from home", "Hispanic of any race", "Population 5 Years and Over. The number of people in a geographic area who are over the age of 5.  This is primarily used as a denominator of measures of language spoken at home.", "Speaks only English at Home. The number of people in a geographic area over age 5 who speak only English at home.", "Speaks Spanish at Home. The number of people in a geographic area over age 5 who speak Spanish at home, possibly in addition to other languages.", "Speaks Spanish at Home, speaks English less than \"very well\"", null, "Population 15 Years and Over. The number of people in a geographic area who are over the age of 15.  This is used mostly as a denominator of marital status.", "Never Married. The number of people in a geographic area who have never been married.", "Currently married. The number of people in a geographic area who are currently married", "Married but separated. The number of people in a geographic area who are married but separated", "Widowed. The number of people in a geographic area who are widowed", "Divorced. The number of people in a geographic area who are divorced"], "sample_rows": [{"geo_id": "1223", "nonfamily_households": 96394.0, "family_households": 177863.0, "median_year_structure_built": 1984.0, "rent_burden_not_computed": 6910.0, "rent_over_50_percent": 28740.0, "rent_40_to_50_percent": 11559.0, "rent_35_to_40_percent": 6765.0, "rent_30_to_35_percent": 9270.0, "rent_25_to_30_percent": 10124.0, "rent_20_to_25_percent": 10416.0, "rent_15_to_20_percent": 8826.0, "rent_10_to_15_percent": 3996.0, "rent_under_10_percent": 949.0, "total_pop": 749377.0, "male_pop": 358972.0, "female_pop": 390405.0, "median_age": 41.6, "white_pop": 324351.0, "black_pop": 92871.0, "asian_pop": 33273.0, "hispanic_pop": 278102.0, "amerindian_pop": 1996.0, "other_race_pop": 4318.0, "two_or_more_races_pop": 13984.0, "not_hispanic_pop": 471275.0, "commuters_by_public_transportation": 5372.0, "households": 274257.0, "median_income": 61513.0, "income_per_capita": 34835.0, "housing_units": 348787.0, "vacant_housing_units": 74530.0, "vacant_housing_units_for_rent": 9723.0, "vacant_housing_units_for_sale": 3083.0, "median_rent": 1357.0, "percent_income_spent_on_rent": 36.3, "owner_occupied_housing_units": 176702.0, "million_dollar_housing_units": 3928.0, "mortgaged_housing_units": 107792.0, "families_with_young_children": 45588.0, "two_parent_families_with_young_children": 30854.0, "two_parents_in_labor_force_families_with_young_children": 18691.0, "two_parents_father_in_labor_force_families_with_young_children": 11051.0, "two_parents_mother_in_labor_force_families_with_young_children": 465.0, "two_parents_not_in_labor_force_families_with_young_children": 647.0, "one_parent_families_with_young_children": 14734.0, "father_one_parent_families_with_young_children": 3102.0, "father_in_labor_force_one_parent_families_with_young_children": 3085.0, "commute_10_14_mins": 38495.0, "commute_15_19_mins": 43309.0, "commute_20_24_mins": 55813.0, "commute_25_29_mins": 23864.0, "commute_30_34_mins": 57489.0, "commute_45_59_mins": 41369.0, "aggregate_travel_time_to_work": 10315790.0, "income_less_10000": 20300.0, "income_10000_14999": 10428.0, "income_15000_19999": 11748.0, "income_20000_24999": 13890.0, "income_25000_29999": 11088.0, "income_30000_34999": 11702.0, "income_35000_39999": 10899.0, "income_40000_44999": 12516.0, "income_45000_49999": 10701.0, "income_50000_59999": 19543.0, "income_60000_74999": 27478.0, "income_75000_99999": 32685.0, "income_100000_124999": 24132.0, "income_125000_149999": 15829.0, "income_150000_199999": 18008.0, "income_200000_or_more": 23310.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 1462.0, "owner_occupied_housing_units_lower_value_quartile": 178600.0, "owner_occupied_housing_units_median_value": 292600.0, "owner_occupied_housing_units_upper_value_quartile": 446600.0, "married_households": 128843.0, "occupied_housing_units": 274257.0, "housing_units_renter_occupied": 97555.0, "dwellings_1_units_detached": 130040.0, "dwellings_1_units_attached": 34117.0, "dwellings_2_units": 7169.0, "dwellings_3_to_4_units": 10701.0, "dwellings_5_to_9_units": 10352.0, "dwellings_10_to_19_units": 18585.0, "dwellings_20_to_49_units": 34658.0, "dwellings_50_or_more_units": 91993.0, "mobile_homes": 11057.0, "housing_built_2005_or_later": 2148.0, "housing_built_2000_to_2004": 8155.0, "housing_built_1939_or_earlier": 2942.0, "male_under_5": 19696.0, "male_5_to_9": 21324.0, "male_10_to_14": 23814.0, "male_15_to_17": 13595.0, "male_18_to_19": 6830.0, "male_20": 4374.0, "male_21": 4687.0, "male_22_to_24": 15087.0, "male_25_to_29": 22748.0, "male_30_to_34": 26055.0, "male_35_to_39": 23123.0, "male_40_to_44": 22706.0, "male_45_to_49": 26016.0, "male_50_to_54": 28584.0, "male_55_to_59": 25609.0, "male_60_61": 8539.0, "male_62_64": 12469.0, "male_65_to_66": 6701.0, "male_67_to_69": 11934.0, "male_70_to_74": 13185.0, "male_75_to_79": 9702.0, "male_80_to_84": 5753.0, "male_85_and_over": 6441.0, "female_under_5": 19352.0, "female_5_to_9": 18086.0, "female_10_to_14": 21572.0, "female_15_to_17": 14287.0, "female_18_to_19": 6751.0, "female_20": 6371.0, "female_21": 4525.0, "female_22_to_24": 11663.0, "female_25_to_29": 24217.0, "female_30_to_34": 26134.0, "female_35_to_39": 25215.0, "female_40_to_44": 26546.0, "female_45_to_49": 29819.0, "female_50_to_54": 28291.0, "female_55_to_59": 27245.0, "female_60_to_61": 8740.0, "female_62_to_64": 15132.0, "female_65_to_66": 10061.0, "female_67_to_69": 12986.0, "female_70_to_74": 16790.0, "female_75_to_79": 13733.0, "female_80_to_84": 9314.0, "female_85_and_over": 13575.0, "white_including_hispanic": 556073.0, "black_including_hispanic": 100461.0, "amerindian_including_hispanic": 2082.0, "asian_including_hispanic": 33776.0, "commute_5_9_mins": 18938.0, "commute_35_39_mins": 13794.0, "commute_40_44_mins": 17056.0, "commute_60_89_mins": 27052.0, "commute_90_more_mins": 7401.0, "households_retirement_income": 38801.0, "armed_forces": 508.0, "civilian_labor_force": 398595.0, "employed_pop": 378041.0, "unemployed_pop": 20554.0, "not_in_labor_force": 217540.0, "pop_16_over": 616643.0, "pop_in_labor_force": 399103.0, "asian_male_45_54": 1594.0, "asian_male_55_64": 1890.0, "black_male_45_54": 6790.0, "black_male_55_64": 5149.0, "hispanic_male_45_54": 20943.0, "hispanic_male_55_64": 13732.0, "white_male_45_54": 24683.0, "white_male_55_64": 25159.0, "bachelors_degree_2": 127455.0, "bachelors_degree_or_higher_25_64": 167080.0, "children": 151726.0, "children_in_single_female_hh": 39313.0, "commuters_by_bus": 5039.0, "commuters_by_car_truck_van": 329178.0, "commuters_by_carpool": 27569.0, "commuters_by_subway_or_elevated": 253.0, "commuters_drove_alone": 301609.0, "different_house_year_ago_different_city": 72963.0, "different_house_year_ago_same_city": 29740.0, "employed_agriculture_forestry_fishing_hunting_mining": 1817.0, "employed_arts_entertainment_recreation_accommodation_food": 42347.0, "employed_construction": 20349.0, "employed_education_health_social": 78312.0, "employed_finance_insurance_real_estate": 37195.0, "employed_information": 8589.0, "employed_manufacturing": 15664.0, "employed_other_services_not_public_admin": 18098.0, "employed_public_administration": 16743.0, "employed_retail_trade": 46296.0, "employed_science_management_admin_waste": 54196.0, "employed_transportation_warehousing_utilities": 20328.0, "employed_wholesale_trade": 18107.0, "female_female_households": 322.0, "four_more_cars": 11226.0, "gini_index": 0.4993, "graduate_professional_degree": 79857.0, "group_quarters": 3792.0, "high_school_including_ged": 119905.0, "households_public_asst_or_food_stamps": 25463.0, "in_grades_1_to_4": 32425.0, "in_grades_5_to_8": 38399.0, "in_grades_9_to_12": 37324.0, "in_school": 187699.0, "in_undergrad_college": 48785.0, "less_than_high_school_graduate": 41802.0, "male_45_64_associates_degree": 9565.0, "male_45_64_bachelors_degree": 20839.0, "male_45_64_graduate_degree": 17954.0, "male_45_64_less_than_9_grade": 2863.0, "male_45_64_grade_9_12": 2905.0, "male_45_64_high_school": 25307.0, "male_45_64_some_college": 21784.0, "male_45_to_64": 101217.0, "male_male_households": 696.0, "management_business_sci_arts_employed": 159345.0, "no_car": 6708.0, "no_cars": 18125.0, "not_us_citizen_pop": 90681.0, "occupation_management_arts": 159345.0, "occupation_natural_resources_construction_maintenance": 23082.0, "occupation_production_transportation_material": 26750.0, "occupation_sales_office": 105571.0, "occupation_services": 63293.0, "one_car": 103887.0, "two_cars": 105887.0, "three_cars": 35132.0, "pop_25_64": 407188.0, "pop_determined_poverty_status": 745818.0, "population_1_year_and_over": 741841.0, "population_3_years_over": 726612.0, "poverty": 80152.0, "sales_office_employed": 105571.0, "some_college_and_associates_degree": 168344.0, "walked_to_work": 6002.0, "worked_at_home": 20502.0, "workers_16_and_over": 368783.0, "associates_degree": 54602.0, "bachelors_degree": 127455.0, "high_school_diploma": 107677.0, "less_one_year_college": 37454.0, "masters_degree": 53460.0, "one_year_more_college": 76288.0, "pop_25_years_over": 537363.0, "commute_35_44_mins": 30850.0, "commute_60_more_mins": 34453.0, "commute_less_10_mins": 22639.0, "commuters_16_over": 348281.0, "hispanic_any_race": 278102.0, "pop_5_years_over": 710329.0, "speak_only_english_at_home": 375582.0, "speak_spanish_at_home": 239774.0, "speak_spanish_at_home_low_english": 89084.0, "do_date": "2016", "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN}, {"geo_id": "1000", "nonfamily_households": 118652.0, "family_households": 232433.0, "median_year_structure_built": 1984.0, "rent_burden_not_computed": 9340.0, "rent_over_50_percent": 21152.0, "rent_40_to_50_percent": 10948.0, "rent_35_to_40_percent": 6242.0, "rent_30_to_35_percent": 9979.0, "rent_25_to_30_percent": 11170.0, "rent_20_to_25_percent": 11437.0, "rent_15_to_20_percent": 13965.0, "rent_10_to_15_percent": 7786.0, "rent_under_10_percent": 4061.0, "total_pop": 952065.0, "male_pop": 460232.0, "female_pop": 491833.0, "median_age": 40.6, "white_pop": 597331.0, "black_pop": 203278.0, "asian_pop": 35591.0, "hispanic_pop": 87152.0, "amerindian_pop": 3281.0, "other_race_pop": 1419.0, "two_or_more_races_pop": 23023.0, "not_hispanic_pop": 864913.0, "commuters_by_public_transportation": 11758.0, "households": 351085.0, "median_income": 61757.0, "income_per_capita": 31712.0, "housing_units": 426154.0, "vacant_housing_units": 75069.0, "vacant_housing_units_for_rent": 9396.0, "vacant_housing_units_for_sale": 5490.0, "median_rent": 893.0, "percent_income_spent_on_rent": 30.0, "owner_occupied_housing_units": 245005.0, "million_dollar_housing_units": 827.0, "mortgaged_housing_units": 157296.0, "families_with_young_children": 62987.0, "two_parent_families_with_young_children": 38355.0, "two_parents_in_labor_force_families_with_young_children": 25361.0, "two_parents_father_in_labor_force_families_with_young_children": 12330.0, "two_parents_mother_in_labor_force_families_with_young_children": 554.0, "two_parents_not_in_labor_force_families_with_young_children": 110.0, "one_parent_families_with_young_children": 24632.0, "father_one_parent_families_with_young_children": 4592.0, "father_in_labor_force_one_parent_families_with_young_children": 3727.0, "commute_10_14_mins": 57263.0, "commute_15_19_mins": 65469.0, "commute_20_24_mins": 72284.0, "commute_25_29_mins": 30583.0, "commute_30_34_mins": 66755.0, "commute_45_59_mins": 28309.0, "aggregate_travel_time_to_work": 10966250.0, "income_less_10000": 20673.0, "income_10000_14999": 12893.0, "income_15000_19999": 12843.0, "income_20000_24999": 16476.0, "income_25000_29999": 14993.0, "income_30000_34999": 18790.0, "income_35000_39999": 16634.0, "income_40000_44999": 14516.0, "income_45000_49999": 15046.0, "income_50000_59999": 26974.0, "income_60000_74999": 37988.0, "income_75000_99999": 48649.0, "income_100000_124999": 32836.0, "income_125000_149999": 22554.0, "income_150000_199999": 19635.0, "income_200000_or_more": 19585.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 1048.0, "owner_occupied_housing_units_lower_value_quartile": 167300.0, "owner_occupied_housing_units_median_value": 243400.0, "owner_occupied_housing_units_upper_value_quartile": 345900.0, "married_households": 167306.0, "occupied_housing_units": 351085.0, "housing_units_renter_occupied": 106080.0, "dwellings_1_units_detached": 246431.0, "dwellings_1_units_attached": 69916.0, "dwellings_2_units": 6815.0, "dwellings_3_to_4_units": 9279.0, "dwellings_5_to_9_units": 17421.0, "dwellings_10_to_19_units": 21710.0, "dwellings_20_to_49_units": 8691.0, "dwellings_50_or_more_units": 11730.0, "mobile_homes": 33947.0, "housing_built_2005_or_later": 10502.0, "housing_built_2000_to_2004": 19388.0, "housing_built_1939_or_earlier": 20920.0, "male_under_5": 28376.0, "male_5_to_9": 26153.0, "male_10_to_14": 31329.0, "male_15_to_17": 17692.0, "male_18_to_19": 12994.0, "male_20": 5766.0, "male_21": 6683.0, "male_22_to_24": 17633.0, "male_25_to_29": 33658.0, "male_30_to_34": 30097.0, "male_35_to_39": 24686.0, "male_40_to_44": 29459.0, "male_45_to_49": 28586.0, "male_50_to_54": 31615.0, "male_55_to_59": 34245.0, "male_60_61": 10979.0, "male_62_64": 16352.0, "male_65_to_66": 12140.0, "male_67_to_69": 15715.0, "male_70_to_74": 18293.0, "male_75_to_79": 13435.0, "male_80_to_84": 7369.0, "male_85_and_over": 6977.0, "female_under_5": 26865.0, "female_5_to_9": 28563.0, "female_10_to_14": 27946.0, "female_15_to_17": 17313.0, "female_18_to_19": 12835.0, "female_20": 5768.0, "female_21": 5472.0, "female_22_to_24": 18130.0, "female_25_to_29": 33432.0, "female_30_to_34": 31382.0, "female_35_to_39": 26247.0, "female_40_to_44": 29626.0, "female_45_to_49": 31225.0, "female_50_to_54": 34437.0, "female_55_to_59": 35569.0, "female_60_to_61": 12718.0, "female_62_to_64": 21560.0, "female_65_to_66": 12928.0, "female_67_to_69": 18973.0, "female_70_to_74": 22241.0, "female_75_to_79": 17097.0, "female_80_to_84": 10293.0, "female_85_and_over": 11213.0, "white_including_hispanic": 659091.0, "black_including_hispanic": 209911.0, "amerindian_including_hispanic": 4055.0, "asian_including_hispanic": 35646.0, "commute_5_9_mins": 30136.0, "commute_35_39_mins": 9932.0, "commute_40_44_mins": 17291.0, "commute_60_89_mins": 22069.0, "commute_90_more_mins": 10414.0, "households_retirement_income": 83351.0, "armed_forces": 2529.0, "civilian_labor_force": 470999.0, "employed_pop": 444321.0, "unemployed_pop": 26678.0, "not_in_labor_force": 297531.0, "pop_16_over": 771059.0, "pop_in_labor_force": 473528.0, "asian_male_45_54": 2142.0, "asian_male_55_64": 1617.0, "black_male_45_54": 13397.0, "black_male_55_64": 11135.0, "hispanic_male_45_54": 3810.0, "hispanic_male_55_64": 2883.0, "white_male_45_54": 40459.0, "white_male_55_64": 45297.0, "bachelors_degree_2": 121717.0, "bachelors_degree_or_higher_25_64": 158440.0, "children": 204237.0, "children_in_single_female_hh": 59950.0, "commuters_by_bus": 10429.0, "commuters_by_car_truck_van": 395271.0, "commuters_by_carpool": 39457.0, "commuters_by_subway_or_elevated": 348.0, "commuters_drove_alone": 355814.0, "different_house_year_ago_different_city": 102768.0, "different_house_year_ago_same_city": 15779.0, "employed_agriculture_forestry_fishing_hunting_mining": 4001.0, "employed_arts_entertainment_recreation_accommodation_food": 40799.0, "employed_construction": 33546.0, "employed_education_health_social": 116136.0, "employed_finance_insurance_real_estate": 39722.0, "employed_information": 5639.0, "employed_manufacturing": 35022.0, "employed_other_services_not_public_admin": 18214.0, "employed_public_administration": 20816.0, "employed_retail_trade": 54049.0, "employed_science_management_admin_waste": 45030.0, "employed_transportation_warehousing_utilities": 21995.0, "employed_wholesale_trade": 9352.0, "female_female_households": 800.0, "four_more_cars": 20235.0, "gini_index": 0.4522, "graduate_professional_degree": 83352.0, "group_quarters": 25059.0, "high_school_including_ged": 212474.0, "households_public_asst_or_food_stamps": 42031.0, "in_grades_1_to_4": 42859.0, "in_grades_5_to_8": 47488.0, "in_grades_9_to_12": 47757.0, "in_school": 227224.0, "in_undergrad_college": 50404.0, "less_than_high_school_graduate": 70594.0, "male_45_64_associates_degree": 7443.0, "male_45_64_bachelors_degree": 20391.0, "male_45_64_graduate_degree": 15698.0, "male_45_64_less_than_9_grade": 3268.0, "male_45_64_grade_9_12": 9124.0, "male_45_64_high_school": 42762.0, "male_45_64_some_college": 23091.0, "male_45_to_64": 121777.0, "male_male_households": 705.0, "management_business_sci_arts_employed": 177213.0, "no_car": 10425.0, "no_cars": 20032.0, "not_us_citizen_pop": 45308.0, "occupation_management_arts": 177213.0, "occupation_natural_resources_construction_maintenance": 42046.0, "occupation_production_transportation_material": 47560.0, "occupation_sales_office": 99828.0, "occupation_services": 77674.0, "one_car": 123117.0, "two_cars": 140649.0, "three_cars": 47052.0, "pop_25_64": 495873.0, "pop_determined_poverty_status": 926864.0, "population_1_year_and_over": 942073.0, "population_3_years_over": 919219.0, "poverty": 108211.0, "sales_office_employed": 99828.0, "some_college_and_associates_degree": 174410.0, "walked_to_work": 8167.0, "worked_at_home": 19662.0, "workers_16_and_over": 439253.0, "associates_degree": 52228.0, "bachelors_degree": 121717.0, "high_school_diploma": 188694.0, "less_one_year_college": 44010.0, "masters_degree": 58672.0, "one_year_more_college": 78172.0, "pop_25_years_over": 662547.0, "commute_35_44_mins": 27223.0, "commute_60_more_mins": 32483.0, "commute_less_10_mins": 39222.0, "commuters_16_over": 419591.0, "hispanic_any_race": 87152.0, "pop_5_years_over": 896824.0, "speak_only_english_at_home": 780876.0, "speak_spanish_at_home": 64049.0, "speak_spanish_at_home_low_english": 23305.0, "do_date": "2016", "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN}, {"geo_id": "0645", "nonfamily_households": 80733.0, "family_households": 195974.0, "median_year_structure_built": 1985.0, "rent_burden_not_computed": 6364.0, "rent_over_50_percent": 28852.0, "rent_40_to_50_percent": 9173.0, "rent_35_to_40_percent": 7372.0, "rent_30_to_35_percent": 9748.0, "rent_25_to_30_percent": 11762.0, "rent_20_to_25_percent": 12423.0, "rent_15_to_20_percent": 9627.0, "rent_10_to_15_percent": 5791.0, "rent_under_10_percent": 1550.0, "total_pop": 774130.0, "male_pop": 383011.0, "female_pop": 391119.0, "median_age": 38.9, "white_pop": 392949.0, "black_pop": 11046.0, "asian_pop": 196721.0, "hispanic_pop": 140494.0, "amerindian_pop": 636.0, "other_race_pop": 1182.0, "two_or_more_races_pop": 28252.0, "not_hispanic_pop": 633636.0, "commuters_by_public_transportation": 5619.0, "households": 276707.0, "median_income": 97356.0, "income_per_capita": 48000.0, "housing_units": 290404.0, "vacant_housing_units": 13697.0, "vacant_housing_units_for_rent": 3766.0, "vacant_housing_units_for_sale": 1411.0, "median_rent": 1949.0, "percent_income_spent_on_rent": 33.6, "owner_occupied_housing_units": 174045.0, "million_dollar_housing_units": 15332.0, "mortgaged_housing_units": 127376.0, "families_with_young_children": 53545.0, "two_parent_families_with_young_children": 43785.0, "two_parents_in_labor_force_families_with_young_children": 25961.0, "two_parents_father_in_labor_force_families_with_young_children": 16173.0, "two_parents_mother_in_labor_force_families_with_young_children": 648.0, "two_parents_not_in_labor_force_families_with_young_children": 1003.0, "one_parent_families_with_young_children": 9760.0, "father_one_parent_families_with_young_children": 2890.0, "father_in_labor_force_one_parent_families_with_young_children": 2890.0, "commute_10_14_mins": 43458.0, "commute_15_19_mins": 61788.0, "commute_20_24_mins": 59026.0, "commute_25_29_mins": 28229.0, "commute_30_34_mins": 64480.0, "commute_45_59_mins": 21386.0, "aggregate_travel_time_to_work": 9276460.0, "income_less_10000": 13082.0, "income_10000_14999": 6065.0, "income_15000_19999": 5356.0, "income_20000_24999": 8319.0, "income_25000_29999": 6351.0, "income_30000_34999": 8943.0, "income_35000_39999": 6499.0, "income_40000_44999": 8257.0, "income_45000_49999": 6894.0, "income_50000_59999": 15335.0, "income_60000_74999": 23452.0, "income_75000_99999": 31818.0, "income_100000_124999": 32834.0, "income_125000_149999": 20989.0, "income_150000_199999": 32791.0, "income_200000_or_more": 49722.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 2079.0, "owner_occupied_housing_units_lower_value_quartile": 513100.0, "owner_occupied_housing_units_median_value": 685800.0, "owner_occupied_housing_units_upper_value_quartile": 903400.0, "married_households": 162005.0, "occupied_housing_units": 276707.0, "housing_units_renter_occupied": 102662.0, "dwellings_1_units_detached": 147972.0, "dwellings_1_units_attached": 47528.0, "dwellings_2_units": 3188.0, "dwellings_3_to_4_units": 15707.0, "dwellings_5_to_9_units": 22766.0, "dwellings_10_to_19_units": 14265.0, "dwellings_20_to_49_units": 9976.0, "dwellings_50_or_more_units": 25219.0, "mobile_homes": 3548.0, "housing_built_2005_or_later": 10336.0, "housing_built_2000_to_2004": 11062.0, "housing_built_1939_or_earlier": 1290.0, "male_under_5": 24751.0, "male_5_to_9": 22590.0, "male_10_to_14": 25270.0, "male_15_to_17": 15608.0, "male_18_to_19": 11612.0, "male_20": 6495.0, "male_21": 6937.0, "male_22_to_24": 13216.0, "male_25_to_29": 28977.0, "male_30_to_34": 25029.0, "male_35_to_39": 27918.0, "male_40_to_44": 24582.0, "male_45_to_49": 27001.0, "male_50_to_54": 29659.0, "male_55_to_59": 25218.0, "male_60_61": 9099.0, "male_62_64": 10752.0, "male_65_to_66": 6572.0, "male_67_to_69": 9876.0, "male_70_to_74": 11892.0, "male_75_to_79": 8181.0, "male_80_to_84": 6058.0, "male_85_and_over": 5718.0, "female_under_5": 20383.0, "female_5_to_9": 18564.0, "female_10_to_14": 25657.0, "female_15_to_17": 13376.0, "female_18_to_19": 13744.0, "female_20": 6889.0, "female_21": 3941.0, "female_22_to_24": 13196.0, "female_25_to_29": 21813.0, "female_30_to_34": 24735.0, "female_35_to_39": 28112.0, "female_40_to_44": 26420.0, "female_45_to_49": 32315.0, "female_50_to_54": 30225.0, "female_55_to_59": 25215.0, "female_60_to_61": 9011.0, "female_62_to_64": 14165.0, "female_65_to_66": 8014.0, "female_67_to_69": 12128.0, "female_70_to_74": 14179.0, "female_75_to_79": 11290.0, "female_80_to_84": 6658.0, "female_85_and_over": 11089.0, "white_including_hispanic": 486619.0, "black_including_hispanic": 12535.0, "amerindian_including_hispanic": 1821.0, "asian_including_hispanic": 198172.0, "commute_5_9_mins": 25511.0, "commute_35_39_mins": 10016.0, "commute_40_44_mins": 14503.0, "commute_60_89_mins": 15069.0, "commute_90_more_mins": 8961.0, "households_retirement_income": 39372.0, "armed_forces": 0.0, "civilian_labor_force": 413296.0, "employed_pop": 392853.0, "unemployed_pop": 20443.0, "not_in_labor_force": 216700.0, "pop_16_over": 629996.0, "pop_in_labor_force": 413296.0, "asian_male_45_54": 13996.0, "asian_male_55_64": 8956.0, "black_male_45_54": 1008.0, "black_male_55_64": 878.0, "hispanic_male_45_54": 8376.0, "hispanic_male_55_64": 4413.0, "white_male_45_54": 31887.0, "white_male_55_64": 29785.0, "bachelors_degree_2": 181876.0, "bachelors_degree_or_higher_25_64": 244112.0, "children": 166199.0, "children_in_single_female_hh": 21935.0, "commuters_by_bus": 3798.0, "commuters_by_car_truck_van": 335453.0, "commuters_by_carpool": 28246.0, "commuters_by_subway_or_elevated": 259.0, "commuters_drove_alone": 307207.0, "different_house_year_ago_different_city": 76505.0, "different_house_year_ago_same_city": 32841.0, "employed_agriculture_forestry_fishing_hunting_mining": 1990.0, "employed_arts_entertainment_recreation_accommodation_food": 36639.0, "employed_construction": 12950.0, "employed_education_health_social": 81159.0, "employed_finance_insurance_real_estate": 42837.0, "employed_information": 9273.0, "employed_manufacturing": 45871.0, "employed_other_services_not_public_admin": 19663.0, "employed_public_administration": 11943.0, "employed_retail_trade": 36788.0, "employed_science_management_admin_waste": 67091.0, "employed_transportation_warehousing_utilities": 10587.0, "employed_wholesale_trade": 16062.0, "female_female_households": 191.0, "four_more_cars": 21466.0, "gini_index": 0.4698, "graduate_professional_degree": 115749.0, "group_quarters": 12446.0, "high_school_including_ged": 66345.0, "households_public_asst_or_food_stamps": 9594.0, "in_grades_1_to_4": 32380.0, "in_grades_5_to_8": 38131.0, "in_grades_9_to_12": 41410.0, "in_school": 220862.0, "in_undergrad_college": 66541.0, "less_than_high_school_graduate": 30061.0, "male_45_64_associates_degree": 7081.0, "male_45_64_bachelors_degree": 34994.0, "male_45_64_graduate_degree": 27289.0, "male_45_64_less_than_9_grade": 1583.0, "male_45_64_grade_9_12": 2434.0, "male_45_64_high_school": 10609.0, "male_45_64_some_college": 17739.0, "male_45_to_64": 101729.0, "male_male_households": 191.0, "management_business_sci_arts_employed": 213916.0, "no_car": 3868.0, "no_cars": 9806.0, "not_us_citizen_pop": 94153.0, "occupation_management_arts": 213916.0, "occupation_natural_resources_construction_maintenance": 15016.0, "occupation_production_transportation_material": 21781.0, "occupation_sales_office": 92105.0, "occupation_services": 50035.0, "one_car": 76601.0, "two_cars": 122538.0, "three_cars": 46296.0, "pop_25_64": 420246.0, "pop_determined_poverty_status": 764816.0, "population_1_year_and_over": 765112.0, "population_3_years_over": 748406.0, "poverty": 67189.0, "sales_office_employed": 92105.0, "some_college_and_associates_degree": 137870.0, "walked_to_work": 8102.0, "worked_at_home": 28001.0, "workers_16_and_over": 384489.0, "associates_degree": 46596.0, "bachelors_degree": 181876.0, "high_school_diploma": 59597.0, "less_one_year_college": 21862.0, "masters_degree": 78142.0, "one_year_more_college": 69412.0, "pop_25_years_over": 531901.0, "commute_35_44_mins": 24519.0, "commute_60_more_mins": 24030.0, "commute_less_10_mins": 29572.0, "commuters_16_over": 356488.0, "hispanic_any_race": 140494.0, "pop_5_years_over": 728996.0, "speak_only_english_at_home": 456359.0, "speak_spanish_at_home": 87890.0, "speak_spanish_at_home_low_english": 25916.0, "do_date": "2016", "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN}, {"geo_id": "0625", "nonfamily_households": 55482.0, "family_households": 169727.0, "median_year_structure_built": 1985.0, "rent_burden_not_computed": 5632.0, "rent_over_50_percent": 22229.0, "rent_40_to_50_percent": 7446.0, "rent_35_to_40_percent": 6321.0, "rent_30_to_35_percent": 6529.0, "rent_25_to_30_percent": 8835.0, "rent_20_to_25_percent": 7984.0, "rent_15_to_20_percent": 7257.0, "rent_10_to_15_percent": 4417.0, "rent_under_10_percent": 1352.0, "total_pop": 728858.0, "male_pop": 364529.0, "female_pop": 364329.0, "median_age": 36.3, "white_pop": 309452.0, "black_pop": 49931.0, "asian_pop": 58339.0, "hispanic_pop": 284066.0, "amerindian_pop": 1478.0, "other_race_pop": 647.0, "two_or_more_races_pop": 24560.0, "not_hispanic_pop": 444792.0, "commuters_by_public_transportation": 6386.0, "households": 225209.0, "median_income": 75367.0, "income_per_capita": 31565.0, "housing_units": 238192.0, "vacant_housing_units": 12983.0, "vacant_housing_units_for_rent": 3407.0, "vacant_housing_units_for_sale": 1246.0, "median_rent": 1292.0, "percent_income_spent_on_rent": 34.9, "owner_occupied_housing_units": 147207.0, "million_dollar_housing_units": 3408.0, "mortgaged_housing_units": 116249.0, "families_with_young_children": 56781.0, "two_parent_families_with_young_children": 37074.0, "two_parents_in_labor_force_families_with_young_children": 17555.0, "two_parents_father_in_labor_force_families_with_young_children": 16828.0, "two_parents_mother_in_labor_force_families_with_young_children": 1755.0, "two_parents_not_in_labor_force_families_with_young_children": 936.0, "one_parent_families_with_young_children": 19707.0, "father_one_parent_families_with_young_children": 3529.0, "father_in_labor_force_one_parent_families_with_young_children": 3263.0, "commute_10_14_mins": 36020.0, "commute_15_19_mins": 38288.0, "commute_20_24_mins": 32018.0, "commute_25_29_mins": 11571.0, "commute_30_34_mins": 28997.0, "commute_45_59_mins": 37295.0, "aggregate_travel_time_to_work": 10975040.0, "income_less_10000": 12258.0, "income_10000_14999": 7538.0, "income_15000_19999": 8463.0, "income_20000_24999": 8538.0, "income_25000_29999": 6927.0, "income_30000_34999": 8286.0, "income_35000_39999": 7657.0, "income_40000_44999": 11850.0, "income_45000_49999": 7050.0, "income_50000_59999": 13122.0, "income_60000_74999": 20270.0, "income_75000_99999": 29418.0, "income_100000_124999": 23760.0, "income_125000_149999": 17295.0, "income_150000_199999": 19708.0, "income_200000_or_more": 23069.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 1445.0, "owner_occupied_housing_units_lower_value_quartile": 263400.0, "owner_occupied_housing_units_median_value": 435200.0, "owner_occupied_housing_units_upper_value_quartile": 627600.0, "married_households": 124313.0, "occupied_housing_units": 225209.0, "housing_units_renter_occupied": 78002.0, "dwellings_1_units_detached": 168448.0, "dwellings_1_units_attached": 12605.0, "dwellings_2_units": 1096.0, "dwellings_3_to_4_units": 9176.0, "dwellings_5_to_9_units": 12742.0, "dwellings_10_to_19_units": 8111.0, "dwellings_20_to_49_units": 4371.0, "dwellings_50_or_more_units": 10299.0, "mobile_homes": 11133.0, "housing_built_2005_or_later": 1047.0, "housing_built_2000_to_2004": 3274.0, "housing_built_1939_or_earlier": 1831.0, "male_under_5": 25493.0, "male_5_to_9": 28665.0, "male_10_to_14": 26265.0, "male_15_to_17": 15867.0, "male_18_to_19": 11349.0, "male_20": 6034.0, "male_21": 5607.0, "male_22_to_24": 15689.0, "male_25_to_29": 26723.0, "male_30_to_34": 20957.0, "male_35_to_39": 22356.0, "male_40_to_44": 21672.0, "male_45_to_49": 26184.0, "male_50_to_54": 26717.0, "male_55_to_59": 26277.0, "male_60_61": 8904.0, "male_62_64": 11747.0, "male_65_to_66": 6365.0, "male_67_to_69": 9234.0, "male_70_to_74": 9015.0, "male_75_to_79": 6360.0, "male_80_to_84": 3298.0, "male_85_and_over": 3751.0, "female_under_5": 23307.0, "female_5_to_9": 25415.0, "female_10_to_14": 25797.0, "female_15_to_17": 17234.0, "female_18_to_19": 9488.0, "female_20": 5096.0, "female_21": 4127.0, "female_22_to_24": 13480.0, "female_25_to_29": 24333.0, "female_30_to_34": 20776.0, "female_35_to_39": 21828.0, "female_40_to_44": 23251.0, "female_45_to_49": 28143.0, "female_50_to_54": 29962.0, "female_55_to_59": 26054.0, "female_60_to_61": 8865.0, "female_62_to_64": 11528.0, "female_65_to_66": 7477.0, "female_67_to_69": 8922.0, "female_70_to_74": 10358.0, "female_75_to_79": 6955.0, "female_80_to_84": 6155.0, "female_85_and_over": 5778.0, "white_including_hispanic": 470038.0, "black_including_hispanic": 50867.0, "amerindian_including_hispanic": 5258.0, "asian_including_hispanic": 59730.0, "commute_5_9_mins": 19617.0, "commute_35_39_mins": 7975.0, "commute_40_44_mins": 13929.0, "commute_60_89_mins": 46666.0, "commute_90_more_mins": 23469.0, "households_retirement_income": 35725.0, "armed_forces": 358.0, "civilian_labor_force": 350747.0, "employed_pop": 328318.0, "unemployed_pop": 22429.0, "not_in_labor_force": 213331.0, "pop_16_over": 564436.0, "pop_in_labor_force": 351105.0, "asian_male_45_54": 4411.0, "asian_male_55_64": 3528.0, "black_male_45_54": 3341.0, "black_male_55_64": 2829.0, "hispanic_male_45_54": 18862.0, "hispanic_male_55_64": 12224.0, "white_male_45_54": 25251.0, "white_male_55_64": 27409.0, "bachelors_degree_2": 85760.0, "bachelors_degree_or_higher_25_64": 111090.0, "children": 188043.0, "children_in_single_female_hh": 41451.0, "commuters_by_bus": 3870.0, "commuters_by_car_truck_van": 287467.0, "commuters_by_carpool": 35719.0, "commuters_by_subway_or_elevated": 77.0, "commuters_drove_alone": 251748.0, "different_house_year_ago_different_city": 56639.0, "different_house_year_ago_same_city": 21574.0, "employed_agriculture_forestry_fishing_hunting_mining": 2476.0, "employed_arts_entertainment_recreation_accommodation_food": 29359.0, "employed_construction": 20673.0, "employed_education_health_social": 75133.0, "employed_finance_insurance_real_estate": 22473.0, "employed_information": 14155.0, "employed_manufacturing": 34495.0, "employed_other_services_not_public_admin": 17482.0, "employed_public_administration": 17123.0, "employed_retail_trade": 34135.0, "employed_science_management_admin_waste": 35758.0, "employed_transportation_warehousing_utilities": 16686.0, "employed_wholesale_trade": 8370.0, "female_female_households": 402.0, "four_more_cars": 25291.0, "gini_index": 0.4555, "graduate_professional_degree": 44830.0, "group_quarters": 10079.0, "high_school_including_ged": 102501.0, "households_public_asst_or_food_stamps": 19449.0, "in_grades_1_to_4": 41264.0, "in_grades_5_to_8": 42498.0, "in_grades_9_to_12": 47917.0, "in_school": 209633.0, "in_undergrad_college": 49262.0, "less_than_high_school_graduate": 73492.0, "male_45_64_associates_degree": 8289.0, "male_45_64_bachelors_degree": 17219.0, "male_45_64_graduate_degree": 9893.0, "male_45_64_less_than_9_grade": 7858.0, "male_45_64_grade_9_12": 8604.0, "male_45_64_high_school": 23333.0, "male_45_64_some_college": 24633.0, "male_45_to_64": 99829.0, "male_male_households": 264.0, "management_business_sci_arts_employed": 127614.0, "no_car": 4060.0, "no_cars": 9863.0, "not_us_citizen_pop": 57627.0, "occupation_management_arts": 127614.0, "occupation_natural_resources_construction_maintenance": 26350.0, "occupation_production_transportation_material": 39222.0, "occupation_sales_office": 74580.0, "occupation_services": 60552.0, "one_car": 55974.0, "two_cars": 90474.0, "three_cars": 43607.0, "pop_25_64": 386277.0, "pop_determined_poverty_status": 717433.0, "population_1_year_and_over": 720645.0, "population_3_years_over": 701456.0, "poverty": 90881.0, "sales_office_employed": 74580.0, "some_college_and_associates_degree": 163362.0, "walked_to_work": 2172.0, "worked_at_home": 21169.0, "workers_16_and_over": 320823.0, "associates_degree": 44329.0, "bachelors_degree": 85760.0, "high_school_diploma": 89936.0, "less_one_year_college": 31493.0, "masters_degree": 33124.0, "one_year_more_college": 87540.0, "pop_25_years_over": 469945.0, "commute_35_44_mins": 21904.0, "commute_60_more_mins": 70135.0, "commute_less_10_mins": 23426.0, "commuters_16_over": 299654.0, "hispanic_any_race": 284066.0, "pop_5_years_over": 680058.0, "speak_only_english_at_home": 467417.0, "speak_spanish_at_home": 155480.0, "speak_spanish_at_home_low_english": 64968.0, "do_date": "2016", "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN}, {"geo_id": "1221", "nonfamily_households": 106923.0, "family_households": 175158.0, "median_year_structure_built": 1986.0, "rent_burden_not_computed": 4656.0, "rent_over_50_percent": 25737.0, "rent_40_to_50_percent": 8135.0, "rent_35_to_40_percent": 7029.0, "rent_30_to_35_percent": 7056.0, "rent_25_to_30_percent": 9800.0, "rent_20_to_25_percent": 9965.0, "rent_15_to_20_percent": 7910.0, "rent_10_to_15_percent": 4479.0, "rent_under_10_percent": 1599.0, "total_pop": 758201.0, "male_pop": 362578.0, "female_pop": 395623.0, "median_age": 45.4, "white_pop": 429305.0, "black_pop": 119893.0, "asian_pop": 17914.0, "hispanic_pop": 177488.0, "amerindian_pop": 710.0, "other_race_pop": 2035.0, "two_or_more_races_pop": 10551.0, "not_hispanic_pop": 580713.0, "commuters_by_public_transportation": 6394.0, "households": 282081.0, "median_income": 55864.0, "income_per_capita": 34037.0, "housing_units": 360035.0, "vacant_housing_units": 77954.0, "vacant_housing_units_for_rent": 7127.0, "vacant_housing_units_for_sale": 5002.0, "median_rent": 1189.0, "percent_income_spent_on_rent": 35.0, "owner_occupied_housing_units": 195715.0, "million_dollar_housing_units": 3065.0, "mortgaged_housing_units": 103485.0, "families_with_young_children": 43822.0, "two_parent_families_with_young_children": 29802.0, "two_parents_in_labor_force_families_with_young_children": 17685.0, "two_parents_father_in_labor_force_families_with_young_children": 11117.0, "two_parents_mother_in_labor_force_families_with_young_children": 832.0, "two_parents_not_in_labor_force_families_with_young_children": 168.0, "one_parent_families_with_young_children": 14020.0, "father_one_parent_families_with_young_children": 3205.0, "father_in_labor_force_one_parent_families_with_young_children": 3142.0, "commute_10_14_mins": 38800.0, "commute_15_19_mins": 50379.0, "commute_20_24_mins": 54250.0, "commute_25_29_mins": 21415.0, "commute_30_34_mins": 61324.0, "commute_45_59_mins": 23234.0, "aggregate_travel_time_to_work": 8188600.0, "income_less_10000": 16554.0, "income_10000_14999": 11946.0, "income_15000_19999": 15092.0, "income_20000_24999": 15555.0, "income_25000_29999": 15591.0, "income_30000_34999": 13950.0, "income_35000_39999": 13522.0, "income_40000_44999": 14328.0, "income_45000_49999": 9511.0, "income_50000_59999": 22851.0, "income_60000_74999": 30323.0, "income_75000_99999": 31356.0, "income_100000_124999": 22966.0, "income_125000_149999": 14304.0, "income_150000_199999": 14466.0, "income_200000_or_more": 19766.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 1344.0, "owner_occupied_housing_units_lower_value_quartile": 139800.0, "owner_occupied_housing_units_median_value": 252300.0, "owner_occupied_housing_units_upper_value_quartile": 383700.0, "married_households": 133752.0, "occupied_housing_units": 282081.0, "housing_units_renter_occupied": 86366.0, "dwellings_1_units_detached": 162082.0, "dwellings_1_units_attached": 42219.0, "dwellings_2_units": 9302.0, "dwellings_3_to_4_units": 23987.0, "dwellings_5_to_9_units": 22643.0, "dwellings_10_to_19_units": 22728.0, "dwellings_20_to_49_units": 32937.0, "dwellings_50_or_more_units": 30827.0, "mobile_homes": 13093.0, "housing_built_2005_or_later": 4760.0, "housing_built_2000_to_2004": 7669.0, "housing_built_1939_or_earlier": 4627.0, "male_under_5": 19202.0, "male_5_to_9": 21534.0, "male_10_to_14": 19504.0, "male_15_to_17": 14556.0, "male_18_to_19": 7477.0, "male_20": 3527.0, "male_21": 3385.0, "male_22_to_24": 12947.0, "male_25_to_29": 21738.0, "male_30_to_34": 22401.0, "male_35_to_39": 20912.0, "male_40_to_44": 20014.0, "male_45_to_49": 24656.0, "male_50_to_54": 24946.0, "male_55_to_59": 21592.0, "male_60_61": 9256.0, "male_62_64": 12111.0, "male_65_to_66": 8741.0, "male_67_to_69": 12707.0, "male_70_to_74": 19091.0, "male_75_to_79": 16721.0, "male_80_to_84": 11039.0, "male_85_and_over": 14521.0, "female_under_5": 19118.0, "female_5_to_9": 18783.0, "female_10_to_14": 19638.0, "female_15_to_17": 13052.0, "female_18_to_19": 8360.0, "female_20": 3556.0, "female_21": 3291.0, "female_22_to_24": 12010.0, "female_25_to_29": 22083.0, "female_30_to_34": 22302.0, "female_35_to_39": 22085.0, "female_40_to_44": 23083.0, "female_45_to_49": 23689.0, "female_50_to_54": 25463.0, "female_55_to_59": 26916.0, "female_60_to_61": 10690.0, "female_62_to_64": 13615.0, "female_65_to_66": 10933.0, "female_67_to_69": 15060.0, "female_70_to_74": 24004.0, "female_75_to_79": 19637.0, "female_80_to_84": 16395.0, "female_85_and_over": 21860.0, "white_including_hispanic": 582382.0, "black_including_hispanic": 122644.0, "amerindian_including_hispanic": 911.0, "asian_including_hispanic": 17996.0, "commute_5_9_mins": 23177.0, "commute_35_39_mins": 8963.0, "commute_40_44_mins": 12675.0, "commute_60_89_mins": 14946.0, "commute_90_more_mins": 5466.0, "households_retirement_income": 55431.0, "armed_forces": 109.0, "civilian_labor_force": 369819.0, "employed_pop": 345838.0, "unemployed_pop": 23981.0, "not_in_labor_force": 260812.0, "pop_16_over": 630740.0, "pop_in_labor_force": 369928.0, "asian_male_45_54": 1002.0, "asian_male_55_64": 1169.0, "black_male_45_54": 8851.0, "black_male_55_64": 4817.0, "hispanic_male_45_54": 13476.0, "hispanic_male_55_64": 7497.0, "white_male_45_54": 26183.0, "white_male_55_64": 29000.0, "bachelors_degree_2": 119505.0, "bachelors_degree_or_higher_25_64": 121535.0, "children": 145387.0, "children_in_single_female_hh": 34824.0, "commuters_by_bus": 5243.0, "commuters_by_car_truck_van": 300992.0, "commuters_by_carpool": 33558.0, "commuters_by_subway_or_elevated": 29.0, "commuters_drove_alone": 267434.0, "different_house_year_ago_different_city": 91342.0, "different_house_year_ago_same_city": 15622.0, "employed_agriculture_forestry_fishing_hunting_mining": 948.0, "employed_arts_entertainment_recreation_accommodation_food": 41301.0, "employed_construction": 30785.0, "employed_education_health_social": 73208.0, "employed_finance_insurance_real_estate": 24099.0, "employed_information": 6718.0, "employed_manufacturing": 13137.0, "employed_other_services_not_public_admin": 22197.0, "employed_public_administration": 10981.0, "employed_retail_trade": 44533.0, "employed_science_management_admin_waste": 56882.0, "employed_transportation_warehousing_utilities": 13618.0, "employed_wholesale_trade": 7431.0, "female_female_households": 373.0, "four_more_cars": 7840.0, "gini_index": 0.5101, "graduate_professional_degree": 71812.0, "group_quarters": 6262.0, "high_school_including_ged": 137908.0, "households_public_asst_or_food_stamps": 26861.0, "in_grades_1_to_4": 33067.0, "in_grades_5_to_8": 31004.0, "in_grades_9_to_12": 37644.0, "in_school": 161934.0, "in_undergrad_college": 34631.0, "less_than_high_school_graduate": 74184.0, "male_45_64_associates_degree": 6594.0, "male_45_64_bachelors_degree": 20377.0, "male_45_64_graduate_degree": 10929.0, "male_45_64_less_than_9_grade": 5719.0, "male_45_64_grade_9_12": 7491.0, "male_45_64_high_school": 23948.0, "male_45_64_some_college": 17503.0, "male_45_to_64": 92561.0, "male_male_households": 539.0, "management_business_sci_arts_employed": 116984.0, "no_car": 9108.0, "no_cars": 17119.0, "not_us_citizen_pop": 89599.0, "occupation_management_arts": 116984.0, "occupation_natural_resources_construction_maintenance": 33043.0, "occupation_production_transportation_material": 23750.0, "occupation_sales_office": 87927.0, "occupation_services": 84134.0, "one_car": 120345.0, "two_cars": 109840.0, "three_cars": 26937.0, "pop_25_64": 367552.0, "pop_determined_poverty_status": 753002.0, "population_1_year_and_over": 752266.0, "population_3_years_over": 735120.0, "poverty": 83569.0, "sales_office_employed": 87927.0, "some_college_and_associates_degree": 154852.0, "walked_to_work": 4392.0, "worked_at_home": 21733.0, "workers_16_and_over": 340504.0, "associates_degree": 50008.0, "bachelors_degree": 119505.0, "high_school_diploma": 122676.0, "less_one_year_college": 31912.0, "masters_degree": 48872.0, "one_year_more_college": 72932.0, "pop_25_years_over": 558261.0, "commute_35_44_mins": 21638.0, "commute_60_more_mins": 20412.0, "commute_less_10_mins": 27319.0, "commuters_16_over": 318771.0, "hispanic_any_race": 177488.0, "pop_5_years_over": 719881.0, "speak_only_english_at_home": 471491.0, "speak_spanish_at_home": 146109.0, "speak_spanish_at_home_low_english": 61281.0, "do_date": "2016", "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN}]}