{"table_name": "congressionaldistrict_2017_1yr", "table_fullname": "bigquery-public-data.census_bureau_acs.congressionaldistrict_2017_1yr", "column_names": ["geo_id", "nonfamily_households", "family_households", "median_year_structure_built", "rent_burden_not_computed", "rent_over_50_percent", "rent_40_to_50_percent", "rent_35_to_40_percent", "rent_30_to_35_percent", "rent_25_to_30_percent", "rent_20_to_25_percent", "rent_15_to_20_percent", "rent_10_to_15_percent", "rent_under_10_percent", "total_pop", "male_pop", "female_pop", "median_age", "white_pop", "black_pop", "asian_pop", "hispanic_pop", "amerindian_pop", "other_race_pop", "two_or_more_races_pop", "not_hispanic_pop", "commuters_by_public_transportation", "households", "median_income", "income_per_capita", "housing_units", "vacant_housing_units", "vacant_housing_units_for_rent", "vacant_housing_units_for_sale", "median_rent", "percent_income_spent_on_rent", "owner_occupied_housing_units", "million_dollar_housing_units", "mortgaged_housing_units", "families_with_young_children", "two_parent_families_with_young_children", "two_parents_in_labor_force_families_with_young_children", "two_parents_father_in_labor_force_families_with_young_children", "two_parents_mother_in_labor_force_families_with_young_children", "two_parents_not_in_labor_force_families_with_young_children", "one_parent_families_with_young_children", "father_one_parent_families_with_young_children", "father_in_labor_force_one_parent_families_with_young_children", "commute_10_14_mins", "commute_15_19_mins", "commute_20_24_mins", "commute_25_29_mins", "commute_30_34_mins", "commute_45_59_mins", "aggregate_travel_time_to_work", "income_less_10000", "income_10000_14999", "income_15000_19999", "income_20000_24999", "income_25000_29999", "income_30000_34999", "income_35000_39999", "income_40000_44999", "income_45000_49999", "income_50000_59999", "income_60000_74999", "income_75000_99999", "income_100000_124999", "income_125000_149999", "income_150000_199999", "income_200000_or_more", "renter_occupied_housing_units_paying_cash_median_gross_rent", "owner_occupied_housing_units_lower_value_quartile", "owner_occupied_housing_units_median_value", "owner_occupied_housing_units_upper_value_quartile", "married_households", "occupied_housing_units", "housing_units_renter_occupied", "dwellings_1_units_detached", "dwellings_1_units_attached", "dwellings_2_units", "dwellings_3_to_4_units", "dwellings_5_to_9_units", "dwellings_10_to_19_units", "dwellings_20_to_49_units", "dwellings_50_or_more_units", "mobile_homes", "housing_built_2005_or_later", "housing_built_2000_to_2004", "housing_built_1939_or_earlier", "male_under_5", "male_5_to_9", "male_10_to_14", "male_15_to_17", "male_18_to_19", "male_20", "male_21", "male_22_to_24", "male_25_to_29", "male_30_to_34", "male_35_to_39", "male_40_to_44", "male_45_to_49", "male_50_to_54", "male_55_to_59", "male_60_61", "male_62_64", "male_65_to_66", "male_67_to_69", "male_70_to_74", "male_75_to_79", "male_80_to_84", "male_85_and_over", "female_under_5", "female_5_to_9", "female_10_to_14", "female_15_to_17", "female_18_to_19", "female_20", "female_21", "female_22_to_24", "female_25_to_29", "female_30_to_34", "female_35_to_39", "female_40_to_44", "female_45_to_49", "female_50_to_54", "female_55_to_59", "female_60_to_61", "female_62_to_64", "female_65_to_66", "female_67_to_69", "female_70_to_74", "female_75_to_79", "female_80_to_84", "female_85_and_over", "white_including_hispanic", "black_including_hispanic", "amerindian_including_hispanic", "asian_including_hispanic", "commute_5_9_mins", "commute_35_39_mins", "commute_40_44_mins", "commute_60_89_mins", "commute_90_more_mins", "households_retirement_income", "armed_forces", "civilian_labor_force", "employed_pop", "unemployed_pop", "not_in_labor_force", "pop_16_over", "pop_in_labor_force", "asian_male_45_54", "asian_male_55_64", "black_male_45_54", "black_male_55_64", "hispanic_male_45_54", "hispanic_male_55_64", "white_male_45_54", "white_male_55_64", "bachelors_degree_2", "bachelors_degree_or_higher_25_64", "children", "children_in_single_female_hh", "commuters_by_bus", "commuters_by_car_truck_van", "commuters_by_carpool", "commuters_by_subway_or_elevated", "commuters_drove_alone", "different_house_year_ago_different_city", "different_house_year_ago_same_city", "employed_agriculture_forestry_fishing_hunting_mining", "employed_arts_entertainment_recreation_accommodation_food", "employed_construction", "employed_education_health_social", "employed_finance_insurance_real_estate", "employed_information", "employed_manufacturing", "employed_other_services_not_public_admin", "employed_public_administration", "employed_retail_trade", "employed_science_management_admin_waste", "employed_transportation_warehousing_utilities", "employed_wholesale_trade", "female_female_households", "four_more_cars", "gini_index", "graduate_professional_degree", "group_quarters", "high_school_including_ged", "households_public_asst_or_food_stamps", "in_grades_1_to_4", "in_grades_5_to_8", "in_grades_9_to_12", "in_school", "in_undergrad_college", "less_than_high_school_graduate", "male_45_64_associates_degree", "male_45_64_bachelors_degree", "male_45_64_graduate_degree", "male_45_64_less_than_9_grade", "male_45_64_grade_9_12", "male_45_64_high_school", "male_45_64_some_college", "male_45_to_64", "male_male_households", "management_business_sci_arts_employed", "no_car", "no_cars", "not_us_citizen_pop", "occupation_management_arts", "occupation_natural_resources_construction_maintenance", "occupation_production_transportation_material", "occupation_sales_office", "occupation_services", "one_car", "two_cars", "three_cars", "pop_25_64", "pop_determined_poverty_status", "population_1_year_and_over", "population_3_years_over", "poverty", "sales_office_employed", "some_college_and_associates_degree", "walked_to_work", "worked_at_home", "workers_16_and_over", "associates_degree", "bachelors_degree", "high_school_diploma", "less_one_year_college", "masters_degree", "one_year_more_college", "pop_25_years_over", "commute_35_44_mins", "commute_60_more_mins", "commute_less_10_mins", "commuters_16_over", "hispanic_any_race", "pop_5_years_over", "speak_only_english_at_home", "speak_spanish_at_home", "speak_spanish_at_home_low_english", "pop_15_and_over", "pop_never_married", "pop_now_married", "pop_separated", "pop_widowed", "pop_divorced", "do_date"], "column_types": ["STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "STRING"], "nested_column_names": ["geo_id", "nonfamily_households", "family_households", "median_year_structure_built", "rent_burden_not_computed", "rent_over_50_percent", "rent_40_to_50_percent", "rent_35_to_40_percent", "rent_30_to_35_percent", "rent_25_to_30_percent", "rent_20_to_25_percent", "rent_15_to_20_percent", "rent_10_to_15_percent", "rent_under_10_percent", "total_pop", "male_pop", "female_pop", "median_age", "white_pop", "black_pop", "asian_pop", "hispanic_pop", "amerindian_pop", "other_race_pop", "two_or_more_races_pop", "not_hispanic_pop", "commuters_by_public_transportation", "households", "median_income", "income_per_capita", "housing_units", "vacant_housing_units", "vacant_housing_units_for_rent", "vacant_housing_units_for_sale", "median_rent", "percent_income_spent_on_rent", "owner_occupied_housing_units", "million_dollar_housing_units", "mortgaged_housing_units", "families_with_young_children", "two_parent_families_with_young_children", "two_parents_in_labor_force_families_with_young_children", "two_parents_father_in_labor_force_families_with_young_children", "two_parents_mother_in_labor_force_families_with_young_children", "two_parents_not_in_labor_force_families_with_young_children", "one_parent_families_with_young_children", "father_one_parent_families_with_young_children", "father_in_labor_force_one_parent_families_with_young_children", "commute_10_14_mins", "commute_15_19_mins", "commute_20_24_mins", "commute_25_29_mins", "commute_30_34_mins", "commute_45_59_mins", "aggregate_travel_time_to_work", "income_less_10000", "income_10000_14999", "income_15000_19999", "income_20000_24999", "income_25000_29999", "income_30000_34999", "income_35000_39999", "income_40000_44999", "income_45000_49999", "income_50000_59999", "income_60000_74999", "income_75000_99999", "income_100000_124999", "income_125000_149999", "income_150000_199999", "income_200000_or_more", "renter_occupied_housing_units_paying_cash_median_gross_rent", "owner_occupied_housing_units_lower_value_quartile", "owner_occupied_housing_units_median_value", "owner_occupied_housing_units_upper_value_quartile", "married_households", "occupied_housing_units", "housing_units_renter_occupied", "dwellings_1_units_detached", "dwellings_1_units_attached", "dwellings_2_units", "dwellings_3_to_4_units", "dwellings_5_to_9_units", "dwellings_10_to_19_units", "dwellings_20_to_49_units", "dwellings_50_or_more_units", "mobile_homes", "housing_built_2005_or_later", "housing_built_2000_to_2004", "housing_built_1939_or_earlier", "male_under_5", "male_5_to_9", "male_10_to_14", "male_15_to_17", "male_18_to_19", "male_20", "male_21", "male_22_to_24", "male_25_to_29", "male_30_to_34", "male_35_to_39", "male_40_to_44", "male_45_to_49", "male_50_to_54", "male_55_to_59", "male_60_61", "male_62_64", "male_65_to_66", "male_67_to_69", "male_70_to_74", "male_75_to_79", "male_80_to_84", "male_85_and_over", "female_under_5", "female_5_to_9", "female_10_to_14", "female_15_to_17", "female_18_to_19", "female_20", "female_21", "female_22_to_24", "female_25_to_29", "female_30_to_34", "female_35_to_39", "female_40_to_44", "female_45_to_49", "female_50_to_54", "female_55_to_59", "female_60_to_61", "female_62_to_64", "female_65_to_66", "female_67_to_69", "female_70_to_74", "female_75_to_79", "female_80_to_84", "female_85_and_over", "white_including_hispanic", "black_including_hispanic", "amerindian_including_hispanic", "asian_including_hispanic", "commute_5_9_mins", "commute_35_39_mins", "commute_40_44_mins", "commute_60_89_mins", "commute_90_more_mins", "households_retirement_income", "armed_forces", "civilian_labor_force", "employed_pop", "unemployed_pop", "not_in_labor_force", "pop_16_over", "pop_in_labor_force", "asian_male_45_54", "asian_male_55_64", "black_male_45_54", "black_male_55_64", "hispanic_male_45_54", "hispanic_male_55_64", "white_male_45_54", "white_male_55_64", "bachelors_degree_2", "bachelors_degree_or_higher_25_64", "children", "children_in_single_female_hh", "commuters_by_bus", "commuters_by_car_truck_van", "commuters_by_carpool", "commuters_by_subway_or_elevated", "commuters_drove_alone", "different_house_year_ago_different_city", "different_house_year_ago_same_city", "employed_agriculture_forestry_fishing_hunting_mining", "employed_arts_entertainment_recreation_accommodation_food", "employed_construction", "employed_education_health_social", "employed_finance_insurance_real_estate", "employed_information", "employed_manufacturing", "employed_other_services_not_public_admin", "employed_public_administration", "employed_retail_trade", "employed_science_management_admin_waste", "employed_transportation_warehousing_utilities", "employed_wholesale_trade", "female_female_households", "four_more_cars", "gini_index", "graduate_professional_degree", "group_quarters", "high_school_including_ged", "households_public_asst_or_food_stamps", "in_grades_1_to_4", "in_grades_5_to_8", "in_grades_9_to_12", "in_school", "in_undergrad_college", "less_than_high_school_graduate", "male_45_64_associates_degree", "male_45_64_bachelors_degree", "male_45_64_graduate_degree", "male_45_64_less_than_9_grade", "male_45_64_grade_9_12", "male_45_64_high_school", "male_45_64_some_college", "male_45_to_64", "male_male_households", "management_business_sci_arts_employed", "no_car", "no_cars", "not_us_citizen_pop", "occupation_management_arts", "occupation_natural_resources_construction_maintenance", "occupation_production_transportation_material", "occupation_sales_office", "occupation_services", "one_car", "two_cars", "three_cars", "pop_25_64", "pop_determined_poverty_status", "population_1_year_and_over", "population_3_years_over", "poverty", "sales_office_employed", "some_college_and_associates_degree", "walked_to_work", "worked_at_home", "workers_16_and_over", "associates_degree", "bachelors_degree", "high_school_diploma", "less_one_year_college", "masters_degree", "one_year_more_college", "pop_25_years_over", "commute_35_44_mins", "commute_60_more_mins", "commute_less_10_mins", "commuters_16_over", "hispanic_any_race", "pop_5_years_over", "speak_only_english_at_home", "speak_spanish_at_home", "speak_spanish_at_home_low_english", "pop_15_and_over", "pop_never_married", "pop_now_married", "pop_separated", "pop_widowed", "pop_divorced", "do_date"], "nested_column_types": ["STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "STRING"], "description": ["US Congressional Districts Geoids", "Nonfamily Households. A householder living alone or with nonrelatives only. Unmarried couples households, whether opposite-sex or same-sex, with no relatives of the householder present are tabulated in nonfamily households.", "Family Households. A family consists of a householder and one or more other people living in the same household who are related to the householder by birth, marriage, or adoption. All people in a household who are related to the householder are regarded as members of his or her family. A family household may contain people not related to the householder, but those people are not included as part of the family of the householder in tabulations. Thus, the number of family households is equal to the number of families, but family households may include more members than do families. A household can contain only one family for purposes of tabulations. Not all households contain families since a household may be comprised of a group of unrelated people or of one person living alone -- these are called nonfamily households. Families are classified by type as either a \"married couple family\" or \"other family\" according to the sex of the householder and the presence of relatives. The data on family type are based ...", "Median Year Structure Built. Median Year Structure Built", "Housing units without rent burden computed. Units for which no rent is paid and units occupied by households that reported no income or a net loss comprise this category", "Housing units spending over 50% income on rent. Gross rent over 50 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 40 to 49.9% income on rent. Gross rent from 40.0 to 49.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 35 to 39.9% income on rent. Gross rent from 35.0 to 39.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 30 to 34.9% income on rent. Gross rent from 30.0 to 34.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 25 to 29.9% income on rent. Gross rent from 25.0 to 29.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 20 to 24.9% income on rent. Gross rent from 20.0 to 24.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 15 to 19.9% income on rent. Gross rent from 15.0 to 19.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 10 to 14.9% income on rent. Gross rent from 10.0 to 14.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending less than 10% on rent. Gross rent less than 10 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Total Population. The total number of all people living in a given geographic area.  This is a very useful catch-all denominator when calculating rates.", "Male Population. The number of people within each geography who are male.", "Female Population. The number of people within each geography who are female.", "Median Age. The median age of all people in a given geographic area.", "White Population. The number of people identifying as white, non-Hispanic in each geography.", "Black or African American Population. The number of people identifying as black or African American, non-Hispanic in each geography.", "Asian Population. The number of people identifying as Asian, non-Hispanic in each geography.", "Hispanic Population. The number of people identifying as Hispanic or Latino in each geography.", "American Indian and Alaska Native Population. The number of people identifying as American Indian or Alaska native in each geography.", "Other Race population. The number of people identifying as another race in each geography", "Two or more races population. The number of people identifying as two or more races in each geography", "Population not Hispanic. The number of people not identifying as Hispanic or Latino in each geography.", "Commuters by Public Transportation. The number of workers age 16 years and over within a geographic area who primarily traveled to work by public transportation.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Households. A count of the number of households in each geography. A household consists of one or more people who live in the same dwelling and also share at meals or living accommodation, and may consist of a single family or some other grouping of people. ", "Median Household Income in the past 12 Months. Within a geographic area, the median income received by every household on a regular basis before payments for personal income taxes, social security, union dues, medicare deductions, etc.  It includes income received from wages, salary, commissions, bonuses, and tips; self-employment income from own nonfarm or farm businesses, including proprietorships and partnerships; interest, dividends, net rental income, royalty income, or income from estates and trusts; Social Security or Railroad Retirement income; Supplemental Security Income (SSI); any cash public assistance or welfare payments from the state or local welfare office; retirement, survivor, or disability benefits; and any other sources of income received regularly such as Veterans' (VA) payments, unemployment and/or worker's compensation, child support, and alimony.", "Per Capita Income in the past 12 Months. Per capita income is the mean income computed for every man, woman, and child in a particular group. It is derived by dividing the total income of a particular group by the total population.", "Housing Units. A count of housing units in each geography.  A housing unit is a house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters.", "Vacant Housing Units. The count of vacant housing units in a geographic area. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Vacant Housing Units for Rent. The count of vacant housing units in a geographic area that are for rent. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Vacant Housing Units for Sale. The count of vacant housing units in a geographic area that are for sale. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Median Rent. The median contract rent within a geographic area. The contract rent is the monthly rent agreed to or contracted for, regardless of any furnishings, utilities, fees, meals, or services that may be included. For vacant units, it is the monthly rent asked for the rental unit at the time of interview.", "Percent of Household Income Spent on Rent. Within a geographic area, the median percentage of household income which was spent on gross rent.  Gross rent is the amount of the contract rent plus the estimated average monthly cost of utilities (electricity, gas, water, sewer etc.) and fuels (oil, coal, wood, etc.) if these are paid by the renter.  Household income is the sum of the income of all people 15 years and older living in the household.", "Owner-occupied Housing Units", "Owner-occupied Housing Units valued at $1,000,000 or more.. The count of owner occupied housing units in a geographic area that are valued at $1,000,000 or more.  Value is the respondent's estimate of how much the property (house and lot, mobile home and lot, or condominium unit) would sell for if it were for sale.", "Owner-occupied Housing Units with a Mortgage. The count of housing units within a geographic area that are mortagaged. \"Mortgage\" refers to all forms of debt where the property is pledged as security for repayment of the debt, including deeds of trust, trust deed, contracts to purchase, land contracts, junior mortgages, and home equity loans.", "Families with young children (under 6 years of age)", "Two-parent families with young children (under 6 years of age)", "Two-parent families, both parents in labor force with young children (under 6 years of age)", "Two-parent families, father only in labor force with young children (under 6 years of age)", "Two-parent families, mother only in labor force with young children (under 6 years of age)", "Two-parent families, neither parent in labor force with young children (under 6 years of age)", "One-parent families with young children (under 6 years of age)", "One-parent families, father, with young children (under 6 years of age)", "One-parent families, father in labor force, with young children (under 6 years of age)", "Number of workers with a commute between 10 and 14 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 10 and 14 minutes. ", "Number of workers with a commute between 15 and 19 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 15 and 19 minutes. ", "Number of workers with a commute between 20 and 24 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 20 and 24 minutes.", "Number of workers with a commute between 25 and 29 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 25 and 29 minutes. ", "Number of workers with a commute between 30 and 34 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 30 and 34 minutes. ", "Number of workers with a commute between 45 and 59 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 45 and 59 minutes. ", "Aggregate travel time to work. The total number of minutes every worker in a geographic area over the age of 16 who did not work from home spent spent commuting to work in one day", "Households with income less than $10,000. The number of households in a geographic area whose annual income was less than $10,000.", "Households with income of $10,000 to $14,999. The number of households in a geographic area whose annual income was between $10,000 and $14,999.", "Households with income of $15,000 to $19,999. The number of households in a geographic area whose annual income was between $15,000 and $19,999.", "Households with income of $20,000 to $24,999. The number of households in a geographic area whose annual income was between $20,000 and $24,999.", "Households with income of $25,000 to $29,999. The number of households in a geographic area whose annual income was between $20,000 and $24,999.", "Households with income of $30,000 to $34,999. The number of households in a geographic area whose annual income was between $30,000 and $34,999.", "Households with income of $35,000 to $39,999. The number of households in a geographic area whose annual income was between $35,000 and $39,999.", "Households with income of $40,000 to $44,999. The number of households in a geographic area whose annual income was between $40,000 and $44,999.", "Households with income of $45,000 to $49,999. The number of households in a geographic area whose annual income was between $45,000 and $49,999.", "Households with income of $50,000 to $59,999. The number of households in a geographic area whose annual income was between $50,000 and $59,999.", "Households with income of $60,000 to $74,999. The number of households in a geographic area whose annual income was between $60,000 and $74,999.", "Households with income of $75,000 to $99,999. The number of households in a geographic area whose annual income was between $75,000 and $99,999.", "Households with income of $100,000 to $124,999. The number of households in a geographic area whose annual income was between $100,000 and $124,999.", "Households with income of $125,000 to $149,999. The number of households in a geographic area whose annual income was between $125,000 and $149,999.", "Households with income of $150,000 to $199,999. The number of households in a geographic area whose annual income was between $150,000 and $1999,999.", "Households with income of $200,000 Or More. The number of households in a geographic area whose annual income was more than $200,000.", "Renter-Occupied Housing Units Paying Cash Rent Median Gross Rent", "Owner-Occupied Housing Units Lower Value Quartile", "Owner-Occupied Housing Units Median Value. The middle value (median) in a geographic area owner occupied housing units.", "Owner-Occupied Housing Units Upper Value Quartile", "Married households. People in formal marriages, as well as people in common-law marriages, are included. Does not include same-sex marriages.", "Occupied housing units. A housing unit is classified as occupied if it is the usual place of residence of the person or group of people living in it at the time of enumeration.", "Renter occupied housing units. All occupied units which are not owner occupied, whether they are rented for cash rent or occupied without payment of cash rent, are classified as renter-occupied.", "Single-family (one unit) detached dwellings. This is a 1-unit structure detached from any other house, that is, with open space on all four sides. Such structures are considered detached even if they have an adjoining shed or garage. A one-family house that contains a business is considered detached as long as the building has open space on all four sides. Mobile homes to which one or more permanent rooms have been added or built also are included.", "Single-family (one unit) attached dwellings. This is a 1-unit structure that has one or more walls extending from ground to roof separating it from adjoining structures. In row houses (sometimes called townhouses), double houses, or houses attached to nonresidential structures, each house is a separate, attached structure if the dividing or common wall goes from ground to roof.", "Two-family (two unit) dwellings", "Multifamily dwellings with three to 4 units", "Apartment buildings with 5 to 9 units", "Apartment buildings with 10 to 19 units", "Apartment buildings with 20 to 49 units", "Apartment buildings with 50 or more units", "Mobile homes. A manufactured home is defined as a movable dwelling, 8 feet or more wide and 40 feet or more long, designed to be towed on its own chassis, with transportation gear integral to the unit when it leaves the factory, and without need of a permanent foundation. These homes are built in accordance with the U.S. Department of Housing and Urban Development (HUD) building code.", "Housing units built in 2005 or later. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built in 2005 or later.", "Housing units built between 2000 and 2004. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built from 2000 to 2004.", "Housing units built before 1939. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built in 1939 or earlier.", "Male under 5 years. The male population over the age of five years within the specified area.", "Male age 5 to 9. The male population between the age of five years to nine years within the specified area.", "Male age 10 to 14. The male population between the age of ten years to fourteen years within the specified area.", "Male age 15 to 17. The male population between the age of fifteeen years to seventeen years within the specified area.", "Male age 18 and 19. The male population between the age of eighteen years to nineteen years within the specified area.", "Male age 20. The male population with an age of twenty years within the specified area.", "Male age 21. The male population with an age of twenty-one years within the specified area.", "Male age 22 to 24. The male population between the age of twenty-two years to twenty-four years within the specified area.", "Male age 25 to 29. The male population between the age of twenty-five years to twenty-nine years within the specified area.", "Male age 30 to 34. The male population between the age of thirty years to thirty-four years within the specified area.", "Male age 35 to 39. The male population between the age of thirty-five years to thirty-nine years within the specified area.", "Male age 40 to 44. The male population between the age of fourty years to fourty-four years within the specified area.", "Men age 45 to 49. The male population between the age of fourty-five years to fourty-nine years within the specified area.", "Men age 50 to 54. The male population between the age of fifty years to fifty-four years within the specified area.", "Men age 55 to 59. The male population between the age of fifty-five years to fifty-nine years within the specified area.", "Men age 60 to 61. The male population between the age of sixty years to sixty-one years within the specified area.", "Men age 62 to 64. The male population between the age of sixty-two years to sixty-four years within the specified area.", "Male age 65 to 66. The male population between the age of sixty-five years to sixty-six years within the specified area.", "Male age 67 to 69. The male population between the age of sixty-seven years to sixty-nine years within the specified area.", "Male age 70 to 74. The male population between the age of seventy years to seventy-four years within the specified area.", "Male age 75 to 79. The male population between the age of seventy-five years to seventy-nine years within the specified area.", "Male age 80 to 84. The male population between the age of eighty years to eighty-four years within the specified area.", "Male age 85 and over. The male population of the age of eighty-five years and over within the specified area.", "Female under 5 years. The female population over the age of five years within the specified area.", "Female age 5 to 9. The female population between the age of five years to nine years within the specified area.", "Female age 10 to 14. The female population between the age of ten years to fourteen years within the specified area.", "Female age 15 to 17. The female population between the age of fifteeen years to seventeen years within the specified area.", "Female age 18 and 19. The female population between the age of eighteen years to nineteen years within the specified area.", "Female age 20. The female population with an age of twenty years within the specified area.", "Female age 21. The female population with an age of twenty-one years within the specified area.", "Female age 22 to 24. The female population between the age of twenty-two years to twenty-four years within the specified area.", "Female age 25 to 29. The female population between the age of twenty-five years to twenty-nine years within the specified area.", "Female age 30 to 34. The female population between the age of thirty years to thirty-four years within the specified area.", "Female age 35 to 39. The female population between the age of thirty-five years to thirty-nine years within the specified area.", "Female age 40 to 44. The female population between the age of fourty years to fourty-four years within the specified area.", "Female age 45 to 49. The female population between the age of fourty-five years to fourty-nine years within the specified area.", "Female age 50 to 54. The female population between the age of fifty years to fifty-four years within the specified area.", "Female age 55 to 59. The female population between the age of fifty-five years to fifty-nine years within the specified area.", "Female age 60 and 61. The female population between the age of sixty years to sixty-one years within the specified area.", "Female age 62 to 64. The female population between the age of sixty-two years to sixty-four years within the specified area.", "Female age 65 to 66. The female population between the age of sixty-five years to sixty-six years within the specified area.", "Female age 67 to 69. The female population between the age of sixty-seven years to sixty-nine years within the specified area.", "Female age 70 to 74. The female population between the age of seventy years to seventy-four years within the specified area.", "Female age 75 to 79. The female population between the age of seventy-five years to seventy-nine years within the specified area.", "Female age 80 to 84. The female population between the age of eighty years to eighty-four years within the specified area.", "Female age 85 and over. The female population of the age of eighty-five years and over within the specified area.", "White including Hispanic", "Black including Hispanic", "American Indian including Hispanic", "Asian including Hispanic", "Number of workers with a commute between 5 and 9 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 5 and 9 minutes.", "Number of workers with a commute between 35 and 39 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 35 and 39 minutes. ", "Number of workers with a commute between 40 and 44 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 40 and 44 minutes. ", "Number of workers with a commute between 60 and 89 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 60 and 89 minutes .", "Number of workers with a commute of more than 90 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute more than 90 minutes.", "Households receiving retirement income", "Population in Armed Forces. The number of people in each geography who are members of the U.S. Armed Forces (people on active duty with the United States Army, Air Force, Navy, Marine Corps, or Coast Guard).", "Population in Civilian Labor Force. The number of civilians 16 years and over in each geography who can be classified as either \"employed\" or \"unemployed\" below.", "Employed Population. The number of civilians 16 years old and over in each geography who either (1) were \"at work,\" that is, those who did any work at all during the reference week as paid employees, worked in their own business or profession, worked on their own farm, or worked 15 hours or more as unpaid workers on a family farm or in a family business; or (2) were \"with a job but not at work,\" that is, those who did not work during the reference week but had jobs or businesses from which they were temporarily absent due to illness, bad weather, industrial dispute, vacation, or other personal reasons. Excluded from the employed are people whose only activity consisted of work around the house or unpaid volunteer work for religious, charitable, and similar organizations; also excluded are all institutionalized people and people on active duty in the United States Armed Forces.", "", "Population Not in Labor Force. The number of people in each geography who are 16 years old and over who are not classified as members of the labor force. This category consists mainly of students, homemakers, retired workers, seasonal workers interviewed in an off season who were not looking for work, institutionalized people, and people doing only incidental unpaid family work (less than 15 hours during the reference week).", "Population age 16 and over. The number of people in each geography who are age 16 or over.", "Population in Labor Force. The number of people in each geography who are either in the civilian labor force or are members of the U.S. Armed Forces (people on active duty with the United States Army, Air Force, Navy, Marine Corps, or Coast Guard).", "Asian Men age 45 to 54", "Asian Men age 55 to 64", "Black Men age 45 to 54", "Black Men age 55 to 64", "Hispanic Men age 45 to 54", "Hispanic Men age 55 to 64", "White Men age 45 to 54", "White Men age 55 to 64", "Population who completed a bachelor's degree. From mobility table.", "Population with Bachelors Degree or Higher, Ages 25 to 64. The number of people in each geography who are between the ages of 25 and 64 who have attained a bachelors degree or higher. ", "Children under 18 Years of Age. The number of people within each geography who are under 18 years of age.", "Children under 18 years of age in single female-led household", "Commuters by Bus. The number of workers age 16 years and over within a geographic area who primarily traveled to work by bus.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.  This is a subset of workers who commuted by public transport.", "Commuters by Car, Truck, or Van. The number of workers age 16 years and over within  a geographic area who primarily traveled to work by car, truck or  van.  This is the principal mode of travel or type of conveyance,  by distance rather than time, that the worker usually used to get  from home to work.", "Commuters by Carpool. The number of workers age 16 years and over within a geographic area who primarily traveled to work by carpool.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Commuters by Subway or Elevated. The number of workers age 16 years and over within a geographic area who primarily traveled to work by subway or elevated train.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.  This is a subset of workers who commuted by public transport.", "Commuters who drove alone. The number of workers age 16 years and over within a geographic area who primarily traveled by car driving alone. This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Lived in a different house one year ago in a different city. All people in a geographic area who lived in a different city within the year prior to the survey.", "Lived in a different house one year ago in the same city. All people in a geographic area who lived in the same city but moved to a different unit within the year prior to the survey.", "Workers employed in firms in agriculture, forestry, fishing, hunting, or mining. The Agriculture, Forestry, Fishing and Hunting sector comprises establishments primarily engaged in growing crops, raising animals, harvesting timber, and harvesting fish and other animals from a farm, ranch, or their natural habitats.", "Workers employed in firms in arts, entertainment, recreation, accommodation and food services. The Arts, Entertainment, and Recreation sector includes a wide range of establishments that operate facilities or provide services to meet varied cultural, entertainment, and recreational interests of their patrons. This sector comprises (1) establishments that are involved in producing, promoting, or participating in live performances, events, or exhibits intended for public viewing; (2) establishments that preserve and exhibit objects and sites of historical, cultural, or educational interest; and (3) establishments that operate facilities or provide services that enable patrons to participate in recreational activities or pursue amusement, hobby, and leisure-time interests.", "Workers employed in firms in construction. The Construction sector comprises establishments primarily engaged in the construction of buildings or engineering projects (e.g., highways and utility systems). Construction work done may include new work, additions, alterations, or maintenance and repairs.", "Workers employed in firms in educational services, health care, and social assistance. Outpatient health services, other than hospital care, including: public health administration; research and education; categorical health programs; treatment and immunization clinics; nursing; environmental health activities such as air and water pollution control; ambulance service if provided separately from fire protection services, and other general public health activities such as mosquito abatement. School health services provided by health agencies (rather than school agencies) are included here. Sewage treatment operations are classified under Sewerage.", "Workers employed in firms in finance, insurance, real estate and rental and leasing. The Real Estate and Rental and Leasing sector comprises establishments primarily engaged in renting, leasing, or otherwise allowing the use of tangible or intangible assets, and establishments providing related services. The major portion of this sector comprises establishments that rent, lease, or otherwise allow the use of their own assets by others. The assets may be tangible, as is the case of real estate and equipment, or intangible, as is the case with patents and trademarks.", "Workers employed in firms in information. The Information sector comprises establishments engaged in the following processes: (a) producing and distributing information and cultural products, (b) providing the means to transmit or distribute these products as well as data or communications, and (c) processing data. Included are the publishing industries, the motion picture and sound recording industries; the broadcasting industries, the telecommunications industries; Web search portals, data processing industries, and the information services industries.", "Workers employed in firms in manufacturing. The Manufacturing sector comprises establishments engaged in the mechanical, physical, or chemical transformation of materials, substances, or components into new products.", "Workers employed in firms in other services except public administration. The Other Services (Except Public Administration) sector comprises establishments engaged in providing services not specifically provided for elsewhere in the classification system. Establishments in this sector are primarily engaged in activities such as equipment and machinery repairing, promoting or administering religious activities, grantmaking, advocacy, and providing drycleaning and laundry services, personal care services, death care services, pet care services, photofinishing services, temporary parking services, and dating services. Private households that engage in employing workers on or about the premises in activities primarily concerned with the operation of the household are included in this sector.", "Workers employed in firms in public administration. The Public Administration sector consists of establishments of federal, state, and local government agencies that administer, oversee, and manage public programs and have executive, legislative, or judicial authority over other institutions within a given area. These agencies also set policy, create laws, adjudicate civil and criminal legal cases, provide for public safety and for national defense. In general, government establishments in the public administration sector oversee governmental programs and activities that are not performed by private establishments.", "Workers employed in firms in retail trade. The Retail Trade sector comprises establishments engaged in retailing merchandise, generally without transformation, and rendering services incidental to the sale of merchandise. The retailing process is the final step in the distribution of merchandise; retailers are, therefore, organized to sell merchandise in small quantities to the general public.", "Workers employed in firms in professional scientific, management, administrative and waste management services. The Administrative and Support and Waste Management and Remediation Services sector comprises establishments performing routine support activities for the day-to-day operations of other organizations. The establishments in this sector specialize in one or more of these support activities and provide these services to clients in a variety of industries and, in some cases, to households. Activities performed include office administration, hiring and placing of personnel, document preparation and similar clerical services, solicitation, collection, security and surveillance services, cleaning, and waste disposal services.", "Workers employed in firms in transportation, warehousing, and utilities. The Transportation and Warehousing sector includes industries providing transportation of passengers and cargo, warehousing and storage for goods, scenic and sightseeing transportation, and support activities related to modes of transportation. The modes of transportation are air, rail, water, road, and pipeline.", "Workers employed in firms in wholesale trade. The Wholesale Trade sector comprises establishments engaged in wholesaling merchandise, generally without transformation, and rendering services incidental to the sale of merchandise. The wholesaling process is an intermediate step in the distribution of merchandise. Wholesalers are organized to sell or arrange the purchase or sale of (a) goods for resale (i.e., goods sold to other wholesalers or retailers), (b) capital or durable nonconsumer goods, and (c) raw and intermediate materials and supplies used in production.", "Households with two female partners. An unmarried partner is a person age 15 years and over, who is not related to the householder, who shares living quarters, and who has an intimate relationship with the householder.", "Four car households. The number of households with four or more passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Gini Index. The Gini index, or index of income concentration, is a statistical measure of income inequality ranging from 0 to 1. A measure of 1 indicates perfect inequality, i.e., one household having all the income and rest having none. A measure of 0 indicates perfect equality, i.e., all households having an equal share of income.", "Population who completed a graduate or professional degree", "Population living in group quarters", "Population with high school degree, including GED. The number of people in a geographic area over the age of 25 who attained a high school degree or GED.", "Households on cash public assistance or receiving food stamps (SNAP)", "Students Enrolled in Grades 1 to 4. The total number of people in each geography currently enrolled in grades 1 through 4 inclusive.  This corresponds roughly to elementary school.", "Students Enrolled in Grades 5 to 8. The total number of people in each geography currently enrolled in grades 5 through 8 inclusive.  This corresponds roughly to middle school.", "Students Enrolled in Grades 9 to 12. The total number of people in each geography currently enrolled in grades 9 through 12 inclusive.  This corresponds roughly to high school.", "Students Enrolled in School. The total number of people in each geography currently enrolled at any level of school, from nursery or pre-school to advanced post-graduate education.  Only includes those over the age of 3.", "Students Enrolled as Undergraduate in College. The number of people in a geographic area who are enrolled in college at the undergraduate level. Enrollment refers to being registered or listed as a student in an educational program leading to a college degree. This may be a public school or college, a private school or college.", "Less than high school graduate. The number of people in a geographic area over the age of 25 who have not completed high school or any other advanced degree.", "Men age 45 to 64 who obtained an associate's degree", "Men age 45 to 64 who obtained a bachelor's degree", "Men age 45 to 64 who obtained a graduate or professional degree", "Men age 45 to 64 who attained less than a 9th grade education", "Men age 45 to 64 who attained between 9th and 12th grade, no diploma", "Men age 45 to 64 who completed high school or obtained GED", "Men age 45 to 64 who completed some college, no degree", "Men age 45 to 64 (\"middle aged\"). The male population between the age of fourty-five years to sixty-four years within the specified area.", "Households with two male partners. An unmarried partner is a person age 15 years and over, who is not related to the householder, who shares living quarters, and who has an intimate relationship with the householder.", "Civilian Employed Population in Management, Business, Science, and Arts Occupations. The number of employed civilians 16 years old and over in each geography in the Management, Business, Science, and Arts occupations. Occupation codes are based on the Standard Occupational Classification (SOC), published by the Executive Office of the President, Office of Management and Budget.", "Workers age 16 and over with no vehicle. All people in a geographic area over the age of 16 who do not own a car.", "Car-free households. The number of households without passenger cars, vans, and pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Not a U.S. Citizen Population. The number of people within each geography who indicated that they are not U.S. citizens.", "Workers employed in management business science and arts occupations", "Workers employed in natural resources, construction, and maintenance occupations", "Workers employed in production, transportation, and material moving", "Workers employed in sales and office occupations", "Workers employed in service occupations", "One car households. The number of households with one passenger car, van, , pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Two car households. The number of households with two passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Three car households. The number of households with one passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Population age 25 to 64. The number of people in each geography who are between the ages of 25 and 64. ", "Population for Whom Poverty Status Determined. The number of people in each geography who could be identified as either living in poverty or not.  This should be used as the denominator when calculating poverty rates, as it excludes people for whom it was not possible to determine poverty.", "Population 1 year and over. All people, male and female, child and adult, living in a given geographic area that are 1 year and older.", "Population 3 Years and Over. The total number of people in each geography age 3 years and over.  This denominator is mostly used to calculate rates of school enrollment.", "Income In The Past 12 Months Below Poverty Level. The number of people in a geographic area who are part of a family (which could be just them as an individual) determined to be \"in poverty\" following the Office of Management and Budget's Directive 14. (https://www.census.gov/hhes/povmeas/methodology/ombdir14.html)", "Civilian Employed Population in Sales and Office Occupations. The number of employed civilians 16 years old and over in each geography in the Sales and Office occupations. Occupation codes are based on the Standard Occupational Classification (SOC), published by the Executive Office of the President, Office of Management and Budget.", "Population who completed some college or obtained associate's degree. The number of people in a geographic area over the age of 25 who obtained an associate's degree, and did not complete a more advanced degree.", "Walked to Work. The number of workers age 16 years and over within a geographic area who primarily walked to work.  This would mean that of any way of getting to work, they travelled the most distance walking.", "Worked at Home. The count within a geographical area of workers over the age of 16 who worked at home.", "Workers over the Age of 16. The number of people in each geography who work. Workers include those employed at private for-profit companies, the self-employed, government workers and non-profit employees.", "Population Completed Associate's Degree. The number of people in a geographic area over the age of 25 who obtained a associate's degree, and did not complete a more advanced degree.", "Population Completed Bachelor's Degree. The number of people in a geographic area over the age of 25 who obtained a bachelor's degree, and did not complete a more advanced degree.", "Population Completed High School. The number of people in a geographic area over the age of 25 who completed high school, and did not complete a more advanced degree.", "Population completed less than one year of college, no degree. The number of people in a geographic area over the age of 25 who attended college for less than one year and no further.", "Population Completed Master's Degree. The number of people in a geographic area over the age of 25 who obtained a master's degree, but did not complete a more advanced degree.", "Population completed more than one year of college, no degree. The number of people in a geographic area over the age of 25 who attended college for more than one year but did not obtain a degree", "Population 25 Years and Over. The number of people in a geographic area who are over the age of 25.  This is used mostly as a denominator of educational attainment.", "Number of workers with a commute between 35 and 44 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 35 and 44 minutes. ", "Number of workers with a commute of over 60 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in over 60 minutes.", "Number of workers with less than 10 minute commute. The number of workers in a geographic area over the age of 16 who do not work from home and commute in less than 10 minutes.", "Workers age 16 and over who do not work from home. The number of workers in a geographic area over the age of 16 who do not work from home", "Hispanic of any race", "Population 5 Years and Over. The number of people in a geographic area who are over the age of 5.  This is primarily used as a denominator of measures of language spoken at home.", "Speaks only English at Home. The number of people in a geographic area over age 5 who speak only English at home.", "Speaks Spanish at Home. The number of people in a geographic area over age 5 who speak Spanish at home, possibly in addition to other languages.", "Speaks Spanish at Home, speaks English less than \"very well\"", "Population 15 Years and Over. The number of people in a geographic area who are over the age of 15.  This is used mostly as a denominator of marital status.", "Never Married. The number of people in a geographic area who have never been married.", "Currently married. The number of people in a geographic area who are currently married", "Married but separated. The number of people in a geographic area who are married but separated", "Widowed. The number of people in a geographic area who are widowed", "Divorced. The number of people in a geographic area who are divorced", null], "sample_rows": [{"geo_id": "5111", "nonfamily_households": 79243.0, "family_households": 188726.0, "median_year_structure_built": 1984.0, "rent_burden_not_computed": 3458.0, "rent_over_50_percent": 16912.0, "rent_40_to_50_percent": 6334.0, "rent_35_to_40_percent": 5862.0, "rent_30_to_35_percent": 9153.0, "rent_25_to_30_percent": 10110.0, "rent_20_to_25_percent": 14144.0, "rent_15_to_20_percent": 12869.0, "rent_10_to_15_percent": 7457.0, "rent_under_10_percent": 2456.0, "total_pop": 778215.0, "male_pop": 382819.0, "female_pop": 395396.0, "median_age": 37.4, "white_pop": 367122.0, "black_pop": 97866.0, "asian_pop": 142613.0, "hispanic_pop": 135582.0, "amerindian_pop": 2312.0, "other_race_pop": 2013.0, "two_or_more_races_pop": 30534.0, "not_hispanic_pop": 642633.0, "commuters_by_public_transportation": 34353.0, "households": 267969.0, "median_income": 106527.0, "income_per_capita": 47344.0, "housing_units": 282576.0, "vacant_housing_units": 14607.0, "vacant_housing_units_for_rent": 7128.0, "vacant_housing_units_for_sale": 1301.0, "median_rent": 1694.0, "percent_income_spent_on_rent": 27.8, "owner_occupied_housing_units": 179214.0, "million_dollar_housing_units": 4367.0, "mortgaged_housing_units": 138509.0, "families_with_young_children": 59828.0, "two_parent_families_with_young_children": 47042.0, "two_parents_in_labor_force_families_with_young_children": 30219.0, "two_parents_father_in_labor_force_families_with_young_children": 15826.0, "two_parents_mother_in_labor_force_families_with_young_children": 895.0, "two_parents_not_in_labor_force_families_with_young_children": 102.0, "one_parent_families_with_young_children": 12786.0, "father_one_parent_families_with_young_children": 2979.0, "father_in_labor_force_one_parent_families_with_young_children": 2715.0, "commute_10_14_mins": 30078.0, "commute_15_19_mins": 46654.0, "commute_20_24_mins": 38816.0, "commute_25_29_mins": 23769.0, "commute_30_34_mins": 68445.0, "commute_45_59_mins": 61058.0, "aggregate_travel_time_to_work": 13577120.0, "income_less_10000": 8103.0, "income_10000_14999": 3483.0, "income_15000_19999": 3461.0, "income_20000_24999": 4808.0, "income_25000_29999": 6129.0, "income_30000_34999": 6406.0, "income_35000_39999": 6163.0, "income_40000_44999": 6055.0, "income_45000_49999": 6736.0, "income_50000_59999": 14867.0, "income_60000_74999": 21199.0, "income_75000_99999": 37685.0, "income_100000_124999": 31006.0, "income_125000_149999": 26103.0, "income_150000_199999": 36199.0, "income_200000_or_more": 49566.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 1795.0, "owner_occupied_housing_units_lower_value_quartile": 332200.0, "owner_occupied_housing_units_median_value": 474300.0, "owner_occupied_housing_units_upper_value_quartile": 665300.0, "married_households": 150078.0, "occupied_housing_units": 267969.0, "housing_units_renter_occupied": 88755.0, "dwellings_1_units_detached": 118508.0, "dwellings_1_units_attached": 79401.0, "dwellings_2_units": 900.0, "dwellings_3_to_4_units": 2912.0, "dwellings_5_to_9_units": 13257.0, "dwellings_10_to_19_units": 31936.0, "dwellings_20_to_49_units": 8756.0, "dwellings_50_or_more_units": 25729.0, "mobile_homes": 1177.0, "housing_built_2005_or_later": 7321.0, "housing_built_2000_to_2004": 9513.0, "housing_built_1939_or_earlier": 2427.0, "male_under_5": 25401.0, "male_5_to_9": 25186.0, "male_10_to_14": 27377.0, "male_15_to_17": 16480.0, "male_18_to_19": 9581.0, "male_20": 5799.0, "male_21": 3581.0, "male_22_to_24": 15976.0, "male_25_to_29": 25319.0, "male_30_to_34": 26537.0, "male_35_to_39": 28640.0, "male_40_to_44": 27216.0, "male_45_to_49": 26731.0, "male_50_to_54": 28470.0, "male_55_to_59": 25531.0, "male_60_61": 10064.0, "male_62_64": 13038.0, "male_65_to_66": 7509.0, "male_67_to_69": 8942.0, "male_70_to_74": 10606.0, "male_75_to_79": 7301.0, "male_80_to_84": 3719.0, "male_85_and_over": 3815.0, "female_under_5": 23093.0, "female_5_to_9": 26104.0, "female_10_to_14": 24473.0, "female_15_to_17": 15224.0, "female_18_to_19": 9071.0, "female_20": 5265.0, "female_21": 5686.0, "female_22_to_24": 16209.0, "female_25_to_29": 26871.0, "female_30_to_34": 28346.0, "female_35_to_39": 30661.0, "female_40_to_44": 27413.0, "female_45_to_49": 28201.0, "female_50_to_54": 28200.0, "female_55_to_59": 24815.0, "female_60_to_61": 9869.0, "female_62_to_64": 12483.0, "female_65_to_66": 8630.0, "female_67_to_69": 10009.0, "female_70_to_74": 13564.0, "female_75_to_79": 9600.0, "female_80_to_84": 4794.0, "female_85_and_over": 6815.0, "white_including_hispanic": 449294.0, "black_including_hispanic": 102810.0, "amerindian_including_hispanic": 2986.0, "asian_including_hispanic": 144074.0, "commute_5_9_mins": 17911.0, "commute_35_39_mins": 17627.0, "commute_40_44_mins": 22896.0, "commute_60_89_mins": 48645.0, "commute_90_more_mins": 14555.0, "households_retirement_income": 53433.0, "armed_forces": 5157.0, "civilian_labor_force": 439354.0, "employed_pop": 421944.0, "unemployed_pop": 17410.0, "not_in_labor_force": 172891.0, "pop_16_over": 617402.0, "pop_in_labor_force": 444511.0, "asian_male_45_54": 10613.0, "asian_male_55_64": 7445.0, "black_male_45_54": 7057.0, "black_male_55_64": 7074.0, "hispanic_male_45_54": 8732.0, "hispanic_male_55_64": 7478.0, "white_male_45_54": 27861.0, "white_male_55_64": 25718.0, "bachelors_degree_2": 151977.0, "bachelors_degree_or_higher_25_64": 246450.0, "children": 183338.0, "children_in_single_female_hh": 31459.0, "commuters_by_bus": 14536.0, "commuters_by_car_truck_van": 343992.0, "commuters_by_carpool": 39877.0, "commuters_by_subway_or_elevated": 15092.0, "commuters_drove_alone": 304115.0, "different_house_year_ago_different_city": 88681.0, "different_house_year_ago_same_city": 22188.0, "employed_agriculture_forestry_fishing_hunting_mining": 348.0, "employed_arts_entertainment_recreation_accommodation_food": 36589.0, "employed_construction": 22937.0, "employed_education_health_social": 80843.0, "employed_finance_insurance_real_estate": 29829.0, "employed_information": 9340.0, "employed_manufacturing": 9537.0, "employed_other_services_not_public_admin": 25327.0, "employed_public_administration": 45878.0, "employed_retail_trade": 35919.0, "employed_science_management_admin_waste": 101927.0, "employed_transportation_warehousing_utilities": 18834.0, "employed_wholesale_trade": 4636.0, "female_female_households": 253.0, "four_more_cars": 18050.0, "gini_index": 0.4086, "graduate_professional_degree": 145657.0, "group_quarters": 7733.0, "high_school_including_ged": 80515.0, "households_public_asst_or_food_stamps": 12331.0, "in_grades_1_to_4": 38277.0, "in_grades_5_to_8": 41253.0, "in_grades_9_to_12": 43126.0, "in_school": 212244.0, "in_undergrad_college": 48073.0, "less_than_high_school_graduate": 41743.0, "male_45_64_associates_degree": 5853.0, "male_45_64_bachelors_degree": 27382.0, "male_45_64_graduate_degree": 32565.0, "male_45_64_less_than_9_grade": 4601.0, "male_45_64_grade_9_12": 3719.0, "male_45_64_high_school": 16687.0, "male_45_64_some_college": 13027.0, "male_45_to_64": 103834.0, "male_male_households": 507.0, "management_business_sci_arts_employed": 230927.0, "no_car": 9229.0, "no_cars": 10269.0, "not_us_citizen_pop": 114957.0, "occupation_management_arts": 230927.0, "occupation_natural_resources_construction_maintenance": 22769.0, "occupation_production_transportation_material": 24490.0, "occupation_sales_office": 81324.0, "occupation_services": 62434.0, "one_car": 83702.0, "two_cars": 114216.0, "three_cars": 41732.0, "pop_25_64": 428405.0, "pop_determined_poverty_status": 769770.0, "population_1_year_and_over": 770086.0, "population_3_years_over": 750021.0, "poverty": 50955.0, "sales_office_employed": 81324.0, "some_college_and_associates_degree": 103817.0, "walked_to_work": 7588.0, "worked_at_home": 25403.0, "workers_16_and_over": 419868.0, "associates_degree": 32690.0, "bachelors_degree": 151977.0, "high_school_diploma": 73330.0, "less_one_year_college": 21069.0, "masters_degree": 112779.0, "one_year_more_college": 50058.0, "pop_25_years_over": 523709.0, "commute_35_44_mins": 40523.0, "commute_60_more_mins": 63200.0, "commute_less_10_mins": 21922.0, "commuters_16_over": 394465.0, "hispanic_any_race": 135582.0, "pop_5_years_over": 729721.0, "speak_only_english_at_home": 434816.0, "speak_spanish_at_home": 107674.0, "speak_spanish_at_home_low_english": 51281.0, "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "do_date": "2017"}, {"geo_id": "1000", "nonfamily_households": 122142.0, "family_households": 235795.0, "median_year_structure_built": 1984.0, "rent_burden_not_computed": 10825.0, "rent_over_50_percent": 22741.0, "rent_40_to_50_percent": 7557.0, "rent_35_to_40_percent": 6579.0, "rent_30_to_35_percent": 8469.0, "rent_25_to_30_percent": 11871.0, "rent_20_to_25_percent": 11156.0, "rent_15_to_20_percent": 12570.0, "rent_10_to_15_percent": 8684.0, "rent_under_10_percent": 3537.0, "total_pop": 961939.0, "male_pop": 465510.0, "female_pop": 496429.0, "median_age": 40.1, "white_pop": 597917.0, "black_pop": 206690.0, "asian_pop": 38404.0, "hispanic_pop": 89540.0, "amerindian_pop": 2344.0, "other_race_pop": 1545.0, "two_or_more_races_pop": 25255.0, "not_hispanic_pop": 872399.0, "commuters_by_public_transportation": 9598.0, "households": 357937.0, "median_income": 62852.0, "income_per_capita": 33887.0, "housing_units": 432853.0, "vacant_housing_units": 74916.0, "vacant_housing_units_for_rent": 8425.0, "vacant_housing_units_for_sale": 6878.0, "median_rent": 935.0, "percent_income_spent_on_rent": 29.5, "owner_occupied_housing_units": 253948.0, "million_dollar_housing_units": 1827.0, "mortgaged_housing_units": 164071.0, "families_with_young_children": 61245.0, "two_parent_families_with_young_children": 37445.0, "two_parents_in_labor_force_families_with_young_children": 21910.0, "two_parents_father_in_labor_force_families_with_young_children": 13998.0, "two_parents_mother_in_labor_force_families_with_young_children": 1211.0, "two_parents_not_in_labor_force_families_with_young_children": 326.0, "one_parent_families_with_young_children": 23800.0, "father_one_parent_families_with_young_children": 5362.0, "father_in_labor_force_one_parent_families_with_young_children": 4471.0, "commute_10_14_mins": 56948.0, "commute_15_19_mins": 65771.0, "commute_20_24_mins": 70805.0, "commute_25_29_mins": 30661.0, "commute_30_34_mins": 57683.0, "commute_45_59_mins": 26509.0, "aggregate_travel_time_to_work": 10811665.0, "income_less_10000": 25688.0, "income_10000_14999": 15113.0, "income_15000_19999": 12419.0, "income_20000_24999": 15207.0, "income_25000_29999": 14389.0, "income_30000_34999": 18798.0, "income_35000_39999": 14436.0, "income_40000_44999": 13327.0, "income_45000_49999": 13692.0, "income_50000_59999": 25865.0, "income_60000_74999": 38165.0, "income_75000_99999": 48224.0, "income_100000_124999": 31982.0, "income_125000_149999": 20333.0, "income_150000_199999": 25921.0, "income_200000_or_more": 24378.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 1086.0, "owner_occupied_housing_units_lower_value_quartile": 178200.0, "owner_occupied_housing_units_median_value": 252800.0, "owner_occupied_housing_units_upper_value_quartile": 364100.0, "married_households": 176947.0, "occupied_housing_units": 357937.0, "housing_units_renter_occupied": 103989.0, "dwellings_1_units_detached": 252594.0, "dwellings_1_units_attached": 68872.0, "dwellings_2_units": 7605.0, "dwellings_3_to_4_units": 9450.0, "dwellings_5_to_9_units": 16309.0, "dwellings_10_to_19_units": 26927.0, "dwellings_20_to_49_units": 7950.0, "dwellings_50_or_more_units": 11653.0, "mobile_homes": 31086.0, "housing_built_2005_or_later": 18267.0, "housing_built_2000_to_2004": 19499.0, "housing_built_1939_or_earlier": 21693.0, "male_under_5": 28071.0, "male_5_to_9": 29483.0, "male_10_to_14": 28803.0, "male_15_to_17": 17726.0, "male_18_to_19": 12512.0, "male_20": 6850.0, "male_21": 5981.0, "male_22_to_24": 16699.0, "male_25_to_29": 34469.0, "male_30_to_34": 30893.0, "male_35_to_39": 28720.0, "male_40_to_44": 25454.0, "male_45_to_49": 28360.0, "male_50_to_54": 31559.0, "male_55_to_59": 31051.0, "male_60_61": 14028.0, "male_62_64": 17837.0, "male_65_to_66": 10998.0, "male_67_to_69": 15531.0, "male_70_to_74": 21349.0, "male_75_to_79": 14960.0, "male_80_to_84": 7545.0, "male_85_and_over": 6631.0, "female_under_5": 26836.0, "female_5_to_9": 27441.0, "female_10_to_14": 28794.0, "female_15_to_17": 17159.0, "female_18_to_19": 12529.0, "female_20": 7497.0, "female_21": 6252.0, "female_22_to_24": 15620.0, "female_25_to_29": 34870.0, "female_30_to_34": 30717.0, "female_35_to_39": 31436.0, "female_40_to_44": 25458.0, "female_45_to_49": 30395.0, "female_50_to_54": 34218.0, "female_55_to_59": 36078.0, "female_60_to_61": 13898.0, "female_62_to_64": 21028.0, "female_65_to_66": 14573.0, "female_67_to_69": 17517.0, "female_70_to_74": 24390.0, "female_75_to_79": 16541.0, "female_80_to_84": 10909.0, "female_85_and_over": 12273.0, "white_including_hispanic": 662084.0, "black_including_hispanic": 210356.0, "amerindian_including_hispanic": 2729.0, "asian_including_hispanic": 38930.0, "commute_5_9_mins": 29935.0, "commute_35_39_mins": 11248.0, "commute_40_44_mins": 17772.0, "commute_60_89_mins": 23076.0, "commute_90_more_mins": 10309.0, "households_retirement_income": 85057.0, "armed_forces": 3080.0, "civilian_labor_force": 460676.0, "employed_pop": 436376.0, "unemployed_pop": 24300.0, "not_in_labor_force": 318207.0, "pop_16_over": 781963.0, "pop_in_labor_force": 463756.0, "asian_male_45_54": 2136.0, "asian_male_55_64": 1978.0, "black_male_45_54": 12727.0, "black_male_55_64": 11799.0, "hispanic_male_45_54": 4967.0, "hispanic_male_55_64": 2865.0, "white_male_45_54": 39166.0, "white_male_55_64": 45554.0, "bachelors_degree_2": 121442.0, "bachelors_degree_or_higher_25_64": 161972.0, "children": 204313.0, "children_in_single_female_hh": 52452.0, "commuters_by_bus": 6902.0, "commuters_by_car_truck_van": 384125.0, "commuters_by_carpool": 30680.0, "commuters_by_subway_or_elevated": 179.0, "commuters_drove_alone": 353445.0, "different_house_year_ago_different_city": 103193.0, "different_house_year_ago_same_city": 14337.0, "employed_agriculture_forestry_fishing_hunting_mining": 4963.0, "employed_arts_entertainment_recreation_accommodation_food": 38861.0, "employed_construction": 30719.0, "employed_education_health_social": 103422.0, "employed_finance_insurance_real_estate": 44951.0, "employed_information": 6912.0, "employed_manufacturing": 33965.0, "employed_other_services_not_public_admin": 17863.0, "employed_public_administration": 25844.0, "employed_retail_trade": 52153.0, "employed_science_management_admin_waste": 41203.0, "employed_transportation_warehousing_utilities": 25648.0, "employed_wholesale_trade": 9872.0, "female_female_households": 704.0, "four_more_cars": 21923.0, "gini_index": 0.4811, "graduate_professional_degree": 90641.0, "group_quarters": 25034.0, "high_school_including_ged": 218309.0, "households_public_asst_or_food_stamps": 39015.0, "in_grades_1_to_4": 44179.0, "in_grades_5_to_8": 48957.0, "in_grades_9_to_12": 45142.0, "in_school": 233046.0, "in_undergrad_college": 55239.0, "less_than_high_school_graduate": 63551.0, "male_45_64_associates_degree": 7148.0, "male_45_64_bachelors_degree": 23724.0, "male_45_64_graduate_degree": 14883.0, "male_45_64_less_than_9_grade": 4269.0, "male_45_64_grade_9_12": 7725.0, "male_45_64_high_school": 43407.0, "male_45_64_some_college": 21679.0, "male_45_to_64": 122835.0, "male_male_households": 527.0, "management_business_sci_arts_employed": 177418.0, "no_car": 10411.0, "no_cars": 20143.0, "not_us_citizen_pop": 52509.0, "occupation_management_arts": 177418.0, "occupation_natural_resources_construction_maintenance": 36868.0, "occupation_production_transportation_material": 50388.0, "occupation_sales_office": 102606.0, "occupation_services": 69096.0, "one_car": 123077.0, "two_cars": 143302.0, "three_cars": 49492.0, "pop_25_64": 500469.0, "pop_determined_poverty_status": 934947.0, "population_1_year_and_over": 952449.0, "population_3_years_over": 929575.0, "poverty": 126986.0, "sales_office_employed": 102606.0, "some_college_and_associates_degree": 179743.0, "walked_to_work": 9566.0, "worked_at_home": 22227.0, "workers_16_and_over": 430780.0, "associates_degree": 51777.0, "bachelors_degree": 121442.0, "high_school_diploma": 196249.0, "less_one_year_college": 36066.0, "masters_degree": 65823.0, "one_year_more_college": 91900.0, "pop_25_years_over": 673686.0, "commute_35_44_mins": 29020.0, "commute_60_more_mins": 33385.0, "commute_less_10_mins": 37771.0, "commuters_16_over": 408553.0, "hispanic_any_race": 89540.0, "pop_5_years_over": 907032.0, "speak_only_english_at_home": 780824.0, "speak_spanish_at_home": 66227.0, "speak_spanish_at_home_low_english": 22018.0, "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "do_date": "2017"}, {"geo_id": "0645", "nonfamily_households": 80856.0, "family_households": 194798.0, "median_year_structure_built": 1985.0, "rent_burden_not_computed": 6986.0, "rent_over_50_percent": 23439.0, "rent_40_to_50_percent": 12205.0, "rent_35_to_40_percent": 6615.0, "rent_30_to_35_percent": 9060.0, "rent_25_to_30_percent": 11237.0, "rent_20_to_25_percent": 13988.0, "rent_15_to_20_percent": 10232.0, "rent_10_to_15_percent": 6929.0, "rent_under_10_percent": 1135.0, "total_pop": 777871.0, "male_pop": 375776.0, "female_pop": 402095.0, "median_age": 38.8, "white_pop": 389485.0, "black_pop": 14918.0, "asian_pop": 197705.0, "hispanic_pop": 141853.0, "amerindian_pop": 2075.0, "other_race_pop": 655.0, "two_or_more_races_pop": 30334.0, "not_hispanic_pop": 636018.0, "commuters_by_public_transportation": 3447.0, "households": 275654.0, "median_income": 102040.0, "income_per_capita": 46133.0, "housing_units": 289849.0, "vacant_housing_units": 14195.0, "vacant_housing_units_for_rent": 3097.0, "vacant_housing_units_for_sale": 2319.0, "median_rent": 2007.0, "percent_income_spent_on_rent": 32.2, "owner_occupied_housing_units": 173828.0, "million_dollar_housing_units": 20326.0, "mortgaged_housing_units": 129640.0, "families_with_young_children": 49755.0, "two_parent_families_with_young_children": 42866.0, "two_parents_in_labor_force_families_with_young_children": 23070.0, "two_parents_father_in_labor_force_families_with_young_children": 18269.0, "two_parents_mother_in_labor_force_families_with_young_children": 1088.0, "two_parents_not_in_labor_force_families_with_young_children": 439.0, "one_parent_families_with_young_children": 6889.0, "father_one_parent_families_with_young_children": 1518.0, "father_in_labor_force_one_parent_families_with_young_children": 1518.0, "commute_10_14_mins": 49631.0, "commute_15_19_mins": 52663.0, "commute_20_24_mins": 58902.0, "commute_25_29_mins": 25299.0, "commute_30_34_mins": 60712.0, "commute_45_59_mins": 22788.0, "aggregate_travel_time_to_work": 9783320.0, "income_less_10000": 13433.0, "income_10000_14999": 7174.0, "income_15000_19999": 4852.0, "income_20000_24999": 6400.0, "income_25000_29999": 5713.0, "income_30000_34999": 7417.0, "income_35000_39999": 6243.0, "income_40000_44999": 5875.0, "income_45000_49999": 6691.0, "income_50000_59999": 13101.0, "income_60000_74999": 25431.0, "income_75000_99999": 30987.0, "income_100000_124999": 32695.0, "income_125000_149999": 25122.0, "income_150000_199999": 35723.0, "income_200000_or_more": 48797.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 2130.0, "owner_occupied_housing_units_lower_value_quartile": 535200.0, "owner_occupied_housing_units_median_value": 719700.0, "owner_occupied_housing_units_upper_value_quartile": 933400.0, "married_households": 157617.0, "occupied_housing_units": 275654.0, "housing_units_renter_occupied": 101826.0, "dwellings_1_units_detached": 146697.0, "dwellings_1_units_attached": 45582.0, "dwellings_2_units": 2714.0, "dwellings_3_to_4_units": 16074.0, "dwellings_5_to_9_units": 19980.0, "dwellings_10_to_19_units": 15249.0, "dwellings_20_to_49_units": 8809.0, "dwellings_50_or_more_units": 30875.0, "mobile_homes": 3869.0, "housing_built_2005_or_later": 14719.0, "housing_built_2000_to_2004": 9334.0, "housing_built_1939_or_earlier": 680.0, "male_under_5": 21198.0, "male_5_to_9": 21710.0, "male_10_to_14": 25104.0, "male_15_to_17": 16137.0, "male_18_to_19": 9209.0, "male_20": 6322.0, "male_21": 4570.0, "male_22_to_24": 13975.0, "male_25_to_29": 29300.0, "male_30_to_34": 25872.0, "male_35_to_39": 25682.0, "male_40_to_44": 23861.0, "male_45_to_49": 28263.0, "male_50_to_54": 25391.0, "male_55_to_59": 27633.0, "male_60_61": 8630.0, "male_62_64": 14240.0, "male_65_to_66": 9152.0, "male_67_to_69": 8427.0, "male_70_to_74": 12088.0, "male_75_to_79": 7112.0, "male_80_to_84": 5905.0, "male_85_and_over": 5995.0, "female_under_5": 21403.0, "female_5_to_9": 22381.0, "female_10_to_14": 26414.0, "female_15_to_17": 13980.0, "female_18_to_19": 10281.0, "female_20": 6938.0, "female_21": 4604.0, "female_22_to_24": 14495.0, "female_25_to_29": 27503.0, "female_30_to_34": 26226.0, "female_35_to_39": 28565.0, "female_40_to_44": 27462.0, "female_45_to_49": 28445.0, "female_50_to_54": 30069.0, "female_55_to_59": 27094.0, "female_60_to_61": 9931.0, "female_62_to_64": 14679.0, "female_65_to_66": 7588.0, "female_67_to_69": 10474.0, "female_70_to_74": 14664.0, "female_75_to_79": 10391.0, "female_80_to_84": 8688.0, "female_85_and_over": 9820.0, "white_including_hispanic": 472558.0, "black_including_hispanic": 15666.0, "amerindian_including_hispanic": 4559.0, "asian_including_hispanic": 199942.0, "commute_5_9_mins": 21845.0, "commute_35_39_mins": 9651.0, "commute_40_44_mins": 15403.0, "commute_60_89_mins": 22102.0, "commute_90_more_mins": 11220.0, "households_retirement_income": 39777.0, "armed_forces": 129.0, "civilian_labor_force": 409768.0, "employed_pop": 393531.0, "unemployed_pop": 16237.0, "not_in_labor_force": 219402.0, "pop_16_over": 629299.0, "pop_in_labor_force": 409897.0, "asian_male_45_54": 14226.0, "asian_male_55_64": 9346.0, "black_male_45_54": 921.0, "black_male_55_64": 1124.0, "hispanic_male_45_54": 8611.0, "hispanic_male_55_64": 5957.0, "white_male_45_54": 28636.0, "white_male_55_64": 33060.0, "bachelors_degree_2": 181043.0, "bachelors_degree_or_higher_25_64": 246009.0, "children": 168327.0, "children_in_single_female_hh": 22322.0, "commuters_by_bus": 1497.0, "commuters_by_car_truck_van": 333262.0, "commuters_by_carpool": 31056.0, "commuters_by_subway_or_elevated": 602.0, "commuters_drove_alone": 302206.0, "different_house_year_ago_different_city": 70073.0, "different_house_year_ago_same_city": 33154.0, "employed_agriculture_forestry_fishing_hunting_mining": 1946.0, "employed_arts_entertainment_recreation_accommodation_food": 32842.0, "employed_construction": 17766.0, "employed_education_health_social": 82493.0, "employed_finance_insurance_real_estate": 41515.0, "employed_information": 10357.0, "employed_manufacturing": 46920.0, "employed_other_services_not_public_admin": 17815.0, "employed_public_administration": 10058.0, "employed_retail_trade": 36253.0, "employed_science_management_admin_waste": 71317.0, "employed_transportation_warehousing_utilities": 11897.0, "employed_wholesale_trade": 12352.0, "female_female_households": 532.0, "four_more_cars": 20851.0, "gini_index": 0.4313, "graduate_professional_degree": 115384.0, "group_quarters": 12754.0, "high_school_including_ged": 65516.0, "households_public_asst_or_food_stamps": 10961.0, "in_grades_1_to_4": 33992.0, "in_grades_5_to_8": 40839.0, "in_grades_9_to_12": 38968.0, "in_school": 217867.0, "in_undergrad_college": 63535.0, "less_than_high_school_graduate": 33798.0, "male_45_64_associates_degree": 7807.0, "male_45_64_bachelors_degree": 34199.0, "male_45_64_graduate_degree": 24706.0, "male_45_64_less_than_9_grade": 2412.0, "male_45_64_grade_9_12": 3329.0, "male_45_64_high_school": 11020.0, "male_45_64_some_college": 20684.0, "male_45_to_64": 104157.0, "male_male_households": 187.0, "management_business_sci_arts_employed": 213733.0, "no_car": 5258.0, "no_cars": 11602.0, "not_us_citizen_pop": 91384.0, "occupation_management_arts": 213733.0, "occupation_natural_resources_construction_maintenance": 15770.0, "occupation_production_transportation_material": 22305.0, "occupation_sales_office": 94622.0, "occupation_services": 47101.0, "one_car": 77852.0, "two_cars": 124670.0, "three_cars": 40679.0, "pop_25_64": 428846.0, "pop_determined_poverty_status": 767022.0, "population_1_year_and_over": 771399.0, "population_3_years_over": 754754.0, "poverty": 69904.0, "sales_office_employed": 94622.0, "some_college_and_associates_degree": 143409.0, "walked_to_work": 8572.0, "worked_at_home": 30484.0, "workers_16_and_over": 385041.0, "associates_degree": 43894.0, "bachelors_degree": 181043.0, "high_school_diploma": 58192.0, "less_one_year_college": 24819.0, "masters_degree": 78274.0, "one_year_more_college": 74696.0, "pop_25_years_over": 539150.0, "commute_35_44_mins": 25054.0, "commute_60_more_mins": 33322.0, "commute_less_10_mins": 26186.0, "commuters_16_over": 354557.0, "hispanic_any_race": 141853.0, "pop_5_years_over": 735270.0, "speak_only_english_at_home": 457824.0, "speak_spanish_at_home": 81645.0, "speak_spanish_at_home_low_english": 24769.0, "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "do_date": "2017"}, {"geo_id": "1223", "nonfamily_households": 89821.0, "family_households": 177806.0, "median_year_structure_built": 1985.0, "rent_burden_not_computed": 5207.0, "rent_over_50_percent": 23370.0, "rent_40_to_50_percent": 8896.0, "rent_35_to_40_percent": 7204.0, "rent_30_to_35_percent": 9455.0, "rent_25_to_30_percent": 10735.0, "rent_20_to_25_percent": 10774.0, "rent_15_to_20_percent": 8813.0, "rent_10_to_15_percent": 4710.0, "rent_under_10_percent": 1832.0, "total_pop": 753950.0, "male_pop": 360584.0, "female_pop": 393366.0, "median_age": 41.0, "white_pop": 298875.0, "black_pop": 111305.0, "asian_pop": 31785.0, "hispanic_pop": 285845.0, "amerindian_pop": 1772.0, "other_race_pop": 3822.0, "two_or_more_races_pop": 19898.0, "not_hispanic_pop": 468105.0, "commuters_by_public_transportation": 5190.0, "households": 267627.0, "median_income": 66872.0, "income_per_capita": 36096.0, "housing_units": 346151.0, "vacant_housing_units": 78524.0, "vacant_housing_units_for_rent": 10076.0, "vacant_housing_units_for_sale": 1993.0, "median_rent": 1372.0, "percent_income_spent_on_rent": 33.2, "owner_occupied_housing_units": 176631.0, "million_dollar_housing_units": 4330.0, "mortgaged_housing_units": 101901.0, "families_with_young_children": 49105.0, "two_parent_families_with_young_children": 30655.0, "two_parents_in_labor_force_families_with_young_children": 18068.0, "two_parents_father_in_labor_force_families_with_young_children": 11736.0, "two_parents_mother_in_labor_force_families_with_young_children": 518.0, "two_parents_not_in_labor_force_families_with_young_children": 333.0, "one_parent_families_with_young_children": 18450.0, "father_one_parent_families_with_young_children": 4013.0, "father_in_labor_force_one_parent_families_with_young_children": 3725.0, "commute_10_14_mins": 37129.0, "commute_15_19_mins": 34501.0, "commute_20_24_mins": 53086.0, "commute_25_29_mins": 21358.0, "commute_30_34_mins": 57031.0, "commute_45_59_mins": 40835.0, "aggregate_travel_time_to_work": 10351350.0, "income_less_10000": 18460.0, "income_10000_14999": 9423.0, "income_15000_19999": 9209.0, "income_20000_24999": 11151.0, "income_25000_29999": 10156.0, "income_30000_34999": 11492.0, "income_35000_39999": 11059.0, "income_40000_44999": 12445.0, "income_45000_49999": 10980.0, "income_50000_59999": 17221.0, "income_60000_74999": 24953.0, "income_75000_99999": 38497.0, "income_100000_124999": 22048.0, "income_125000_149999": 17455.0, "income_150000_199999": 18323.0, "income_200000_or_more": 24755.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 1484.0, "owner_occupied_housing_units_lower_value_quartile": 189600.0, "owner_occupied_housing_units_median_value": 316400.0, "owner_occupied_housing_units_upper_value_quartile": 461200.0, "married_households": 129364.0, "occupied_housing_units": 267627.0, "housing_units_renter_occupied": 90996.0, "dwellings_1_units_detached": 125719.0, "dwellings_1_units_attached": 31367.0, "dwellings_2_units": 6743.0, "dwellings_3_to_4_units": 10461.0, "dwellings_5_to_9_units": 12553.0, "dwellings_10_to_19_units": 18770.0, "dwellings_20_to_49_units": 36676.0, "dwellings_50_or_more_units": 92061.0, "mobile_homes": 11610.0, "housing_built_2005_or_later": 5461.0, "housing_built_2000_to_2004": 5433.0, "housing_built_1939_or_earlier": 5786.0, "male_under_5": 22325.0, "male_5_to_9": 23319.0, "male_10_to_14": 22344.0, "male_15_to_17": 16530.0, "male_18_to_19": 7435.0, "male_20": 6024.0, "male_21": 2792.0, "male_22_to_24": 13751.0, "male_25_to_29": 21463.0, "male_30_to_34": 21739.0, "male_35_to_39": 24218.0, "male_40_to_44": 22573.0, "male_45_to_49": 28018.0, "male_50_to_54": 27503.0, "male_55_to_59": 25936.0, "male_60_61": 9426.0, "male_62_64": 11606.0, "male_65_to_66": 7756.0, "male_67_to_69": 8986.0, "male_70_to_74": 13413.0, "male_75_to_79": 8645.0, "male_80_to_84": 7641.0, "male_85_and_over": 7141.0, "female_under_5": 19870.0, "female_5_to_9": 20595.0, "female_10_to_14": 22123.0, "female_15_to_17": 15584.0, "female_18_to_19": 7693.0, "female_20": 2712.0, "female_21": 4828.0, "female_22_to_24": 14013.0, "female_25_to_29": 25325.0, "female_30_to_34": 24209.0, "female_35_to_39": 26481.0, "female_40_to_44": 24623.0, "female_45_to_49": 30163.0, "female_50_to_54": 28278.0, "female_55_to_59": 26951.0, "female_60_to_61": 9718.0, "female_62_to_64": 15495.0, "female_65_to_66": 8611.0, "female_67_to_69": 11837.0, "female_70_to_74": 17039.0, "female_75_to_79": 12065.0, "female_80_to_84": 13077.0, "female_85_and_over": 12076.0, "white_including_hispanic": 543715.0, "black_including_hispanic": 117992.0, "amerindian_including_hispanic": 1979.0, "asian_including_hispanic": 32998.0, "commute_5_9_mins": 19285.0, "commute_35_39_mins": 12553.0, "commute_40_44_mins": 20467.0, "commute_60_89_mins": 27349.0, "commute_90_more_mins": 9816.0, "households_retirement_income": 38611.0, "armed_forces": 1292.0, "civilian_labor_force": 384962.0, "employed_pop": 363808.0, "unemployed_pop": 21154.0, "not_in_labor_force": 226787.0, "pop_16_over": 613041.0, "pop_in_labor_force": 386254.0, "asian_male_45_54": 2379.0, "asian_male_55_64": 1837.0, "black_male_45_54": 6718.0, "black_male_55_64": 5234.0, "hispanic_male_45_54": 22720.0, "hispanic_male_55_64": 16151.0, "white_male_45_54": 22662.0, "white_male_55_64": 22879.0, "bachelors_degree_2": 132922.0, "bachelors_degree_or_higher_25_64": 174254.0, "children": 162690.0, "children_in_single_female_hh": 44281.0, "commuters_by_bus": 4138.0, "commuters_by_car_truck_van": 322552.0, "commuters_by_carpool": 27260.0, "commuters_by_subway_or_elevated": 513.0, "commuters_drove_alone": 295292.0, "different_house_year_ago_different_city": 62770.0, "different_house_year_ago_same_city": 24538.0, "employed_agriculture_forestry_fishing_hunting_mining": 1042.0, "employed_arts_entertainment_recreation_accommodation_food": 35158.0, "employed_construction": 25243.0, "employed_education_health_social": 74139.0, "employed_finance_insurance_real_estate": 35074.0, "employed_information": 8628.0, "employed_manufacturing": 13860.0, "employed_other_services_not_public_admin": 17348.0, "employed_public_administration": 16878.0, "employed_retail_trade": 42178.0, "employed_science_management_admin_waste": 53371.0, "employed_transportation_warehousing_utilities": 23766.0, "employed_wholesale_trade": 17123.0, "female_female_households": 594.0, "four_more_cars": 10594.0, "gini_index": 0.4975, "graduate_professional_degree": 82957.0, "group_quarters": 4137.0, "high_school_including_ged": 115961.0, "households_public_asst_or_food_stamps": 24628.0, "in_grades_1_to_4": 34821.0, "in_grades_5_to_8": 36084.0, "in_grades_9_to_12": 39692.0, "in_school": 192953.0, "in_undergrad_college": 44026.0, "less_than_high_school_graduate": 40379.0, "male_45_64_associates_degree": 8603.0, "male_45_64_bachelors_degree": 25349.0, "male_45_64_graduate_degree": 18231.0, "male_45_64_less_than_9_grade": 2311.0, "male_45_64_grade_9_12": 4175.0, "male_45_64_high_school": 23392.0, "male_45_64_some_college": 20428.0, "male_45_to_64": 102489.0, "male_male_households": 854.0, "management_business_sci_arts_employed": 158073.0, "no_car": 7329.0, "no_cars": 15188.0, "not_us_citizen_pop": 107028.0, "occupation_management_arts": 158073.0, "occupation_natural_resources_construction_maintenance": 25976.0, "occupation_production_transportation_material": 29563.0, "occupation_sales_office": 95303.0, "occupation_services": 54893.0, "one_car": 98602.0, "two_cars": 109019.0, "three_cars": 34224.0, "pop_25_64": 403725.0, "pop_determined_poverty_status": 749427.0, "population_1_year_and_over": 746386.0, "population_3_years_over": 728628.0, "poverty": 76219.0, "sales_office_employed": 95303.0, "some_college_and_associates_degree": 159793.0, "walked_to_work": 3350.0, "worked_at_home": 20043.0, "workers_16_and_over": 356854.0, "associates_degree": 54904.0, "bachelors_degree": 132922.0, "high_school_diploma": 102277.0, "less_one_year_college": 33037.0, "masters_degree": 55800.0, "one_year_more_college": 71852.0, "pop_25_years_over": 532012.0, "commute_35_44_mins": 33020.0, "commute_60_more_mins": 37165.0, "commute_less_10_mins": 22686.0, "commuters_16_over": 336811.0, "hispanic_any_race": 285845.0, "pop_5_years_over": 711755.0, "speak_only_english_at_home": 373937.0, "speak_spanish_at_home": 250669.0, "speak_spanish_at_home_low_english": 104343.0, "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "do_date": "2017"}, {"geo_id": "0625", "nonfamily_households": 52037.0, "family_households": 165131.0, "median_year_structure_built": 1985.0, "rent_burden_not_computed": 4901.0, "rent_over_50_percent": 19329.0, "rent_40_to_50_percent": 8924.0, "rent_35_to_40_percent": 5946.0, "rent_30_to_35_percent": 6369.0, "rent_25_to_30_percent": 6666.0, "rent_20_to_25_percent": 8607.0, "rent_15_to_20_percent": 6304.0, "rent_10_to_15_percent": 2668.0, "rent_under_10_percent": 1772.0, "total_pop": 721740.0, "male_pop": 363474.0, "female_pop": 358266.0, "median_age": 35.8, "white_pop": 291459.0, "black_pop": 51022.0, "asian_pop": 63074.0, "hispanic_pop": 286537.0, "amerindian_pop": 5392.0, "other_race_pop": 1390.0, "two_or_more_races_pop": 21676.0, "not_hispanic_pop": 435203.0, "commuters_by_public_transportation": 6531.0, "households": 217168.0, "median_income": 76866.0, "income_per_capita": 30875.0, "housing_units": 229818.0, "vacant_housing_units": 12650.0, "vacant_housing_units_for_rent": 2923.0, "vacant_housing_units_for_sale": 1860.0, "median_rent": 1354.0, "percent_income_spent_on_rent": 35.8, "owner_occupied_housing_units": 145682.0, "million_dollar_housing_units": 3013.0, "mortgaged_housing_units": 112150.0, "families_with_young_children": 63368.0, "two_parent_families_with_young_children": 41179.0, "two_parents_in_labor_force_families_with_young_children": 22313.0, "two_parents_father_in_labor_force_families_with_young_children": 17628.0, "two_parents_mother_in_labor_force_families_with_young_children": 927.0, "two_parents_not_in_labor_force_families_with_young_children": 311.0, "one_parent_families_with_young_children": 22189.0, "father_one_parent_families_with_young_children": 5929.0, "father_in_labor_force_one_parent_families_with_young_children": 4252.0, "commute_10_14_mins": 33489.0, "commute_15_19_mins": 45074.0, "commute_20_24_mins": 29910.0, "commute_25_29_mins": 12027.0, "commute_30_34_mins": 28711.0, "commute_45_59_mins": 36517.0, "aggregate_travel_time_to_work": 11003040.0, "income_less_10000": 10544.0, "income_10000_14999": 8788.0, "income_15000_19999": 5039.0, "income_20000_24999": 7974.0, "income_25000_29999": 6454.0, "income_30000_34999": 9060.0, "income_35000_39999": 8356.0, "income_40000_44999": 8305.0, "income_45000_49999": 5698.0, "income_50000_59999": 14806.0, "income_60000_74999": 20599.0, "income_75000_99999": 29995.0, "income_100000_124999": 22695.0, "income_125000_149999": 15480.0, "income_150000_199999": 22282.0, "income_200000_or_more": 21093.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 1546.0, "owner_occupied_housing_units_lower_value_quartile": 276700.0, "owner_occupied_housing_units_median_value": 461800.0, "owner_occupied_housing_units_upper_value_quartile": 646500.0, "married_households": 122808.0, "occupied_housing_units": 217168.0, "housing_units_renter_occupied": 71486.0, "dwellings_1_units_detached": 162828.0, "dwellings_1_units_attached": 13301.0, "dwellings_2_units": 1326.0, "dwellings_3_to_4_units": 7002.0, "dwellings_5_to_9_units": 10022.0, "dwellings_10_to_19_units": 8111.0, "dwellings_20_to_49_units": 4675.0, "dwellings_50_or_more_units": 10804.0, "mobile_homes": 11560.0, "housing_built_2005_or_later": 2905.0, "housing_built_2000_to_2004": 3201.0, "housing_built_1939_or_earlier": 2627.0, "male_under_5": 29124.0, "male_5_to_9": 26030.0, "male_10_to_14": 26760.0, "male_15_to_17": 17451.0, "male_18_to_19": 9711.0, "male_20": 5214.0, "male_21": 5704.0, "male_22_to_24": 15487.0, "male_25_to_29": 24522.0, "male_30_to_34": 22750.0, "male_35_to_39": 20967.0, "male_40_to_44": 21446.0, "male_45_to_49": 27326.0, "male_50_to_54": 26412.0, "male_55_to_59": 25878.0, "male_60_61": 8427.0, "male_62_64": 11835.0, "male_65_to_66": 6531.0, "male_67_to_69": 8139.0, "male_70_to_74": 8219.0, "male_75_to_79": 7423.0, "male_80_to_84": 4078.0, "male_85_and_over": 4040.0, "female_under_5": 24903.0, "female_5_to_9": 24738.0, "female_10_to_14": 24909.0, "female_15_to_17": 14901.0, "female_18_to_19": 11079.0, "female_20": 3644.0, "female_21": 5406.0, "female_22_to_24": 12651.0, "female_25_to_29": 27068.0, "female_30_to_34": 23267.0, "female_35_to_39": 20633.0, "female_40_to_44": 22787.0, "female_45_to_49": 25409.0, "female_50_to_54": 26664.0, "female_55_to_59": 24959.0, "female_60_to_61": 8684.0, "female_62_to_64": 10942.0, "female_65_to_66": 7621.0, "female_67_to_69": 8248.0, "female_70_to_74": 10676.0, "female_75_to_79": 8428.0, "female_80_to_84": 5379.0, "female_85_and_over": 5270.0, "white_including_hispanic": 457045.0, "black_including_hispanic": 51987.0, "amerindian_including_hispanic": 8363.0, "asian_including_hispanic": 65141.0, "commute_5_9_mins": 16970.0, "commute_35_39_mins": 5926.0, "commute_40_44_mins": 12182.0, "commute_60_89_mins": 44704.0, "commute_90_more_mins": 26229.0, "households_retirement_income": 37068.0, "armed_forces": 353.0, "civilian_labor_force": 340928.0, "employed_pop": 321054.0, "unemployed_pop": 19874.0, "not_in_labor_force": 212729.0, "pop_16_over": 554010.0, "pop_in_labor_force": 341281.0, "asian_male_45_54": 4412.0, "asian_male_55_64": 3737.0, "black_male_45_54": 4406.0, "black_male_55_64": 3339.0, "hispanic_male_45_54": 18792.0, "hispanic_male_55_64": 11851.0, "white_male_45_54": 24588.0, "white_male_55_64": 26253.0, "bachelors_degree_2": 86297.0, "bachelors_degree_or_higher_25_64": 106374.0, "children": 188816.0, "children_in_single_female_hh": 35740.0, "commuters_by_bus": 3639.0, "commuters_by_car_truck_van": 281257.0, "commuters_by_carpool": 34142.0, "commuters_by_subway_or_elevated": 685.0, "commuters_drove_alone": 247115.0, "different_house_year_ago_different_city": 44327.0, "different_house_year_ago_same_city": 26456.0, "employed_agriculture_forestry_fishing_hunting_mining": 2124.0, "employed_arts_entertainment_recreation_accommodation_food": 30521.0, "employed_construction": 25231.0, "employed_education_health_social": 71833.0, "employed_finance_insurance_real_estate": 21550.0, "employed_information": 11381.0, "employed_manufacturing": 33597.0, "employed_other_services_not_public_admin": 17983.0, "employed_public_administration": 17067.0, "employed_retail_trade": 33141.0, "employed_science_management_admin_waste": 33059.0, "employed_transportation_warehousing_utilities": 15877.0, "employed_wholesale_trade": 7690.0, "female_female_households": 359.0, "four_more_cars": 29123.0, "gini_index": 0.4371, "graduate_professional_degree": 41725.0, "group_quarters": 10717.0, "high_school_including_ged": 105201.0, "households_public_asst_or_food_stamps": 18257.0, "in_grades_1_to_4": 39733.0, "in_grades_5_to_8": 40432.0, "in_grades_9_to_12": 46423.0, "in_school": 207860.0, "in_undergrad_college": 51637.0, "less_than_high_school_graduate": 64870.0, "male_45_64_associates_degree": 8598.0, "male_45_64_bachelors_degree": 18230.0, "male_45_64_graduate_degree": 9342.0, "male_45_64_less_than_9_grade": 6856.0, "male_45_64_grade_9_12": 8157.0, "male_45_64_high_school": 20899.0, "male_45_64_some_college": 27796.0, "male_45_to_64": 99878.0, "male_male_households": 330.0, "management_business_sci_arts_employed": 125513.0, "no_car": 4252.0, "no_cars": 9024.0, "not_us_citizen_pop": 51909.0, "occupation_management_arts": 125513.0, "occupation_natural_resources_construction_maintenance": 33437.0, "occupation_production_transportation_material": 32538.0, "occupation_sales_office": 75413.0, "occupation_services": 54153.0, "one_car": 50362.0, "two_cars": 86458.0, "three_cars": 42201.0, "pop_25_64": 379976.0, "pop_determined_poverty_status": 710452.0, "population_1_year_and_over": 712268.0, "population_3_years_over": 689379.0, "poverty": 93742.0, "sales_office_employed": 75413.0, "some_college_and_associates_degree": 165935.0, "walked_to_work": 3698.0, "worked_at_home": 19327.0, "workers_16_and_over": 315137.0, "associates_degree": 44596.0, "bachelors_degree": 86297.0, "high_school_diploma": 93658.0, "less_one_year_college": 29655.0, "masters_degree": 29860.0, "one_year_more_college": 91684.0, "pop_25_years_over": 464028.0, "commute_35_44_mins": 18108.0, "commute_60_more_mins": 70933.0, "commute_less_10_mins": 21041.0, "commuters_16_over": 295810.0, "hispanic_any_race": 286537.0, "pop_5_years_over": 667713.0, "speak_only_english_at_home": 454292.0, "speak_spanish_at_home": 151927.0, "speak_spanish_at_home_low_english": 61227.0, "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "do_date": "2017"}]}