{"table_name": "congressionaldistrict_2016_5yr", "table_fullname": "bigquery-public-data.census_bureau_acs.congressionaldistrict_2016_5yr", "column_names": ["geo_id", "nonfamily_households", "family_households", "median_year_structure_built", "rent_burden_not_computed", "rent_over_50_percent", "rent_40_to_50_percent", "rent_35_to_40_percent", "rent_30_to_35_percent", "rent_25_to_30_percent", "rent_20_to_25_percent", "rent_15_to_20_percent", "rent_10_to_15_percent", "rent_under_10_percent", "total_pop", "male_pop", "female_pop", "median_age", "white_pop", "black_pop", "asian_pop", "hispanic_pop", "amerindian_pop", "other_race_pop", "two_or_more_races_pop", "not_hispanic_pop", "commuters_by_public_transportation", "households", "median_income", "income_per_capita", "housing_units", "vacant_housing_units", "vacant_housing_units_for_rent", "vacant_housing_units_for_sale", "median_rent", "percent_income_spent_on_rent", "owner_occupied_housing_units", "million_dollar_housing_units", "mortgaged_housing_units", "families_with_young_children", "two_parent_families_with_young_children", "two_parents_in_labor_force_families_with_young_children", "two_parents_father_in_labor_force_families_with_young_children", "two_parents_mother_in_labor_force_families_with_young_children", "two_parents_not_in_labor_force_families_with_young_children", "one_parent_families_with_young_children", "father_one_parent_families_with_young_children", "father_in_labor_force_one_parent_families_with_young_children", "commute_10_14_mins", "commute_15_19_mins", "commute_20_24_mins", "commute_25_29_mins", "commute_30_34_mins", "commute_45_59_mins", "aggregate_travel_time_to_work", "income_less_10000", "income_10000_14999", "income_15000_19999", "income_20000_24999", "income_25000_29999", "income_30000_34999", "income_35000_39999", "income_40000_44999", "income_45000_49999", "income_50000_59999", "income_60000_74999", "income_75000_99999", "income_100000_124999", "income_125000_149999", "income_150000_199999", "income_200000_or_more", "renter_occupied_housing_units_paying_cash_median_gross_rent", "owner_occupied_housing_units_lower_value_quartile", "owner_occupied_housing_units_median_value", "owner_occupied_housing_units_upper_value_quartile", "married_households", "occupied_housing_units", "housing_units_renter_occupied", "dwellings_1_units_detached", "dwellings_1_units_attached", "dwellings_2_units", "dwellings_3_to_4_units", "dwellings_5_to_9_units", "dwellings_10_to_19_units", "dwellings_20_to_49_units", "dwellings_50_or_more_units", "mobile_homes", "housing_built_2005_or_later", "housing_built_2000_to_2004", "housing_built_1939_or_earlier", "male_under_5", "male_5_to_9", "male_10_to_14", "male_15_to_17", "male_18_to_19", "male_20", "male_21", "male_22_to_24", "male_25_to_29", "male_30_to_34", "male_35_to_39", "male_40_to_44", "male_45_to_49", "male_50_to_54", "male_55_to_59", "male_60_61", "male_62_64", "male_65_to_66", "male_67_to_69", "male_70_to_74", "male_75_to_79", "male_80_to_84", "male_85_and_over", "female_under_5", "female_5_to_9", "female_10_to_14", "female_15_to_17", "female_18_to_19", "female_20", "female_21", "female_22_to_24", "female_25_to_29", "female_30_to_34", "female_35_to_39", "female_40_to_44", "female_45_to_49", "female_50_to_54", "female_55_to_59", "female_60_to_61", "female_62_to_64", "female_65_to_66", "female_67_to_69", "female_70_to_74", "female_75_to_79", "female_80_to_84", "female_85_and_over", "white_including_hispanic", "black_including_hispanic", "amerindian_including_hispanic", "asian_including_hispanic", "commute_5_9_mins", "commute_35_39_mins", "commute_40_44_mins", "commute_60_89_mins", "commute_90_more_mins", "households_retirement_income", "armed_forces", "civilian_labor_force", "employed_pop", "unemployed_pop", "not_in_labor_force", "pop_16_over", "pop_in_labor_force", "asian_male_45_54", "asian_male_55_64", "black_male_45_54", "black_male_55_64", "hispanic_male_45_54", "hispanic_male_55_64", "white_male_45_54", "white_male_55_64", "bachelors_degree_2", "bachelors_degree_or_higher_25_64", "children", "children_in_single_female_hh", "commuters_by_bus", "commuters_by_car_truck_van", "commuters_by_carpool", "commuters_by_subway_or_elevated", "commuters_drove_alone", "different_house_year_ago_different_city", "different_house_year_ago_same_city", "employed_agriculture_forestry_fishing_hunting_mining", "employed_arts_entertainment_recreation_accommodation_food", "employed_construction", "employed_education_health_social", "employed_finance_insurance_real_estate", "employed_information", "employed_manufacturing", "employed_other_services_not_public_admin", "employed_public_administration", "employed_retail_trade", "employed_science_management_admin_waste", "employed_transportation_warehousing_utilities", "employed_wholesale_trade", "female_female_households", "four_more_cars", "gini_index", "graduate_professional_degree", "group_quarters", "high_school_including_ged", "households_public_asst_or_food_stamps", "in_grades_1_to_4", "in_grades_5_to_8", "in_grades_9_to_12", "in_school", "in_undergrad_college", "less_than_high_school_graduate", "male_45_64_associates_degree", "male_45_64_bachelors_degree", "male_45_64_graduate_degree", "male_45_64_less_than_9_grade", "male_45_64_grade_9_12", "male_45_64_high_school", "male_45_64_some_college", "male_45_to_64", "male_male_households", "management_business_sci_arts_employed", "no_car", "no_cars", "not_us_citizen_pop", "occupation_management_arts", "occupation_natural_resources_construction_maintenance", "occupation_production_transportation_material", "occupation_sales_office", "occupation_services", "one_car", "two_cars", "three_cars", "pop_25_64", "pop_determined_poverty_status", "population_1_year_and_over", "population_3_years_over", "poverty", "sales_office_employed", "some_college_and_associates_degree", "walked_to_work", "worked_at_home", "workers_16_and_over", "associates_degree", "bachelors_degree", "high_school_diploma", "less_one_year_college", "masters_degree", "one_year_more_college", "pop_25_years_over", "commute_35_44_mins", "commute_60_more_mins", "commute_less_10_mins", "commuters_16_over", "hispanic_any_race", "pop_5_years_over", "speak_only_english_at_home", "speak_spanish_at_home", "speak_spanish_at_home_low_english", "do_date", "pop_15_and_over", "pop_never_married", "pop_now_married", "pop_separated", "pop_widowed", "pop_divorced"], "column_types": ["STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64"], "nested_column_names": ["geo_id", "nonfamily_households", "family_households", "median_year_structure_built", "rent_burden_not_computed", "rent_over_50_percent", "rent_40_to_50_percent", "rent_35_to_40_percent", "rent_30_to_35_percent", "rent_25_to_30_percent", "rent_20_to_25_percent", "rent_15_to_20_percent", "rent_10_to_15_percent", "rent_under_10_percent", "total_pop", "male_pop", "female_pop", "median_age", "white_pop", "black_pop", "asian_pop", "hispanic_pop", "amerindian_pop", "other_race_pop", "two_or_more_races_pop", "not_hispanic_pop", "commuters_by_public_transportation", "households", "median_income", "income_per_capita", "housing_units", "vacant_housing_units", "vacant_housing_units_for_rent", "vacant_housing_units_for_sale", "median_rent", "percent_income_spent_on_rent", "owner_occupied_housing_units", "million_dollar_housing_units", "mortgaged_housing_units", "families_with_young_children", "two_parent_families_with_young_children", "two_parents_in_labor_force_families_with_young_children", "two_parents_father_in_labor_force_families_with_young_children", "two_parents_mother_in_labor_force_families_with_young_children", "two_parents_not_in_labor_force_families_with_young_children", "one_parent_families_with_young_children", "father_one_parent_families_with_young_children", "father_in_labor_force_one_parent_families_with_young_children", "commute_10_14_mins", "commute_15_19_mins", "commute_20_24_mins", "commute_25_29_mins", "commute_30_34_mins", "commute_45_59_mins", "aggregate_travel_time_to_work", "income_less_10000", "income_10000_14999", "income_15000_19999", "income_20000_24999", "income_25000_29999", "income_30000_34999", "income_35000_39999", "income_40000_44999", "income_45000_49999", "income_50000_59999", "income_60000_74999", "income_75000_99999", "income_100000_124999", "income_125000_149999", "income_150000_199999", "income_200000_or_more", "renter_occupied_housing_units_paying_cash_median_gross_rent", "owner_occupied_housing_units_lower_value_quartile", "owner_occupied_housing_units_median_value", "owner_occupied_housing_units_upper_value_quartile", "married_households", "occupied_housing_units", "housing_units_renter_occupied", "dwellings_1_units_detached", "dwellings_1_units_attached", "dwellings_2_units", "dwellings_3_to_4_units", "dwellings_5_to_9_units", "dwellings_10_to_19_units", "dwellings_20_to_49_units", "dwellings_50_or_more_units", "mobile_homes", "housing_built_2005_or_later", "housing_built_2000_to_2004", "housing_built_1939_or_earlier", "male_under_5", "male_5_to_9", "male_10_to_14", "male_15_to_17", "male_18_to_19", "male_20", "male_21", "male_22_to_24", "male_25_to_29", "male_30_to_34", "male_35_to_39", "male_40_to_44", "male_45_to_49", "male_50_to_54", "male_55_to_59", "male_60_61", "male_62_64", "male_65_to_66", "male_67_to_69", "male_70_to_74", "male_75_to_79", "male_80_to_84", "male_85_and_over", "female_under_5", "female_5_to_9", "female_10_to_14", "female_15_to_17", "female_18_to_19", "female_20", "female_21", "female_22_to_24", "female_25_to_29", "female_30_to_34", "female_35_to_39", "female_40_to_44", "female_45_to_49", "female_50_to_54", "female_55_to_59", "female_60_to_61", "female_62_to_64", "female_65_to_66", "female_67_to_69", "female_70_to_74", "female_75_to_79", "female_80_to_84", "female_85_and_over", "white_including_hispanic", "black_including_hispanic", "amerindian_including_hispanic", "asian_including_hispanic", "commute_5_9_mins", "commute_35_39_mins", "commute_40_44_mins", "commute_60_89_mins", "commute_90_more_mins", "households_retirement_income", "armed_forces", "civilian_labor_force", "employed_pop", "unemployed_pop", "not_in_labor_force", "pop_16_over", "pop_in_labor_force", "asian_male_45_54", "asian_male_55_64", "black_male_45_54", "black_male_55_64", "hispanic_male_45_54", "hispanic_male_55_64", "white_male_45_54", "white_male_55_64", "bachelors_degree_2", "bachelors_degree_or_higher_25_64", "children", "children_in_single_female_hh", "commuters_by_bus", "commuters_by_car_truck_van", "commuters_by_carpool", "commuters_by_subway_or_elevated", "commuters_drove_alone", "different_house_year_ago_different_city", "different_house_year_ago_same_city", "employed_agriculture_forestry_fishing_hunting_mining", "employed_arts_entertainment_recreation_accommodation_food", "employed_construction", "employed_education_health_social", "employed_finance_insurance_real_estate", "employed_information", "employed_manufacturing", "employed_other_services_not_public_admin", "employed_public_administration", "employed_retail_trade", "employed_science_management_admin_waste", "employed_transportation_warehousing_utilities", "employed_wholesale_trade", "female_female_households", "four_more_cars", "gini_index", "graduate_professional_degree", "group_quarters", "high_school_including_ged", "households_public_asst_or_food_stamps", "in_grades_1_to_4", "in_grades_5_to_8", "in_grades_9_to_12", "in_school", "in_undergrad_college", "less_than_high_school_graduate", "male_45_64_associates_degree", "male_45_64_bachelors_degree", "male_45_64_graduate_degree", "male_45_64_less_than_9_grade", "male_45_64_grade_9_12", "male_45_64_high_school", "male_45_64_some_college", "male_45_to_64", "male_male_households", "management_business_sci_arts_employed", "no_car", "no_cars", "not_us_citizen_pop", "occupation_management_arts", "occupation_natural_resources_construction_maintenance", "occupation_production_transportation_material", "occupation_sales_office", "occupation_services", "one_car", "two_cars", "three_cars", "pop_25_64", "pop_determined_poverty_status", "population_1_year_and_over", "population_3_years_over", "poverty", "sales_office_employed", "some_college_and_associates_degree", "walked_to_work", "worked_at_home", "workers_16_and_over", "associates_degree", "bachelors_degree", "high_school_diploma", "less_one_year_college", "masters_degree", "one_year_more_college", "pop_25_years_over", "commute_35_44_mins", "commute_60_more_mins", "commute_less_10_mins", "commuters_16_over", "hispanic_any_race", "pop_5_years_over", "speak_only_english_at_home", "speak_spanish_at_home", "speak_spanish_at_home_low_english", "do_date", "pop_15_and_over", "pop_never_married", "pop_now_married", "pop_separated", "pop_widowed", "pop_divorced"], "nested_column_types": ["STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64"], "description": ["US Congressional Districts Geoids", "Nonfamily Households. A householder living alone or with nonrelatives only. Unmarried couples households, whether opposite-sex or same-sex, with no relatives of the householder present are tabulated in nonfamily households.", "Family Households. A family consists of a householder and one or more other people living in the same household who are related to the householder by birth, marriage, or adoption. All people in a household who are related to the householder are regarded as members of his or her family. A family household may contain people not related to the householder, but those people are not included as part of the family of the householder in tabulations. Thus, the number of family households is equal to the number of families, but family households may include more members than do families. A household can contain only one family for purposes of tabulations. Not all households contain families since a household may be comprised of a group of unrelated people or of one person living alone -- these are called nonfamily households. Families are classified by type as either a \"married couple family\" or \"other family\" according to the sex of the householder and the presence of relatives. The data on family type are based ...", "Median Year Structure Built. Median Year Structure Built", "Housing units without rent burden computed. Units for which no rent is paid and units occupied by households that reported no income or a net loss comprise this category", "Housing units spending over 50% income on rent. Gross rent over 50 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 40 to 49.9% income on rent. Gross rent from 40.0 to 49.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 35 to 39.9% income on rent. Gross rent from 35.0 to 39.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 30 to 34.9% income on rent. Gross rent from 30.0 to 34.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 25 to 29.9% income on rent. Gross rent from 25.0 to 29.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 20 to 24.9% income on rent. Gross rent from 20.0 to 24.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 15 to 19.9% income on rent. Gross rent from 15.0 to 19.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 10 to 14.9% income on rent. Gross rent from 10.0 to 14.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending less than 10% on rent. Gross rent less than 10 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Total Population. The total number of all people living in a given geographic area.  This is a very useful catch-all denominator when calculating rates.", "Male Population. The number of people within each geography who are male.", "Female Population. The number of people within each geography who are female.", "Median Age. The median age of all people in a given geographic area.", "White Population. The number of people identifying as white, non-Hispanic in each geography.", "Black or African American Population. The number of people identifying as black or African American, non-Hispanic in each geography.", "Asian Population. The number of people identifying as Asian, non-Hispanic in each geography.", "Hispanic Population. The number of people identifying as Hispanic or Latino in each geography.", "American Indian and Alaska Native Population. The number of people identifying as American Indian or Alaska native in each geography.", "Other Race population. The number of people identifying as another race in each geography", "Two or more races population. The number of people identifying as two or more races in each geography", "Population not Hispanic. The number of people not identifying as Hispanic or Latino in each geography.", "Commuters by Public Transportation. The number of workers age 16 years and over within a geographic area who primarily traveled to work by public transportation.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Households. A count of the number of households in each geography. A household consists of one or more people who live in the same dwelling and also share at meals or living accommodation, and may consist of a single family or some other grouping of people. ", "Median Household Income in the past 12 Months. Within a geographic area, the median income received by every household on a regular basis before payments for personal income taxes, social security, union dues, medicare deductions, etc.  It includes income received from wages, salary, commissions, bonuses, and tips; self-employment income from own nonfarm or farm businesses, including proprietorships and partnerships; interest, dividends, net rental income, royalty income, or income from estates and trusts; Social Security or Railroad Retirement income; Supplemental Security Income (SSI); any cash public assistance or welfare payments from the state or local welfare office; retirement, survivor, or disability benefits; and any other sources of income received regularly such as Veterans' (VA) payments, unemployment and/or worker's compensation, child support, and alimony.", "Per Capita Income in the past 12 Months. Per capita income is the mean income computed for every man, woman, and child in a particular group. It is derived by dividing the total income of a particular group by the total population.", "Housing Units. A count of housing units in each geography.  A housing unit is a house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters.", "Vacant Housing Units. The count of vacant housing units in a geographic area. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Vacant Housing Units for Rent. The count of vacant housing units in a geographic area that are for rent. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Vacant Housing Units for Sale. The count of vacant housing units in a geographic area that are for sale. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Median Rent. The median contract rent within a geographic area. The contract rent is the monthly rent agreed to or contracted for, regardless of any furnishings, utilities, fees, meals, or services that may be included. For vacant units, it is the monthly rent asked for the rental unit at the time of interview.", "Percent of Household Income Spent on Rent. Within a geographic area, the median percentage of household income which was spent on gross rent.  Gross rent is the amount of the contract rent plus the estimated average monthly cost of utilities (electricity, gas, water, sewer etc.) and fuels (oil, coal, wood, etc.) if these are paid by the renter.  Household income is the sum of the income of all people 15 years and older living in the household.", "Owner-occupied Housing Units", "Owner-occupied Housing Units valued at $1,000,000 or more.. The count of owner occupied housing units in a geographic area that are valued at $1,000,000 or more.  Value is the respondent's estimate of how much the property (house and lot, mobile home and lot, or condominium unit) would sell for if it were for sale.", "Owner-occupied Housing Units with a Mortgage. The count of housing units within a geographic area that are mortagaged. \"Mortgage\" refers to all forms of debt where the property is pledged as security for repayment of the debt, including deeds of trust, trust deed, contracts to purchase, land contracts, junior mortgages, and home equity loans.", "Families with young children (under 6 years of age)", "Two-parent families with young children (under 6 years of age)", "Two-parent families, both parents in labor force with young children (under 6 years of age)", "Two-parent families, father only in labor force with young children (under 6 years of age)", "Two-parent families, mother only in labor force with young children (under 6 years of age)", "Two-parent families, neither parent in labor force with young children (under 6 years of age)", "One-parent families with young children (under 6 years of age)", "One-parent families, father, with young children (under 6 years of age)", "One-parent families, father in labor force, with young children (under 6 years of age)", "Number of workers with a commute between 10 and 14 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 10 and 14 minutes. ", "Number of workers with a commute between 15 and 19 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 15 and 19 minutes. ", "Number of workers with a commute between 20 and 24 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 20 and 24 minutes.", "Number of workers with a commute between 25 and 29 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 25 and 29 minutes. ", "Number of workers with a commute between 30 and 34 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 30 and 34 minutes. ", "Number of workers with a commute between 45 and 59 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 45 and 59 minutes. ", "Aggregate travel time to work. The total number of minutes every worker in a geographic area over the age of 16 who did not work from home spent spent commuting to work in one day", "Households with income less than $10,000. The number of households in a geographic area whose annual income was less than $10,000.", "Households with income of $10,000 to $14,999. The number of households in a geographic area whose annual income was between $10,000 and $14,999.", "Households with income of $15,000 to $19,999. The number of households in a geographic area whose annual income was between $15,000 and $19,999.", "Households with income of $20,000 to $24,999. The number of households in a geographic area whose annual income was between $20,000 and $24,999.", "Households with income of $25,000 to $29,999. The number of households in a geographic area whose annual income was between $20,000 and $24,999.", "Households with income of $30,000 to $34,999. The number of households in a geographic area whose annual income was between $30,000 and $34,999.", "Households with income of $35,000 to $39,999. The number of households in a geographic area whose annual income was between $35,000 and $39,999.", "Households with income of $40,000 to $44,999. The number of households in a geographic area whose annual income was between $40,000 and $44,999.", "Households with income of $45,000 to $49,999. The number of households in a geographic area whose annual income was between $45,000 and $49,999.", "Households with income of $50,000 to $59,999. The number of households in a geographic area whose annual income was between $50,000 and $59,999.", "Households with income of $60,000 to $74,999. The number of households in a geographic area whose annual income was between $60,000 and $74,999.", "Households with income of $75,000 to $99,999. The number of households in a geographic area whose annual income was between $75,000 and $99,999.", "Households with income of $100,000 to $124,999. The number of households in a geographic area whose annual income was between $100,000 and $124,999.", "Households with income of $125,000 to $149,999. The number of households in a geographic area whose annual income was between $125,000 and $149,999.", "Households with income of $150,000 to $199,999. The number of households in a geographic area whose annual income was between $150,000 and $1999,999.", "Households with income of $200,000 Or More. The number of households in a geographic area whose annual income was more than $200,000.", "Renter-Occupied Housing Units Paying Cash Rent Median Gross Rent", "Owner-Occupied Housing Units Lower Value Quartile", "Owner-Occupied Housing Units Median Value. The middle value (median) in a geographic area owner occupied housing units.", "Owner-Occupied Housing Units Upper Value Quartile", "Married households. People in formal marriages, as well as people in common-law marriages, are included. Does not include same-sex marriages.", "Occupied housing units. A housing unit is classified as occupied if it is the usual place of residence of the person or group of people living in it at the time of enumeration.", "Renter occupied housing units. All occupied units which are not owner occupied, whether they are rented for cash rent or occupied without payment of cash rent, are classified as renter-occupied.", "Single-family (one unit) detached dwellings. This is a 1-unit structure detached from any other house, that is, with open space on all four sides. Such structures are considered detached even if they have an adjoining shed or garage. A one-family house that contains a business is considered detached as long as the building has open space on all four sides. Mobile homes to which one or more permanent rooms have been added or built also are included.", "Single-family (one unit) attached dwellings. This is a 1-unit structure that has one or more walls extending from ground to roof separating it from adjoining structures. In row houses (sometimes called townhouses), double houses, or houses attached to nonresidential structures, each house is a separate, attached structure if the dividing or common wall goes from ground to roof.", "Two-family (two unit) dwellings", "Multifamily dwellings with three to 4 units", "Apartment buildings with 5 to 9 units", "Apartment buildings with 10 to 19 units", "Apartment buildings with 20 to 49 units", "Apartment buildings with 50 or more units", "Mobile homes. A manufactured home is defined as a movable dwelling, 8 feet or more wide and 40 feet or more long, designed to be towed on its own chassis, with transportation gear integral to the unit when it leaves the factory, and without need of a permanent foundation. These homes are built in accordance with the U.S. Department of Housing and Urban Development (HUD) building code.", "Housing units built in 2005 or later. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built in 2005 or later.", "Housing units built between 2000 and 2004. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built from 2000 to 2004.", "Housing units built before 1939. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built in 1939 or earlier.", "Male under 5 years. The male population over the age of five years within the specified area.", "Male age 5 to 9. The male population between the age of five years to nine years within the specified area.", "Male age 10 to 14. The male population between the age of ten years to fourteen years within the specified area.", "Male age 15 to 17. The male population between the age of fifteeen years to seventeen years within the specified area.", "Male age 18 and 19. The male population between the age of eighteen years to nineteen years within the specified area.", "Male age 20. The male population with an age of twenty years within the specified area.", "Male age 21. The male population with an age of twenty-one years within the specified area.", "Male age 22 to 24. The male population between the age of twenty-two years to twenty-four years within the specified area.", "Male age 25 to 29. The male population between the age of twenty-five years to twenty-nine years within the specified area.", "Male age 30 to 34. The male population between the age of thirty years to thirty-four years within the specified area.", "Male age 35 to 39. The male population between the age of thirty-five years to thirty-nine years within the specified area.", "Male age 40 to 44. The male population between the age of fourty years to fourty-four years within the specified area.", "Men age 45 to 49. The male population between the age of fourty-five years to fourty-nine years within the specified area.", "Men age 50 to 54. The male population between the age of fifty years to fifty-four years within the specified area.", "Men age 55 to 59. The male population between the age of fifty-five years to fifty-nine years within the specified area.", "Men age 60 to 61. The male population between the age of sixty years to sixty-one years within the specified area.", "Men age 62 to 64. The male population between the age of sixty-two years to sixty-four years within the specified area.", "Male age 65 to 66. The male population between the age of sixty-five years to sixty-six years within the specified area.", "Male age 67 to 69. The male population between the age of sixty-seven years to sixty-nine years within the specified area.", "Male age 70 to 74. The male population between the age of seventy years to seventy-four years within the specified area.", "Male age 75 to 79. The male population between the age of seventy-five years to seventy-nine years within the specified area.", "Male age 80 to 84. The male population between the age of eighty years to eighty-four years within the specified area.", "Male age 85 and over. The male population of the age of eighty-five years and over within the specified area.", "Female under 5 years. The female population over the age of five years within the specified area.", "Female age 5 to 9. The female population between the age of five years to nine years within the specified area.", "Female age 10 to 14. The female population between the age of ten years to fourteen years within the specified area.", "Female age 15 to 17. The female population between the age of fifteeen years to seventeen years within the specified area.", "Female age 18 and 19. The female population between the age of eighteen years to nineteen years within the specified area.", "Female age 20. The female population with an age of twenty years within the specified area.", "Female age 21. The female population with an age of twenty-one years within the specified area.", "Female age 22 to 24. The female population between the age of twenty-two years to twenty-four years within the specified area.", "Female age 25 to 29. The female population between the age of twenty-five years to twenty-nine years within the specified area.", "Female age 30 to 34. The female population between the age of thirty years to thirty-four years within the specified area.", "Female age 35 to 39. The female population between the age of thirty-five years to thirty-nine years within the specified area.", "Female age 40 to 44. The female population between the age of fourty years to fourty-four years within the specified area.", "Female age 45 to 49. The female population between the age of fourty-five years to fourty-nine years within the specified area.", "Female age 50 to 54. The female population between the age of fifty years to fifty-four years within the specified area.", "Female age 55 to 59. The female population between the age of fifty-five years to fifty-nine years within the specified area.", "Female age 60 and 61. The female population between the age of sixty years to sixty-one years within the specified area.", "Female age 62 to 64. The female population between the age of sixty-two years to sixty-four years within the specified area.", "Female age 65 to 66. The female population between the age of sixty-five years to sixty-six years within the specified area.", "Female age 67 to 69. The female population between the age of sixty-seven years to sixty-nine years within the specified area.", "Female age 70 to 74. The female population between the age of seventy years to seventy-four years within the specified area.", "Female age 75 to 79. The female population between the age of seventy-five years to seventy-nine years within the specified area.", "Female age 80 to 84. The female population between the age of eighty years to eighty-four years within the specified area.", "Female age 85 and over. The female population of the age of eighty-five years and over within the specified area.", "White including Hispanic", "Black including Hispanic", "American Indian including Hispanic", "Asian including Hispanic", "Number of workers with a commute between 5 and 9 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 5 and 9 minutes.", "Number of workers with a commute between 35 and 39 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 35 and 39 minutes. ", "Number of workers with a commute between 40 and 44 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 40 and 44 minutes. ", "Number of workers with a commute between 60 and 89 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 60 and 89 minutes .", "Number of workers with a commute of more than 90 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute more than 90 minutes.", "Households receiving retirement income", "Population in Armed Forces. The number of people in each geography who are members of the U.S. Armed Forces (people on active duty with the United States Army, Air Force, Navy, Marine Corps, or Coast Guard).", "Population in Civilian Labor Force. The number of civilians 16 years and over in each geography who can be classified as either \"employed\" or \"unemployed\" below.", "Employed Population. The number of civilians 16 years old and over in each geography who either (1) were \"at work,\" that is, those who did any work at all during the reference week as paid employees, worked in their own business or profession, worked on their own farm, or worked 15 hours or more as unpaid workers on a family farm or in a family business; or (2) were \"with a job but not at work,\" that is, those who did not work during the reference week but had jobs or businesses from which they were temporarily absent due to illness, bad weather, industrial dispute, vacation, or other personal reasons. Excluded from the employed are people whose only activity consisted of work around the house or unpaid volunteer work for religious, charitable, and similar organizations; also excluded are all institutionalized people and people on active duty in the United States Armed Forces.", "", "Population Not in Labor Force. The number of people in each geography who are 16 years old and over who are not classified as members of the labor force. This category consists mainly of students, homemakers, retired workers, seasonal workers interviewed in an off season who were not looking for work, institutionalized people, and people doing only incidental unpaid family work (less than 15 hours during the reference week).", "Population age 16 and over. The number of people in each geography who are age 16 or over.", "Population in Labor Force. The number of people in each geography who are either in the civilian labor force or are members of the U.S. Armed Forces (people on active duty with the United States Army, Air Force, Navy, Marine Corps, or Coast Guard).", "Asian Men age 45 to 54", "Asian Men age 55 to 64", "Black Men age 45 to 54", "Black Men age 55 to 64", "Hispanic Men age 45 to 54", "Hispanic Men age 55 to 64", "White Men age 45 to 54", "White Men age 55 to 64", "Population who completed a bachelor's degree. From mobility table.", "Population with Bachelors Degree or Higher, Ages 25 to 64. The number of people in each geography who are between the ages of 25 and 64 who have attained a bachelors degree or higher. ", "Children under 18 Years of Age. The number of people within each geography who are under 18 years of age.", "Children under 18 years of age in single female-led household", "Commuters by Bus. The number of workers age 16 years and over within a geographic area who primarily traveled to work by bus.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.  This is a subset of workers who commuted by public transport.", "Commuters by Car, Truck, or Van. The number of workers age 16 years and over within  a geographic area who primarily traveled to work by car, truck or  van.  This is the principal mode of travel or type of conveyance,  by distance rather than time, that the worker usually used to get  from home to work.", "Commuters by Carpool. The number of workers age 16 years and over within a geographic area who primarily traveled to work by carpool.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Commuters by Subway or Elevated. The number of workers age 16 years and over within a geographic area who primarily traveled to work by subway or elevated train.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.  This is a subset of workers who commuted by public transport.", "Commuters who drove alone. The number of workers age 16 years and over within a geographic area who primarily traveled by car driving alone. This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Lived in a different house one year ago in a different city. All people in a geographic area who lived in a different city within the year prior to the survey.", "Lived in a different house one year ago in the same city. All people in a geographic area who lived in the same city but moved to a different unit within the year prior to the survey.", "Workers employed in firms in agriculture, forestry, fishing, hunting, or mining. The Agriculture, Forestry, Fishing and Hunting sector comprises establishments primarily engaged in growing crops, raising animals, harvesting timber, and harvesting fish and other animals from a farm, ranch, or their natural habitats.", "Workers employed in firms in arts, entertainment, recreation, accommodation and food services. The Arts, Entertainment, and Recreation sector includes a wide range of establishments that operate facilities or provide services to meet varied cultural, entertainment, and recreational interests of their patrons. This sector comprises (1) establishments that are involved in producing, promoting, or participating in live performances, events, or exhibits intended for public viewing; (2) establishments that preserve and exhibit objects and sites of historical, cultural, or educational interest; and (3) establishments that operate facilities or provide services that enable patrons to participate in recreational activities or pursue amusement, hobby, and leisure-time interests.", "Workers employed in firms in construction. The Construction sector comprises establishments primarily engaged in the construction of buildings or engineering projects (e.g., highways and utility systems). Construction work done may include new work, additions, alterations, or maintenance and repairs.", "Workers employed in firms in educational services, health care, and social assistance. Outpatient health services, other than hospital care, including: public health administration; research and education; categorical health programs; treatment and immunization clinics; nursing; environmental health activities such as air and water pollution control; ambulance service if provided separately from fire protection services, and other general public health activities such as mosquito abatement. School health services provided by health agencies (rather than school agencies) are included here. Sewage treatment operations are classified under Sewerage.", "Workers employed in firms in finance, insurance, real estate and rental and leasing. The Real Estate and Rental and Leasing sector comprises establishments primarily engaged in renting, leasing, or otherwise allowing the use of tangible or intangible assets, and establishments providing related services. The major portion of this sector comprises establishments that rent, lease, or otherwise allow the use of their own assets by others. The assets may be tangible, as is the case of real estate and equipment, or intangible, as is the case with patents and trademarks.", "Workers employed in firms in information. The Information sector comprises establishments engaged in the following processes: (a) producing and distributing information and cultural products, (b) providing the means to transmit or distribute these products as well as data or communications, and (c) processing data. Included are the publishing industries, the motion picture and sound recording industries; the broadcasting industries, the telecommunications industries; Web search portals, data processing industries, and the information services industries.", "Workers employed in firms in manufacturing. The Manufacturing sector comprises establishments engaged in the mechanical, physical, or chemical transformation of materials, substances, or components into new products.", "Workers employed in firms in other services except public administration. The Other Services (Except Public Administration) sector comprises establishments engaged in providing services not specifically provided for elsewhere in the classification system. Establishments in this sector are primarily engaged in activities such as equipment and machinery repairing, promoting or administering religious activities, grantmaking, advocacy, and providing drycleaning and laundry services, personal care services, death care services, pet care services, photofinishing services, temporary parking services, and dating services. Private households that engage in employing workers on or about the premises in activities primarily concerned with the operation of the household are included in this sector.", "Workers employed in firms in public administration. The Public Administration sector consists of establishments of federal, state, and local government agencies that administer, oversee, and manage public programs and have executive, legislative, or judicial authority over other institutions within a given area. These agencies also set policy, create laws, adjudicate civil and criminal legal cases, provide for public safety and for national defense. In general, government establishments in the public administration sector oversee governmental programs and activities that are not performed by private establishments.", "Workers employed in firms in retail trade. The Retail Trade sector comprises establishments engaged in retailing merchandise, generally without transformation, and rendering services incidental to the sale of merchandise. The retailing process is the final step in the distribution of merchandise; retailers are, therefore, organized to sell merchandise in small quantities to the general public.", "Workers employed in firms in professional scientific, management, administrative and waste management services. The Administrative and Support and Waste Management and Remediation Services sector comprises establishments performing routine support activities for the day-to-day operations of other organizations. The establishments in this sector specialize in one or more of these support activities and provide these services to clients in a variety of industries and, in some cases, to households. Activities performed include office administration, hiring and placing of personnel, document preparation and similar clerical services, solicitation, collection, security and surveillance services, cleaning, and waste disposal services.", "Workers employed in firms in transportation, warehousing, and utilities. The Transportation and Warehousing sector includes industries providing transportation of passengers and cargo, warehousing and storage for goods, scenic and sightseeing transportation, and support activities related to modes of transportation. The modes of transportation are air, rail, water, road, and pipeline.", "Workers employed in firms in wholesale trade. The Wholesale Trade sector comprises establishments engaged in wholesaling merchandise, generally without transformation, and rendering services incidental to the sale of merchandise. The wholesaling process is an intermediate step in the distribution of merchandise. Wholesalers are organized to sell or arrange the purchase or sale of (a) goods for resale (i.e., goods sold to other wholesalers or retailers), (b) capital or durable nonconsumer goods, and (c) raw and intermediate materials and supplies used in production.", "Households with two female partners. An unmarried partner is a person age 15 years and over, who is not related to the householder, who shares living quarters, and who has an intimate relationship with the householder.", "Four car households. The number of households with four or more passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Gini Index. The Gini index, or index of income concentration, is a statistical measure of income inequality ranging from 0 to 1. A measure of 1 indicates perfect inequality, i.e., one household having all the income and rest having none. A measure of 0 indicates perfect equality, i.e., all households having an equal share of income.", "Population who completed a graduate or professional degree", "Population living in group quarters", "Population with high school degree, including GED. The number of people in a geographic area over the age of 25 who attained a high school degree or GED.", "Households on cash public assistance or receiving food stamps (SNAP)", "Students Enrolled in Grades 1 to 4. The total number of people in each geography currently enrolled in grades 1 through 4 inclusive.  This corresponds roughly to elementary school.", "Students Enrolled in Grades 5 to 8. The total number of people in each geography currently enrolled in grades 5 through 8 inclusive.  This corresponds roughly to middle school.", "Students Enrolled in Grades 9 to 12. The total number of people in each geography currently enrolled in grades 9 through 12 inclusive.  This corresponds roughly to high school.", "Students Enrolled in School. The total number of people in each geography currently enrolled at any level of school, from nursery or pre-school to advanced post-graduate education.  Only includes those over the age of 3.", "Students Enrolled as Undergraduate in College. The number of people in a geographic area who are enrolled in college at the undergraduate level. Enrollment refers to being registered or listed as a student in an educational program leading to a college degree. This may be a public school or college, a private school or college.", "Less than high school graduate. The number of people in a geographic area over the age of 25 who have not completed high school or any other advanced degree.", "Men age 45 to 64 who obtained an associate's degree", "Men age 45 to 64 who obtained a bachelor's degree", "Men age 45 to 64 who obtained a graduate or professional degree", "Men age 45 to 64 who attained less than a 9th grade education", "Men age 45 to 64 who attained between 9th and 12th grade, no diploma", "Men age 45 to 64 who completed high school or obtained GED", "Men age 45 to 64 who completed some college, no degree", "Men age 45 to 64 (\"middle aged\"). The male population between the age of fourty-five years to sixty-four years within the specified area.", "Households with two male partners. An unmarried partner is a person age 15 years and over, who is not related to the householder, who shares living quarters, and who has an intimate relationship with the householder.", "Civilian Employed Population in Management, Business, Science, and Arts Occupations. The number of employed civilians 16 years old and over in each geography in the Management, Business, Science, and Arts occupations. Occupation codes are based on the Standard Occupational Classification (SOC), published by the Executive Office of the President, Office of Management and Budget.", "Workers age 16 and over with no vehicle. All people in a geographic area over the age of 16 who do not own a car.", "Car-free households. The number of households without passenger cars, vans, and pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Not a U.S. Citizen Population. The number of people within each geography who indicated that they are not U.S. citizens.", "Workers employed in management business science and arts occupations", "Workers employed in natural resources, construction, and maintenance occupations", "Workers employed in production, transportation, and material moving", "Workers employed in sales and office occupations", "Workers employed in service occupations", "One car households. The number of households with one passenger car, van, , pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Two car households. The number of households with two passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Three car households. The number of households with one passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Population age 25 to 64. The number of people in each geography who are between the ages of 25 and 64. ", "Population for Whom Poverty Status Determined. The number of people in each geography who could be identified as either living in poverty or not.  This should be used as the denominator when calculating poverty rates, as it excludes people for whom it was not possible to determine poverty.", "Population 1 year and over. All people, male and female, child and adult, living in a given geographic area that are 1 year and older.", "Population 3 Years and Over. The total number of people in each geography age 3 years and over.  This denominator is mostly used to calculate rates of school enrollment.", "Income In The Past 12 Months Below Poverty Level. The number of people in a geographic area who are part of a family (which could be just them as an individual) determined to be \"in poverty\" following the Office of Management and Budget's Directive 14. (https://www.census.gov/hhes/povmeas/methodology/ombdir14.html)", "Civilian Employed Population in Sales and Office Occupations. The number of employed civilians 16 years old and over in each geography in the Sales and Office occupations. Occupation codes are based on the Standard Occupational Classification (SOC), published by the Executive Office of the President, Office of Management and Budget.", "Population who completed some college or obtained associate's degree. The number of people in a geographic area over the age of 25 who obtained an associate's degree, and did not complete a more advanced degree.", "Walked to Work. The number of workers age 16 years and over within a geographic area who primarily walked to work.  This would mean that of any way of getting to work, they travelled the most distance walking.", "Worked at Home. The count within a geographical area of workers over the age of 16 who worked at home.", "Workers over the Age of 16. The number of people in each geography who work. Workers include those employed at private for-profit companies, the self-employed, government workers and non-profit employees.", "Population Completed Associate's Degree. The number of people in a geographic area over the age of 25 who obtained a associate's degree, and did not complete a more advanced degree.", "Population Completed Bachelor's Degree. The number of people in a geographic area over the age of 25 who obtained a bachelor's degree, and did not complete a more advanced degree.", "Population Completed High School. The number of people in a geographic area over the age of 25 who completed high school, and did not complete a more advanced degree.", "Population completed less than one year of college, no degree. The number of people in a geographic area over the age of 25 who attended college for less than one year and no further.", "Population Completed Master's Degree. The number of people in a geographic area over the age of 25 who obtained a master's degree, but did not complete a more advanced degree.", "Population completed more than one year of college, no degree. The number of people in a geographic area over the age of 25 who attended college for more than one year but did not obtain a degree", "Population 25 Years and Over. The number of people in a geographic area who are over the age of 25.  This is used mostly as a denominator of educational attainment.", "Number of workers with a commute between 35 and 44 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 35 and 44 minutes. ", "Number of workers with a commute of over 60 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in over 60 minutes.", "Number of workers with less than 10 minute commute. The number of workers in a geographic area over the age of 16 who do not work from home and commute in less than 10 minutes.", "Workers age 16 and over who do not work from home. The number of workers in a geographic area over the age of 16 who do not work from home", "Hispanic of any race", "Population 5 Years and Over. The number of people in a geographic area who are over the age of 5.  This is primarily used as a denominator of measures of language spoken at home.", "Speaks only English at Home. The number of people in a geographic area over age 5 who speak only English at home.", "Speaks Spanish at Home. The number of people in a geographic area over age 5 who speak Spanish at home, possibly in addition to other languages.", "Speaks Spanish at Home, speaks English less than \"very well\"", null, "Population 15 Years and Over. The number of people in a geographic area who are over the age of 15.  This is used mostly as a denominator of marital status.", "Never Married. The number of people in a geographic area who have never been married.", "Currently married. The number of people in a geographic area who are currently married", "Married but separated. The number of people in a geographic area who are married but separated", "Widowed. The number of people in a geographic area who are widowed", "Divorced. The number of people in a geographic area who are divorced"], "sample_rows": [{"geo_id": "0622", "nonfamily_households": 63161.0, "family_households": 176315.0, "median_year_structure_built": 1984.0, "rent_burden_not_computed": 5808.0, "rent_over_50_percent": 26209.0, "rent_40_to_50_percent": 9978.0, "rent_35_to_40_percent": 6474.0, "rent_30_to_35_percent": 8291.0, "rent_25_to_30_percent": 10696.0, "rent_20_to_25_percent": 11723.0, "rent_15_to_20_percent": 10180.0, "rent_10_to_15_percent": 6397.0, "rent_under_10_percent": 3015.0, "total_pop": 739449.0, "male_pop": 364833.0, "female_pop": 374616.0, "median_age": 32.5, "white_pop": 298264.0, "black_pop": 20709.0, "asian_pop": 54124.0, "hispanic_pop": 345109.0, "amerindian_pop": 3009.0, "other_race_pop": 1284.0, "two_or_more_races_pop": 15917.0, "not_hispanic_pop": 394340.0, "commuters_by_public_transportation": 2646.0, "households": 239476.0, "median_income": 53868.0, "income_per_capita": 24501.0, "housing_units": 253840.0, "vacant_housing_units": 14364.0, "vacant_housing_units_for_rent": 3831.0, "vacant_housing_units_for_sale": 1954.0, "median_rent": 827.0, "percent_income_spent_on_rent": 32.7, "owner_occupied_housing_units": 140705.0, "million_dollar_housing_units": 988.0, "mortgaged_housing_units": 101449.0, "families_with_young_children": 68030.0, "two_parent_families_with_young_children": 40162.0, "two_parents_in_labor_force_families_with_young_children": 20968.0, "two_parents_father_in_labor_force_families_with_young_children": 16692.0, "two_parents_mother_in_labor_force_families_with_young_children": 1606.0, "two_parents_not_in_labor_force_families_with_young_children": 896.0, "one_parent_families_with_young_children": 27868.0, "father_one_parent_families_with_young_children": 6965.0, "father_in_labor_force_one_parent_families_with_young_children": 6276.0, "commute_10_14_mins": 50625.0, "commute_15_19_mins": 59384.0, "commute_20_24_mins": 49834.0, "commute_25_29_mins": 17067.0, "commute_30_34_mins": 30172.0, "commute_45_59_mins": 14473.0, "aggregate_travel_time_to_work": 6241090.0, "income_less_10000": 15450.0, "income_10000_14999": 12734.0, "income_15000_19999": 13145.0, "income_20000_24999": 13653.0, "income_25000_29999": 12053.0, "income_30000_34999": 12666.0, "income_35000_39999": 11263.0, "income_40000_44999": 10631.0, "income_45000_49999": 10083.0, "income_50000_59999": 19085.0, "income_60000_74999": 24022.0, "income_75000_99999": 28740.0, "income_100000_124999": 20384.0, "income_125000_149999": 12209.0, "income_150000_199999": 12413.0, "income_200000_or_more": 10945.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 967.0, "owner_occupied_housing_units_lower_value_quartile": 148100.0, "owner_occupied_housing_units_median_value": 220500.0, "owner_occupied_housing_units_upper_value_quartile": 320200.0, "married_households": 122193.0, "occupied_housing_units": 239476.0, "housing_units_renter_occupied": 98771.0, "dwellings_1_units_detached": 184376.0, "dwellings_1_units_attached": 7014.0, "dwellings_2_units": 5534.0, "dwellings_3_to_4_units": 16868.0, "dwellings_5_to_9_units": 13991.0, "dwellings_10_to_19_units": 5436.0, "dwellings_20_to_49_units": 3325.0, "dwellings_50_or_more_units": 7682.0, "mobile_homes": 9380.0, "housing_built_2005_or_later": 1396.0, "housing_built_2000_to_2004": 6326.0, "housing_built_1939_or_earlier": 7789.0, "male_under_5": 29432.0, "male_5_to_9": 30378.0, "male_10_to_14": 29944.0, "male_15_to_17": 17317.0, "male_18_to_19": 11293.0, "male_20": 6087.0, "male_21": 5557.0, "male_22_to_24": 16628.0, "male_25_to_29": 27673.0, "male_30_to_34": 25974.0, "male_35_to_39": 23087.0, "male_40_to_44": 22383.0, "male_45_to_49": 21925.0, "male_50_to_54": 21619.0, "male_55_to_59": 19649.0, "male_60_61": 7471.0, "male_62_64": 9876.0, "male_65_to_66": 6194.0, "male_67_to_69": 7662.0, "male_70_to_74": 9333.0, "male_75_to_79": 6648.0, "male_80_to_84": 4552.0, "male_85_and_over": 4151.0, "female_under_5": 28928.0, "female_5_to_9": 28845.0, "female_10_to_14": 27104.0, "female_15_to_17": 17351.0, "female_18_to_19": 10714.0, "female_20": 6623.0, "female_21": 5459.0, "female_22_to_24": 16543.0, "female_25_to_29": 27079.0, "female_30_to_34": 25621.0, "female_35_to_39": 22136.0, "female_40_to_44": 23285.0, "female_45_to_49": 22523.0, "female_50_to_54": 22853.0, "female_55_to_59": 21646.0, "female_60_to_61": 8251.0, "female_62_to_64": 10557.0, "female_65_to_66": 6931.0, "female_67_to_69": 8418.0, "female_70_to_74": 11073.0, "female_75_to_79": 7924.0, "female_80_to_84": 6233.0, "female_85_and_over": 8519.0, "white_including_hispanic": 543803.0, "black_including_hispanic": 23239.0, "amerindian_including_hispanic": 7265.0, "asian_including_hispanic": 56341.0, "commute_5_9_mins": 33485.0, "commute_35_39_mins": 4290.0, "commute_40_44_mins": 5944.0, "commute_60_89_mins": 8783.0, "commute_90_more_mins": 5190.0, "households_retirement_income": 39729.0, "armed_forces": 691.0, "civilian_labor_force": 343310.0, "employed_pop": 308834.0, "unemployed_pop": 34476.0, "not_in_labor_force": 208812.0, "pop_16_over": 552813.0, "pop_in_labor_force": 344001.0, "asian_male_45_54": 3037.0, "asian_male_55_64": 3030.0, "black_male_45_54": 1547.0, "black_male_55_64": 920.0, "hispanic_male_45_54": 17962.0, "hispanic_male_55_64": 11731.0, "white_male_45_54": 20063.0, "white_male_55_64": 20625.0, "bachelors_degree_2": 71240.0, "bachelors_degree_or_higher_25_64": 88507.0, "children": 209299.0, "children_in_single_female_hh": 53801.0, "commuters_by_bus": 2527.0, "commuters_by_car_truck_van": 273722.0, "commuters_by_carpool": 33927.0, "commuters_by_subway_or_elevated": 32.0, "commuters_drove_alone": 239795.0, "different_house_year_ago_different_city": 55110.0, "different_house_year_ago_same_city": 52895.0, "employed_agriculture_forestry_fishing_hunting_mining": 26581.0, "employed_arts_entertainment_recreation_accommodation_food": 25830.0, "employed_construction": 15502.0, "employed_education_health_social": 77275.0, "employed_finance_insurance_real_estate": 16572.0, "employed_information": 4009.0, "employed_manufacturing": 22126.0, "employed_other_services_not_public_admin": 14643.0, "employed_public_administration": 20502.0, "employed_retail_trade": 34529.0, "employed_science_management_admin_waste": 25319.0, "employed_transportation_warehousing_utilities": 13870.0, "employed_wholesale_trade": 12076.0, "female_female_households": 351.0, "four_more_cars": 15173.0, "gini_index": 0.4647, "graduate_professional_degree": 36948.0, "group_quarters": 7987.0, "high_school_including_ged": 103292.0, "households_public_asst_or_food_stamps": 42253.0, "in_grades_1_to_4": 46999.0, "in_grades_5_to_8": 46010.0, "in_grades_9_to_12": 47664.0, "in_school": 223442.0, "in_undergrad_college": 52065.0, "less_than_high_school_graduate": 89593.0, "male_45_64_associates_degree": 6469.0, "male_45_64_bachelors_degree": 12867.0, "male_45_64_graduate_degree": 7266.0, "male_45_64_less_than_9_grade": 11348.0, "male_45_64_grade_9_12": 6532.0, "male_45_64_high_school": 17674.0, "male_45_64_some_college": 18384.0, "male_45_to_64": 80540.0, "male_male_households": 456.0, "management_business_sci_arts_employed": 102636.0, "no_car": 7171.0, "no_cars": 14800.0, "not_us_citizen_pop": 77013.0, "occupation_management_arts": 102636.0, "occupation_natural_resources_construction_maintenance": 40045.0, "occupation_production_transportation_material": 36210.0, "occupation_sales_office": 73340.0, "occupation_services": 56603.0, "one_car": 76344.0, "two_cars": 95636.0, "three_cars": 37523.0, "pop_25_64": 363608.0, "pop_determined_poverty_status": 730602.0, "population_1_year_and_over": 729603.0, "population_3_years_over": 706041.0, "poverty": 153304.0, "sales_office_employed": 73340.0, "some_college_and_associates_degree": 150173.0, "walked_to_work": 4504.0, "worked_at_home": 11602.0, "workers_16_and_over": 299476.0, "associates_degree": 40525.0, "bachelors_degree": 71240.0, "high_school_diploma": 90557.0, "less_one_year_college": 30581.0, "masters_degree": 23438.0, "one_year_more_college": 79067.0, "pop_25_years_over": 451246.0, "commute_35_44_mins": 10234.0, "commute_60_more_mins": 13973.0, "commute_less_10_mins": 42112.0, "commuters_16_over": 287874.0, "hispanic_any_race": 345109.0, "pop_5_years_over": 681089.0, "speak_only_english_at_home": 422010.0, "speak_spanish_at_home": 200291.0, "speak_spanish_at_home_low_english": 87537.0, "do_date": "20122016", "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN}, {"geo_id": "4503", "nonfamily_households": 80594.0, "family_households": 175720.0, "median_year_structure_built": 1984.0, "rent_burden_not_computed": 10175.0, "rent_over_50_percent": 16827.0, "rent_40_to_50_percent": 5943.0, "rent_35_to_40_percent": 4294.0, "rent_30_to_35_percent": 5665.0, "rent_25_to_30_percent": 6805.0, "rent_20_to_25_percent": 8235.0, "rent_15_to_20_percent": 8265.0, "rent_10_to_15_percent": 5582.0, "rent_under_10_percent": 2673.0, "total_pop": 672526.0, "male_pop": 329408.0, "female_pop": 343118.0, "median_age": 40.2, "white_pop": 500065.0, "black_pop": 123521.0, "asian_pop": 5786.0, "hispanic_pop": 30244.0, "amerindian_pop": 1252.0, "other_race_pop": 699.0, "two_or_more_races_pop": 10737.0, "not_hispanic_pop": 642282.0, "commuters_by_public_transportation": 1545.0, "households": 256314.0, "median_income": 42513.0, "income_per_capita": 22809.0, "housing_units": 302082.0, "vacant_housing_units": 45768.0, "vacant_housing_units_for_rent": 5743.0, "vacant_housing_units_for_sale": 3676.0, "median_rent": 502.0, "percent_income_spent_on_rent": 30.5, "owner_occupied_housing_units": 181850.0, "million_dollar_housing_units": 637.0, "mortgaged_housing_units": 99516.0, "families_with_young_children": 44755.0, "two_parent_families_with_young_children": 25923.0, "two_parents_in_labor_force_families_with_young_children": 14475.0, "two_parents_father_in_labor_force_families_with_young_children": 10299.0, "two_parents_mother_in_labor_force_families_with_young_children": 915.0, "two_parents_not_in_labor_force_families_with_young_children": 234.0, "one_parent_families_with_young_children": 18832.0, "father_one_parent_families_with_young_children": 4347.0, "father_in_labor_force_one_parent_families_with_young_children": 4053.0, "commute_10_14_mins": 40313.0, "commute_15_19_mins": 42612.0, "commute_20_24_mins": 40950.0, "commute_25_29_mins": 18950.0, "commute_30_34_mins": 35082.0, "commute_45_59_mins": 21435.0, "aggregate_travel_time_to_work": 6505055.0, "income_less_10000": 21734.0, "income_10000_14999": 17832.0, "income_15000_19999": 17978.0, "income_20000_24999": 17176.0, "income_25000_29999": 16949.0, "income_30000_34999": 15074.0, "income_35000_39999": 13675.0, "income_40000_44999": 13682.0, "income_45000_49999": 12291.0, "income_50000_59999": 20289.0, "income_60000_74999": 24236.0, "income_75000_99999": 28481.0, "income_100000_124999": 15788.0, "income_125000_149999": 8364.0, "income_150000_199999": 7386.0, "income_200000_or_more": 5379.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 687.0, "owner_occupied_housing_units_lower_value_quartile": 69300.0, "owner_occupied_housing_units_median_value": 121900.0, "owner_occupied_housing_units_upper_value_quartile": 199100.0, "married_households": 127501.0, "occupied_housing_units": 256314.0, "housing_units_renter_occupied": 74464.0, "dwellings_1_units_detached": 200597.0, "dwellings_1_units_attached": 4667.0, "dwellings_2_units": 6264.0, "dwellings_3_to_4_units": 7472.0, "dwellings_5_to_9_units": 9447.0, "dwellings_10_to_19_units": 6388.0, "dwellings_20_to_49_units": 2946.0, "dwellings_50_or_more_units": 1482.0, "mobile_homes": 62538.0, "housing_built_2005_or_later": 1635.0, "housing_built_2000_to_2004": 6022.0, "housing_built_1939_or_earlier": 13384.0, "male_under_5": 19591.0, "male_5_to_9": 21037.0, "male_10_to_14": 21263.0, "male_15_to_17": 13240.0, "male_18_to_19": 10582.0, "male_20": 5659.0, "male_21": 6310.0, "male_22_to_24": 13299.0, "male_25_to_29": 20075.0, "male_30_to_34": 18910.0, "male_35_to_39": 19535.0, "male_40_to_44": 20965.0, "male_45_to_49": 21728.0, "male_50_to_54": 22877.0, "male_55_to_59": 22888.0, "male_60_61": 8017.0, "male_62_64": 12315.0, "male_65_to_66": 7904.0, "male_67_to_69": 10759.0, "male_70_to_74": 14060.0, "male_75_to_79": 8958.0, "male_80_to_84": 5264.0, "male_85_and_over": 4172.0, "female_under_5": 19135.0, "female_5_to_9": 20084.0, "female_10_to_14": 20652.0, "female_15_to_17": 12437.0, "female_18_to_19": 9809.0, "female_20": 5768.0, "female_21": 5687.0, "female_22_to_24": 12792.0, "female_25_to_29": 19456.0, "female_30_to_34": 19373.0, "female_35_to_39": 19529.0, "female_40_to_44": 21138.0, "female_45_to_49": 22183.0, "female_50_to_54": 24238.0, "female_55_to_59": 24494.0, "female_60_to_61": 9103.0, "female_62_to_64": 13305.0, "female_65_to_66": 8708.0, "female_67_to_69": 11870.0, "female_70_to_74": 15469.0, "female_75_to_79": 11282.0, "female_80_to_84": 8152.0, "female_85_and_over": 8454.0, "white_including_hispanic": 517477.0, "black_including_hispanic": 124650.0, "amerindian_including_hispanic": 1653.0, "asian_including_hispanic": 5892.0, "commute_5_9_mins": 28230.0, "commute_35_39_mins": 9628.0, "commute_40_44_mins": 9230.0, "commute_60_89_mins": 9942.0, "commute_90_more_mins": 4558.0, "households_retirement_income": 52959.0, "armed_forces": 354.0, "civilian_labor_force": 309310.0, "employed_pop": 283389.0, "unemployed_pop": 25921.0, "not_in_labor_force": 232492.0, "pop_16_over": 542156.0, "pop_in_labor_force": 309664.0, "asian_male_45_54": 342.0, "asian_male_55_64": 168.0, "black_male_45_54": 8011.0, "black_male_55_64": 7101.0, "hispanic_male_45_54": 1592.0, "hispanic_male_55_64": 899.0, "white_male_45_54": 34269.0, "white_male_55_64": 34822.0, "bachelors_degree_2": 59245.0, "bachelors_degree_or_higher_25_64": 71787.0, "children": 147439.0, "children_in_single_female_hh": 42695.0, "commuters_by_bus": 1383.0, "commuters_by_car_truck_van": 261396.0, "commuters_by_carpool": 25762.0, "commuters_by_subway_or_elevated": 39.0, "commuters_drove_alone": 235634.0, "different_house_year_ago_different_city": 83418.0, "different_house_year_ago_same_city": 12334.0, "employed_agriculture_forestry_fishing_hunting_mining": 3888.0, "employed_arts_entertainment_recreation_accommodation_food": 23832.0, "employed_construction": 17413.0, "employed_education_health_social": 65773.0, "employed_finance_insurance_real_estate": 11796.0, "employed_information": 4206.0, "employed_manufacturing": 58802.0, "employed_other_services_not_public_admin": 14015.0, "employed_public_administration": 9195.0, "employed_retail_trade": 32356.0, "employed_science_management_admin_waste": 21606.0, "employed_transportation_warehousing_utilities": 12911.0, "employed_wholesale_trade": 7596.0, "female_female_households": 310.0, "four_more_cars": 20778.0, "gini_index": 0.4577, "graduate_professional_degree": 34326.0, "group_quarters": 22002.0, "high_school_including_ged": 146193.0, "households_public_asst_or_food_stamps": 40030.0, "in_grades_1_to_4": 33094.0, "in_grades_5_to_8": 33263.0, "in_grades_9_to_12": 33075.0, "in_school": 167500.0, "in_undergrad_college": 43716.0, "less_than_high_school_graduate": 78211.0, "male_45_64_associates_degree": 8272.0, "male_45_64_bachelors_degree": 12666.0, "male_45_64_graduate_degree": 6541.0, "male_45_64_less_than_9_grade": 4498.0, "male_45_64_grade_9_12": 10237.0, "male_45_64_high_school": 29014.0, "male_45_64_some_college": 16597.0, "male_45_to_64": 87825.0, "male_male_households": 188.0, "management_business_sci_arts_employed": 86343.0, "no_car": 6146.0, "no_cars": 16870.0, "not_us_citizen_pop": 16305.0, "occupation_management_arts": 86343.0, "occupation_natural_resources_construction_maintenance": 27821.0, "occupation_production_transportation_material": 55015.0, "occupation_sales_office": 64770.0, "occupation_services": 49440.0, "one_car": 79960.0, "two_cars": 95087.0, "three_cars": 43619.0, "pop_25_64": 340129.0, "pop_determined_poverty_status": 649994.0, "population_1_year_and_over": 665332.0, "population_3_years_over": 650141.0, "poverty": 118374.0, "sales_office_employed": 64770.0, "some_college_and_associates_degree": 137206.0, "walked_to_work": 3806.0, "worked_at_home": 7870.0, "workers_16_and_over": 277302.0, "associates_degree": 44333.0, "bachelors_degree": 59245.0, "high_school_diploma": 117849.0, "less_one_year_college": 29352.0, "masters_degree": 25310.0, "one_year_more_college": 63521.0, "pop_25_years_over": 455181.0, "commute_35_44_mins": 18858.0, "commute_60_more_mins": 14500.0, "commute_less_10_mins": 36732.0, "commuters_16_over": 269432.0, "hispanic_any_race": 30244.0, "pop_5_years_over": 633800.0, "speak_only_english_at_home": 601094.0, "speak_spanish_at_home": 22639.0, "speak_spanish_at_home_low_english": 10660.0, "do_date": "20122016", "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN}, {"geo_id": "3201", "nonfamily_households": 101258.0, "family_households": 141927.0, "median_year_structure_built": 1984.0, "rent_burden_not_computed": 6732.0, "rent_over_50_percent": 38479.0, "rent_40_to_50_percent": 14351.0, "rent_35_to_40_percent": 10150.0, "rent_30_to_35_percent": 12407.0, "rent_25_to_30_percent": 15748.0, "rent_20_to_25_percent": 16145.0, "rent_15_to_20_percent": 15207.0, "rent_10_to_15_percent": 10252.0, "rent_under_10_percent": 3897.0, "total_pop": 685133.0, "male_pop": 350884.0, "female_pop": 334249.0, "median_age": 36.3, "white_pop": 223352.0, "black_pop": 71004.0, "asian_pop": 55407.0, "hispanic_pop": 308465.0, "amerindian_pop": 2397.0, "other_race_pop": 1659.0, "two_or_more_races_pop": 18433.0, "not_hispanic_pop": 376668.0, "commuters_by_public_transportation": 24022.0, "households": 243185.0, "median_income": 38941.0, "income_per_capita": 20610.0, "housing_units": 299034.0, "vacant_housing_units": 55849.0, "vacant_housing_units_for_rent": 23192.0, "vacant_housing_units_for_sale": 2672.0, "median_rent": 727.0, "percent_income_spent_on_rent": 32.8, "owner_occupied_housing_units": 99817.0, "million_dollar_housing_units": 335.0, "mortgaged_housing_units": 65019.0, "families_with_young_children": 51339.0, "two_parent_families_with_young_children": 24499.0, "two_parents_in_labor_force_families_with_young_children": 11929.0, "two_parents_father_in_labor_force_families_with_young_children": 11104.0, "two_parents_mother_in_labor_force_families_with_young_children": 985.0, "two_parents_not_in_labor_force_families_with_young_children": 481.0, "one_parent_families_with_young_children": 26840.0, "father_one_parent_families_with_young_children": 7731.0, "father_in_labor_force_one_parent_families_with_young_children": 7260.0, "commute_10_14_mins": 37002.0, "commute_15_19_mins": 52844.0, "commute_20_24_mins": 61445.0, "commute_25_29_mins": 24785.0, "commute_30_34_mins": 55632.0, "commute_45_59_mins": 11692.0, "aggregate_travel_time_to_work": 7272355.0, "income_less_10000": 23503.0, "income_10000_14999": 16366.0, "income_15000_19999": 16811.0, "income_20000_24999": 17810.0, "income_25000_29999": 17046.0, "income_30000_34999": 17400.0, "income_35000_39999": 15156.0, "income_40000_44999": 14008.0, "income_45000_49999": 11946.0, "income_50000_59999": 19789.0, "income_60000_74999": 23297.0, "income_75000_99999": 22823.0, "income_100000_124999": 12427.0, "income_125000_149999": 6300.0, "income_150000_199999": 4487.0, "income_200000_or_more": 4016.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 859.0, "owner_occupied_housing_units_lower_value_quartile": 79600.0, "owner_occupied_housing_units_median_value": 127900.0, "owner_occupied_housing_units_upper_value_quartile": 190200.0, "married_households": 85400.0, "occupied_housing_units": 243185.0, "housing_units_renter_occupied": 143368.0, "dwellings_1_units_detached": 118289.0, "dwellings_1_units_attached": 15489.0, "dwellings_2_units": 4462.0, "dwellings_3_to_4_units": 29942.0, "dwellings_5_to_9_units": 38609.0, "dwellings_10_to_19_units": 27934.0, "dwellings_20_to_49_units": 14828.0, "dwellings_50_or_more_units": 35793.0, "mobile_homes": 13444.0, "housing_built_2005_or_later": 439.0, "housing_built_2000_to_2004": 2787.0, "housing_built_1939_or_earlier": 3605.0, "male_under_5": 22876.0, "male_5_to_9": 22651.0, "male_10_to_14": 21580.0, "male_15_to_17": 12590.0, "male_18_to_19": 9832.0, "male_20": 5117.0, "male_21": 4932.0, "male_22_to_24": 16836.0, "male_25_to_29": 27751.0, "male_30_to_34": 26423.0, "male_35_to_39": 22999.0, "male_40_to_44": 24881.0, "male_45_to_49": 25252.0, "male_50_to_54": 24407.0, "male_55_to_59": 23337.0, "male_60_61": 7901.0, "male_62_64": 10582.0, "male_65_to_66": 6862.0, "male_67_to_69": 8122.0, "male_70_to_74": 10852.0, "male_75_to_79": 7237.0, "male_80_to_84": 4796.0, "male_85_and_over": 3068.0, "female_under_5": 21848.0, "female_5_to_9": 22312.0, "female_10_to_14": 21609.0, "female_15_to_17": 12995.0, "female_18_to_19": 8173.0, "female_20": 5221.0, "female_21": 5545.0, "female_22_to_24": 14440.0, "female_25_to_29": 24899.0, "female_30_to_34": 22739.0, "female_35_to_39": 21691.0, "female_40_to_44": 23493.0, "female_45_to_49": 22486.0, "female_50_to_54": 21752.0, "female_55_to_59": 20793.0, "female_60_to_61": 7722.0, "female_62_to_64": 11118.0, "female_65_to_66": 6802.0, "female_67_to_69": 8346.0, "female_70_to_74": 10994.0, "female_75_to_79": 7638.0, "female_80_to_84": 6153.0, "female_85_and_over": 5480.0, "white_including_hispanic": 386576.0, "black_including_hispanic": 75178.0, "amerindian_including_hispanic": 5046.0, "asian_including_hispanic": 56348.0, "commute_5_9_mins": 18492.0, "commute_35_39_mins": 5688.0, "commute_40_44_mins": 7485.0, "commute_60_89_mins": 9999.0, "commute_90_more_mins": 7018.0, "households_retirement_income": 36834.0, "armed_forces": 263.0, "civilian_labor_force": 349852.0, "employed_pop": 309387.0, "unemployed_pop": 40465.0, "not_in_labor_force": 193341.0, "pop_16_over": 543456.0, "pop_in_labor_force": 350115.0, "asian_male_45_54": 4230.0, "asian_male_55_64": 3821.0, "black_male_45_54": 4846.0, "black_male_55_64": 4517.0, "hispanic_male_45_54": 18973.0, "hispanic_male_55_64": 10709.0, "white_male_45_54": 20644.0, "white_male_55_64": 21923.0, "bachelors_degree_2": 47371.0, "bachelors_degree_or_higher_25_64": 51647.0, "children": 158461.0, "children_in_single_female_hh": 51749.0, "commuters_by_bus": 23837.0, "commuters_by_car_truck_van": 255754.0, "commuters_by_carpool": 35448.0, "commuters_by_subway_or_elevated": 94.0, "commuters_drove_alone": 220306.0, "different_house_year_ago_different_city": 93378.0, "different_house_year_ago_same_city": 53851.0, "employed_agriculture_forestry_fishing_hunting_mining": 677.0, "employed_arts_entertainment_recreation_accommodation_food": 108666.0, "employed_construction": 21024.0, "employed_education_health_social": 33624.0, "employed_finance_insurance_real_estate": 13751.0, "employed_information": 3989.0, "employed_manufacturing": 9632.0, "employed_other_services_not_public_admin": 15680.0, "employed_public_administration": 5849.0, "employed_retail_trade": 39542.0, "employed_science_management_admin_waste": 35905.0, "employed_transportation_warehousing_utilities": 16944.0, "employed_wholesale_trade": 4104.0, "female_female_households": 561.0, "four_more_cars": 8656.0, "gini_index": 0.4602, "graduate_professional_degree": 20421.0, "group_quarters": 9330.0, "high_school_including_ged": 147814.0, "households_public_asst_or_food_stamps": 51905.0, "in_grades_1_to_4": 35295.0, "in_grades_5_to_8": 34834.0, "in_grades_9_to_12": 36424.0, "in_school": 156002.0, "in_undergrad_college": 30396.0, "less_than_high_school_graduate": 108041.0, "male_45_64_associates_degree": 5669.0, "male_45_64_bachelors_degree": 9562.0, "male_45_64_graduate_degree": 3924.0, "male_45_64_less_than_9_grade": 10361.0, "male_45_64_grade_9_12": 10625.0, "male_45_64_high_school": 28920.0, "male_45_64_some_college": 22418.0, "male_45_to_64": 91479.0, "male_male_households": 828.0, "management_business_sci_arts_employed": 53712.0, "no_car": 22356.0, "no_cars": 38276.0, "not_us_citizen_pop": 130888.0, "occupation_management_arts": 53712.0, "occupation_natural_resources_construction_maintenance": 28013.0, "occupation_production_transportation_material": 35385.0, "occupation_sales_office": 78807.0, "occupation_services": 113470.0, "one_car": 102159.0, "two_cars": 70603.0, "three_cars": 23491.0, "pop_25_64": 370226.0, "pop_determined_poverty_status": 675527.0, "population_1_year_and_over": 677532.0, "population_3_years_over": 659005.0, "poverty": 149327.0, "sales_office_employed": 78807.0, "some_college_and_associates_degree": 132929.0, "walked_to_work": 8014.0, "worked_at_home": 6724.0, "workers_16_and_over": 303386.0, "associates_degree": 27319.0, "bachelors_degree": 47371.0, "high_school_diploma": 128003.0, "less_one_year_college": 29783.0, "masters_degree": 14049.0, "one_year_more_college": 75827.0, "pop_25_years_over": 456576.0, "commute_35_44_mins": 13173.0, "commute_60_more_mins": 17017.0, "commute_less_10_mins": 23072.0, "commuters_16_over": 296662.0, "hispanic_any_race": 308465.0, "pop_5_years_over": 640409.0, "speak_only_english_at_home": 328655.0, "speak_spanish_at_home": 243962.0, "speak_spanish_at_home_low_english": 113454.0, "do_date": "20122016", "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN}, {"geo_id": "4101", "nonfamily_households": 104587.0, "family_households": 201673.0, "median_year_structure_built": 1984.0, "rent_burden_not_computed": 5080.0, "rent_over_50_percent": 27078.0, "rent_40_to_50_percent": 10559.0, "rent_35_to_40_percent": 7984.0, "rent_30_to_35_percent": 10490.0, "rent_25_to_30_percent": 13365.0, "rent_20_to_25_percent": 15600.0, "rent_15_to_20_percent": 15956.0, "rent_10_to_15_percent": 10081.0, "rent_under_10_percent": 3241.0, "total_pop": 807470.0, "male_pop": 399343.0, "female_pop": 408127.0, "median_age": 37.4, "white_pop": 583852.0, "black_pop": 12273.0, "asian_pop": 58336.0, "hispanic_pop": 115166.0, "amerindian_pop": 3516.0, "other_race_pop": 1013.0, "two_or_more_races_pop": 30712.0, "not_hispanic_pop": 692304.0, "commuters_by_public_transportation": 21183.0, "households": 306260.0, "median_income": 66106.0, "income_per_capita": 33784.0, "housing_units": 330058.0, "vacant_housing_units": 23798.0, "vacant_housing_units_for_rent": 4169.0, "vacant_housing_units_for_sale": 2846.0, "median_rent": 929.0, "percent_income_spent_on_rent": 29.6, "owner_occupied_housing_units": 186826.0, "million_dollar_housing_units": 1941.0, "mortgaged_housing_units": 136274.0, "families_with_young_children": 58685.0, "two_parent_families_with_young_children": 44579.0, "two_parents_in_labor_force_families_with_young_children": 24678.0, "two_parents_father_in_labor_force_families_with_young_children": 18377.0, "two_parents_mother_in_labor_force_families_with_young_children": 1240.0, "two_parents_not_in_labor_force_families_with_young_children": 284.0, "one_parent_families_with_young_children": 14106.0, "father_one_parent_families_with_young_children": 3533.0, "father_in_labor_force_one_parent_families_with_young_children": 3089.0, "commute_10_14_mins": 53830.0, "commute_15_19_mins": 56999.0, "commute_20_24_mins": 52007.0, "commute_25_29_mins": 24258.0, "commute_30_34_mins": 46750.0, "commute_45_59_mins": 30368.0, "aggregate_travel_time_to_work": 9061690.0, "income_less_10000": 14821.0, "income_10000_14999": 11262.0, "income_15000_19999": 11680.0, "income_20000_24999": 13748.0, "income_25000_29999": 13643.0, "income_30000_34999": 12737.0, "income_35000_39999": 13252.0, "income_40000_44999": 13574.0, "income_45000_49999": 11867.0, "income_50000_59999": 23613.0, "income_60000_74999": 30715.0, "income_75000_99999": 42678.0, "income_100000_124999": 31311.0, "income_125000_149999": 19484.0, "income_150000_199999": 21143.0, "income_200000_or_more": 20732.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 1075.0, "owner_occupied_housing_units_lower_value_quartile": 207300.0, "owner_occupied_housing_units_median_value": 292400.0, "owner_occupied_housing_units_upper_value_quartile": 416300.0, "married_households": 159083.0, "occupied_housing_units": 306260.0, "housing_units_renter_occupied": 119434.0, "dwellings_1_units_detached": 196193.0, "dwellings_1_units_attached": 21336.0, "dwellings_2_units": 7762.0, "dwellings_3_to_4_units": 14747.0, "dwellings_5_to_9_units": 18944.0, "dwellings_10_to_19_units": 17315.0, "dwellings_20_to_49_units": 14676.0, "dwellings_50_or_more_units": 24782.0, "mobile_homes": 13964.0, "housing_built_2005_or_later": 2018.0, "housing_built_2000_to_2004": 6563.0, "housing_built_1939_or_earlier": 11794.0, "male_under_5": 25679.0, "male_5_to_9": 26302.0, "male_10_to_14": 28042.0, "male_15_to_17": 15996.0, "male_18_to_19": 9367.0, "male_20": 4965.0, "male_21": 5112.0, "male_22_to_24": 14301.0, "male_25_to_29": 29286.0, "male_30_to_34": 31166.0, "male_35_to_39": 28711.0, "male_40_to_44": 29870.0, "male_45_to_49": 27882.0, "male_50_to_54": 27002.0, "male_55_to_59": 25615.0, "male_60_61": 9971.0, "male_62_64": 12945.0, "male_65_to_66": 8420.0, "male_67_to_69": 9858.0, "male_70_to_74": 11890.0, "male_75_to_79": 7199.0, "male_80_to_84": 5063.0, "male_85_and_over": 4701.0, "female_under_5": 24142.0, "female_5_to_9": 26507.0, "female_10_to_14": 25208.0, "female_15_to_17": 15595.0, "female_18_to_19": 9313.0, "female_20": 4280.0, "female_21": 5091.0, "female_22_to_24": 14727.0, "female_25_to_29": 28418.0, "female_30_to_34": 30482.0, "female_35_to_39": 29431.0, "female_40_to_44": 28370.0, "female_45_to_49": 26898.0, "female_50_to_54": 27676.0, "female_55_to_59": 27119.0, "female_60_to_61": 10250.0, "female_62_to_64": 15122.0, "female_65_to_66": 8514.0, "female_67_to_69": 12044.0, "female_70_to_74": 13002.0, "female_75_to_79": 9370.0, "female_80_to_84": 6377.0, "female_85_and_over": 10191.0, "white_including_hispanic": 654813.0, "black_including_hispanic": 12797.0, "amerindian_including_hispanic": 5799.0, "asian_including_hispanic": 58923.0, "commute_5_9_mins": 37937.0, "commute_35_39_mins": 11073.0, "commute_40_44_mins": 16218.0, "commute_60_89_mins": 18916.0, "commute_90_more_mins": 5912.0, "households_retirement_income": 51374.0, "armed_forces": 871.0, "civilian_labor_force": 424873.0, "employed_pop": 395963.0, "unemployed_pop": 28910.0, "not_in_labor_force": 215496.0, "pop_16_over": 641240.0, "pop_in_labor_force": 425744.0, "asian_male_45_54": 4098.0, "asian_male_55_64": 2441.0, "black_male_45_54": 1115.0, "black_male_55_64": 698.0, "hispanic_male_45_54": 6204.0, "hispanic_male_55_64": 2879.0, "white_male_45_54": 42050.0, "white_male_55_64": 41359.0, "bachelors_degree_2": 134303.0, "bachelors_degree_or_higher_25_64": 179721.0, "children": 187471.0, "children_in_single_female_hh": 34406.0, "commuters_by_bus": 13156.0, "commuters_by_car_truck_van": 321658.0, "commuters_by_carpool": 40068.0, "commuters_by_subway_or_elevated": 3214.0, "commuters_drove_alone": 281590.0, "different_house_year_ago_different_city": 102240.0, "different_house_year_ago_same_city": 32366.0, "employed_agriculture_forestry_fishing_hunting_mining": 8262.0, "employed_arts_entertainment_recreation_accommodation_food": 33972.0, "employed_construction": 20626.0, "employed_education_health_social": 80020.0, "employed_finance_insurance_real_estate": 27310.0, "employed_information": 8574.0, "employed_manufacturing": 64990.0, "employed_other_services_not_public_admin": 16535.0, "employed_public_administration": 13678.0, "employed_retail_trade": 43645.0, "employed_science_management_admin_waste": 51190.0, "employed_transportation_warehousing_utilities": 14589.0, "employed_wholesale_trade": 12572.0, "female_female_households": 592.0, "four_more_cars": 17933.0, "gini_index": 0.4465, "graduate_professional_degree": 82078.0, "group_quarters": 14154.0, "high_school_including_ged": 110957.0, "households_public_asst_or_food_stamps": 44924.0, "in_grades_1_to_4": 41406.0, "in_grades_5_to_8": 41479.0, "in_grades_9_to_12": 42119.0, "in_school": 199421.0, "in_undergrad_college": 41178.0, "less_than_high_school_graduate": 49635.0, "male_45_64_associates_degree": 9327.0, "male_45_64_bachelors_degree": 24270.0, "male_45_64_graduate_degree": 16052.0, "male_45_64_less_than_9_grade": 3692.0, "male_45_64_grade_9_12": 4907.0, "male_45_64_high_school": 19918.0, "male_45_64_some_college": 25249.0, "male_45_to_64": 103415.0, "male_male_households": 814.0, "management_business_sci_arts_employed": 171939.0, "no_car": 12725.0, "no_cars": 22590.0, "not_us_citizen_pop": 65255.0, "occupation_management_arts": 171939.0, "occupation_natural_resources_construction_maintenance": 29186.0, "occupation_production_transportation_material": 40723.0, "occupation_sales_office": 90770.0, "occupation_services": 63345.0, "one_car": 99085.0, "two_cars": 122290.0, "three_cars": 44362.0, "pop_25_64": 446214.0, "pop_determined_poverty_status": 794983.0, "population_1_year_and_over": 798403.0, "population_3_years_over": 778666.0, "poverty": 93829.0, "sales_office_employed": 90770.0, "some_college_and_associates_degree": 175870.0, "walked_to_work": 13699.0, "worked_at_home": 23319.0, "workers_16_and_over": 388400.0, "associates_degree": 46421.0, "bachelors_degree": 134303.0, "high_school_diploma": 90458.0, "less_one_year_college": 41030.0, "masters_degree": 57427.0, "one_year_more_college": 88419.0, "pop_25_years_over": 552843.0, "commute_35_44_mins": 27291.0, "commute_60_more_mins": 24828.0, "commute_less_10_mins": 48750.0, "commuters_16_over": 365081.0, "hispanic_any_race": 115166.0, "pop_5_years_over": 757649.0, "speak_only_english_at_home": 605333.0, "speak_spanish_at_home": 82348.0, "speak_spanish_at_home_low_english": 34599.0, "do_date": "20122016", "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN}, {"geo_id": "4504", "nonfamily_households": 88771.0, "family_households": 177394.0, "median_year_structure_built": 1984.0, "rent_burden_not_computed": 8610.0, "rent_over_50_percent": 19223.0, "rent_40_to_50_percent": 7330.0, "rent_35_to_40_percent": 5271.0, "rent_30_to_35_percent": 7405.0, "rent_25_to_30_percent": 9848.0, "rent_20_to_25_percent": 11157.0, "rent_15_to_20_percent": 11032.0, "rent_10_to_15_percent": 7781.0, "rent_under_10_percent": 3623.0, "total_pop": 697852.0, "male_pop": 337871.0, "female_pop": 359981.0, "median_age": 37.9, "white_pop": 480738.0, "black_pop": 131873.0, "asian_pop": 16161.0, "hispanic_pop": 56381.0, "amerindian_pop": 1193.0, "other_race_pop": 838.0, "two_or_more_races_pop": 10336.0, "not_hispanic_pop": 641471.0, "commuters_by_public_transportation": 1407.0, "households": 266165.0, "median_income": 49093.0, "income_per_capita": 26543.0, "housing_units": 295150.0, "vacant_housing_units": 28985.0, "vacant_housing_units_for_rent": 6401.0, "vacant_housing_units_for_sale": 3719.0, "median_rent": 616.0, "percent_income_spent_on_rent": 28.9, "owner_occupied_housing_units": 174885.0, "million_dollar_housing_units": 694.0, "mortgaged_housing_units": 111883.0, "families_with_young_children": 50390.0, "two_parent_families_with_young_children": 31639.0, "two_parents_in_labor_force_families_with_young_children": 18175.0, "two_parents_father_in_labor_force_families_with_young_children": 12283.0, "two_parents_mother_in_labor_force_families_with_young_children": 810.0, "two_parents_not_in_labor_force_families_with_young_children": 371.0, "one_parent_families_with_young_children": 18751.0, "father_one_parent_families_with_young_children": 3091.0, "father_in_labor_force_one_parent_families_with_young_children": 2768.0, "commute_10_14_mins": 47365.0, "commute_15_19_mins": 61543.0, "commute_20_24_mins": 52887.0, "commute_25_29_mins": 24003.0, "commute_30_34_mins": 40813.0, "commute_45_59_mins": 12762.0, "aggregate_travel_time_to_work": 6654965.0, "income_less_10000": 19517.0, "income_10000_14999": 16259.0, "income_15000_19999": 15216.0, "income_20000_24999": 17028.0, "income_25000_29999": 15290.0, "income_30000_34999": 14478.0, "income_35000_39999": 12888.0, "income_40000_44999": 13204.0, "income_45000_49999": 11140.0, "income_50000_59999": 21620.0, "income_60000_74999": 27250.0, "income_75000_99999": 31124.0, "income_100000_124999": 18984.0, "income_125000_149999": 11850.0, "income_150000_199999": 10569.0, "income_200000_or_more": 9748.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 774.0, "owner_occupied_housing_units_lower_value_quartile": 93800.0, "owner_occupied_housing_units_median_value": 148200.0, "owner_occupied_housing_units_upper_value_quartile": 229200.0, "married_households": 128368.0, "occupied_housing_units": 266165.0, "housing_units_renter_occupied": 91280.0, "dwellings_1_units_detached": 200263.0, "dwellings_1_units_attached": 9528.0, "dwellings_2_units": 8539.0, "dwellings_3_to_4_units": 7230.0, "dwellings_5_to_9_units": 13328.0, "dwellings_10_to_19_units": 12618.0, "dwellings_20_to_49_units": 8795.0, "dwellings_50_or_more_units": 6399.0, "mobile_homes": 28375.0, "housing_built_2005_or_later": 1833.0, "housing_built_2000_to_2004": 7976.0, "housing_built_1939_or_earlier": 12005.0, "male_under_5": 22557.0, "male_5_to_9": 24726.0, "male_10_to_14": 22520.0, "male_15_to_17": 13827.0, "male_18_to_19": 9112.0, "male_20": 4935.0, "male_21": 4828.0, "male_22_to_24": 13666.0, "male_25_to_29": 23207.0, "male_30_to_34": 22149.0, "male_35_to_39": 21288.0, "male_40_to_44": 22680.0, "male_45_to_49": 23408.0, "male_50_to_54": 23953.0, "male_55_to_59": 22026.0, "male_60_61": 8108.0, "male_62_64": 10813.0, "male_65_to_66": 6870.0, "male_67_to_69": 9629.0, "male_70_to_74": 11022.0, "male_75_to_79": 7536.0, "male_80_to_84": 4897.0, "male_85_and_over": 4114.0, "female_under_5": 21802.0, "female_5_to_9": 22659.0, "female_10_to_14": 22803.0, "female_15_to_17": 13309.0, "female_18_to_19": 9474.0, "female_20": 5152.0, "female_21": 4922.0, "female_22_to_24": 14248.0, "female_25_to_29": 24124.0, "female_30_to_34": 22618.0, "female_35_to_39": 23437.0, "female_40_to_44": 21996.0, "female_45_to_49": 24417.0, "female_50_to_54": 25236.0, "female_55_to_59": 24691.0, "female_60_to_61": 8614.0, "female_62_to_64": 12421.0, "female_65_to_66": 8502.0, "female_67_to_69": 10396.0, "female_70_to_74": 13887.0, "female_75_to_79": 9643.0, "female_80_to_84": 7926.0, "female_85_and_over": 7704.0, "white_including_hispanic": 522366.0, "black_including_hispanic": 133388.0, "amerindian_including_hispanic": 1403.0, "asian_including_hispanic": 16286.0, "commute_5_9_mins": 30827.0, "commute_35_39_mins": 8895.0, "commute_40_44_mins": 7874.0, "commute_60_89_mins": 5447.0, "commute_90_more_mins": 4586.0, "households_retirement_income": 44880.0, "armed_forces": 415.0, "civilian_labor_force": 346244.0, "employed_pop": 322427.0, "unemployed_pop": 23817.0, "not_in_labor_force": 205183.0, "pop_16_over": 551842.0, "pop_in_labor_force": 346659.0, "asian_male_45_54": 1121.0, "asian_male_55_64": 723.0, "black_male_45_54": 8331.0, "black_male_55_64": 6628.0, "hispanic_male_45_54": 3249.0, "hispanic_male_55_64": 1540.0, "white_male_45_54": 34276.0, "white_male_55_64": 31644.0, "bachelors_degree_2": 89946.0, "bachelors_degree_or_higher_25_64": 116147.0, "children": 164203.0, "children_in_single_female_hh": 46108.0, "commuters_by_bus": 1238.0, "commuters_by_car_truck_van": 295355.0, "commuters_by_carpool": 29960.0, "commuters_by_subway_or_elevated": 29.0, "commuters_drove_alone": 265395.0, "different_house_year_ago_different_city": 89030.0, "different_house_year_ago_same_city": 13791.0, "employed_agriculture_forestry_fishing_hunting_mining": 1706.0, "employed_arts_entertainment_recreation_accommodation_food": 30660.0, "employed_construction": 18241.0, "employed_education_health_social": 67669.0, "employed_finance_insurance_real_estate": 16615.0, "employed_information": 6522.0, "employed_manufacturing": 58872.0, "employed_other_services_not_public_admin": 16862.0, "employed_public_administration": 7998.0, "employed_retail_trade": 36385.0, "employed_science_management_admin_waste": 36084.0, "employed_transportation_warehousing_utilities": 13314.0, "employed_wholesale_trade": 11499.0, "female_female_households": 421.0, "four_more_cars": 16500.0, "gini_index": 0.4687, "graduate_professional_degree": 50826.0, "group_quarters": 17490.0, "high_school_including_ged": 123769.0, "households_public_asst_or_food_stamps": 33581.0, "in_grades_1_to_4": 37335.0, "in_grades_5_to_8": 36166.0, "in_grades_9_to_12": 36777.0, "in_school": 176628.0, "in_undergrad_college": 37176.0, "less_than_high_school_graduate": 66122.0, "male_45_64_associates_degree": 7597.0, "male_45_64_bachelors_degree": 18093.0, "male_45_64_graduate_degree": 9251.0, "male_45_64_less_than_9_grade": 4477.0, "male_45_64_grade_9_12": 8160.0, "male_45_64_high_school": 24667.0, "male_45_64_some_college": 16063.0, "male_45_to_64": 88308.0, "male_male_households": 287.0, "management_business_sci_arts_employed": 115178.0, "no_car": 7066.0, "no_cars": 17724.0, "not_us_citizen_pop": 31959.0, "occupation_management_arts": 115178.0, "occupation_natural_resources_construction_maintenance": 24751.0, "occupation_production_transportation_material": 53041.0, "occupation_sales_office": 77072.0, "occupation_services": 52385.0, "one_car": 88550.0, "two_cars": 105437.0, "three_cars": 37954.0, "pop_25_64": 365186.0, "pop_determined_poverty_status": 679241.0, "population_1_year_and_over": 689586.0, "population_3_years_over": 672354.0, "poverty": 104814.0, "sales_office_employed": 77072.0, "some_college_and_associates_degree": 136649.0, "walked_to_work": 4903.0, "worked_at_home": 11651.0, "workers_16_and_over": 316588.0, "associates_degree": 42795.0, "bachelors_degree": 89946.0, "high_school_diploma": 102530.0, "less_one_year_college": 29920.0, "masters_degree": 37467.0, "one_year_more_college": 63934.0, "pop_25_years_over": 467312.0, "commute_35_44_mins": 16769.0, "commute_60_more_mins": 10033.0, "commute_less_10_mins": 38762.0, "commuters_16_over": 304937.0, "hispanic_any_race": 56381.0, "pop_5_years_over": 653493.0, "speak_only_english_at_home": 581575.0, "speak_spanish_at_home": 46571.0, "speak_spanish_at_home_low_english": 22327.0, "do_date": "20122016", "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN}]}