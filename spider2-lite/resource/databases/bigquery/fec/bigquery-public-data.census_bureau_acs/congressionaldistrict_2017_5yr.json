{"table_name": "congressionaldistrict_2017_5yr", "table_fullname": "bigquery-public-data.census_bureau_acs.congressionaldistrict_2017_5yr", "column_names": ["geo_id", "nonfamily_households", "family_households", "median_year_structure_built", "rent_burden_not_computed", "rent_over_50_percent", "rent_40_to_50_percent", "rent_35_to_40_percent", "rent_30_to_35_percent", "rent_25_to_30_percent", "rent_20_to_25_percent", "rent_15_to_20_percent", "rent_10_to_15_percent", "rent_under_10_percent", "total_pop", "male_pop", "female_pop", "median_age", "white_pop", "black_pop", "asian_pop", "hispanic_pop", "amerindian_pop", "other_race_pop", "two_or_more_races_pop", "not_hispanic_pop", "commuters_by_public_transportation", "households", "median_income", "income_per_capita", "housing_units", "vacant_housing_units", "vacant_housing_units_for_rent", "vacant_housing_units_for_sale", "median_rent", "percent_income_spent_on_rent", "owner_occupied_housing_units", "million_dollar_housing_units", "mortgaged_housing_units", "families_with_young_children", "two_parent_families_with_young_children", "two_parents_in_labor_force_families_with_young_children", "two_parents_father_in_labor_force_families_with_young_children", "two_parents_mother_in_labor_force_families_with_young_children", "two_parents_not_in_labor_force_families_with_young_children", "one_parent_families_with_young_children", "father_one_parent_families_with_young_children", "father_in_labor_force_one_parent_families_with_young_children", "commute_10_14_mins", "commute_15_19_mins", "commute_20_24_mins", "commute_25_29_mins", "commute_30_34_mins", "commute_45_59_mins", "aggregate_travel_time_to_work", "income_less_10000", "income_10000_14999", "income_15000_19999", "income_20000_24999", "income_25000_29999", "income_30000_34999", "income_35000_39999", "income_40000_44999", "income_45000_49999", "income_50000_59999", "income_60000_74999", "income_75000_99999", "income_100000_124999", "income_125000_149999", "income_150000_199999", "income_200000_or_more", "renter_occupied_housing_units_paying_cash_median_gross_rent", "owner_occupied_housing_units_lower_value_quartile", "owner_occupied_housing_units_median_value", "owner_occupied_housing_units_upper_value_quartile", "married_households", "occupied_housing_units", "housing_units_renter_occupied", "dwellings_1_units_detached", "dwellings_1_units_attached", "dwellings_2_units", "dwellings_3_to_4_units", "dwellings_5_to_9_units", "dwellings_10_to_19_units", "dwellings_20_to_49_units", "dwellings_50_or_more_units", "mobile_homes", "housing_built_2005_or_later", "housing_built_2000_to_2004", "housing_built_1939_or_earlier", "male_under_5", "male_5_to_9", "male_10_to_14", "male_15_to_17", "male_18_to_19", "male_20", "male_21", "male_22_to_24", "male_25_to_29", "male_30_to_34", "male_35_to_39", "male_40_to_44", "male_45_to_49", "male_50_to_54", "male_55_to_59", "male_60_61", "male_62_64", "male_65_to_66", "male_67_to_69", "male_70_to_74", "male_75_to_79", "male_80_to_84", "male_85_and_over", "female_under_5", "female_5_to_9", "female_10_to_14", "female_15_to_17", "female_18_to_19", "female_20", "female_21", "female_22_to_24", "female_25_to_29", "female_30_to_34", "female_35_to_39", "female_40_to_44", "female_45_to_49", "female_50_to_54", "female_55_to_59", "female_60_to_61", "female_62_to_64", "female_65_to_66", "female_67_to_69", "female_70_to_74", "female_75_to_79", "female_80_to_84", "female_85_and_over", "white_including_hispanic", "black_including_hispanic", "amerindian_including_hispanic", "asian_including_hispanic", "commute_5_9_mins", "commute_35_39_mins", "commute_40_44_mins", "commute_60_89_mins", "commute_90_more_mins", "households_retirement_income", "armed_forces", "civilian_labor_force", "employed_pop", "unemployed_pop", "not_in_labor_force", "pop_16_over", "pop_in_labor_force", "asian_male_45_54", "asian_male_55_64", "black_male_45_54", "black_male_55_64", "hispanic_male_45_54", "hispanic_male_55_64", "white_male_45_54", "white_male_55_64", "bachelors_degree_2", "bachelors_degree_or_higher_25_64", "children", "children_in_single_female_hh", "commuters_by_bus", "commuters_by_car_truck_van", "commuters_by_carpool", "commuters_by_subway_or_elevated", "commuters_drove_alone", "different_house_year_ago_different_city", "different_house_year_ago_same_city", "employed_agriculture_forestry_fishing_hunting_mining", "employed_arts_entertainment_recreation_accommodation_food", "employed_construction", "employed_education_health_social", "employed_finance_insurance_real_estate", "employed_information", "employed_manufacturing", "employed_other_services_not_public_admin", "employed_public_administration", "employed_retail_trade", "employed_science_management_admin_waste", "employed_transportation_warehousing_utilities", "employed_wholesale_trade", "female_female_households", "four_more_cars", "gini_index", "graduate_professional_degree", "group_quarters", "high_school_including_ged", "households_public_asst_or_food_stamps", "in_grades_1_to_4", "in_grades_5_to_8", "in_grades_9_to_12", "in_school", "in_undergrad_college", "less_than_high_school_graduate", "male_45_64_associates_degree", "male_45_64_bachelors_degree", "male_45_64_graduate_degree", "male_45_64_less_than_9_grade", "male_45_64_grade_9_12", "male_45_64_high_school", "male_45_64_some_college", "male_45_to_64", "male_male_households", "management_business_sci_arts_employed", "no_car", "no_cars", "not_us_citizen_pop", "occupation_management_arts", "occupation_natural_resources_construction_maintenance", "occupation_production_transportation_material", "occupation_sales_office", "occupation_services", "one_car", "two_cars", "three_cars", "pop_25_64", "pop_determined_poverty_status", "population_1_year_and_over", "population_3_years_over", "poverty", "sales_office_employed", "some_college_and_associates_degree", "walked_to_work", "worked_at_home", "workers_16_and_over", "associates_degree", "bachelors_degree", "high_school_diploma", "less_one_year_college", "masters_degree", "one_year_more_college", "pop_25_years_over", "commute_35_44_mins", "commute_60_more_mins", "commute_less_10_mins", "commuters_16_over", "hispanic_any_race", "pop_5_years_over", "speak_only_english_at_home", "speak_spanish_at_home", "speak_spanish_at_home_low_english", "pop_15_and_over", "pop_never_married", "pop_now_married", "pop_separated", "pop_widowed", "pop_divorced", "do_date"], "column_types": ["STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "STRING"], "nested_column_names": ["geo_id", "nonfamily_households", "family_households", "median_year_structure_built", "rent_burden_not_computed", "rent_over_50_percent", "rent_40_to_50_percent", "rent_35_to_40_percent", "rent_30_to_35_percent", "rent_25_to_30_percent", "rent_20_to_25_percent", "rent_15_to_20_percent", "rent_10_to_15_percent", "rent_under_10_percent", "total_pop", "male_pop", "female_pop", "median_age", "white_pop", "black_pop", "asian_pop", "hispanic_pop", "amerindian_pop", "other_race_pop", "two_or_more_races_pop", "not_hispanic_pop", "commuters_by_public_transportation", "households", "median_income", "income_per_capita", "housing_units", "vacant_housing_units", "vacant_housing_units_for_rent", "vacant_housing_units_for_sale", "median_rent", "percent_income_spent_on_rent", "owner_occupied_housing_units", "million_dollar_housing_units", "mortgaged_housing_units", "families_with_young_children", "two_parent_families_with_young_children", "two_parents_in_labor_force_families_with_young_children", "two_parents_father_in_labor_force_families_with_young_children", "two_parents_mother_in_labor_force_families_with_young_children", "two_parents_not_in_labor_force_families_with_young_children", "one_parent_families_with_young_children", "father_one_parent_families_with_young_children", "father_in_labor_force_one_parent_families_with_young_children", "commute_10_14_mins", "commute_15_19_mins", "commute_20_24_mins", "commute_25_29_mins", "commute_30_34_mins", "commute_45_59_mins", "aggregate_travel_time_to_work", "income_less_10000", "income_10000_14999", "income_15000_19999", "income_20000_24999", "income_25000_29999", "income_30000_34999", "income_35000_39999", "income_40000_44999", "income_45000_49999", "income_50000_59999", "income_60000_74999", "income_75000_99999", "income_100000_124999", "income_125000_149999", "income_150000_199999", "income_200000_or_more", "renter_occupied_housing_units_paying_cash_median_gross_rent", "owner_occupied_housing_units_lower_value_quartile", "owner_occupied_housing_units_median_value", "owner_occupied_housing_units_upper_value_quartile", "married_households", "occupied_housing_units", "housing_units_renter_occupied", "dwellings_1_units_detached", "dwellings_1_units_attached", "dwellings_2_units", "dwellings_3_to_4_units", "dwellings_5_to_9_units", "dwellings_10_to_19_units", "dwellings_20_to_49_units", "dwellings_50_or_more_units", "mobile_homes", "housing_built_2005_or_later", "housing_built_2000_to_2004", "housing_built_1939_or_earlier", "male_under_5", "male_5_to_9", "male_10_to_14", "male_15_to_17", "male_18_to_19", "male_20", "male_21", "male_22_to_24", "male_25_to_29", "male_30_to_34", "male_35_to_39", "male_40_to_44", "male_45_to_49", "male_50_to_54", "male_55_to_59", "male_60_61", "male_62_64", "male_65_to_66", "male_67_to_69", "male_70_to_74", "male_75_to_79", "male_80_to_84", "male_85_and_over", "female_under_5", "female_5_to_9", "female_10_to_14", "female_15_to_17", "female_18_to_19", "female_20", "female_21", "female_22_to_24", "female_25_to_29", "female_30_to_34", "female_35_to_39", "female_40_to_44", "female_45_to_49", "female_50_to_54", "female_55_to_59", "female_60_to_61", "female_62_to_64", "female_65_to_66", "female_67_to_69", "female_70_to_74", "female_75_to_79", "female_80_to_84", "female_85_and_over", "white_including_hispanic", "black_including_hispanic", "amerindian_including_hispanic", "asian_including_hispanic", "commute_5_9_mins", "commute_35_39_mins", "commute_40_44_mins", "commute_60_89_mins", "commute_90_more_mins", "households_retirement_income", "armed_forces", "civilian_labor_force", "employed_pop", "unemployed_pop", "not_in_labor_force", "pop_16_over", "pop_in_labor_force", "asian_male_45_54", "asian_male_55_64", "black_male_45_54", "black_male_55_64", "hispanic_male_45_54", "hispanic_male_55_64", "white_male_45_54", "white_male_55_64", "bachelors_degree_2", "bachelors_degree_or_higher_25_64", "children", "children_in_single_female_hh", "commuters_by_bus", "commuters_by_car_truck_van", "commuters_by_carpool", "commuters_by_subway_or_elevated", "commuters_drove_alone", "different_house_year_ago_different_city", "different_house_year_ago_same_city", "employed_agriculture_forestry_fishing_hunting_mining", "employed_arts_entertainment_recreation_accommodation_food", "employed_construction", "employed_education_health_social", "employed_finance_insurance_real_estate", "employed_information", "employed_manufacturing", "employed_other_services_not_public_admin", "employed_public_administration", "employed_retail_trade", "employed_science_management_admin_waste", "employed_transportation_warehousing_utilities", "employed_wholesale_trade", "female_female_households", "four_more_cars", "gini_index", "graduate_professional_degree", "group_quarters", "high_school_including_ged", "households_public_asst_or_food_stamps", "in_grades_1_to_4", "in_grades_5_to_8", "in_grades_9_to_12", "in_school", "in_undergrad_college", "less_than_high_school_graduate", "male_45_64_associates_degree", "male_45_64_bachelors_degree", "male_45_64_graduate_degree", "male_45_64_less_than_9_grade", "male_45_64_grade_9_12", "male_45_64_high_school", "male_45_64_some_college", "male_45_to_64", "male_male_households", "management_business_sci_arts_employed", "no_car", "no_cars", "not_us_citizen_pop", "occupation_management_arts", "occupation_natural_resources_construction_maintenance", "occupation_production_transportation_material", "occupation_sales_office", "occupation_services", "one_car", "two_cars", "three_cars", "pop_25_64", "pop_determined_poverty_status", "population_1_year_and_over", "population_3_years_over", "poverty", "sales_office_employed", "some_college_and_associates_degree", "walked_to_work", "worked_at_home", "workers_16_and_over", "associates_degree", "bachelors_degree", "high_school_diploma", "less_one_year_college", "masters_degree", "one_year_more_college", "pop_25_years_over", "commute_35_44_mins", "commute_60_more_mins", "commute_less_10_mins", "commuters_16_over", "hispanic_any_race", "pop_5_years_over", "speak_only_english_at_home", "speak_spanish_at_home", "speak_spanish_at_home_low_english", "pop_15_and_over", "pop_never_married", "pop_now_married", "pop_separated", "pop_widowed", "pop_divorced", "do_date"], "nested_column_types": ["STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "STRING"], "description": ["US Congressional Districts Geoids", "Nonfamily Households. A householder living alone or with nonrelatives only. Unmarried couples households, whether opposite-sex or same-sex, with no relatives of the householder present are tabulated in nonfamily households.", "Family Households. A family consists of a householder and one or more other people living in the same household who are related to the householder by birth, marriage, or adoption. All people in a household who are related to the householder are regarded as members of his or her family. A family household may contain people not related to the householder, but those people are not included as part of the family of the householder in tabulations. Thus, the number of family households is equal to the number of families, but family households may include more members than do families. A household can contain only one family for purposes of tabulations. Not all households contain families since a household may be comprised of a group of unrelated people or of one person living alone -- these are called nonfamily households. Families are classified by type as either a \"married couple family\" or \"other family\" according to the sex of the householder and the presence of relatives. The data on family type are based ...", "Median Year Structure Built. Median Year Structure Built", "Housing units without rent burden computed. Units for which no rent is paid and units occupied by households that reported no income or a net loss comprise this category", "Housing units spending over 50% income on rent. Gross rent over 50 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 40 to 49.9% income on rent. Gross rent from 40.0 to 49.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 35 to 39.9% income on rent. Gross rent from 35.0 to 39.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 30 to 34.9% income on rent. Gross rent from 30.0 to 34.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 25 to 29.9% income on rent. Gross rent from 25.0 to 29.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 20 to 24.9% income on rent. Gross rent from 20.0 to 24.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 15 to 19.9% income on rent. Gross rent from 15.0 to 19.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 10 to 14.9% income on rent. Gross rent from 10.0 to 14.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending less than 10% on rent. Gross rent less than 10 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Total Population. The total number of all people living in a given geographic area.  This is a very useful catch-all denominator when calculating rates.", "Male Population. The number of people within each geography who are male.", "Female Population. The number of people within each geography who are female.", "Median Age. The median age of all people in a given geographic area.", "White Population. The number of people identifying as white, non-Hispanic in each geography.", "Black or African American Population. The number of people identifying as black or African American, non-Hispanic in each geography.", "Asian Population. The number of people identifying as Asian, non-Hispanic in each geography.", "Hispanic Population. The number of people identifying as Hispanic or Latino in each geography.", "American Indian and Alaska Native Population. The number of people identifying as American Indian or Alaska native in each geography.", "Other Race population. The number of people identifying as another race in each geography", "Two or more races population. The number of people identifying as two or more races in each geography", "Population not Hispanic. The number of people not identifying as Hispanic or Latino in each geography.", "Commuters by Public Transportation. The number of workers age 16 years and over within a geographic area who primarily traveled to work by public transportation.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Households. A count of the number of households in each geography. A household consists of one or more people who live in the same dwelling and also share at meals or living accommodation, and may consist of a single family or some other grouping of people. ", "Median Household Income in the past 12 Months. Within a geographic area, the median income received by every household on a regular basis before payments for personal income taxes, social security, union dues, medicare deductions, etc.  It includes income received from wages, salary, commissions, bonuses, and tips; self-employment income from own nonfarm or farm businesses, including proprietorships and partnerships; interest, dividends, net rental income, royalty income, or income from estates and trusts; Social Security or Railroad Retirement income; Supplemental Security Income (SSI); any cash public assistance or welfare payments from the state or local welfare office; retirement, survivor, or disability benefits; and any other sources of income received regularly such as Veterans' (VA) payments, unemployment and/or worker's compensation, child support, and alimony.", "Per Capita Income in the past 12 Months. Per capita income is the mean income computed for every man, woman, and child in a particular group. It is derived by dividing the total income of a particular group by the total population.", "Housing Units. A count of housing units in each geography.  A housing unit is a house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters.", "Vacant Housing Units. The count of vacant housing units in a geographic area. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Vacant Housing Units for Rent. The count of vacant housing units in a geographic area that are for rent. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Vacant Housing Units for Sale. The count of vacant housing units in a geographic area that are for sale. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Median Rent. The median contract rent within a geographic area. The contract rent is the monthly rent agreed to or contracted for, regardless of any furnishings, utilities, fees, meals, or services that may be included. For vacant units, it is the monthly rent asked for the rental unit at the time of interview.", "Percent of Household Income Spent on Rent. Within a geographic area, the median percentage of household income which was spent on gross rent.  Gross rent is the amount of the contract rent plus the estimated average monthly cost of utilities (electricity, gas, water, sewer etc.) and fuels (oil, coal, wood, etc.) if these are paid by the renter.  Household income is the sum of the income of all people 15 years and older living in the household.", "Owner-occupied Housing Units", "Owner-occupied Housing Units valued at $1,000,000 or more.. The count of owner occupied housing units in a geographic area that are valued at $1,000,000 or more.  Value is the respondent's estimate of how much the property (house and lot, mobile home and lot, or condominium unit) would sell for if it were for sale.", "Owner-occupied Housing Units with a Mortgage. The count of housing units within a geographic area that are mortagaged. \"Mortgage\" refers to all forms of debt where the property is pledged as security for repayment of the debt, including deeds of trust, trust deed, contracts to purchase, land contracts, junior mortgages, and home equity loans.", "Families with young children (under 6 years of age)", "Two-parent families with young children (under 6 years of age)", "Two-parent families, both parents in labor force with young children (under 6 years of age)", "Two-parent families, father only in labor force with young children (under 6 years of age)", "Two-parent families, mother only in labor force with young children (under 6 years of age)", "Two-parent families, neither parent in labor force with young children (under 6 years of age)", "One-parent families with young children (under 6 years of age)", "One-parent families, father, with young children (under 6 years of age)", "One-parent families, father in labor force, with young children (under 6 years of age)", "Number of workers with a commute between 10 and 14 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 10 and 14 minutes. ", "Number of workers with a commute between 15 and 19 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 15 and 19 minutes. ", "Number of workers with a commute between 20 and 24 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 20 and 24 minutes.", "Number of workers with a commute between 25 and 29 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 25 and 29 minutes. ", "Number of workers with a commute between 30 and 34 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 30 and 34 minutes. ", "Number of workers with a commute between 45 and 59 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 45 and 59 minutes. ", "Aggregate travel time to work. The total number of minutes every worker in a geographic area over the age of 16 who did not work from home spent spent commuting to work in one day", "Households with income less than $10,000. The number of households in a geographic area whose annual income was less than $10,000.", "Households with income of $10,000 to $14,999. The number of households in a geographic area whose annual income was between $10,000 and $14,999.", "Households with income of $15,000 to $19,999. The number of households in a geographic area whose annual income was between $15,000 and $19,999.", "Households with income of $20,000 to $24,999. The number of households in a geographic area whose annual income was between $20,000 and $24,999.", "Households with income of $25,000 to $29,999. The number of households in a geographic area whose annual income was between $20,000 and $24,999.", "Households with income of $30,000 to $34,999. The number of households in a geographic area whose annual income was between $30,000 and $34,999.", "Households with income of $35,000 to $39,999. The number of households in a geographic area whose annual income was between $35,000 and $39,999.", "Households with income of $40,000 to $44,999. The number of households in a geographic area whose annual income was between $40,000 and $44,999.", "Households with income of $45,000 to $49,999. The number of households in a geographic area whose annual income was between $45,000 and $49,999.", "Households with income of $50,000 to $59,999. The number of households in a geographic area whose annual income was between $50,000 and $59,999.", "Households with income of $60,000 to $74,999. The number of households in a geographic area whose annual income was between $60,000 and $74,999.", "Households with income of $75,000 to $99,999. The number of households in a geographic area whose annual income was between $75,000 and $99,999.", "Households with income of $100,000 to $124,999. The number of households in a geographic area whose annual income was between $100,000 and $124,999.", "Households with income of $125,000 to $149,999. The number of households in a geographic area whose annual income was between $125,000 and $149,999.", "Households with income of $150,000 to $199,999. The number of households in a geographic area whose annual income was between $150,000 and $1999,999.", "Households with income of $200,000 Or More. The number of households in a geographic area whose annual income was more than $200,000.", "Renter-Occupied Housing Units Paying Cash Rent Median Gross Rent", "Owner-Occupied Housing Units Lower Value Quartile", "Owner-Occupied Housing Units Median Value. The middle value (median) in a geographic area owner occupied housing units.", "Owner-Occupied Housing Units Upper Value Quartile", "Married households. People in formal marriages, as well as people in common-law marriages, are included. Does not include same-sex marriages.", "Occupied housing units. A housing unit is classified as occupied if it is the usual place of residence of the person or group of people living in it at the time of enumeration.", "Renter occupied housing units. All occupied units which are not owner occupied, whether they are rented for cash rent or occupied without payment of cash rent, are classified as renter-occupied.", "Single-family (one unit) detached dwellings. This is a 1-unit structure detached from any other house, that is, with open space on all four sides. Such structures are considered detached even if they have an adjoining shed or garage. A one-family house that contains a business is considered detached as long as the building has open space on all four sides. Mobile homes to which one or more permanent rooms have been added or built also are included.", "Single-family (one unit) attached dwellings. This is a 1-unit structure that has one or more walls extending from ground to roof separating it from adjoining structures. In row houses (sometimes called townhouses), double houses, or houses attached to nonresidential structures, each house is a separate, attached structure if the dividing or common wall goes from ground to roof.", "Two-family (two unit) dwellings", "Multifamily dwellings with three to 4 units", "Apartment buildings with 5 to 9 units", "Apartment buildings with 10 to 19 units", "Apartment buildings with 20 to 49 units", "Apartment buildings with 50 or more units", "Mobile homes. A manufactured home is defined as a movable dwelling, 8 feet or more wide and 40 feet or more long, designed to be towed on its own chassis, with transportation gear integral to the unit when it leaves the factory, and without need of a permanent foundation. These homes are built in accordance with the U.S. Department of Housing and Urban Development (HUD) building code.", "Housing units built in 2005 or later. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built in 2005 or later.", "Housing units built between 2000 and 2004. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built from 2000 to 2004.", "Housing units built before 1939. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built in 1939 or earlier.", "Male under 5 years. The male population over the age of five years within the specified area.", "Male age 5 to 9. The male population between the age of five years to nine years within the specified area.", "Male age 10 to 14. The male population between the age of ten years to fourteen years within the specified area.", "Male age 15 to 17. The male population between the age of fifteeen years to seventeen years within the specified area.", "Male age 18 and 19. The male population between the age of eighteen years to nineteen years within the specified area.", "Male age 20. The male population with an age of twenty years within the specified area.", "Male age 21. The male population with an age of twenty-one years within the specified area.", "Male age 22 to 24. The male population between the age of twenty-two years to twenty-four years within the specified area.", "Male age 25 to 29. The male population between the age of twenty-five years to twenty-nine years within the specified area.", "Male age 30 to 34. The male population between the age of thirty years to thirty-four years within the specified area.", "Male age 35 to 39. The male population between the age of thirty-five years to thirty-nine years within the specified area.", "Male age 40 to 44. The male population between the age of fourty years to fourty-four years within the specified area.", "Men age 45 to 49. The male population between the age of fourty-five years to fourty-nine years within the specified area.", "Men age 50 to 54. The male population between the age of fifty years to fifty-four years within the specified area.", "Men age 55 to 59. The male population between the age of fifty-five years to fifty-nine years within the specified area.", "Men age 60 to 61. The male population between the age of sixty years to sixty-one years within the specified area.", "Men age 62 to 64. The male population between the age of sixty-two years to sixty-four years within the specified area.", "Male age 65 to 66. The male population between the age of sixty-five years to sixty-six years within the specified area.", "Male age 67 to 69. The male population between the age of sixty-seven years to sixty-nine years within the specified area.", "Male age 70 to 74. The male population between the age of seventy years to seventy-four years within the specified area.", "Male age 75 to 79. The male population between the age of seventy-five years to seventy-nine years within the specified area.", "Male age 80 to 84. The male population between the age of eighty years to eighty-four years within the specified area.", "Male age 85 and over. The male population of the age of eighty-five years and over within the specified area.", "Female under 5 years. The female population over the age of five years within the specified area.", "Female age 5 to 9. The female population between the age of five years to nine years within the specified area.", "Female age 10 to 14. The female population between the age of ten years to fourteen years within the specified area.", "Female age 15 to 17. The female population between the age of fifteeen years to seventeen years within the specified area.", "Female age 18 and 19. The female population between the age of eighteen years to nineteen years within the specified area.", "Female age 20. The female population with an age of twenty years within the specified area.", "Female age 21. The female population with an age of twenty-one years within the specified area.", "Female age 22 to 24. The female population between the age of twenty-two years to twenty-four years within the specified area.", "Female age 25 to 29. The female population between the age of twenty-five years to twenty-nine years within the specified area.", "Female age 30 to 34. The female population between the age of thirty years to thirty-four years within the specified area.", "Female age 35 to 39. The female population between the age of thirty-five years to thirty-nine years within the specified area.", "Female age 40 to 44. The female population between the age of fourty years to fourty-four years within the specified area.", "Female age 45 to 49. The female population between the age of fourty-five years to fourty-nine years within the specified area.", "Female age 50 to 54. The female population between the age of fifty years to fifty-four years within the specified area.", "Female age 55 to 59. The female population between the age of fifty-five years to fifty-nine years within the specified area.", "Female age 60 and 61. The female population between the age of sixty years to sixty-one years within the specified area.", "Female age 62 to 64. The female population between the age of sixty-two years to sixty-four years within the specified area.", "Female age 65 to 66. The female population between the age of sixty-five years to sixty-six years within the specified area.", "Female age 67 to 69. The female population between the age of sixty-seven years to sixty-nine years within the specified area.", "Female age 70 to 74. The female population between the age of seventy years to seventy-four years within the specified area.", "Female age 75 to 79. The female population between the age of seventy-five years to seventy-nine years within the specified area.", "Female age 80 to 84. The female population between the age of eighty years to eighty-four years within the specified area.", "Female age 85 and over. The female population of the age of eighty-five years and over within the specified area.", "White including Hispanic", "Black including Hispanic", "American Indian including Hispanic", "Asian including Hispanic", "Number of workers with a commute between 5 and 9 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 5 and 9 minutes.", "Number of workers with a commute between 35 and 39 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 35 and 39 minutes. ", "Number of workers with a commute between 40 and 44 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 40 and 44 minutes. ", "Number of workers with a commute between 60 and 89 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 60 and 89 minutes .", "Number of workers with a commute of more than 90 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute more than 90 minutes.", "Households receiving retirement income", "Population in Armed Forces. The number of people in each geography who are members of the U.S. Armed Forces (people on active duty with the United States Army, Air Force, Navy, Marine Corps, or Coast Guard).", "Population in Civilian Labor Force. The number of civilians 16 years and over in each geography who can be classified as either \"employed\" or \"unemployed\" below.", "Employed Population. The number of civilians 16 years old and over in each geography who either (1) were \"at work,\" that is, those who did any work at all during the reference week as paid employees, worked in their own business or profession, worked on their own farm, or worked 15 hours or more as unpaid workers on a family farm or in a family business; or (2) were \"with a job but not at work,\" that is, those who did not work during the reference week but had jobs or businesses from which they were temporarily absent due to illness, bad weather, industrial dispute, vacation, or other personal reasons. Excluded from the employed are people whose only activity consisted of work around the house or unpaid volunteer work for religious, charitable, and similar organizations; also excluded are all institutionalized people and people on active duty in the United States Armed Forces.", "", "Population Not in Labor Force. The number of people in each geography who are 16 years old and over who are not classified as members of the labor force. This category consists mainly of students, homemakers, retired workers, seasonal workers interviewed in an off season who were not looking for work, institutionalized people, and people doing only incidental unpaid family work (less than 15 hours during the reference week).", "Population age 16 and over. The number of people in each geography who are age 16 or over.", "Population in Labor Force. The number of people in each geography who are either in the civilian labor force or are members of the U.S. Armed Forces (people on active duty with the United States Army, Air Force, Navy, Marine Corps, or Coast Guard).", "Asian Men age 45 to 54", "Asian Men age 55 to 64", "Black Men age 45 to 54", "Black Men age 55 to 64", "Hispanic Men age 45 to 54", "Hispanic Men age 55 to 64", "White Men age 45 to 54", "White Men age 55 to 64", "Population who completed a bachelor's degree. From mobility table.", "Population with Bachelors Degree or Higher, Ages 25 to 64. The number of people in each geography who are between the ages of 25 and 64 who have attained a bachelors degree or higher. ", "Children under 18 Years of Age. The number of people within each geography who are under 18 years of age.", "Children under 18 years of age in single female-led household", "Commuters by Bus. The number of workers age 16 years and over within a geographic area who primarily traveled to work by bus.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.  This is a subset of workers who commuted by public transport.", "Commuters by Car, Truck, or Van. The number of workers age 16 years and over within  a geographic area who primarily traveled to work by car, truck or  van.  This is the principal mode of travel or type of conveyance,  by distance rather than time, that the worker usually used to get  from home to work.", "Commuters by Carpool. The number of workers age 16 years and over within a geographic area who primarily traveled to work by carpool.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Commuters by Subway or Elevated. The number of workers age 16 years and over within a geographic area who primarily traveled to work by subway or elevated train.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.  This is a subset of workers who commuted by public transport.", "Commuters who drove alone. The number of workers age 16 years and over within a geographic area who primarily traveled by car driving alone. This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Lived in a different house one year ago in a different city. All people in a geographic area who lived in a different city within the year prior to the survey.", "Lived in a different house one year ago in the same city. All people in a geographic area who lived in the same city but moved to a different unit within the year prior to the survey.", "Workers employed in firms in agriculture, forestry, fishing, hunting, or mining. The Agriculture, Forestry, Fishing and Hunting sector comprises establishments primarily engaged in growing crops, raising animals, harvesting timber, and harvesting fish and other animals from a farm, ranch, or their natural habitats.", "Workers employed in firms in arts, entertainment, recreation, accommodation and food services. The Arts, Entertainment, and Recreation sector includes a wide range of establishments that operate facilities or provide services to meet varied cultural, entertainment, and recreational interests of their patrons. This sector comprises (1) establishments that are involved in producing, promoting, or participating in live performances, events, or exhibits intended for public viewing; (2) establishments that preserve and exhibit objects and sites of historical, cultural, or educational interest; and (3) establishments that operate facilities or provide services that enable patrons to participate in recreational activities or pursue amusement, hobby, and leisure-time interests.", "Workers employed in firms in construction. The Construction sector comprises establishments primarily engaged in the construction of buildings or engineering projects (e.g., highways and utility systems). Construction work done may include new work, additions, alterations, or maintenance and repairs.", "Workers employed in firms in educational services, health care, and social assistance. Outpatient health services, other than hospital care, including: public health administration; research and education; categorical health programs; treatment and immunization clinics; nursing; environmental health activities such as air and water pollution control; ambulance service if provided separately from fire protection services, and other general public health activities such as mosquito abatement. School health services provided by health agencies (rather than school agencies) are included here. Sewage treatment operations are classified under Sewerage.", "Workers employed in firms in finance, insurance, real estate and rental and leasing. The Real Estate and Rental and Leasing sector comprises establishments primarily engaged in renting, leasing, or otherwise allowing the use of tangible or intangible assets, and establishments providing related services. The major portion of this sector comprises establishments that rent, lease, or otherwise allow the use of their own assets by others. The assets may be tangible, as is the case of real estate and equipment, or intangible, as is the case with patents and trademarks.", "Workers employed in firms in information. The Information sector comprises establishments engaged in the following processes: (a) producing and distributing information and cultural products, (b) providing the means to transmit or distribute these products as well as data or communications, and (c) processing data. Included are the publishing industries, the motion picture and sound recording industries; the broadcasting industries, the telecommunications industries; Web search portals, data processing industries, and the information services industries.", "Workers employed in firms in manufacturing. The Manufacturing sector comprises establishments engaged in the mechanical, physical, or chemical transformation of materials, substances, or components into new products.", "Workers employed in firms in other services except public administration. The Other Services (Except Public Administration) sector comprises establishments engaged in providing services not specifically provided for elsewhere in the classification system. Establishments in this sector are primarily engaged in activities such as equipment and machinery repairing, promoting or administering religious activities, grantmaking, advocacy, and providing drycleaning and laundry services, personal care services, death care services, pet care services, photofinishing services, temporary parking services, and dating services. Private households that engage in employing workers on or about the premises in activities primarily concerned with the operation of the household are included in this sector.", "Workers employed in firms in public administration. The Public Administration sector consists of establishments of federal, state, and local government agencies that administer, oversee, and manage public programs and have executive, legislative, or judicial authority over other institutions within a given area. These agencies also set policy, create laws, adjudicate civil and criminal legal cases, provide for public safety and for national defense. In general, government establishments in the public administration sector oversee governmental programs and activities that are not performed by private establishments.", "Workers employed in firms in retail trade. The Retail Trade sector comprises establishments engaged in retailing merchandise, generally without transformation, and rendering services incidental to the sale of merchandise. The retailing process is the final step in the distribution of merchandise; retailers are, therefore, organized to sell merchandise in small quantities to the general public.", "Workers employed in firms in professional scientific, management, administrative and waste management services. The Administrative and Support and Waste Management and Remediation Services sector comprises establishments performing routine support activities for the day-to-day operations of other organizations. The establishments in this sector specialize in one or more of these support activities and provide these services to clients in a variety of industries and, in some cases, to households. Activities performed include office administration, hiring and placing of personnel, document preparation and similar clerical services, solicitation, collection, security and surveillance services, cleaning, and waste disposal services.", "Workers employed in firms in transportation, warehousing, and utilities. The Transportation and Warehousing sector includes industries providing transportation of passengers and cargo, warehousing and storage for goods, scenic and sightseeing transportation, and support activities related to modes of transportation. The modes of transportation are air, rail, water, road, and pipeline.", "Workers employed in firms in wholesale trade. The Wholesale Trade sector comprises establishments engaged in wholesaling merchandise, generally without transformation, and rendering services incidental to the sale of merchandise. The wholesaling process is an intermediate step in the distribution of merchandise. Wholesalers are organized to sell or arrange the purchase or sale of (a) goods for resale (i.e., goods sold to other wholesalers or retailers), (b) capital or durable nonconsumer goods, and (c) raw and intermediate materials and supplies used in production.", "Households with two female partners. An unmarried partner is a person age 15 years and over, who is not related to the householder, who shares living quarters, and who has an intimate relationship with the householder.", "Four car households. The number of households with four or more passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Gini Index. The Gini index, or index of income concentration, is a statistical measure of income inequality ranging from 0 to 1. A measure of 1 indicates perfect inequality, i.e., one household having all the income and rest having none. A measure of 0 indicates perfect equality, i.e., all households having an equal share of income.", "Population who completed a graduate or professional degree", "Population living in group quarters", "Population with high school degree, including GED. The number of people in a geographic area over the age of 25 who attained a high school degree or GED.", "Households on cash public assistance or receiving food stamps (SNAP)", "Students Enrolled in Grades 1 to 4. The total number of people in each geography currently enrolled in grades 1 through 4 inclusive.  This corresponds roughly to elementary school.", "Students Enrolled in Grades 5 to 8. The total number of people in each geography currently enrolled in grades 5 through 8 inclusive.  This corresponds roughly to middle school.", "Students Enrolled in Grades 9 to 12. The total number of people in each geography currently enrolled in grades 9 through 12 inclusive.  This corresponds roughly to high school.", "Students Enrolled in School. The total number of people in each geography currently enrolled at any level of school, from nursery or pre-school to advanced post-graduate education.  Only includes those over the age of 3.", "Students Enrolled as Undergraduate in College. The number of people in a geographic area who are enrolled in college at the undergraduate level. Enrollment refers to being registered or listed as a student in an educational program leading to a college degree. This may be a public school or college, a private school or college.", "Less than high school graduate. The number of people in a geographic area over the age of 25 who have not completed high school or any other advanced degree.", "Men age 45 to 64 who obtained an associate's degree", "Men age 45 to 64 who obtained a bachelor's degree", "Men age 45 to 64 who obtained a graduate or professional degree", "Men age 45 to 64 who attained less than a 9th grade education", "Men age 45 to 64 who attained between 9th and 12th grade, no diploma", "Men age 45 to 64 who completed high school or obtained GED", "Men age 45 to 64 who completed some college, no degree", "Men age 45 to 64 (\"middle aged\"). The male population between the age of fourty-five years to sixty-four years within the specified area.", "Households with two male partners. An unmarried partner is a person age 15 years and over, who is not related to the householder, who shares living quarters, and who has an intimate relationship with the householder.", "Civilian Employed Population in Management, Business, Science, and Arts Occupations. The number of employed civilians 16 years old and over in each geography in the Management, Business, Science, and Arts occupations. Occupation codes are based on the Standard Occupational Classification (SOC), published by the Executive Office of the President, Office of Management and Budget.", "Workers age 16 and over with no vehicle. All people in a geographic area over the age of 16 who do not own a car.", "Car-free households. The number of households without passenger cars, vans, and pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Not a U.S. Citizen Population. The number of people within each geography who indicated that they are not U.S. citizens.", "Workers employed in management business science and arts occupations", "Workers employed in natural resources, construction, and maintenance occupations", "Workers employed in production, transportation, and material moving", "Workers employed in sales and office occupations", "Workers employed in service occupations", "One car households. The number of households with one passenger car, van, , pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Two car households. The number of households with two passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Three car households. The number of households with one passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Population age 25 to 64. The number of people in each geography who are between the ages of 25 and 64. ", "Population for Whom Poverty Status Determined. The number of people in each geography who could be identified as either living in poverty or not.  This should be used as the denominator when calculating poverty rates, as it excludes people for whom it was not possible to determine poverty.", "Population 1 year and over. All people, male and female, child and adult, living in a given geographic area that are 1 year and older.", "Population 3 Years and Over. The total number of people in each geography age 3 years and over.  This denominator is mostly used to calculate rates of school enrollment.", "Income In The Past 12 Months Below Poverty Level. The number of people in a geographic area who are part of a family (which could be just them as an individual) determined to be \"in poverty\" following the Office of Management and Budget's Directive 14. (https://www.census.gov/hhes/povmeas/methodology/ombdir14.html)", "Civilian Employed Population in Sales and Office Occupations. The number of employed civilians 16 years old and over in each geography in the Sales and Office occupations. Occupation codes are based on the Standard Occupational Classification (SOC), published by the Executive Office of the President, Office of Management and Budget.", "Population who completed some college or obtained associate's degree. The number of people in a geographic area over the age of 25 who obtained an associate's degree, and did not complete a more advanced degree.", "Walked to Work. The number of workers age 16 years and over within a geographic area who primarily walked to work.  This would mean that of any way of getting to work, they travelled the most distance walking.", "Worked at Home. The count within a geographical area of workers over the age of 16 who worked at home.", "Workers over the Age of 16. The number of people in each geography who work. Workers include those employed at private for-profit companies, the self-employed, government workers and non-profit employees.", "Population Completed Associate's Degree. The number of people in a geographic area over the age of 25 who obtained a associate's degree, and did not complete a more advanced degree.", "Population Completed Bachelor's Degree. The number of people in a geographic area over the age of 25 who obtained a bachelor's degree, and did not complete a more advanced degree.", "Population Completed High School. The number of people in a geographic area over the age of 25 who completed high school, and did not complete a more advanced degree.", "Population completed less than one year of college, no degree. The number of people in a geographic area over the age of 25 who attended college for less than one year and no further.", "Population Completed Master's Degree. The number of people in a geographic area over the age of 25 who obtained a master's degree, but did not complete a more advanced degree.", "Population completed more than one year of college, no degree. The number of people in a geographic area over the age of 25 who attended college for more than one year but did not obtain a degree", "Population 25 Years and Over. The number of people in a geographic area who are over the age of 25.  This is used mostly as a denominator of educational attainment.", "Number of workers with a commute between 35 and 44 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 35 and 44 minutes. ", "Number of workers with a commute of over 60 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in over 60 minutes.", "Number of workers with less than 10 minute commute. The number of workers in a geographic area over the age of 16 who do not work from home and commute in less than 10 minutes.", "Workers age 16 and over who do not work from home. The number of workers in a geographic area over the age of 16 who do not work from home", "Hispanic of any race", "Population 5 Years and Over. The number of people in a geographic area who are over the age of 5.  This is primarily used as a denominator of measures of language spoken at home.", "Speaks only English at Home. The number of people in a geographic area over age 5 who speak only English at home.", "Speaks Spanish at Home. The number of people in a geographic area over age 5 who speak Spanish at home, possibly in addition to other languages.", "Speaks Spanish at Home, speaks English less than \"very well\"", "Population 15 Years and Over. The number of people in a geographic area who are over the age of 15.  This is used mostly as a denominator of marital status.", "Never Married. The number of people in a geographic area who have never been married.", "Currently married. The number of people in a geographic area who are currently married", "Married but separated. The number of people in a geographic area who are married but separated", "Widowed. The number of people in a geographic area who are widowed", "Divorced. The number of people in a geographic area who are divorced", null], "sample_rows": [{"geo_id": "0622", "nonfamily_households": 63689.0, "family_households": 177895.0, "median_year_structure_built": 1984.0, "rent_burden_not_computed": 5594.0, "rent_over_50_percent": 25429.0, "rent_40_to_50_percent": 9865.0, "rent_35_to_40_percent": 6757.0, "rent_30_to_35_percent": 8504.0, "rent_25_to_30_percent": 11213.0, "rent_20_to_25_percent": 11886.0, "rent_15_to_20_percent": 10562.0, "rent_10_to_15_percent": 6673.0, "rent_under_10_percent": 3203.0, "total_pop": 748078.0, "male_pop": 369077.0, "female_pop": 379001.0, "median_age": 32.6, "white_pop": 292523.0, "black_pop": 21628.0, "asian_pop": 57078.0, "hispanic_pop": 355272.0, "amerindian_pop": 2955.0, "other_race_pop": 1232.0, "two_or_more_races_pop": 16199.0, "not_hispanic_pop": 392806.0, "commuters_by_public_transportation": 2289.0, "households": 241584.0, "median_income": 56097.0, "income_per_capita": 25531.0, "housing_units": 256114.0, "vacant_housing_units": 14530.0, "vacant_housing_units_for_rent": 3751.0, "vacant_housing_units_for_sale": 1709.0, "median_rent": 845.0, "percent_income_spent_on_rent": 32.1, "owner_occupied_housing_units": 141898.0, "million_dollar_housing_units": 975.0, "mortgaged_housing_units": 101553.0, "families_with_young_children": 68736.0, "two_parent_families_with_young_children": 40941.0, "two_parents_in_labor_force_families_with_young_children": 21735.0, "two_parents_father_in_labor_force_families_with_young_children": 16591.0, "two_parents_mother_in_labor_force_families_with_young_children": 1851.0, "two_parents_not_in_labor_force_families_with_young_children": 764.0, "one_parent_families_with_young_children": 27795.0, "father_one_parent_families_with_young_children": 7678.0, "father_in_labor_force_one_parent_families_with_young_children": 6834.0, "commute_10_14_mins": 50158.0, "commute_15_19_mins": 58959.0, "commute_20_24_mins": 52407.0, "commute_25_29_mins": 19014.0, "commute_30_34_mins": 31987.0, "commute_45_59_mins": 14821.0, "aggregate_travel_time_to_work": 6459880.0, "income_less_10000": 14785.0, "income_10000_14999": 11934.0, "income_15000_19999": 12632.0, "income_20000_24999": 13165.0, "income_25000_29999": 11189.0, "income_30000_34999": 12380.0, "income_35000_39999": 11190.0, "income_40000_44999": 10897.0, "income_45000_49999": 10450.0, "income_50000_59999": 19098.0, "income_60000_74999": 23797.0, "income_75000_99999": 30302.0, "income_100000_124999": 20804.0, "income_125000_149999": 12813.0, "income_150000_199999": 13714.0, "income_200000_or_more": 12434.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 1002.0, "owner_occupied_housing_units_lower_value_quartile": 158100.0, "owner_occupied_housing_units_median_value": 235800.0, "owner_occupied_housing_units_upper_value_quartile": 337900.0, "married_households": 123345.0, "occupied_housing_units": 241584.0, "housing_units_renter_occupied": 99686.0, "dwellings_1_units_detached": 185259.0, "dwellings_1_units_attached": 7007.0, "dwellings_2_units": 5943.0, "dwellings_3_to_4_units": 17792.0, "dwellings_5_to_9_units": 13748.0, "dwellings_10_to_19_units": 4989.0, "dwellings_20_to_49_units": 3449.0, "dwellings_50_or_more_units": 8202.0, "mobile_homes": 9484.0, "housing_built_2005_or_later": 3219.0, "housing_built_2000_to_2004": 7758.0, "housing_built_1939_or_earlier": 7765.0, "male_under_5": 29981.0, "male_5_to_9": 30477.0, "male_10_to_14": 31532.0, "male_15_to_17": 17114.0, "male_18_to_19": 10754.0, "male_20": 5839.0, "male_21": 5361.0, "male_22_to_24": 16930.0, "male_25_to_29": 27664.0, "male_30_to_34": 26805.0, "male_35_to_39": 24058.0, "male_40_to_44": 21605.0, "male_45_to_49": 21525.0, "male_50_to_54": 21786.0, "male_55_to_59": 20028.0, "male_60_61": 7962.0, "male_62_64": 10042.0, "male_65_to_66": 6203.0, "male_67_to_69": 7689.0, "male_70_to_74": 10077.0, "male_75_to_79": 6714.0, "male_80_to_84": 4721.0, "male_85_and_over": 4210.0, "female_under_5": 28575.0, "female_5_to_9": 29802.0, "female_10_to_14": 27557.0, "female_15_to_17": 17356.0, "female_18_to_19": 10949.0, "female_20": 6479.0, "female_21": 5374.0, "female_22_to_24": 16369.0, "female_25_to_29": 27713.0, "female_30_to_34": 25850.0, "female_35_to_39": 23589.0, "female_40_to_44": 22825.0, "female_45_to_49": 22466.0, "female_50_to_54": 22475.0, "female_55_to_59": 22018.0, "female_60_to_61": 8285.0, "female_62_to_64": 10846.0, "female_65_to_66": 6770.0, "female_67_to_69": 9077.0, "female_70_to_74": 11628.0, "female_75_to_79": 7972.0, "female_80_to_84": 6028.0, "female_85_and_over": 8998.0, "white_including_hispanic": 547826.0, "black_including_hispanic": 23741.0, "amerindian_including_hispanic": 6858.0, "asian_including_hispanic": 59218.0, "commute_5_9_mins": 33156.0, "commute_35_39_mins": 4348.0, "commute_40_44_mins": 6108.0, "commute_60_89_mins": 9133.0, "commute_90_more_mins": 5488.0, "households_retirement_income": 40427.0, "armed_forces": 747.0, "civilian_labor_force": 345730.0, "employed_pop": 315465.0, "unemployed_pop": 30265.0, "not_in_labor_force": 211335.0, "pop_16_over": 557812.0, "pop_in_labor_force": 346477.0, "asian_male_45_54": 3237.0, "asian_male_55_64": 2982.0, "black_male_45_54": 1504.0, "black_male_55_64": 1066.0, "hispanic_male_45_54": 18197.0, "hispanic_male_55_64": 12373.0, "white_male_45_54": 19507.0, "white_male_55_64": 20802.0, "bachelors_degree_2": 72416.0, "bachelors_degree_or_higher_25_64": 89843.0, "children": 212394.0, "children_in_single_female_hh": 53886.0, "commuters_by_bus": 2217.0, "commuters_by_car_truck_van": 280269.0, "commuters_by_carpool": 33546.0, "commuters_by_subway_or_elevated": 25.0, "commuters_drove_alone": 246723.0, "different_house_year_ago_different_city": 54956.0, "different_house_year_ago_same_city": 51983.0, "employed_agriculture_forestry_fishing_hunting_mining": 26332.0, "employed_arts_entertainment_recreation_accommodation_food": 27111.0, "employed_construction": 15910.0, "employed_education_health_social": 79079.0, "employed_finance_insurance_real_estate": 16783.0, "employed_information": 4405.0, "employed_manufacturing": 22797.0, "employed_other_services_not_public_admin": 14989.0, "employed_public_administration": 20770.0, "employed_retail_trade": 34988.0, "employed_science_management_admin_waste": 26295.0, "employed_transportation_warehousing_utilities": 14671.0, "employed_wholesale_trade": 11335.0, "female_female_households": 332.0, "four_more_cars": 16938.0, "gini_index": 0.4639, "graduate_professional_degree": 38464.0, "group_quarters": 7805.0, "high_school_including_ged": 103474.0, "households_public_asst_or_food_stamps": 43677.0, "in_grades_1_to_4": 46931.0, "in_grades_5_to_8": 47851.0, "in_grades_9_to_12": 47515.0, "in_school": 226059.0, "in_undergrad_college": 51770.0, "less_than_high_school_graduate": 89505.0, "male_45_64_associates_degree": 6577.0, "male_45_64_bachelors_degree": 12790.0, "male_45_64_graduate_degree": 6984.0, "male_45_64_less_than_9_grade": 11047.0, "male_45_64_grade_9_12": 6288.0, "male_45_64_high_school": 18813.0, "male_45_64_some_college": 18844.0, "male_45_to_64": 81343.0, "male_male_households": 371.0, "management_business_sci_arts_employed": 105554.0, "no_car": 6941.0, "no_cars": 14269.0, "not_us_citizen_pop": 76209.0, "occupation_management_arts": 105554.0, "occupation_natural_resources_construction_maintenance": 40954.0, "occupation_production_transportation_material": 36896.0, "occupation_sales_office": 74305.0, "occupation_services": 57756.0, "one_car": 76682.0, "two_cars": 95324.0, "three_cars": 38371.0, "pop_25_64": 367542.0, "pop_determined_poverty_status": 739479.0, "population_1_year_and_over": 738431.0, "population_3_years_over": 715322.0, "poverty": 146435.0, "sales_office_employed": 74305.0, "some_college_and_associates_degree": 153770.0, "walked_to_work": 4555.0, "worked_at_home": 12159.0, "workers_16_and_over": 306335.0, "associates_degree": 41847.0, "bachelors_degree": 72416.0, "high_school_diploma": 90803.0, "less_one_year_college": 30544.0, "masters_degree": 24643.0, "one_year_more_college": 81379.0, "pop_25_years_over": 457629.0, "commute_35_44_mins": 10456.0, "commute_60_more_mins": 14621.0, "commute_less_10_mins": 41753.0, "commuters_16_over": 294176.0, "hispanic_any_race": 355272.0, "pop_5_years_over": 689522.0, "speak_only_english_at_home": 426624.0, "speak_spanish_at_home": 202764.0, "speak_spanish_at_home_low_english": 91027.0, "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "do_date": "20132017"}, {"geo_id": "2803", "nonfamily_households": 91493.0, "family_households": 188839.0, "median_year_structure_built": 1984.0, "rent_burden_not_computed": 14559.0, "rent_over_50_percent": 15383.0, "rent_40_to_50_percent": 6014.0, "rent_35_to_40_percent": 3834.0, "rent_30_to_35_percent": 5436.0, "rent_25_to_30_percent": 6999.0, "rent_20_to_25_percent": 8198.0, "rent_15_to_20_percent": 8684.0, "rent_10_to_15_percent": 6277.0, "rent_under_10_percent": 3089.0, "total_pop": 748858.0, "male_pop": 363055.0, "female_pop": 385803.0, "median_age": 37.2, "white_pop": 446486.0, "black_pop": 262643.0, "asian_pop": 7804.0, "hispanic_pop": 18144.0, "amerindian_pop": 7037.0, "other_race_pop": 530.0, "two_or_more_races_pop": 6131.0, "not_hispanic_pop": 730714.0, "commuters_by_public_transportation": 722.0, "households": 280332.0, "median_income": 44530.0, "income_per_capita": 25012.0, "housing_units": 331057.0, "vacant_housing_units": 50725.0, "vacant_housing_units_for_rent": 6030.0, "vacant_housing_units_for_sale": 3591.0, "median_rent": 585.0, "percent_income_spent_on_rent": 29.1, "owner_occupied_housing_units": 201859.0, "million_dollar_housing_units": 683.0, "mortgaged_housing_units": 99870.0, "families_with_young_children": 53331.0, "two_parent_families_with_young_children": 30278.0, "two_parents_in_labor_force_families_with_young_children": 18994.0, "two_parents_father_in_labor_force_families_with_young_children": 9518.0, "two_parents_mother_in_labor_force_families_with_young_children": 1208.0, "two_parents_not_in_labor_force_families_with_young_children": 558.0, "one_parent_families_with_young_children": 23053.0, "father_one_parent_families_with_young_children": 4159.0, "father_in_labor_force_one_parent_families_with_young_children": 3365.0, "commute_10_14_mins": 46585.0, "commute_15_19_mins": 49266.0, "commute_20_24_mins": 44258.0, "commute_25_29_mins": 18539.0, "commute_30_34_mins": 44205.0, "commute_45_59_mins": 19798.0, "aggregate_travel_time_to_work": 7646330.0, "income_less_10000": 28781.0, "income_10000_14999": 19603.0, "income_15000_19999": 17977.0, "income_20000_24999": 17280.0, "income_25000_29999": 15704.0, "income_30000_34999": 16075.0, "income_35000_39999": 13369.0, "income_40000_44999": 12482.0, "income_45000_49999": 11750.0, "income_50000_59999": 20859.0, "income_60000_74999": 25007.0, "income_75000_99999": 29751.0, "income_100000_124999": 19540.0, "income_125000_149999": 10747.0, "income_150000_199999": 11289.0, "income_200000_or_more": 10118.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 766.0, "owner_occupied_housing_units_lower_value_quartile": 61300.0, "owner_occupied_housing_units_median_value": 117200.0, "owner_occupied_housing_units_upper_value_quartile": 201100.0, "married_households": 131066.0, "occupied_housing_units": 280332.0, "housing_units_renter_occupied": 78473.0, "dwellings_1_units_detached": 223514.0, "dwellings_1_units_attached": 3974.0, "dwellings_2_units": 6189.0, "dwellings_3_to_4_units": 9938.0, "dwellings_5_to_9_units": 13318.0, "dwellings_10_to_19_units": 8722.0, "dwellings_20_to_49_units": 2716.0, "dwellings_50_or_more_units": 2937.0, "mobile_homes": 59489.0, "housing_built_2005_or_later": 3008.0, "housing_built_2000_to_2004": 9870.0, "housing_built_1939_or_earlier": 10855.0, "male_under_5": 23514.0, "male_5_to_9": 25868.0, "male_10_to_14": 26152.0, "male_15_to_17": 15606.0, "male_18_to_19": 11186.0, "male_20": 5519.0, "male_21": 6467.0, "male_22_to_24": 15864.0, "male_25_to_29": 24431.0, "male_30_to_34": 23732.0, "male_35_to_39": 22986.0, "male_40_to_44": 22417.0, "male_45_to_49": 22159.0, "male_50_to_54": 23874.0, "male_55_to_59": 23640.0, "male_60_61": 8806.0, "male_62_64": 12450.0, "male_65_to_66": 7344.0, "male_67_to_69": 10716.0, "male_70_to_74": 12223.0, "male_75_to_79": 8751.0, "male_80_to_84": 5517.0, "male_85_and_over": 3833.0, "female_under_5": 23334.0, "female_5_to_9": 24511.0, "female_10_to_14": 25217.0, "female_15_to_17": 14725.0, "female_18_to_19": 11177.0, "female_20": 5595.0, "female_21": 5798.0, "female_22_to_24": 14718.0, "female_25_to_29": 24765.0, "female_30_to_34": 24517.0, "female_35_to_39": 24664.0, "female_40_to_44": 23041.0, "female_45_to_49": 24201.0, "female_50_to_54": 24954.0, "female_55_to_59": 26486.0, "female_60_to_61": 9687.0, "female_62_to_64": 14133.0, "female_65_to_66": 8447.0, "female_67_to_69": 11272.0, "female_70_to_74": 15347.0, "female_75_to_79": 10936.0, "female_80_to_84": 8880.0, "female_85_and_over": 9398.0, "white_including_hispanic": 458249.0, "black_including_hispanic": 263277.0, "amerindian_including_hispanic": 7307.0, "asian_including_hispanic": 7804.0, "commute_5_9_mins": 33728.0, "commute_35_39_mins": 7093.0, "commute_40_44_mins": 7983.0, "commute_60_89_mins": 13162.0, "commute_90_more_mins": 9031.0, "households_retirement_income": 50277.0, "armed_forces": 1416.0, "civilian_labor_force": 339541.0, "employed_pop": 316193.0, "unemployed_pop": 23348.0, "not_in_labor_force": 249024.0, "pop_16_over": 589981.0, "pop_in_labor_force": 340957.0, "asian_male_45_54": 513.0, "asian_male_55_64": 261.0, "black_male_45_54": 14240.0, "black_male_55_64": 13810.0, "hispanic_male_45_54": 1289.0, "hispanic_male_55_64": 582.0, "white_male_45_54": 29403.0, "white_male_55_64": 29778.0, "bachelors_degree_2": 76875.0, "bachelors_degree_or_higher_25_64": 101805.0, "children": 178927.0, "children_in_single_female_hh": 60888.0, "commuters_by_bus": 681.0, "commuters_by_car_truck_van": 295967.0, "commuters_by_carpool": 30313.0, "commuters_by_subway_or_elevated": 0.0, "commuters_drove_alone": 265654.0, "different_house_year_ago_different_city": 76029.0, "different_house_year_ago_same_city": 19294.0, "employed_agriculture_forestry_fishing_hunting_mining": 11436.0, "employed_arts_entertainment_recreation_accommodation_food": 24479.0, "employed_construction": 21916.0, "employed_education_health_social": 84550.0, "employed_finance_insurance_real_estate": 17581.0, "employed_information": 4422.0, "employed_manufacturing": 34783.0, "employed_other_services_not_public_admin": 16441.0, "employed_public_administration": 17535.0, "employed_retail_trade": 35300.0, "employed_science_management_admin_waste": 24301.0, "employed_transportation_warehousing_utilities": 15342.0, "employed_wholesale_trade": 8107.0, "female_female_households": 331.0, "four_more_cars": 21349.0, "gini_index": 0.4968, "graduate_professional_degree": 49596.0, "group_quarters": 25474.0, "high_school_including_ged": 143709.0, "households_public_asst_or_food_stamps": 41144.0, "in_grades_1_to_4": 41257.0, "in_grades_5_to_8": 41129.0, "in_grades_9_to_12": 39419.0, "in_school": 199279.0, "in_undergrad_college": 44899.0, "less_than_high_school_graduate": 73216.0, "male_45_64_associates_degree": 7263.0, "male_45_64_bachelors_degree": 12974.0, "male_45_64_graduate_degree": 8435.0, "male_45_64_less_than_9_grade": 5069.0, "male_45_64_grade_9_12": 10152.0, "male_45_64_high_school": 30058.0, "male_45_64_some_college": 16978.0, "male_45_to_64": 90929.0, "male_male_households": 263.0, "management_business_sci_arts_employed": 115330.0, "no_car": 5567.0, "no_cars": 17050.0, "not_us_citizen_pop": 11402.0, "occupation_management_arts": 115330.0, "occupation_natural_resources_construction_maintenance": 35577.0, "occupation_production_transportation_material": 42547.0, "occupation_sales_office": 73203.0, "occupation_services": 49536.0, "one_car": 91021.0, "two_cars": 104503.0, "three_cars": 46409.0, "pop_25_64": 380943.0, "pop_determined_poverty_status": 722680.0, "population_1_year_and_over": 740793.0, "population_3_years_over": 722377.0, "poverty": 143563.0, "sales_office_employed": 73203.0, "some_college_and_associates_degree": 150211.0, "walked_to_work": 3698.0, "worked_at_home": 6828.0, "workers_16_and_over": 311400.0, "associates_degree": 45679.0, "bachelors_degree": 76875.0, "high_school_diploma": 116401.0, "less_one_year_college": 26039.0, "masters_degree": 33431.0, "one_year_more_college": 78493.0, "pop_25_years_over": 493607.0, "commute_35_44_mins": 15076.0, "commute_60_more_mins": 22193.0, "commute_less_10_mins": 44652.0, "commuters_16_over": 304572.0, "hispanic_any_race": 18144.0, "pop_5_years_over": 702010.0, "speak_only_english_at_home": 673395.0, "speak_spanish_at_home": 15177.0, "speak_spanish_at_home_low_english": 7838.0, "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "do_date": "20132017"}, {"geo_id": "4804", "nonfamily_households": 74087.0, "family_households": 189471.0, "median_year_structure_built": 1984.0, "rent_burden_not_computed": 8786.0, "rent_over_50_percent": 14790.0, "rent_40_to_50_percent": 5694.0, "rent_35_to_40_percent": 4347.0, "rent_30_to_35_percent": 5850.0, "rent_25_to_30_percent": 7158.0, "rent_20_to_25_percent": 7889.0, "rent_15_to_20_percent": 9777.0, "rent_10_to_15_percent": 7282.0, "rent_under_10_percent": 3337.0, "total_pop": 728435.0, "male_pop": 359437.0, "female_pop": 368998.0, "median_age": 39.2, "white_pop": 526598.0, "black_pop": 74873.0, "asian_pop": 7751.0, "hispanic_pop": 97990.0, "amerindian_pop": 4468.0, "other_race_pop": 517.0, "two_or_more_races_pop": 15857.0, "not_hispanic_pop": 630445.0, "commuters_by_public_transportation": 933.0, "households": 263558.0, "median_income": 51945.0, "income_per_capita": 26549.0, "housing_units": 308700.0, "vacant_housing_units": 45142.0, "vacant_housing_units_for_rent": 6254.0, "vacant_housing_units_for_sale": 3455.0, "median_rent": 601.0, "percent_income_spent_on_rent": 28.3, "owner_occupied_housing_units": 188648.0, "million_dollar_housing_units": 508.0, "mortgaged_housing_units": 97191.0, "families_with_young_children": 52842.0, "two_parent_families_with_young_children": 34473.0, "two_parents_in_labor_force_families_with_young_children": 17537.0, "two_parents_father_in_labor_force_families_with_young_children": 15288.0, "two_parents_mother_in_labor_force_families_with_young_children": 1376.0, "two_parents_not_in_labor_force_families_with_young_children": 272.0, "one_parent_families_with_young_children": 18369.0, "father_one_parent_families_with_young_children": 3716.0, "father_in_labor_force_one_parent_families_with_young_children": 3173.0, "commute_10_14_mins": 45733.0, "commute_15_19_mins": 42171.0, "commute_20_24_mins": 34901.0, "commute_25_29_mins": 12755.0, "commute_30_34_mins": 33998.0, "commute_45_59_mins": 25263.0, "aggregate_travel_time_to_work": 7898720.0, "income_less_10000": 18933.0, "income_10000_14999": 14204.0, "income_15000_19999": 14079.0, "income_20000_24999": 15368.0, "income_25000_29999": 14324.0, "income_30000_34999": 13828.0, "income_35000_39999": 12832.0, "income_40000_44999": 12537.0, "income_45000_49999": 11035.0, "income_50000_59999": 20000.0, "income_60000_74999": 25720.0, "income_75000_99999": 33310.0, "income_100000_124999": 20410.0, "income_125000_149999": 13072.0, "income_150000_199999": 13250.0, "income_200000_or_more": 10656.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 787.0, "owner_occupied_housing_units_lower_value_quartile": 66300.0, "owner_occupied_housing_units_median_value": 120900.0, "owner_occupied_housing_units_upper_value_quartile": 207700.0, "married_households": 144988.0, "occupied_housing_units": 263558.0, "housing_units_renter_occupied": 74910.0, "dwellings_1_units_detached": 226904.0, "dwellings_1_units_attached": 3296.0, "dwellings_2_units": 6329.0, "dwellings_3_to_4_units": 8383.0, "dwellings_5_to_9_units": 8517.0, "dwellings_10_to_19_units": 5161.0, "dwellings_20_to_49_units": 3864.0, "dwellings_50_or_more_units": 3341.0, "mobile_homes": 42003.0, "housing_built_2005_or_later": 4404.0, "housing_built_2000_to_2004": 11864.0, "housing_built_1939_or_earlier": 12685.0, "male_under_5": 23870.0, "male_5_to_9": 25097.0, "male_10_to_14": 26379.0, "male_15_to_17": 15729.0, "male_18_to_19": 9811.0, "male_20": 4658.0, "male_21": 4871.0, "male_22_to_24": 13054.0, "male_25_to_29": 21357.0, "male_30_to_34": 21800.0, "male_35_to_39": 22202.0, "male_40_to_44": 22272.0, "male_45_to_49": 23621.0, "male_50_to_54": 24794.0, "male_55_to_59": 25029.0, "male_60_61": 9075.0, "male_62_64": 11898.0, "male_65_to_66": 7699.0, "male_67_to_69": 11226.0, "male_70_to_74": 14343.0, "male_75_to_79": 10206.0, "male_80_to_84": 5877.0, "male_85_and_over": 4569.0, "female_under_5": 22215.0, "female_5_to_9": 23961.0, "female_10_to_14": 25789.0, "female_15_to_17": 15143.0, "female_18_to_19": 8690.0, "female_20": 4150.0, "female_21": 4141.0, "female_22_to_24": 12222.0, "female_25_to_29": 21236.0, "female_30_to_34": 21851.0, "female_35_to_39": 22819.0, "female_40_to_44": 23187.0, "female_45_to_49": 23876.0, "female_50_to_54": 25395.0, "female_55_to_59": 26325.0, "female_60_to_61": 9840.0, "female_62_to_64": 12403.0, "female_65_to_66": 8504.0, "female_67_to_69": 11855.0, "female_70_to_74": 16240.0, "female_75_to_79": 11929.0, "female_80_to_84": 8216.0, "female_85_and_over": 9011.0, "white_including_hispanic": 592784.0, "black_including_hispanic": 75818.0, "amerindian_including_hispanic": 4938.0, "asian_including_hispanic": 7842.0, "commute_5_9_mins": 37801.0, "commute_35_39_mins": 8362.0, "commute_40_44_mins": 8920.0, "commute_60_89_mins": 23408.0, "commute_90_more_mins": 10250.0, "households_retirement_income": 52210.0, "armed_forces": 432.0, "civilian_labor_force": 337124.0, "employed_pop": 316097.0, "unemployed_pop": 21027.0, "not_in_labor_force": 232409.0, "pop_16_over": 569965.0, "pop_in_labor_force": 337556.0, "asian_male_45_54": 526.0, "asian_male_55_64": 312.0, "black_male_45_54": 4983.0, "black_male_55_64": 4479.0, "hispanic_male_45_54": 5488.0, "hispanic_male_55_64": 3158.0, "white_male_45_54": 36553.0, "white_male_55_64": 37394.0, "bachelors_degree_2": 69766.0, "bachelors_degree_or_higher_25_64": 79413.0, "children": 178183.0, "children_in_single_female_hh": 41945.0, "commuters_by_bus": 644.0, "commuters_by_car_truck_van": 286300.0, "commuters_by_carpool": 30340.0, "commuters_by_subway_or_elevated": 76.0, "commuters_drove_alone": 255960.0, "different_house_year_ago_different_city": 75403.0, "different_house_year_ago_same_city": 24704.0, "employed_agriculture_forestry_fishing_hunting_mining": 9250.0, "employed_arts_entertainment_recreation_accommodation_food": 23111.0, "employed_construction": 26193.0, "employed_education_health_social": 72732.0, "employed_finance_insurance_real_estate": 17814.0, "employed_information": 4088.0, "employed_manufacturing": 39910.0, "employed_other_services_not_public_admin": 15780.0, "employed_public_administration": 15568.0, "employed_retail_trade": 40118.0, "employed_science_management_admin_waste": 25370.0, "employed_transportation_warehousing_utilities": 17063.0, "employed_wholesale_trade": 9100.0, "female_female_households": 284.0, "four_more_cars": 19987.0, "gini_index": 0.462, "graduate_professional_degree": 33490.0, "group_quarters": 17686.0, "high_school_including_ged": 160510.0, "households_public_asst_or_food_stamps": 35727.0, "in_grades_1_to_4": 40566.0, "in_grades_5_to_8": 40515.0, "in_grades_9_to_12": 40996.0, "in_school": 178522.0, "in_undergrad_college": 31672.0, "less_than_high_school_graduate": 67228.0, "male_45_64_associates_degree": 6793.0, "male_45_64_bachelors_degree": 12518.0, "male_45_64_graduate_degree": 6327.0, "male_45_64_less_than_9_grade": 5499.0, "male_45_64_grade_9_12": 7931.0, "male_45_64_high_school": 32754.0, "male_45_64_some_college": 22595.0, "male_45_to_64": 94417.0, "male_male_households": 90.0, "management_business_sci_arts_employed": 102745.0, "no_car": 5355.0, "no_cars": 13527.0, "not_us_citizen_pop": 31082.0, "occupation_management_arts": 102745.0, "occupation_natural_resources_construction_maintenance": 35822.0, "occupation_production_transportation_material": 48411.0, "occupation_sales_office": 75582.0, "occupation_services": 53537.0, "one_car": 76600.0, "two_cars": 108143.0, "three_cars": 45301.0, "pop_25_64": 368980.0, "pop_determined_poverty_status": 709707.0, "population_1_year_and_over": 719306.0, "population_3_years_over": 700581.0, "poverty": 106074.0, "sales_office_employed": 75582.0, "some_college_and_associates_degree": 157661.0, "walked_to_work": 4021.0, "worked_at_home": 13767.0, "workers_16_and_over": 309963.0, "associates_degree": 36076.0, "bachelors_degree": 69766.0, "high_school_diploma": 129522.0, "less_one_year_college": 38324.0, "masters_degree": 25744.0, "one_year_more_college": 83261.0, "pop_25_years_over": 488655.0, "commute_35_44_mins": 17282.0, "commute_60_more_mins": 33658.0, "commute_less_10_mins": 50435.0, "commuters_16_over": 296196.0, "hispanic_any_race": 97990.0, "pop_5_years_over": 682350.0, "speak_only_english_at_home": 600080.0, "speak_spanish_at_home": 69573.0, "speak_spanish_at_home_low_english": 25752.0, "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "do_date": "20132017"}, {"geo_id": "4836", "nonfamily_households": 73665.0, "family_households": 183030.0, "median_year_structure_built": 1984.0, "rent_burden_not_computed": 7370.0, "rent_over_50_percent": 13978.0, "rent_40_to_50_percent": 5916.0, "rent_35_to_40_percent": 3360.0, "rent_30_to_35_percent": 5087.0, "rent_25_to_30_percent": 6897.0, "rent_20_to_25_percent": 8943.0, "rent_15_to_20_percent": 9961.0, "rent_10_to_15_percent": 7247.0, "rent_under_10_percent": 3980.0, "total_pop": 726915.0, "male_pop": 365135.0, "female_pop": 361780.0, "median_age": 37.1, "white_pop": 453821.0, "black_pop": 63375.0, "asian_pop": 14802.0, "hispanic_pop": 179891.0, "amerindian_pop": 2521.0, "other_race_pop": 847.0, "two_or_more_races_pop": 11391.0, "not_hispanic_pop": 547024.0, "commuters_by_public_transportation": 1385.0, "households": 256695.0, "median_income": 58115.0, "income_per_capita": 28478.0, "housing_units": 300017.0, "vacant_housing_units": 43322.0, "vacant_housing_units_for_rent": 7291.0, "vacant_housing_units_for_sale": 3157.0, "median_rent": 716.0, "percent_income_spent_on_rent": 26.9, "owner_occupied_housing_units": 183956.0, "million_dollar_housing_units": 295.0, "mortgaged_housing_units": 88019.0, "families_with_young_children": 55868.0, "two_parent_families_with_young_children": 35292.0, "two_parents_in_labor_force_families_with_young_children": 18157.0, "two_parents_father_in_labor_force_families_with_young_children": 16081.0, "two_parents_mother_in_labor_force_families_with_young_children": 680.0, "two_parents_not_in_labor_force_families_with_young_children": 374.0, "one_parent_families_with_young_children": 20576.0, "father_one_parent_families_with_young_children": 5029.0, "father_in_labor_force_one_parent_families_with_young_children": 4531.0, "commute_10_14_mins": 39304.0, "commute_15_19_mins": 43582.0, "commute_20_24_mins": 37831.0, "commute_25_29_mins": 15899.0, "commute_30_34_mins": 42379.0, "commute_45_59_mins": 28147.0, "aggregate_travel_time_to_work": 8293665.0, "income_less_10000": 16261.0, "income_10000_14999": 12857.0, "income_15000_19999": 12207.0, "income_20000_24999": 12871.0, "income_25000_29999": 11929.0, "income_30000_34999": 12192.0, "income_35000_39999": 11145.0, "income_40000_44999": 12139.0, "income_45000_49999": 9926.0, "income_50000_59999": 20227.0, "income_60000_74999": 26766.0, "income_75000_99999": 30434.0, "income_100000_124999": 23675.0, "income_125000_149999": 15599.0, "income_150000_199999": 15202.0, "income_200000_or_more": 13265.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 885.0, "owner_occupied_housing_units_lower_value_quartile": 70400.0, "owner_occupied_housing_units_median_value": 124500.0, "owner_occupied_housing_units_upper_value_quartile": 193100.0, "married_households": 136447.0, "occupied_housing_units": 256695.0, "housing_units_renter_occupied": 72739.0, "dwellings_1_units_detached": 198091.0, "dwellings_1_units_attached": 4674.0, "dwellings_2_units": 3522.0, "dwellings_3_to_4_units": 5937.0, "dwellings_5_to_9_units": 11300.0, "dwellings_10_to_19_units": 11713.0, "dwellings_20_to_49_units": 6462.0, "dwellings_50_or_more_units": 7213.0, "mobile_homes": 49687.0, "housing_built_2005_or_later": 3511.0, "housing_built_2000_to_2004": 12862.0, "housing_built_1939_or_earlier": 9008.0, "male_under_5": 23839.0, "male_5_to_9": 26106.0, "male_10_to_14": 26749.0, "male_15_to_17": 16626.0, "male_18_to_19": 9958.0, "male_20": 5061.0, "male_21": 4963.0, "male_22_to_24": 14480.0, "male_25_to_29": 24720.0, "male_30_to_34": 25065.0, "male_35_to_39": 21878.0, "male_40_to_44": 24168.0, "male_45_to_49": 24021.0, "male_50_to_54": 25838.0, "male_55_to_59": 23752.0, "male_60_61": 10230.0, "male_62_64": 11946.0, "male_65_to_66": 7341.0, "male_67_to_69": 9541.0, "male_70_to_74": 12798.0, "male_75_to_79": 7300.0, "male_80_to_84": 4926.0, "male_85_and_over": 3829.0, "female_under_5": 24118.0, "female_5_to_9": 25159.0, "female_10_to_14": 24366.0, "female_15_to_17": 15021.0, "female_18_to_19": 8199.0, "female_20": 4692.0, "female_21": 4798.0, "female_22_to_24": 13708.0, "female_25_to_29": 23281.0, "female_30_to_34": 23472.0, "female_35_to_39": 23279.0, "female_40_to_44": 21832.0, "female_45_to_49": 23195.0, "female_50_to_54": 25702.0, "female_55_to_59": 25326.0, "female_60_to_61": 9522.0, "female_62_to_64": 12579.0, "female_65_to_66": 8114.0, "female_67_to_69": 10519.0, "female_70_to_74": 12333.0, "female_75_to_79": 9318.0, "female_80_to_84": 6939.0, "female_85_and_over": 6308.0, "white_including_hispanic": 598929.0, "black_including_hispanic": 64560.0, "amerindian_including_hispanic": 4225.0, "asian_including_hispanic": 14936.0, "commute_5_9_mins": 29212.0, "commute_35_39_mins": 9088.0, "commute_40_44_mins": 11085.0, "commute_60_89_mins": 22789.0, "commute_90_more_mins": 9428.0, "households_retirement_income": 44843.0, "armed_forces": 409.0, "civilian_labor_force": 338620.0, "employed_pop": 312901.0, "unemployed_pop": 25719.0, "not_in_labor_force": 227478.0, "pop_16_over": 566507.0, "pop_in_labor_force": 339029.0, "asian_male_45_54": 1279.0, "asian_male_55_64": 828.0, "black_male_45_54": 4106.0, "black_male_55_64": 3650.0, "hispanic_male_45_54": 11072.0, "hispanic_male_55_64": 6722.0, "white_male_45_54": 32740.0, "white_male_55_64": 34121.0, "bachelors_degree_2": 61543.0, "bachelors_degree_or_higher_25_64": 71208.0, "children": 181984.0, "children_in_single_female_hh": 43566.0, "commuters_by_bus": 1220.0, "commuters_by_car_truck_van": 287916.0, "commuters_by_carpool": 28955.0, "commuters_by_subway_or_elevated": 53.0, "commuters_drove_alone": 258961.0, "different_house_year_ago_different_city": 82905.0, "different_house_year_ago_same_city": 25982.0, "employed_agriculture_forestry_fishing_hunting_mining": 9701.0, "employed_arts_entertainment_recreation_accommodation_food": 25852.0, "employed_construction": 36092.0, "employed_education_health_social": 62182.0, "employed_finance_insurance_real_estate": 13547.0, "employed_information": 2557.0, "employed_manufacturing": 44600.0, "employed_other_services_not_public_admin": 16009.0, "employed_public_administration": 11767.0, "employed_retail_trade": 32735.0, "employed_science_management_admin_waste": 28980.0, "employed_transportation_warehousing_utilities": 19767.0, "employed_wholesale_trade": 9112.0, "female_female_households": 463.0, "four_more_cars": 17589.0, "gini_index": 0.4561, "graduate_professional_degree": 28039.0, "group_quarters": 17681.0, "high_school_including_ged": 155802.0, "households_public_asst_or_food_stamps": 33591.0, "in_grades_1_to_4": 41079.0, "in_grades_5_to_8": 40431.0, "in_grades_9_to_12": 42749.0, "in_school": 180280.0, "in_undergrad_college": 29858.0, "less_than_high_school_graduate": 75985.0, "male_45_64_associates_degree": 6428.0, "male_45_64_bachelors_degree": 11175.0, "male_45_64_graduate_degree": 5665.0, "male_45_64_less_than_9_grade": 6817.0, "male_45_64_grade_9_12": 9367.0, "male_45_64_high_school": 33692.0, "male_45_64_some_college": 22643.0, "male_45_to_64": 95787.0, "male_male_households": 187.0, "management_business_sci_arts_employed": 96881.0, "no_car": 6270.0, "no_cars": 12139.0, "not_us_citizen_pop": 47507.0, "occupation_management_arts": 96881.0, "occupation_natural_resources_construction_maintenance": 46436.0, "occupation_production_transportation_material": 50062.0, "occupation_sales_office": 70638.0, "occupation_services": 48884.0, "one_car": 82386.0, "two_cars": 103764.0, "three_cars": 40817.0, "pop_25_64": 379806.0, "pop_determined_poverty_status": 707440.0, "population_1_year_and_over": 718348.0, "population_3_years_over": 699699.0, "poverty": 98514.0, "sales_office_employed": 70638.0, "some_college_and_associates_degree": 157703.0, "walked_to_work": 3715.0, "worked_at_home": 8095.0, "workers_16_and_over": 305936.0, "associates_degree": 37445.0, "bachelors_degree": 61543.0, "high_school_diploma": 127946.0, "less_one_year_college": 35395.0, "masters_degree": 21583.0, "one_year_more_college": 84863.0, "pop_25_years_over": 479072.0, "commute_35_44_mins": 20173.0, "commute_60_more_mins": 32217.0, "commute_less_10_mins": 38309.0, "commuters_16_over": 297841.0, "hispanic_any_race": 179891.0, "pop_5_years_over": 678958.0, "speak_only_english_at_home": 541113.0, "speak_spanish_at_home": 117832.0, "speak_spanish_at_home_low_english": 46450.0, "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "do_date": "20132017"}, {"geo_id": "4503", "nonfamily_households": 82753.0, "family_households": 177148.0, "median_year_structure_built": 1984.0, "rent_burden_not_computed": 9857.0, "rent_over_50_percent": 17029.0, "rent_40_to_50_percent": 5731.0, "rent_35_to_40_percent": 4556.0, "rent_30_to_35_percent": 5366.0, "rent_25_to_30_percent": 7065.0, "rent_20_to_25_percent": 8545.0, "rent_15_to_20_percent": 8700.0, "rent_10_to_15_percent": 5632.0, "rent_under_10_percent": 2603.0, "total_pop": 677940.0, "male_pop": 332386.0, "female_pop": 345554.0, "median_age": 40.3, "white_pop": 502161.0, "black_pop": 124716.0, "asian_pop": 5794.0, "hispanic_pop": 31967.0, "amerindian_pop": 1147.0, "other_race_pop": 711.0, "two_or_more_races_pop": 11226.0, "not_hispanic_pop": 645973.0, "commuters_by_public_transportation": 1521.0, "households": 259901.0, "median_income": 44345.0, "income_per_capita": 23731.0, "housing_units": 306378.0, "vacant_housing_units": 46477.0, "vacant_housing_units_for_rent": 5000.0, "vacant_housing_units_for_sale": 3653.0, "median_rent": 515.0, "percent_income_spent_on_rent": 30.1, "owner_occupied_housing_units": 184817.0, "million_dollar_housing_units": 816.0, "mortgaged_housing_units": 99535.0, "families_with_young_children": 44278.0, "two_parent_families_with_young_children": 25490.0, "two_parents_in_labor_force_families_with_young_children": 14065.0, "two_parents_father_in_labor_force_families_with_young_children": 10267.0, "two_parents_mother_in_labor_force_families_with_young_children": 817.0, "two_parents_not_in_labor_force_families_with_young_children": 341.0, "one_parent_families_with_young_children": 18788.0, "father_one_parent_families_with_young_children": 4485.0, "father_in_labor_force_one_parent_families_with_young_children": 4018.0, "commute_10_14_mins": 41214.0, "commute_15_19_mins": 44333.0, "commute_20_24_mins": 41203.0, "commute_25_29_mins": 19678.0, "commute_30_34_mins": 35678.0, "commute_45_59_mins": 21673.0, "aggregate_travel_time_to_work": 6650100.0, "income_less_10000": 21215.0, "income_10000_14999": 17411.0, "income_15000_19999": 16813.0, "income_20000_24999": 16881.0, "income_25000_29999": 16663.0, "income_30000_34999": 15154.0, "income_35000_39999": 13608.0, "income_40000_44999": 13874.0, "income_45000_49999": 12875.0, "income_50000_59999": 20140.0, "income_60000_74999": 24996.0, "income_75000_99999": 29814.0, "income_100000_124999": 17199.0, "income_125000_149999": 8884.0, "income_150000_199999": 8388.0, "income_200000_or_more": 5986.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 705.0, "owner_occupied_housing_units_lower_value_quartile": 70700.0, "owner_occupied_housing_units_median_value": 126900.0, "owner_occupied_housing_units_upper_value_quartile": 208000.0, "married_households": 128340.0, "occupied_housing_units": 259901.0, "housing_units_renter_occupied": 75084.0, "dwellings_1_units_detached": 204251.0, "dwellings_1_units_attached": 4364.0, "dwellings_2_units": 6167.0, "dwellings_3_to_4_units": 7405.0, "dwellings_5_to_9_units": 8957.0, "dwellings_10_to_19_units": 6602.0, "dwellings_20_to_49_units": 3279.0, "dwellings_50_or_more_units": 1737.0, "mobile_homes": 63269.0, "housing_built_2005_or_later": 3932.0, "housing_built_2000_to_2004": 7137.0, "housing_built_1939_or_earlier": 12415.0, "male_under_5": 19405.0, "male_5_to_9": 20988.0, "male_10_to_14": 21533.0, "male_15_to_17": 13483.0, "male_18_to_19": 10545.0, "male_20": 5477.0, "male_21": 6151.0, "male_22_to_24": 13618.0, "male_25_to_29": 21034.0, "male_30_to_34": 18986.0, "male_35_to_39": 20428.0, "male_40_to_44": 19678.0, "male_45_to_49": 21696.0, "male_50_to_54": 23097.0, "male_55_to_59": 22442.0, "male_60_61": 8418.0, "male_62_64": 12563.0, "male_65_to_66": 7919.0, "male_67_to_69": 11049.0, "male_70_to_74": 14544.0, "male_75_to_79": 9514.0, "male_80_to_84": 5492.0, "male_85_and_over": 4326.0, "female_under_5": 19161.0, "female_5_to_9": 20861.0, "female_10_to_14": 20064.0, "female_15_to_17": 12468.0, "female_18_to_19": 9954.0, "female_20": 5011.0, "female_21": 5629.0, "female_22_to_24": 13005.0, "female_25_to_29": 20447.0, "female_30_to_34": 19277.0, "female_35_to_39": 19461.0, "female_40_to_44": 21167.0, "female_45_to_49": 22266.0, "female_50_to_54": 23988.0, "female_55_to_59": 24259.0, "female_60_to_61": 9317.0, "female_62_to_64": 13625.0, "female_65_to_66": 8937.0, "female_67_to_69": 11868.0, "female_70_to_74": 16206.0, "female_75_to_79": 11644.0, "female_80_to_84": 8457.0, "female_85_and_over": 8482.0, "white_including_hispanic": 520629.0, "black_including_hispanic": 125670.0, "amerindian_including_hispanic": 1584.0, "asian_including_hispanic": 5934.0, "commute_5_9_mins": 28488.0, "commute_35_39_mins": 9994.0, "commute_40_44_mins": 9422.0, "commute_60_89_mins": 10813.0, "commute_90_more_mins": 4409.0, "households_retirement_income": 53739.0, "armed_forces": 384.0, "civilian_labor_force": 312033.0, "employed_pop": 289746.0, "unemployed_pop": 22287.0, "not_in_labor_force": 234928.0, "pop_16_over": 547345.0, "pop_in_labor_force": 312417.0, "asian_male_45_54": 334.0, "asian_male_55_64": 261.0, "black_male_45_54": 8006.0, "black_male_55_64": 7152.0, "hispanic_male_45_54": 1631.0, "hispanic_male_55_64": 869.0, "white_male_45_54": 34319.0, "white_male_55_64": 34951.0, "bachelors_degree_2": 61105.0, "bachelors_degree_or_higher_25_64": 73460.0, "children": 147963.0, "children_in_single_female_hh": 42477.0, "commuters_by_bus": 1298.0, "commuters_by_car_truck_van": 266854.0, "commuters_by_carpool": 27410.0, "commuters_by_subway_or_elevated": 82.0, "commuters_drove_alone": 239444.0, "different_house_year_ago_different_city": 83364.0, "different_house_year_ago_same_city": 11599.0, "employed_agriculture_forestry_fishing_hunting_mining": 3847.0, "employed_arts_entertainment_recreation_accommodation_food": 24257.0, "employed_construction": 18224.0, "employed_education_health_social": 65961.0, "employed_finance_insurance_real_estate": 11388.0, "employed_information": 3947.0, "employed_manufacturing": 61171.0, "employed_other_services_not_public_admin": 14810.0, "employed_public_administration": 9132.0, "employed_retail_trade": 33312.0, "employed_science_management_admin_waste": 22968.0, "employed_transportation_warehousing_utilities": 12986.0, "employed_wholesale_trade": 7743.0, "female_female_households": 313.0, "four_more_cars": 21516.0, "gini_index": 0.457, "graduate_professional_degree": 35781.0, "group_quarters": 21565.0, "high_school_including_ged": 146812.0, "households_public_asst_or_food_stamps": 38919.0, "in_grades_1_to_4": 34440.0, "in_grades_5_to_8": 32803.0, "in_grades_9_to_12": 33424.0, "in_school": 167429.0, "in_undergrad_college": 42819.0, "less_than_high_school_graduate": 77203.0, "male_45_64_associates_degree": 8470.0, "male_45_64_bachelors_degree": 12563.0, "male_45_64_graduate_degree": 6518.0, "male_45_64_less_than_9_grade": 4315.0, "male_45_64_grade_9_12": 9936.0, "male_45_64_high_school": 29720.0, "male_45_64_some_college": 16694.0, "male_45_to_64": 88216.0, "male_male_households": 163.0, "management_business_sci_arts_employed": 88809.0, "no_car": 6449.0, "no_cars": 17242.0, "not_us_citizen_pop": 16134.0, "occupation_management_arts": 88809.0, "occupation_natural_resources_construction_maintenance": 29198.0, "occupation_production_transportation_material": 55988.0, "occupation_sales_office": 64837.0, "occupation_services": 50914.0, "one_car": 79918.0, "two_cars": 96389.0, "three_cars": 44836.0, "pop_25_64": 342149.0, "pop_determined_poverty_status": 655670.0, "population_1_year_and_over": 670781.0, "population_3_years_over": 655234.0, "poverty": 117445.0, "sales_office_employed": 64837.0, "some_college_and_associates_degree": 139686.0, "walked_to_work": 3850.0, "worked_at_home": 8453.0, "workers_16_and_over": 283708.0, "associates_degree": 46126.0, "bachelors_degree": 61105.0, "high_school_diploma": 118095.0, "less_one_year_college": 28695.0, "masters_degree": 26352.0, "one_year_more_college": 64865.0, "pop_25_years_over": 460587.0, "commute_35_44_mins": 19416.0, "commute_60_more_mins": 15222.0, "commute_less_10_mins": 36838.0, "commuters_16_over": 275255.0, "hispanic_any_race": 31967.0, "pop_5_years_over": 639374.0, "speak_only_english_at_home": 606337.0, "speak_spanish_at_home": 23345.0, "speak_spanish_at_home_low_english": 10494.0, "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "do_date": "20132017"}]}