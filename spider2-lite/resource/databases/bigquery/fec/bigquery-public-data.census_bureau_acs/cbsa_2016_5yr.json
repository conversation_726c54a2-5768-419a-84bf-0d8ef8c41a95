{"table_name": "cbsa_2016_5yr", "table_fullname": "bigquery-public-data.census_bureau_acs.cbsa_2016_5yr", "column_names": ["geo_id", "nonfamily_households", "family_households", "median_year_structure_built", "rent_burden_not_computed", "rent_over_50_percent", "rent_40_to_50_percent", "rent_35_to_40_percent", "rent_30_to_35_percent", "rent_25_to_30_percent", "rent_20_to_25_percent", "rent_15_to_20_percent", "rent_10_to_15_percent", "rent_under_10_percent", "total_pop", "male_pop", "female_pop", "median_age", "white_pop", "black_pop", "asian_pop", "hispanic_pop", "amerindian_pop", "other_race_pop", "two_or_more_races_pop", "not_hispanic_pop", "commuters_by_public_transportation", "households", "median_income", "income_per_capita", "housing_units", "vacant_housing_units", "vacant_housing_units_for_rent", "vacant_housing_units_for_sale", "median_rent", "percent_income_spent_on_rent", "owner_occupied_housing_units", "million_dollar_housing_units", "mortgaged_housing_units", "families_with_young_children", "two_parent_families_with_young_children", "two_parents_in_labor_force_families_with_young_children", "two_parents_father_in_labor_force_families_with_young_children", "two_parents_mother_in_labor_force_families_with_young_children", "two_parents_not_in_labor_force_families_with_young_children", "one_parent_families_with_young_children", "father_one_parent_families_with_young_children", "father_in_labor_force_one_parent_families_with_young_children", "commute_10_14_mins", "commute_15_19_mins", "commute_20_24_mins", "commute_25_29_mins", "commute_30_34_mins", "commute_45_59_mins", "aggregate_travel_time_to_work", "income_less_10000", "income_10000_14999", "income_15000_19999", "income_20000_24999", "income_25000_29999", "income_30000_34999", "income_35000_39999", "income_40000_44999", "income_45000_49999", "income_50000_59999", "income_60000_74999", "income_75000_99999", "income_100000_124999", "income_125000_149999", "income_150000_199999", "income_200000_or_more", "renter_occupied_housing_units_paying_cash_median_gross_rent", "owner_occupied_housing_units_lower_value_quartile", "owner_occupied_housing_units_median_value", "owner_occupied_housing_units_upper_value_quartile", "married_households", "occupied_housing_units", "housing_units_renter_occupied", "dwellings_1_units_detached", "dwellings_1_units_attached", "dwellings_2_units", "dwellings_3_to_4_units", "dwellings_5_to_9_units", "dwellings_10_to_19_units", "dwellings_20_to_49_units", "dwellings_50_or_more_units", "mobile_homes", "housing_built_2005_or_later", "housing_built_2000_to_2004", "housing_built_1939_or_earlier", "male_under_5", "male_5_to_9", "male_10_to_14", "male_15_to_17", "male_18_to_19", "male_20", "male_21", "male_22_to_24", "male_25_to_29", "male_30_to_34", "male_35_to_39", "male_40_to_44", "male_45_to_49", "male_50_to_54", "male_55_to_59", "male_60_61", "male_62_64", "male_65_to_66", "male_67_to_69", "male_70_to_74", "male_75_to_79", "male_80_to_84", "male_85_and_over", "female_under_5", "female_5_to_9", "female_10_to_14", "female_15_to_17", "female_18_to_19", "female_20", "female_21", "female_22_to_24", "female_25_to_29", "female_30_to_34", "female_35_to_39", "female_40_to_44", "female_45_to_49", "female_50_to_54", "female_55_to_59", "female_60_to_61", "female_62_to_64", "female_65_to_66", "female_67_to_69", "female_70_to_74", "female_75_to_79", "female_80_to_84", "female_85_and_over", "white_including_hispanic", "black_including_hispanic", "amerindian_including_hispanic", "asian_including_hispanic", "commute_5_9_mins", "commute_35_39_mins", "commute_40_44_mins", "commute_60_89_mins", "commute_90_more_mins", "households_retirement_income", "armed_forces", "civilian_labor_force", "employed_pop", "unemployed_pop", "not_in_labor_force", "pop_16_over", "pop_in_labor_force", "asian_male_45_54", "asian_male_55_64", "black_male_45_54", "black_male_55_64", "hispanic_male_45_54", "hispanic_male_55_64", "white_male_45_54", "white_male_55_64", "bachelors_degree_2", "bachelors_degree_or_higher_25_64", "children", "children_in_single_female_hh", "commuters_by_bus", "commuters_by_car_truck_van", "commuters_by_carpool", "commuters_by_subway_or_elevated", "commuters_drove_alone", "different_house_year_ago_different_city", "different_house_year_ago_same_city", "employed_agriculture_forestry_fishing_hunting_mining", "employed_arts_entertainment_recreation_accommodation_food", "employed_construction", "employed_education_health_social", "employed_finance_insurance_real_estate", "employed_information", "employed_manufacturing", "employed_other_services_not_public_admin", "employed_public_administration", "employed_retail_trade", "employed_science_management_admin_waste", "employed_transportation_warehousing_utilities", "employed_wholesale_trade", "female_female_households", "four_more_cars", "gini_index", "graduate_professional_degree", "group_quarters", "high_school_including_ged", "households_public_asst_or_food_stamps", "in_grades_1_to_4", "in_grades_5_to_8", "in_grades_9_to_12", "in_school", "in_undergrad_college", "less_than_high_school_graduate", "male_45_64_associates_degree", "male_45_64_bachelors_degree", "male_45_64_graduate_degree", "male_45_64_less_than_9_grade", "male_45_64_grade_9_12", "male_45_64_high_school", "male_45_64_some_college", "male_45_to_64", "male_male_households", "management_business_sci_arts_employed", "no_car", "no_cars", "not_us_citizen_pop", "occupation_management_arts", "occupation_natural_resources_construction_maintenance", "occupation_production_transportation_material", "occupation_sales_office", "occupation_services", "one_car", "two_cars", "three_cars", "pop_25_64", "pop_determined_poverty_status", "population_1_year_and_over", "population_3_years_over", "poverty", "sales_office_employed", "some_college_and_associates_degree", "walked_to_work", "worked_at_home", "workers_16_and_over", "associates_degree", "bachelors_degree", "high_school_diploma", "less_one_year_college", "masters_degree", "one_year_more_college", "pop_25_years_over", "commute_35_44_mins", "commute_60_more_mins", "commute_less_10_mins", "commuters_16_over", "hispanic_any_race", "do_date", "pop_5_years_over", "speak_only_english_at_home", "speak_spanish_at_home", "speak_spanish_at_home_low_english", "pop_15_and_over", "pop_never_married", "pop_now_married", "pop_separated", "pop_widowed", "pop_divorced"], "column_types": ["STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64"], "nested_column_names": ["geo_id", "nonfamily_households", "family_households", "median_year_structure_built", "rent_burden_not_computed", "rent_over_50_percent", "rent_40_to_50_percent", "rent_35_to_40_percent", "rent_30_to_35_percent", "rent_25_to_30_percent", "rent_20_to_25_percent", "rent_15_to_20_percent", "rent_10_to_15_percent", "rent_under_10_percent", "total_pop", "male_pop", "female_pop", "median_age", "white_pop", "black_pop", "asian_pop", "hispanic_pop", "amerindian_pop", "other_race_pop", "two_or_more_races_pop", "not_hispanic_pop", "commuters_by_public_transportation", "households", "median_income", "income_per_capita", "housing_units", "vacant_housing_units", "vacant_housing_units_for_rent", "vacant_housing_units_for_sale", "median_rent", "percent_income_spent_on_rent", "owner_occupied_housing_units", "million_dollar_housing_units", "mortgaged_housing_units", "families_with_young_children", "two_parent_families_with_young_children", "two_parents_in_labor_force_families_with_young_children", "two_parents_father_in_labor_force_families_with_young_children", "two_parents_mother_in_labor_force_families_with_young_children", "two_parents_not_in_labor_force_families_with_young_children", "one_parent_families_with_young_children", "father_one_parent_families_with_young_children", "father_in_labor_force_one_parent_families_with_young_children", "commute_10_14_mins", "commute_15_19_mins", "commute_20_24_mins", "commute_25_29_mins", "commute_30_34_mins", "commute_45_59_mins", "aggregate_travel_time_to_work", "income_less_10000", "income_10000_14999", "income_15000_19999", "income_20000_24999", "income_25000_29999", "income_30000_34999", "income_35000_39999", "income_40000_44999", "income_45000_49999", "income_50000_59999", "income_60000_74999", "income_75000_99999", "income_100000_124999", "income_125000_149999", "income_150000_199999", "income_200000_or_more", "renter_occupied_housing_units_paying_cash_median_gross_rent", "owner_occupied_housing_units_lower_value_quartile", "owner_occupied_housing_units_median_value", "owner_occupied_housing_units_upper_value_quartile", "married_households", "occupied_housing_units", "housing_units_renter_occupied", "dwellings_1_units_detached", "dwellings_1_units_attached", "dwellings_2_units", "dwellings_3_to_4_units", "dwellings_5_to_9_units", "dwellings_10_to_19_units", "dwellings_20_to_49_units", "dwellings_50_or_more_units", "mobile_homes", "housing_built_2005_or_later", "housing_built_2000_to_2004", "housing_built_1939_or_earlier", "male_under_5", "male_5_to_9", "male_10_to_14", "male_15_to_17", "male_18_to_19", "male_20", "male_21", "male_22_to_24", "male_25_to_29", "male_30_to_34", "male_35_to_39", "male_40_to_44", "male_45_to_49", "male_50_to_54", "male_55_to_59", "male_60_61", "male_62_64", "male_65_to_66", "male_67_to_69", "male_70_to_74", "male_75_to_79", "male_80_to_84", "male_85_and_over", "female_under_5", "female_5_to_9", "female_10_to_14", "female_15_to_17", "female_18_to_19", "female_20", "female_21", "female_22_to_24", "female_25_to_29", "female_30_to_34", "female_35_to_39", "female_40_to_44", "female_45_to_49", "female_50_to_54", "female_55_to_59", "female_60_to_61", "female_62_to_64", "female_65_to_66", "female_67_to_69", "female_70_to_74", "female_75_to_79", "female_80_to_84", "female_85_and_over", "white_including_hispanic", "black_including_hispanic", "amerindian_including_hispanic", "asian_including_hispanic", "commute_5_9_mins", "commute_35_39_mins", "commute_40_44_mins", "commute_60_89_mins", "commute_90_more_mins", "households_retirement_income", "armed_forces", "civilian_labor_force", "employed_pop", "unemployed_pop", "not_in_labor_force", "pop_16_over", "pop_in_labor_force", "asian_male_45_54", "asian_male_55_64", "black_male_45_54", "black_male_55_64", "hispanic_male_45_54", "hispanic_male_55_64", "white_male_45_54", "white_male_55_64", "bachelors_degree_2", "bachelors_degree_or_higher_25_64", "children", "children_in_single_female_hh", "commuters_by_bus", "commuters_by_car_truck_van", "commuters_by_carpool", "commuters_by_subway_or_elevated", "commuters_drove_alone", "different_house_year_ago_different_city", "different_house_year_ago_same_city", "employed_agriculture_forestry_fishing_hunting_mining", "employed_arts_entertainment_recreation_accommodation_food", "employed_construction", "employed_education_health_social", "employed_finance_insurance_real_estate", "employed_information", "employed_manufacturing", "employed_other_services_not_public_admin", "employed_public_administration", "employed_retail_trade", "employed_science_management_admin_waste", "employed_transportation_warehousing_utilities", "employed_wholesale_trade", "female_female_households", "four_more_cars", "gini_index", "graduate_professional_degree", "group_quarters", "high_school_including_ged", "households_public_asst_or_food_stamps", "in_grades_1_to_4", "in_grades_5_to_8", "in_grades_9_to_12", "in_school", "in_undergrad_college", "less_than_high_school_graduate", "male_45_64_associates_degree", "male_45_64_bachelors_degree", "male_45_64_graduate_degree", "male_45_64_less_than_9_grade", "male_45_64_grade_9_12", "male_45_64_high_school", "male_45_64_some_college", "male_45_to_64", "male_male_households", "management_business_sci_arts_employed", "no_car", "no_cars", "not_us_citizen_pop", "occupation_management_arts", "occupation_natural_resources_construction_maintenance", "occupation_production_transportation_material", "occupation_sales_office", "occupation_services", "one_car", "two_cars", "three_cars", "pop_25_64", "pop_determined_poverty_status", "population_1_year_and_over", "population_3_years_over", "poverty", "sales_office_employed", "some_college_and_associates_degree", "walked_to_work", "worked_at_home", "workers_16_and_over", "associates_degree", "bachelors_degree", "high_school_diploma", "less_one_year_college", "masters_degree", "one_year_more_college", "pop_25_years_over", "commute_35_44_mins", "commute_60_more_mins", "commute_less_10_mins", "commuters_16_over", "hispanic_any_race", "do_date", "pop_5_years_over", "speak_only_english_at_home", "speak_spanish_at_home", "speak_spanish_at_home_low_english", "pop_15_and_over", "pop_never_married", "pop_now_married", "pop_separated", "pop_widowed", "pop_divorced"], "nested_column_types": ["STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64"], "description": ["Core Based Statistical Area (CBSA) Geoids", "Nonfamily Households. A householder living alone or with nonrelatives only. Unmarried couples households, whether opposite-sex or same-sex, with no relatives of the householder present are tabulated in nonfamily households.", "Family Households. A family consists of a householder and one or more other people living in the same household who are related to the householder by birth, marriage, or adoption. All people in a household who are related to the householder are regarded as members of his or her family. A family household may contain people not related to the householder, but those people are not included as part of the family of the householder in tabulations. Thus, the number of family households is equal to the number of families, but family households may include more members than do families. A household can contain only one family for purposes of tabulations. Not all households contain families since a household may be comprised of a group of unrelated people or of one person living alone -- these are called nonfamily households. Families are classified by type as either a \"married couple family\" or \"other family\" according to the sex of the householder and the presence of relatives. The data on family type are based ...", "Median Year Structure Built. Median Year Structure Built", "Housing units without rent burden computed. Units for which no rent is paid and units occupied by households that reported no income or a net loss comprise this category", "Housing units spending over 50% income on rent. Gross rent over 50 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 40 to 49.9% income on rent. Gross rent from 40.0 to 49.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 35 to 39.9% income on rent. Gross rent from 35.0 to 39.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 30 to 34.9% income on rent. Gross rent from 30.0 to 34.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 25 to 29.9% income on rent. Gross rent from 25.0 to 29.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 20 to 24.9% income on rent. Gross rent from 20.0 to 24.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 15 to 19.9% income on rent. Gross rent from 15.0 to 19.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 10 to 14.9% income on rent. Gross rent from 10.0 to 14.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending less than 10% on rent. Gross rent less than 10 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Total Population. The total number of all people living in a given geographic area.  This is a very useful catch-all denominator when calculating rates.", "Male Population. The number of people within each geography who are male.", "Female Population. The number of people within each geography who are female.", "Median Age. The median age of all people in a given geographic area.", "White Population. The number of people identifying as white, non-Hispanic in each geography.", "Black or African American Population. The number of people identifying as black or African American, non-Hispanic in each geography.", "Asian Population. The number of people identifying as Asian, non-Hispanic in each geography.", "Hispanic Population. The number of people identifying as Hispanic or Latino in each geography.", "American Indian and Alaska Native Population. The number of people identifying as American Indian or Alaska native in each geography.", "Other Race population. The number of people identifying as another race in each geography", "Two or more races population. The number of people identifying as two or more races in each geography", "Population not Hispanic. The number of people not identifying as Hispanic or Latino in each geography.", "Commuters by Public Transportation. The number of workers age 16 years and over within a geographic area who primarily traveled to work by public transportation.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Households. A count of the number of households in each geography. A household consists of one or more people who live in the same dwelling and also share at meals or living accommodation, and may consist of a single family or some other grouping of people. ", "Median Household Income in the past 12 Months. Within a geographic area, the median income received by every household on a regular basis before payments for personal income taxes, social security, union dues, medicare deductions, etc.  It includes income received from wages, salary, commissions, bonuses, and tips; self-employment income from own nonfarm or farm businesses, including proprietorships and partnerships; interest, dividends, net rental income, royalty income, or income from estates and trusts; Social Security or Railroad Retirement income; Supplemental Security Income (SSI); any cash public assistance or welfare payments from the state or local welfare office; retirement, survivor, or disability benefits; and any other sources of income received regularly such as Veterans' (VA) payments, unemployment and/or worker's compensation, child support, and alimony.", "Per Capita Income in the past 12 Months. Per capita income is the mean income computed for every man, woman, and child in a particular group. It is derived by dividing the total income of a particular group by the total population.", "Housing Units. A count of housing units in each geography.  A housing unit is a house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters.", "Vacant Housing Units. The count of vacant housing units in a geographic area. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Vacant Housing Units for Rent. The count of vacant housing units in a geographic area that are for rent. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Vacant Housing Units for Sale. The count of vacant housing units in a geographic area that are for sale. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Median Rent. The median contract rent within a geographic area. The contract rent is the monthly rent agreed to or contracted for, regardless of any furnishings, utilities, fees, meals, or services that may be included. For vacant units, it is the monthly rent asked for the rental unit at the time of interview.", "Percent of Household Income Spent on Rent. Within a geographic area, the median percentage of household income which was spent on gross rent.  Gross rent is the amount of the contract rent plus the estimated average monthly cost of utilities (electricity, gas, water, sewer etc.) and fuels (oil, coal, wood, etc.) if these are paid by the renter.  Household income is the sum of the income of all people 15 years and older living in the household.", "Owner-occupied Housing Units", "Owner-occupied Housing Units valued at $1,000,000 or more.. The count of owner occupied housing units in a geographic area that are valued at $1,000,000 or more.  Value is the respondent's estimate of how much the property (house and lot, mobile home and lot, or condominium unit) would sell for if it were for sale.", "Owner-occupied Housing Units with a Mortgage. The count of housing units within a geographic area that are mortagaged. \"Mortgage\" refers to all forms of debt where the property is pledged as security for repayment of the debt, including deeds of trust, trust deed, contracts to purchase, land contracts, junior mortgages, and home equity loans.", "Families with young children (under 6 years of age)", "Two-parent families with young children (under 6 years of age)", "Two-parent families, both parents in labor force with young children (under 6 years of age)", "Two-parent families, father only in labor force with young children (under 6 years of age)", "Two-parent families, mother only in labor force with young children (under 6 years of age)", "Two-parent families, neither parent in labor force with young children (under 6 years of age)", "One-parent families with young children (under 6 years of age)", "One-parent families, father, with young children (under 6 years of age)", "One-parent families, father in labor force, with young children (under 6 years of age)", "Number of workers with a commute between 10 and 14 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 10 and 14 minutes. ", "Number of workers with a commute between 15 and 19 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 15 and 19 minutes. ", "Number of workers with a commute between 20 and 24 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 20 and 24 minutes.", "Number of workers with a commute between 25 and 29 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 25 and 29 minutes. ", "Number of workers with a commute between 30 and 34 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 30 and 34 minutes. ", "Number of workers with a commute between 45 and 59 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 45 and 59 minutes. ", "Aggregate travel time to work. The total number of minutes every worker in a geographic area over the age of 16 who did not work from home spent spent commuting to work in one day", "Households with income less than $10,000. The number of households in a geographic area whose annual income was less than $10,000.", "Households with income of $10,000 to $14,999. The number of households in a geographic area whose annual income was between $10,000 and $14,999.", "Households with income of $15,000 to $19,999. The number of households in a geographic area whose annual income was between $15,000 and $19,999.", "Households with income of $20,000 to $24,999. The number of households in a geographic area whose annual income was between $20,000 and $24,999.", "Households with income of $25,000 to $29,999. The number of households in a geographic area whose annual income was between $20,000 and $24,999.", "Households with income of $30,000 to $34,999. The number of households in a geographic area whose annual income was between $30,000 and $34,999.", "Households with income of $35,000 to $39,999. The number of households in a geographic area whose annual income was between $35,000 and $39,999.", "Households with income of $40,000 to $44,999. The number of households in a geographic area whose annual income was between $40,000 and $44,999.", "Households with income of $45,000 to $49,999. The number of households in a geographic area whose annual income was between $45,000 and $49,999.", "Households with income of $50,000 to $59,999. The number of households in a geographic area whose annual income was between $50,000 and $59,999.", "Households with income of $60,000 to $74,999. The number of households in a geographic area whose annual income was between $60,000 and $74,999.", "Households with income of $75,000 to $99,999. The number of households in a geographic area whose annual income was between $75,000 and $99,999.", "Households with income of $100,000 to $124,999. The number of households in a geographic area whose annual income was between $100,000 and $124,999.", "Households with income of $125,000 to $149,999. The number of households in a geographic area whose annual income was between $125,000 and $149,999.", "Households with income of $150,000 to $199,999. The number of households in a geographic area whose annual income was between $150,000 and $1999,999.", "Households with income of $200,000 Or More. The number of households in a geographic area whose annual income was more than $200,000.", "Renter-Occupied Housing Units Paying Cash Rent Median Gross Rent", "Owner-Occupied Housing Units Lower Value Quartile", "Owner-Occupied Housing Units Median Value. The middle value (median) in a geographic area owner occupied housing units.", "Owner-Occupied Housing Units Upper Value Quartile", "Married households. People in formal marriages, as well as people in common-law marriages, are included. Does not include same-sex marriages.", "Occupied housing units. A housing unit is classified as occupied if it is the usual place of residence of the person or group of people living in it at the time of enumeration.", "Renter occupied housing units. All occupied units which are not owner occupied, whether they are rented for cash rent or occupied without payment of cash rent, are classified as renter-occupied.", "Single-family (one unit) detached dwellings. This is a 1-unit structure detached from any other house, that is, with open space on all four sides. Such structures are considered detached even if they have an adjoining shed or garage. A one-family house that contains a business is considered detached as long as the building has open space on all four sides. Mobile homes to which one or more permanent rooms have been added or built also are included.", "Single-family (one unit) attached dwellings. This is a 1-unit structure that has one or more walls extending from ground to roof separating it from adjoining structures. In row houses (sometimes called townhouses), double houses, or houses attached to nonresidential structures, each house is a separate, attached structure if the dividing or common wall goes from ground to roof.", "Two-family (two unit) dwellings", "Multifamily dwellings with three to 4 units", "Apartment buildings with 5 to 9 units", "Apartment buildings with 10 to 19 units", "Apartment buildings with 20 to 49 units", "Apartment buildings with 50 or more units", "Mobile homes. A manufactured home is defined as a movable dwelling, 8 feet or more wide and 40 feet or more long, designed to be towed on its own chassis, with transportation gear integral to the unit when it leaves the factory, and without need of a permanent foundation. These homes are built in accordance with the U.S. Department of Housing and Urban Development (HUD) building code.", "Housing units built in 2005 or later. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built in 2005 or later.", "Housing units built between 2000 and 2004. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built from 2000 to 2004.", "Housing units built before 1939. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built in 1939 or earlier.", "Male under 5 years. The male population over the age of five years within the specified area.", "Male age 5 to 9. The male population between the age of five years to nine years within the specified area.", "Male age 10 to 14. The male population between the age of ten years to fourteen years within the specified area.", "Male age 15 to 17. The male population between the age of fifteeen years to seventeen years within the specified area.", "Male age 18 and 19. The male population between the age of eighteen years to nineteen years within the specified area.", "Male age 20. The male population with an age of twenty years within the specified area.", "Male age 21. The male population with an age of twenty-one years within the specified area.", "Male age 22 to 24. The male population between the age of twenty-two years to twenty-four years within the specified area.", "Male age 25 to 29. The male population between the age of twenty-five years to twenty-nine years within the specified area.", "Male age 30 to 34. The male population between the age of thirty years to thirty-four years within the specified area.", "Male age 35 to 39. The male population between the age of thirty-five years to thirty-nine years within the specified area.", "Male age 40 to 44. The male population between the age of fourty years to fourty-four years within the specified area.", "Men age 45 to 49. The male population between the age of fourty-five years to fourty-nine years within the specified area.", "Men age 50 to 54. The male population between the age of fifty years to fifty-four years within the specified area.", "Men age 55 to 59. The male population between the age of fifty-five years to fifty-nine years within the specified area.", "Men age 60 to 61. The male population between the age of sixty years to sixty-one years within the specified area.", "Men age 62 to 64. The male population between the age of sixty-two years to sixty-four years within the specified area.", "Male age 65 to 66. The male population between the age of sixty-five years to sixty-six years within the specified area.", "Male age 67 to 69. The male population between the age of sixty-seven years to sixty-nine years within the specified area.", "Male age 70 to 74. The male population between the age of seventy years to seventy-four years within the specified area.", "Male age 75 to 79. The male population between the age of seventy-five years to seventy-nine years within the specified area.", "Male age 80 to 84. The male population between the age of eighty years to eighty-four years within the specified area.", "Male age 85 and over. The male population of the age of eighty-five years and over within the specified area.", "Female under 5 years. The female population over the age of five years within the specified area.", "Female age 5 to 9. The female population between the age of five years to nine years within the specified area.", "Female age 10 to 14. The female population between the age of ten years to fourteen years within the specified area.", "Female age 15 to 17. The female population between the age of fifteeen years to seventeen years within the specified area.", "Female age 18 and 19. The female population between the age of eighteen years to nineteen years within the specified area.", "Female age 20. The female population with an age of twenty years within the specified area.", "Female age 21. The female population with an age of twenty-one years within the specified area.", "Female age 22 to 24. The female population between the age of twenty-two years to twenty-four years within the specified area.", "Female age 25 to 29. The female population between the age of twenty-five years to twenty-nine years within the specified area.", "Female age 30 to 34. The female population between the age of thirty years to thirty-four years within the specified area.", "Female age 35 to 39. The female population between the age of thirty-five years to thirty-nine years within the specified area.", "Female age 40 to 44. The female population between the age of fourty years to fourty-four years within the specified area.", "Female age 45 to 49. The female population between the age of fourty-five years to fourty-nine years within the specified area.", "Female age 50 to 54. The female population between the age of fifty years to fifty-four years within the specified area.", "Female age 55 to 59. The female population between the age of fifty-five years to fifty-nine years within the specified area.", "Female age 60 and 61. The female population between the age of sixty years to sixty-one years within the specified area.", "Female age 62 to 64. The female population between the age of sixty-two years to sixty-four years within the specified area.", "Female age 65 to 66. The female population between the age of sixty-five years to sixty-six years within the specified area.", "Female age 67 to 69. The female population between the age of sixty-seven years to sixty-nine years within the specified area.", "Female age 70 to 74. The female population between the age of seventy years to seventy-four years within the specified area.", "Female age 75 to 79. The female population between the age of seventy-five years to seventy-nine years within the specified area.", "Female age 80 to 84. The female population between the age of eighty years to eighty-four years within the specified area.", "Female age 85 and over. The female population of the age of eighty-five years and over within the specified area.", "White including Hispanic", "Black including Hispanic", "American Indian including Hispanic", "Asian including Hispanic", "Number of workers with a commute between 5 and 9 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 5 and 9 minutes.", "Number of workers with a commute between 35 and 39 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 35 and 39 minutes. ", "Number of workers with a commute between 40 and 44 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 40 and 44 minutes. ", "Number of workers with a commute between 60 and 89 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 60 and 89 minutes .", "Number of workers with a commute of more than 90 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute more than 90 minutes.", "Households receiving retirement income", "Population in Armed Forces. The number of people in each geography who are members of the U.S. Armed Forces (people on active duty with the United States Army, Air Force, Navy, Marine Corps, or Coast Guard).", "Population in Civilian Labor Force. The number of civilians 16 years and over in each geography who can be classified as either \"employed\" or \"unemployed\" below.", "Employed Population. The number of civilians 16 years old and over in each geography who either (1) were \"at work,\" that is, those who did any work at all during the reference week as paid employees, worked in their own business or profession, worked on their own farm, or worked 15 hours or more as unpaid workers on a family farm or in a family business; or (2) were \"with a job but not at work,\" that is, those who did not work during the reference week but had jobs or businesses from which they were temporarily absent due to illness, bad weather, industrial dispute, vacation, or other personal reasons. Excluded from the employed are people whose only activity consisted of work around the house or unpaid volunteer work for religious, charitable, and similar organizations; also excluded are all institutionalized people and people on active duty in the United States Armed Forces.", "", "Population Not in Labor Force. The number of people in each geography who are 16 years old and over who are not classified as members of the labor force. This category consists mainly of students, homemakers, retired workers, seasonal workers interviewed in an off season who were not looking for work, institutionalized people, and people doing only incidental unpaid family work (less than 15 hours during the reference week).", "Population age 16 and over. The number of people in each geography who are age 16 or over.", "Population in Labor Force. The number of people in each geography who are either in the civilian labor force or are members of the U.S. Armed Forces (people on active duty with the United States Army, Air Force, Navy, Marine Corps, or Coast Guard).", "Asian Men age 45 to 54", "Asian Men age 55 to 64", "Black Men age 45 to 54", "Black Men age 55 to 64", "Hispanic Men age 45 to 54", "Hispanic Men age 55 to 64", "White Men age 45 to 54", "White Men age 55 to 64", "Population who completed a bachelor's degree. From mobility table.", "Population with Bachelors Degree or Higher, Ages 25 to 64. The number of people in each geography who are between the ages of 25 and 64 who have attained a bachelors degree or higher. ", "Children under 18 Years of Age. The number of people within each geography who are under 18 years of age.", "Children under 18 years of age in single female-led household", "Commuters by Bus. The number of workers age 16 years and over within a geographic area who primarily traveled to work by bus.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.  This is a subset of workers who commuted by public transport.", "Commuters by Car, Truck, or Van. The number of workers age 16 years and over within  a geographic area who primarily traveled to work by car, truck or  van.  This is the principal mode of travel or type of conveyance,  by distance rather than time, that the worker usually used to get  from home to work.", "Commuters by Carpool. The number of workers age 16 years and over within a geographic area who primarily traveled to work by carpool.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Commuters by Subway or Elevated. The number of workers age 16 years and over within a geographic area who primarily traveled to work by subway or elevated train.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.  This is a subset of workers who commuted by public transport.", "Commuters who drove alone. The number of workers age 16 years and over within a geographic area who primarily traveled by car driving alone. This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Lived in a different house one year ago in a different city. All people in a geographic area who lived in a different city within the year prior to the survey.", "Lived in a different house one year ago in the same city. All people in a geographic area who lived in the same city but moved to a different unit within the year prior to the survey.", "Workers employed in firms in agriculture, forestry, fishing, hunting, or mining. The Agriculture, Forestry, Fishing and Hunting sector comprises establishments primarily engaged in growing crops, raising animals, harvesting timber, and harvesting fish and other animals from a farm, ranch, or their natural habitats.", "Workers employed in firms in arts, entertainment, recreation, accommodation and food services. The Arts, Entertainment, and Recreation sector includes a wide range of establishments that operate facilities or provide services to meet varied cultural, entertainment, and recreational interests of their patrons. This sector comprises (1) establishments that are involved in producing, promoting, or participating in live performances, events, or exhibits intended for public viewing; (2) establishments that preserve and exhibit objects and sites of historical, cultural, or educational interest; and (3) establishments that operate facilities or provide services that enable patrons to participate in recreational activities or pursue amusement, hobby, and leisure-time interests.", "Workers employed in firms in construction. The Construction sector comprises establishments primarily engaged in the construction of buildings or engineering projects (e.g., highways and utility systems). Construction work done may include new work, additions, alterations, or maintenance and repairs.", "Workers employed in firms in educational services, health care, and social assistance. Outpatient health services, other than hospital care, including: public health administration; research and education; categorical health programs; treatment and immunization clinics; nursing; environmental health activities such as air and water pollution control; ambulance service if provided separately from fire protection services, and other general public health activities such as mosquito abatement. School health services provided by health agencies (rather than school agencies) are included here. Sewage treatment operations are classified under Sewerage.", "Workers employed in firms in finance, insurance, real estate and rental and leasing. The Real Estate and Rental and Leasing sector comprises establishments primarily engaged in renting, leasing, or otherwise allowing the use of tangible or intangible assets, and establishments providing related services. The major portion of this sector comprises establishments that rent, lease, or otherwise allow the use of their own assets by others. The assets may be tangible, as is the case of real estate and equipment, or intangible, as is the case with patents and trademarks.", "Workers employed in firms in information. The Information sector comprises establishments engaged in the following processes: (a) producing and distributing information and cultural products, (b) providing the means to transmit or distribute these products as well as data or communications, and (c) processing data. Included are the publishing industries, the motion picture and sound recording industries; the broadcasting industries, the telecommunications industries; Web search portals, data processing industries, and the information services industries.", "Workers employed in firms in manufacturing. The Manufacturing sector comprises establishments engaged in the mechanical, physical, or chemical transformation of materials, substances, or components into new products.", "Workers employed in firms in other services except public administration. The Other Services (Except Public Administration) sector comprises establishments engaged in providing services not specifically provided for elsewhere in the classification system. Establishments in this sector are primarily engaged in activities such as equipment and machinery repairing, promoting or administering religious activities, grantmaking, advocacy, and providing drycleaning and laundry services, personal care services, death care services, pet care services, photofinishing services, temporary parking services, and dating services. Private households that engage in employing workers on or about the premises in activities primarily concerned with the operation of the household are included in this sector.", "Workers employed in firms in public administration. The Public Administration sector consists of establishments of federal, state, and local government agencies that administer, oversee, and manage public programs and have executive, legislative, or judicial authority over other institutions within a given area. These agencies also set policy, create laws, adjudicate civil and criminal legal cases, provide for public safety and for national defense. In general, government establishments in the public administration sector oversee governmental programs and activities that are not performed by private establishments.", "Workers employed in firms in retail trade. The Retail Trade sector comprises establishments engaged in retailing merchandise, generally without transformation, and rendering services incidental to the sale of merchandise. The retailing process is the final step in the distribution of merchandise; retailers are, therefore, organized to sell merchandise in small quantities to the general public.", "Workers employed in firms in professional scientific, management, administrative and waste management services. The Administrative and Support and Waste Management and Remediation Services sector comprises establishments performing routine support activities for the day-to-day operations of other organizations. The establishments in this sector specialize in one or more of these support activities and provide these services to clients in a variety of industries and, in some cases, to households. Activities performed include office administration, hiring and placing of personnel, document preparation and similar clerical services, solicitation, collection, security and surveillance services, cleaning, and waste disposal services.", "Workers employed in firms in transportation, warehousing, and utilities. The Transportation and Warehousing sector includes industries providing transportation of passengers and cargo, warehousing and storage for goods, scenic and sightseeing transportation, and support activities related to modes of transportation. The modes of transportation are air, rail, water, road, and pipeline.", "Workers employed in firms in wholesale trade. The Wholesale Trade sector comprises establishments engaged in wholesaling merchandise, generally without transformation, and rendering services incidental to the sale of merchandise. The wholesaling process is an intermediate step in the distribution of merchandise. Wholesalers are organized to sell or arrange the purchase or sale of (a) goods for resale (i.e., goods sold to other wholesalers or retailers), (b) capital or durable nonconsumer goods, and (c) raw and intermediate materials and supplies used in production.", "Households with two female partners. An unmarried partner is a person age 15 years and over, who is not related to the householder, who shares living quarters, and who has an intimate relationship with the householder.", "Four car households. The number of households with four or more passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Gini Index. The Gini index, or index of income concentration, is a statistical measure of income inequality ranging from 0 to 1. A measure of 1 indicates perfect inequality, i.e., one household having all the income and rest having none. A measure of 0 indicates perfect equality, i.e., all households having an equal share of income.", "Population who completed a graduate or professional degree", "Population living in group quarters", "Population with high school degree, including GED. The number of people in a geographic area over the age of 25 who attained a high school degree or GED.", "Households on cash public assistance or receiving food stamps (SNAP)", "Students Enrolled in Grades 1 to 4. The total number of people in each geography currently enrolled in grades 1 through 4 inclusive.  This corresponds roughly to elementary school.", "Students Enrolled in Grades 5 to 8. The total number of people in each geography currently enrolled in grades 5 through 8 inclusive.  This corresponds roughly to middle school.", "Students Enrolled in Grades 9 to 12. The total number of people in each geography currently enrolled in grades 9 through 12 inclusive.  This corresponds roughly to high school.", "Students Enrolled in School. The total number of people in each geography currently enrolled at any level of school, from nursery or pre-school to advanced post-graduate education.  Only includes those over the age of 3.", "Students Enrolled as Undergraduate in College. The number of people in a geographic area who are enrolled in college at the undergraduate level. Enrollment refers to being registered or listed as a student in an educational program leading to a college degree. This may be a public school or college, a private school or college.", "Less than high school graduate. The number of people in a geographic area over the age of 25 who have not completed high school or any other advanced degree.", "Men age 45 to 64 who obtained an associate's degree", "Men age 45 to 64 who obtained a bachelor's degree", "Men age 45 to 64 who obtained a graduate or professional degree", "Men age 45 to 64 who attained less than a 9th grade education", "Men age 45 to 64 who attained between 9th and 12th grade, no diploma", "Men age 45 to 64 who completed high school or obtained GED", "Men age 45 to 64 who completed some college, no degree", "Men age 45 to 64 (\"middle aged\"). The male population between the age of fourty-five years to sixty-four years within the specified area.", "Households with two male partners. An unmarried partner is a person age 15 years and over, who is not related to the householder, who shares living quarters, and who has an intimate relationship with the householder.", "Civilian Employed Population in Management, Business, Science, and Arts Occupations. The number of employed civilians 16 years old and over in each geography in the Management, Business, Science, and Arts occupations. Occupation codes are based on the Standard Occupational Classification (SOC), published by the Executive Office of the President, Office of Management and Budget.", "Workers age 16 and over with no vehicle. All people in a geographic area over the age of 16 who do not own a car.", "Car-free households. The number of households without passenger cars, vans, and pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Not a U.S. Citizen Population. The number of people within each geography who indicated that they are not U.S. citizens.", "Workers employed in management business science and arts occupations", "Workers employed in natural resources, construction, and maintenance occupations", "Workers employed in production, transportation, and material moving", "Workers employed in sales and office occupations", "Workers employed in service occupations", "One car households. The number of households with one passenger car, van, , pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Two car households. The number of households with two passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Three car households. The number of households with one passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Population age 25 to 64. The number of people in each geography who are between the ages of 25 and 64. ", "Population for Whom Poverty Status Determined. The number of people in each geography who could be identified as either living in poverty or not.  This should be used as the denominator when calculating poverty rates, as it excludes people for whom it was not possible to determine poverty.", "Population 1 year and over. All people, male and female, child and adult, living in a given geographic area that are 1 year and older.", "Population 3 Years and Over. The total number of people in each geography age 3 years and over.  This denominator is mostly used to calculate rates of school enrollment.", "Income In The Past 12 Months Below Poverty Level. The number of people in a geographic area who are part of a family (which could be just them as an individual) determined to be \"in poverty\" following the Office of Management and Budget's Directive 14. (https://www.census.gov/hhes/povmeas/methodology/ombdir14.html)", "Civilian Employed Population in Sales and Office Occupations. The number of employed civilians 16 years old and over in each geography in the Sales and Office occupations. Occupation codes are based on the Standard Occupational Classification (SOC), published by the Executive Office of the President, Office of Management and Budget.", "Population who completed some college or obtained associate's degree. The number of people in a geographic area over the age of 25 who obtained an associate's degree, and did not complete a more advanced degree.", "Walked to Work. The number of workers age 16 years and over within a geographic area who primarily walked to work.  This would mean that of any way of getting to work, they travelled the most distance walking.", "Worked at Home. The count within a geographical area of workers over the age of 16 who worked at home.", "Workers over the Age of 16. The number of people in each geography who work. Workers include those employed at private for-profit companies, the self-employed, government workers and non-profit employees.", "Population Completed Associate's Degree. The number of people in a geographic area over the age of 25 who obtained a associate's degree, and did not complete a more advanced degree.", "Population Completed Bachelor's Degree. The number of people in a geographic area over the age of 25 who obtained a bachelor's degree, and did not complete a more advanced degree.", "Population Completed High School. The number of people in a geographic area over the age of 25 who completed high school, and did not complete a more advanced degree.", "Population completed less than one year of college, no degree. The number of people in a geographic area over the age of 25 who attended college for less than one year and no further.", "Population Completed Master's Degree. The number of people in a geographic area over the age of 25 who obtained a master's degree, but did not complete a more advanced degree.", "Population completed more than one year of college, no degree. The number of people in a geographic area over the age of 25 who attended college for more than one year but did not obtain a degree", "Population 25 Years and Over. The number of people in a geographic area who are over the age of 25.  This is used mostly as a denominator of educational attainment.", "Number of workers with a commute between 35 and 44 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 35 and 44 minutes. ", "Number of workers with a commute of over 60 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in over 60 minutes.", "Number of workers with less than 10 minute commute. The number of workers in a geographic area over the age of 16 who do not work from home and commute in less than 10 minutes.", "Workers age 16 and over who do not work from home. The number of workers in a geographic area over the age of 16 who do not work from home", "Hispanic of any race", null, "Population 5 Years and Over. The number of people in a geographic area who are over the age of 5.  This is primarily used as a denominator of measures of language spoken at home.", "Speaks only English at Home. The number of people in a geographic area over age 5 who speak only English at home.", "Speaks Spanish at Home. The number of people in a geographic area over age 5 who speak Spanish at home, possibly in addition to other languages.", "Speaks Spanish at Home, speaks English less than \"very well\"", "Population 15 Years and Over. The number of people in a geographic area who are over the age of 15.  This is used mostly as a denominator of marital status.", "Never Married. The number of people in a geographic area who have never been married.", "Currently married. The number of people in a geographic area who are currently married", "Married but separated. The number of people in a geographic area who are married but separated", "Widowed. The number of people in a geographic area who are widowed", "Divorced. The number of people in a geographic area who are divorced"], "sample_rows": [{"geo_id": "20700", "nonfamily_households": 16478.0, "family_households": 40649.0, "median_year_structure_built": 1984.0, "rent_burden_not_computed": 1063.0, "rent_over_50_percent": 3568.0, "rent_40_to_50_percent": 893.0, "rent_35_to_40_percent": 692.0, "rent_30_to_35_percent": 1246.0, "rent_25_to_30_percent": 1464.0, "rent_20_to_25_percent": 1513.0, "rent_15_to_20_percent": 1142.0, "rent_10_to_15_percent": 912.0, "rent_under_10_percent": 410.0, "total_pop": 167126.0, "male_pop": 82532.0, "female_pop": 84594.0, "median_age": 42.0, "white_pop": 113840.0, "black_pop": 21363.0, "asian_pop": 3452.0, "hispanic_pop": 24474.0, "amerindian_pop": 420.0, "other_race_pop": 316.0, "two_or_more_races_pop": 3243.0, "not_hispanic_pop": 142652.0, "commuters_by_public_transportation": 3368.0, "households": 57127.0, "median_income": 58980.0, "income_per_capita": 26128.0, "housing_units": 80675.0, "vacant_housing_units": 23548.0, "vacant_housing_units_for_rent": 1158.0, "vacant_housing_units_for_sale": 1422.0, "median_rent": 839.0, "percent_income_spent_on_rent": 31.9, "owner_occupied_housing_units": 44224.0, "million_dollar_housing_units": 180.0, "mortgaged_housing_units": 30540.0, "families_with_young_children": 8505.0, "two_parent_families_with_young_children": 5041.0, "two_parents_in_labor_force_families_with_young_children": 2836.0, "two_parents_father_in_labor_force_families_with_young_children": 1853.0, "two_parents_mother_in_labor_force_families_with_young_children": 245.0, "two_parents_not_in_labor_force_families_with_young_children": 107.0, "one_parent_families_with_young_children": 3464.0, "father_one_parent_families_with_young_children": 675.0, "father_in_labor_force_one_parent_families_with_young_children": 621.0, "commute_10_14_mins": 8491.0, "commute_15_19_mins": 7892.0, "commute_20_24_mins": 8861.0, "commute_25_29_mins": 3187.0, "commute_30_34_mins": 7373.0, "commute_45_59_mins": 6166.0, "aggregate_travel_time_to_work": 2745610.0, "income_less_10000": 2828.0, "income_10000_14999": 2448.0, "income_15000_19999": 2643.0, "income_20000_24999": 2517.0, "income_25000_29999": 2985.0, "income_30000_34999": 2680.0, "income_35000_39999": 3084.0, "income_40000_44999": 2722.0, "income_45000_49999": 2183.0, "income_50000_59999": 4929.0, "income_60000_74999": 6654.0, "income_75000_99999": 7560.0, "income_100000_124999": 5974.0, "income_125000_149999": 2982.0, "income_150000_199999": 2891.0, "income_200000_or_more": 2047.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 1040.0, "owner_occupied_housing_units_lower_value_quartile": 117500.0, "owner_occupied_housing_units_median_value": 170600.0, "owner_occupied_housing_units_upper_value_quartile": 238400.0, "married_households": 31101.0, "occupied_housing_units": 57127.0, "housing_units_renter_occupied": 12903.0, "dwellings_1_units_detached": 66295.0, "dwellings_1_units_attached": 5477.0, "dwellings_2_units": 1229.0, "dwellings_3_to_4_units": 1750.0, "dwellings_5_to_9_units": 1499.0, "dwellings_10_to_19_units": 557.0, "dwellings_20_to_49_units": 601.0, "dwellings_50_or_more_units": 356.0, "mobile_homes": 2886.0, "housing_built_2005_or_later": 103.0, "housing_built_2000_to_2004": 647.0, "housing_built_1939_or_earlier": 2450.0, "male_under_5": 3810.0, "male_5_to_9": 4994.0, "male_10_to_14": 5346.0, "male_15_to_17": 3946.0, "male_18_to_19": 2990.0, "male_20": 1393.0, "male_21": 1156.0, "male_22_to_24": 3890.0, "male_25_to_29": 4958.0, "male_30_to_34": 4084.0, "male_35_to_39": 4174.0, "male_40_to_44": 4611.0, "male_45_to_49": 6241.0, "male_50_to_54": 7133.0, "male_55_to_59": 6832.0, "male_60_61": 2431.0, "male_62_64": 3012.0, "male_65_to_66": 1992.0, "male_67_to_69": 2385.0, "male_70_to_74": 3165.0, "male_75_to_79": 1707.0, "male_80_to_84": 1300.0, "male_85_and_over": 982.0, "female_under_5": 3777.0, "female_5_to_9": 4507.0, "female_10_to_14": 5343.0, "female_15_to_17": 3670.0, "female_18_to_19": 2938.0, "female_20": 1664.0, "female_21": 1504.0, "female_22_to_24": 2960.0, "female_25_to_29": 4254.0, "female_30_to_34": 4050.0, "female_35_to_39": 4826.0, "female_40_to_44": 5237.0, "female_45_to_49": 6632.0, "female_50_to_54": 7293.0, "female_55_to_59": 6992.0, "female_60_to_61": 2205.0, "female_62_to_64": 3290.0, "female_65_to_66": 1896.0, "female_67_to_69": 2446.0, "female_70_to_74": 3366.0, "female_75_to_79": 2019.0, "female_80_to_84": 1743.0, "female_85_and_over": 1982.0, "white_including_hispanic": 129349.0, "black_including_hispanic": 23320.0, "amerindian_including_hispanic": 539.0, "asian_including_hispanic": 3563.0, "commute_5_9_mins": 6347.0, "commute_35_39_mins": 1801.0, "commute_40_44_mins": 2144.0, "commute_60_89_mins": 7181.0, "commute_90_more_mins": 9506.0, "households_retirement_income": 13158.0, "armed_forces": 114.0, "civilian_labor_force": 86114.0, "employed_pop": 77253.0, "unemployed_pop": 8861.0, "not_in_labor_force": 50945.0, "pop_16_over": 137173.0, "pop_in_labor_force": 86228.0, "asian_male_45_54": 196.0, "asian_male_55_64": 211.0, "black_male_45_54": 2080.0, "black_male_55_64": 1379.0, "hispanic_male_45_54": 1727.0, "hispanic_male_55_64": 1028.0, "white_male_45_54": 9375.0, "white_male_55_64": 9510.0, "bachelors_degree_2": 17027.0, "bachelors_degree_or_higher_25_64": 21542.0, "children": 35393.0, "children_in_single_female_hh": 8309.0, "commuters_by_bus": 3009.0, "commuters_by_car_truck_van": 65587.0, "commuters_by_carpool": 7129.0, "commuters_by_subway_or_elevated": 188.0, "commuters_drove_alone": 58458.0, "different_house_year_ago_different_city": 14956.0, "different_house_year_ago_same_city": 1849.0, "employed_agriculture_forestry_fishing_hunting_mining": 762.0, "employed_arts_entertainment_recreation_accommodation_food": 10531.0, "employed_construction": 4115.0, "employed_education_health_social": 18090.0, "employed_finance_insurance_real_estate": 4190.0, "employed_information": 1275.0, "employed_manufacturing": 6835.0, "employed_other_services_not_public_admin": 3530.0, "employed_public_administration": 3319.0, "employed_retail_trade": 10367.0, "employed_science_management_admin_waste": 7361.0, "employed_transportation_warehousing_utilities": 5066.0, "employed_wholesale_trade": 1812.0, "female_female_households": 131.0, "four_more_cars": 4765.0, "gini_index": 0.4076, "graduate_professional_degree": 9645.0, "group_quarters": 2341.0, "high_school_including_ged": 41302.0, "households_public_asst_or_food_stamps": 7399.0, "in_grades_1_to_4": 8253.0, "in_grades_5_to_8": 8532.0, "in_grades_9_to_12": 10264.0, "in_school": 42125.0, "in_undergrad_college": 10657.0, "less_than_high_school_graduate": 11833.0, "male_45_64_associates_degree": 1701.0, "male_45_64_bachelors_degree": 3547.0, "male_45_64_graduate_degree": 1939.0, "male_45_64_less_than_9_grade": 614.0, "male_45_64_grade_9_12": 1875.0, "male_45_64_high_school": 10453.0, "male_45_64_some_college": 5520.0, "male_45_to_64": 25649.0, "male_male_households": 36.0, "management_business_sci_arts_employed": 25526.0, "no_car": 1431.0, "no_cars": 3114.0, "not_us_citizen_pop": 5614.0, "occupation_management_arts": 25526.0, "occupation_natural_resources_construction_maintenance": 6684.0, "occupation_production_transportation_material": 10150.0, "occupation_sales_office": 18274.0, "occupation_services": 16619.0, "one_car": 17482.0, "two_cars": 22813.0, "three_cars": 8953.0, "pop_25_64": 88255.0, "pop_determined_poverty_status": 164605.0, "population_1_year_and_over": 165877.0, "population_3_years_over": 163268.0, "poverty": 20071.0, "sales_office_employed": 18274.0, "some_college_and_associates_degree": 33431.0, "walked_to_work": 1614.0, "worked_at_home": 3887.0, "workers_16_and_over": 75073.0, "associates_degree": 8973.0, "bachelors_degree": 17027.0, "high_school_diploma": 36473.0, "less_one_year_college": 7394.0, "masters_degree": 7661.0, "one_year_more_college": 17064.0, "pop_25_years_over": 113238.0, "commute_35_44_mins": 3945.0, "commute_60_more_mins": 16687.0, "commute_less_10_mins": 8584.0, "commuters_16_over": 71186.0, "hispanic_any_race": 24474.0, "do_date": "20122016", "pop_5_years_over": NaN, "speak_only_english_at_home": NaN, "speak_spanish_at_home": NaN, "speak_spanish_at_home_low_english": NaN, "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN}, {"geo_id": "44180", "nonfamily_households": 65910.0, "family_households": 114460.0, "median_year_structure_built": 1984.0, "rent_burden_not_computed": 4810.0, "rent_over_50_percent": 15309.0, "rent_40_to_50_percent": 5884.0, "rent_35_to_40_percent": 3881.0, "rent_30_to_35_percent": 6330.0, "rent_25_to_30_percent": 6851.0, "rent_20_to_25_percent": 8295.0, "rent_15_to_20_percent": 8109.0, "rent_10_to_15_percent": 5619.0, "rent_under_10_percent": 1930.0, "total_pop": 452198.0, "male_pop": 221362.0, "female_pop": 230836.0, "median_age": 36.6, "white_pop": 409512.0, "black_pop": 10371.0, "asian_pop": 5881.0, "hispanic_pop": 13651.0, "amerindian_pop": 2190.0, "other_race_pop": 360.0, "two_or_more_races_pop": 9809.0, "not_hispanic_pop": 438547.0, "commuters_by_public_transportation": 967.0, "households": 180370.0, "median_income": 43973.0, "income_per_capita": 23810.0, "housing_units": 197399.0, "vacant_housing_units": 17029.0, "vacant_housing_units_for_rent": 4768.0, "vacant_housing_units_for_sale": 2580.0, "median_rent": 549.0, "percent_income_spent_on_rent": 30.2, "owner_occupied_housing_units": 113352.0, "million_dollar_housing_units": 332.0, "mortgaged_housing_units": 72569.0, "families_with_young_children": 32130.0, "two_parent_families_with_young_children": 22010.0, "two_parents_in_labor_force_families_with_young_children": 12019.0, "two_parents_father_in_labor_force_families_with_young_children": 8747.0, "two_parents_mother_in_labor_force_families_with_young_children": 857.0, "two_parents_not_in_labor_force_families_with_young_children": 387.0, "one_parent_families_with_young_children": 10120.0, "father_one_parent_families_with_young_children": 2368.0, "father_in_labor_force_one_parent_families_with_young_children": 2225.0, "commute_10_14_mins": 30884.0, "commute_15_19_mins": 38792.0, "commute_20_24_mins": 35098.0, "commute_25_29_mins": 13230.0, "commute_30_34_mins": 25790.0, "commute_45_59_mins": 9593.0, "aggregate_travel_time_to_work": 4296880.0, "income_less_10000": 14097.0, "income_10000_14999": 10961.0, "income_15000_19999": 10667.0, "income_20000_24999": 12071.0, "income_25000_29999": 11579.0, "income_30000_34999": 11672.0, "income_35000_39999": 11026.0, "income_40000_44999": 10085.0, "income_45000_49999": 7944.0, "income_50000_59999": 16080.0, "income_60000_74999": 18807.0, "income_75000_99999": 20150.0, "income_100000_124999": 11145.0, "income_125000_149999": 5364.0, "income_150000_199999": 4641.0, "income_200000_or_more": 4081.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 702.0, "owner_occupied_housing_units_lower_value_quartile": 89600.0, "owner_occupied_housing_units_median_value": 133700.0, "owner_occupied_housing_units_upper_value_quartile": 198800.0, "married_households": 88524.0, "occupied_housing_units": 180370.0, "housing_units_renter_occupied": 67018.0, "dwellings_1_units_detached": 144819.0, "dwellings_1_units_attached": 5484.0, "dwellings_2_units": 5661.0, "dwellings_3_to_4_units": 5698.0, "dwellings_5_to_9_units": 5681.0, "dwellings_10_to_19_units": 8362.0, "dwellings_20_to_49_units": 6887.0, "dwellings_50_or_more_units": 3967.0, "mobile_homes": 10752.0, "housing_built_2005_or_later": 565.0, "housing_built_2000_to_2004": 4913.0, "housing_built_1939_or_earlier": 7419.0, "male_under_5": 14473.0, "male_5_to_9": 15253.0, "male_10_to_14": 14298.0, "male_15_to_17": 8747.0, "male_18_to_19": 6755.0, "male_20": 3955.0, "male_21": 4451.0, "male_22_to_24": 11343.0, "male_25_to_29": 15018.0, "male_30_to_34": 15192.0, "male_35_to_39": 13291.0, "male_40_to_44": 14178.0, "male_45_to_49": 13649.0, "male_50_to_54": 14613.0, "male_55_to_59": 13557.0, "male_60_61": 5112.0, "male_62_64": 7375.0, "male_65_to_66": 4712.0, "male_67_to_69": 5836.0, "male_70_to_74": 7428.0, "male_75_to_79": 5053.0, "male_80_to_84": 3891.0, "male_85_and_over": 3182.0, "female_under_5": 13811.0, "female_5_to_9": 14095.0, "female_10_to_14": 13866.0, "female_15_to_17": 8307.0, "female_18_to_19": 6917.0, "female_20": 4594.0, "female_21": 4797.0, "female_22_to_24": 11497.0, "female_25_to_29": 15236.0, "female_30_to_34": 14656.0, "female_35_to_39": 13107.0, "female_40_to_44": 13667.0, "female_45_to_49": 13668.0, "female_50_to_54": 15103.0, "female_55_to_59": 14930.0, "female_60_to_61": 5066.0, "female_62_to_64": 8500.0, "female_65_to_66": 5278.0, "female_67_to_69": 6777.0, "female_70_to_74": 8558.0, "female_75_to_79": 7235.0, "female_80_to_84": 5099.0, "female_85_and_over": 6072.0, "white_including_hispanic": 418450.0, "black_including_hispanic": 10531.0, "amerindian_including_hispanic": 2610.0, "asian_including_hispanic": 5966.0, "commute_5_9_mins": 20293.0, "commute_35_39_mins": 4405.0, "commute_40_44_mins": 4818.0, "commute_60_89_mins": 3884.0, "commute_90_more_mins": 3058.0, "households_retirement_income": 33524.0, "armed_forces": 245.0, "civilian_labor_force": 222757.0, "employed_pop": 208867.0, "unemployed_pop": 13890.0, "not_in_labor_force": 137355.0, "pop_16_over": 360357.0, "pop_in_labor_force": 223002.0, "asian_male_45_54": 295.0, "asian_male_55_64": 160.0, "black_male_45_54": 659.0, "black_male_55_64": 453.0, "hispanic_male_45_54": 549.0, "hispanic_male_55_64": 413.0, "white_male_45_54": 26058.0, "white_male_55_64": 24392.0, "bachelors_degree_2": 51031.0, "bachelors_degree_or_higher_25_64": 64311.0, "children": 102850.0, "children_in_single_female_hh": 21996.0, "commuters_by_bus": 891.0, "commuters_by_car_truck_van": 188359.0, "commuters_by_carpool": 20814.0, "commuters_by_subway_or_elevated": 44.0, "commuters_drove_alone": 167545.0, "different_house_year_ago_different_city": 56586.0, "different_house_year_ago_same_city": 26815.0, "employed_agriculture_forestry_fishing_hunting_mining": 2591.0, "employed_arts_entertainment_recreation_accommodation_food": 20920.0, "employed_construction": 12476.0, "employed_education_health_social": 53754.0, "employed_finance_insurance_real_estate": 12927.0, "employed_information": 4173.0, "employed_manufacturing": 18761.0, "employed_other_services_not_public_admin": 11992.0, "employed_public_administration": 6559.0, "employed_retail_trade": 27509.0, "employed_science_management_admin_waste": 19352.0, "employed_transportation_warehousing_utilities": 11333.0, "employed_wholesale_trade": 6520.0, "female_female_households": 437.0, "four_more_cars": 10691.0, "gini_index": 0.4444, "graduate_professional_degree": 28057.0, "group_quarters": 14586.0, "high_school_including_ged": 91050.0, "households_public_asst_or_food_stamps": 22800.0, "in_grades_1_to_4": 23011.0, "in_grades_5_to_8": 22388.0, "in_grades_9_to_12": 21429.0, "in_school": 121079.0, "in_undergrad_college": 36323.0, "less_than_high_school_graduate": 29305.0, "male_45_64_associates_degree": 3008.0, "male_45_64_bachelors_degree": 8623.0, "male_45_64_graduate_degree": 4716.0, "male_45_64_less_than_9_grade": 1238.0, "male_45_64_grade_9_12": 4225.0, "male_45_64_high_school": 19081.0, "male_45_64_some_college": 13415.0, "male_45_to_64": 54306.0, "male_male_households": 242.0, "management_business_sci_arts_employed": 71601.0, "no_car": 4836.0, "no_cars": 10501.0, "not_us_citizen_pop": 5893.0, "occupation_management_arts": 71601.0, "occupation_natural_resources_construction_maintenance": 18084.0, "occupation_production_transportation_material": 26410.0, "occupation_sales_office": 54326.0, "occupation_services": 38446.0, "one_car": 61086.0, "two_cars": 71863.0, "three_cars": 26229.0, "pop_25_64": 225918.0, "pop_determined_poverty_status": 436911.0, "population_1_year_and_over": 446995.0, "population_3_years_over": 435643.0, "poverty": 75015.0, "sales_office_employed": 54326.0, "some_college_and_associates_degree": 95596.0, "walked_to_work": 3952.0, "worked_at_home": 8688.0, "workers_16_and_over": 205386.0, "associates_degree": 21399.0, "bachelors_degree": 51031.0, "high_school_diploma": 77189.0, "less_one_year_college": 23897.0, "masters_degree": 20310.0, "one_year_more_college": 50300.0, "pop_25_years_over": 295039.0, "commute_35_44_mins": 9223.0, "commute_60_more_mins": 6942.0, "commute_less_10_mins": 27146.0, "commuters_16_over": 196698.0, "hispanic_any_race": 13651.0, "do_date": "20122016", "pop_5_years_over": NaN, "speak_only_english_at_home": NaN, "speak_spanish_at_home": NaN, "speak_spanish_at_home_low_english": NaN, "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN}, {"geo_id": "40140", "nonfamily_households": 336387.0, "family_households": 988251.0, "median_year_structure_built": 1985.0, "rent_burden_not_computed": 28488.0, "rent_over_50_percent": 148774.0, "rent_40_to_50_percent": 52473.0, "rent_35_to_40_percent": 37313.0, "rent_30_to_35_percent": 45630.0, "rent_25_to_30_percent": 53117.0, "rent_20_to_25_percent": 56012.0, "rent_15_to_20_percent": 47757.0, "rent_10_to_15_percent": 24818.0, "rent_under_10_percent": 9756.0, "total_pop": 4430646.0, "male_pop": 2204051.0, "female_pop": 2226595.0, "median_age": 33.7, "white_pop": 1508417.0, "black_pop": 308155.0, "asian_pop": 277859.0, "hispanic_pop": 2192072.0, "amerindian_pop": 16247.0, "other_race_pop": 8166.0, "two_or_more_races_pop": 107100.0, "not_hispanic_pop": 2238574.0, "commuters_by_public_transportation": 25455.0, "households": 1324638.0, "median_income": 56295.0, "income_per_capita": 23213.0, "housing_units": 1528742.0, "vacant_housing_units": 204104.0, "vacant_housing_units_for_rent": 30560.0, "vacant_housing_units_for_sale": 17307.0, "median_rent": 1024.0, "percent_income_spent_on_rent": 35.1, "owner_occupied_housing_units": 820500.0, "million_dollar_housing_units": 6704.0, "mortgaged_housing_units": 600441.0, "families_with_young_children": 363349.0, "two_parent_families_with_young_children": 220755.0, "two_parents_in_labor_force_families_with_young_children": 108148.0, "two_parents_father_in_labor_force_families_with_young_children": 100265.0, "two_parents_mother_in_labor_force_families_with_young_children": 6866.0, "two_parents_not_in_labor_force_families_with_young_children": 5476.0, "one_parent_families_with_young_children": 142594.0, "father_one_parent_families_with_young_children": 37626.0, "father_in_labor_force_one_parent_families_with_young_children": 33640.0, "commute_10_14_mins": 209351.0, "commute_15_19_mins": 235943.0, "commute_20_24_mins": 222157.0, "commute_25_29_mins": 88075.0, "commute_30_34_mins": 219453.0, "commute_45_59_mins": 144505.0, "aggregate_travel_time_to_work": 53226545.0, "income_less_10000": 80624.0, "income_10000_14999": 68018.0, "income_15000_19999": 65553.0, "income_20000_24999": 69449.0, "income_25000_29999": 65210.0, "income_30000_34999": 65174.0, "income_35000_39999": 60961.0, "income_40000_44999": 61568.0, "income_45000_49999": 54853.0, "income_50000_59999": 105365.0, "income_60000_74999": 138452.0, "income_75000_99999": 171130.0, "income_100000_124999": 117368.0, "income_125000_149999": 70997.0, "income_150000_199999": 74848.0, "income_200000_or_more": 55068.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 1176.0, "owner_occupied_housing_units_lower_value_quartile": 163800.0, "owner_occupied_housing_units_median_value": 267500.0, "owner_occupied_housing_units_upper_value_quartile": 388600.0, "married_households": 697846.0, "occupied_housing_units": 1324638.0, "housing_units_renter_occupied": 504138.0, "dwellings_1_units_detached": 1057014.0, "dwellings_1_units_attached": 75451.0, "dwellings_2_units": 24601.0, "dwellings_3_to_4_units": 61628.0, "dwellings_5_to_9_units": 65273.0, "dwellings_10_to_19_units": 51448.0, "dwellings_20_to_49_units": 28843.0, "dwellings_50_or_more_units": 47169.0, "mobile_homes": 115049.0, "housing_built_2005_or_later": 4639.0, "housing_built_2000_to_2004": 23326.0, "housing_built_1939_or_earlier": 44382.0, "male_under_5": 159077.0, "male_5_to_9": 168471.0, "male_10_to_14": 172326.0, "male_15_to_17": 107420.0, "male_18_to_19": 68908.0, "male_20": 38210.0, "male_21": 37535.0, "male_22_to_24": 102467.0, "male_25_to_29": 163156.0, "male_30_to_34": 151683.0, "male_35_to_39": 139976.0, "male_40_to_44": 141218.0, "male_45_to_49": 143110.0, "male_50_to_54": 142808.0, "male_55_to_59": 125974.0, "male_60_61": 45856.0, "male_62_64": 58979.0, "male_65_to_66": 36206.0, "male_67_to_69": 47687.0, "male_70_to_74": 58826.0, "male_75_to_79": 42397.0, "male_80_to_84": 28933.0, "male_85_and_over": 22828.0, "female_under_5": 152873.0, "female_5_to_9": 162820.0, "female_10_to_14": 163729.0, "female_15_to_17": 103313.0, "female_18_to_19": 64320.0, "female_20": 36171.0, "female_21": 35145.0, "female_22_to_24": 95186.0, "female_25_to_29": 155968.0, "female_30_to_34": 149173.0, "female_35_to_39": 144065.0, "female_40_to_44": 143612.0, "female_45_to_49": 143190.0, "female_50_to_54": 145013.0, "female_55_to_59": 130661.0, "female_60_to_61": 48221.0, "female_62_to_64": 65315.0, "female_65_to_66": 40910.0, "female_67_to_69": 53084.0, "female_70_to_74": 66906.0, "female_75_to_79": 50583.0, "female_80_to_84": 36151.0, "female_85_and_over": 40186.0, "white_including_hispanic": 2776876.0, "black_including_hispanic": 325758.0, "amerindian_including_hispanic": 37715.0, "asian_including_hispanic": 285399.0, "commute_5_9_mins": 135412.0, "commute_35_39_mins": 40581.0, "commute_40_44_mins": 64017.0, "commute_60_89_mins": 172713.0, "commute_90_more_mins": 106666.0, "households_retirement_income": 226970.0, "armed_forces": 15145.0, "civilian_labor_force": 2019374.0, "employed_pop": 1793942.0, "unemployed_pop": 225432.0, "not_in_labor_force": 1346745.0, "pop_16_over": 3381264.0, "pop_in_labor_force": 2034519.0, "asian_male_45_54": 18778.0, "asian_male_55_64": 16650.0, "black_male_45_54": 21974.0, "black_male_55_64": 17232.0, "hispanic_male_45_54": 126829.0, "hispanic_male_55_64": 75138.0, "white_male_45_54": 111829.0, "white_male_55_64": 117187.0, "bachelors_degree_2": 360405.0, "bachelors_degree_or_higher_25_64": 446466.0, "children": 1190029.0, "children_in_single_female_hh": 283352.0, "commuters_by_bus": 18309.0, "commuters_by_car_truck_van": 1592602.0, "commuters_by_carpool": 226177.0, "commuters_by_subway_or_elevated": 663.0, "commuters_drove_alone": 1366425.0, "different_house_year_ago_different_city": 436031.0, "different_house_year_ago_same_city": 196209.0, "employed_agriculture_forestry_fishing_hunting_mining": 21299.0, "employed_arts_entertainment_recreation_accommodation_food": 183328.0, "employed_construction": 141782.0, "employed_education_health_social": 383719.0, "employed_finance_insurance_real_estate": 90157.0, "employed_information": 25410.0, "employed_manufacturing": 165387.0, "employed_other_services_not_public_admin": 94304.0, "employed_public_administration": 92594.0, "employed_retail_trade": 235076.0, "employed_science_management_admin_waste": 175417.0, "employed_transportation_warehousing_utilities": 126572.0, "employed_wholesale_trade": 58897.0, "female_female_households": 2906.0, "four_more_cars": 129990.0, "gini_index": 0.4465, "graduate_professional_degree": 200762.0, "group_quarters": 87428.0, "high_school_including_ged": 719496.0, "households_public_asst_or_food_stamps": 187784.0, "in_grades_1_to_4": 259614.0, "in_grades_5_to_8": 270226.0, "in_grades_9_to_12": 300123.0, "in_school": 1289269.0, "in_undergrad_college": 290827.0, "less_than_high_school_graduate": 561127.0, "male_45_64_associates_degree": 38262.0, "male_45_64_bachelors_degree": 66381.0, "male_45_64_graduate_degree": 38221.0, "male_45_64_less_than_9_grade": 58865.0, "male_45_64_grade_9_12": 57585.0, "male_45_64_high_school": 132794.0, "male_45_64_some_college": 124619.0, "male_45_to_64": 516727.0, "male_male_households": 4196.0, "management_business_sci_arts_employed": 518743.0, "no_car": 34992.0, "no_cars": 68454.0, "not_us_citizen_pop": 508809.0, "occupation_management_arts": 518743.0, "occupation_natural_resources_construction_maintenance": 194325.0, "occupation_production_transportation_material": 266430.0, "occupation_sales_office": 453517.0, "occupation_services": 360927.0, "one_car": 394000.0, "two_cars": 501571.0, "three_cars": 230623.0, "pop_25_64": 2237978.0, "pop_determined_poverty_status": 4339925.0, "population_1_year_and_over": 4373857.0, "population_3_years_over": 4249714.0, "poverty": 768884.0, "sales_office_employed": 453517.0, "some_college_and_associates_degree": 920885.0, "walked_to_work": 28394.0, "worked_at_home": 85892.0, "workers_16_and_over": 1758026.0, "associates_degree": 221878.0, "bachelors_degree": 360405.0, "high_school_diploma": 635233.0, "less_one_year_college": 208252.0, "masters_degree": 143587.0, "one_year_more_college": 490755.0, "pop_25_years_over": 2762675.0, "commute_35_44_mins": 104598.0, "commute_60_more_mins": 279379.0, "commute_less_10_mins": 168673.0, "commuters_16_over": 1672134.0, "hispanic_any_race": 2192072.0, "do_date": "20122016", "pop_5_years_over": NaN, "speak_only_english_at_home": NaN, "speak_spanish_at_home": NaN, "speak_spanish_at_home_low_english": NaN, "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN}, {"geo_id": "42340", "nonfamily_households": 47484.0, "family_households": 89748.0, "median_year_structure_built": 1985.0, "rent_burden_not_computed": 4182.0, "rent_over_50_percent": 14429.0, "rent_40_to_50_percent": 4771.0, "rent_35_to_40_percent": 3759.0, "rent_30_to_35_percent": 4743.0, "rent_25_to_30_percent": 6062.0, "rent_20_to_25_percent": 6246.0, "rent_15_to_20_percent": 6280.0, "rent_10_to_15_percent": 4872.0, "rent_under_10_percent": 1918.0, "total_pop": 372569.0, "male_pop": 181151.0, "female_pop": 191418.0, "median_age": 34.8, "white_pop": 209865.0, "black_pop": 123643.0, "asian_pop": 8356.0, "hispanic_pop": 21349.0, "amerindian_pop": 805.0, "other_race_pop": 491.0, "two_or_more_races_pop": 7718.0, "not_hispanic_pop": 351220.0, "commuters_by_public_transportation": 3540.0, "households": 137232.0, "median_income": 52115.0, "income_per_capita": 27126.0, "housing_units": 156428.0, "vacant_housing_units": 19196.0, "vacant_housing_units_for_rent": 4594.0, "vacant_housing_units_for_sale": 1765.0, "median_rent": 776.0, "percent_income_spent_on_rent": 31.2, "owner_occupied_housing_units": 79970.0, "million_dollar_housing_units": 614.0, "mortgaged_housing_units": 54161.0, "families_with_young_children": 28267.0, "two_parent_families_with_young_children": 16133.0, "two_parents_in_labor_force_families_with_young_children": 9041.0, "two_parents_father_in_labor_force_families_with_young_children": 6498.0, "two_parents_mother_in_labor_force_families_with_young_children": 515.0, "two_parents_not_in_labor_force_families_with_young_children": 79.0, "one_parent_families_with_young_children": 12134.0, "father_one_parent_families_with_young_children": 2019.0, "father_in_labor_force_one_parent_families_with_young_children": 1912.0, "commute_10_14_mins": 22774.0, "commute_15_19_mins": 28031.0, "commute_20_24_mins": 28838.0, "commute_25_29_mins": 12799.0, "commute_30_34_mins": 23799.0, "commute_45_59_mins": 11010.0, "aggregate_travel_time_to_work": 3899960.0, "income_less_10000": 10940.0, "income_10000_14999": 7283.0, "income_15000_19999": 6729.0, "income_20000_24999": 7325.0, "income_25000_29999": 6887.0, "income_30000_34999": 7027.0, "income_35000_39999": 6942.0, "income_40000_44999": 6248.0, "income_45000_49999": 5968.0, "income_50000_59999": 11331.0, "income_60000_74999": 13939.0, "income_75000_99999": 17009.0, "income_100000_124999": 11776.0, "income_125000_149999": 5978.0, "income_150000_199999": 6203.0, "income_200000_or_more": 5647.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 964.0, "owner_occupied_housing_units_lower_value_quartile": 110300.0, "owner_occupied_housing_units_median_value": 171500.0, "owner_occupied_housing_units_upper_value_quartile": 268300.0, "married_households": 62038.0, "occupied_housing_units": 137232.0, "housing_units_renter_occupied": 57262.0, "dwellings_1_units_detached": 100552.0, "dwellings_1_units_attached": 6804.0, "dwellings_2_units": 5452.0, "dwellings_3_to_4_units": 7960.0, "dwellings_5_to_9_units": 9344.0, "dwellings_10_to_19_units": 6502.0, "dwellings_20_to_49_units": 4313.0, "dwellings_50_or_more_units": 3511.0, "mobile_homes": 11883.0, "housing_built_2005_or_later": 883.0, "housing_built_2000_to_2004": 5718.0, "housing_built_1939_or_earlier": 7639.0, "male_under_5": 12708.0, "male_5_to_9": 12426.0, "male_10_to_14": 12690.0, "male_15_to_17": 7133.0, "male_18_to_19": 5431.0, "male_20": 3424.0, "male_21": 3009.0, "male_22_to_24": 8806.0, "male_25_to_29": 15471.0, "male_30_to_34": 13705.0, "male_35_to_39": 12231.0, "male_40_to_44": 10800.0, "male_45_to_49": 11135.0, "male_50_to_54": 11343.0, "male_55_to_59": 10730.0, "male_60_61": 3953.0, "male_62_64": 5304.0, "male_65_to_66": 3626.0, "male_67_to_69": 4116.0, "male_70_to_74": 5566.0, "male_75_to_79": 3503.0, "male_80_to_84": 2361.0, "male_85_and_over": 1680.0, "female_under_5": 12353.0, "female_5_to_9": 11272.0, "female_10_to_14": 12423.0, "female_15_to_17": 6598.0, "female_18_to_19": 6061.0, "female_20": 3572.0, "female_21": 2998.0, "female_22_to_24": 8047.0, "female_25_to_29": 15481.0, "female_30_to_34": 13990.0, "female_35_to_39": 11777.0, "female_40_to_44": 12622.0, "female_45_to_49": 11650.0, "female_50_to_54": 12553.0, "female_55_to_59": 12669.0, "female_60_to_61": 3930.0, "female_62_to_64": 6553.0, "female_65_to_66": 3798.0, "female_67_to_69": 5028.0, "female_70_to_74": 6517.0, "female_75_to_79": 4723.0, "female_80_to_84": 2869.0, "female_85_and_over": 3934.0, "white_including_hispanic": 223597.0, "black_including_hispanic": 124999.0, "amerindian_including_hispanic": 975.0, "asian_including_hispanic": 8395.0, "commute_5_9_mins": 15444.0, "commute_35_39_mins": 5803.0, "commute_40_44_mins": 4668.0, "commute_60_89_mins": 6123.0, "commute_90_more_mins": 1896.0, "households_retirement_income": 24697.0, "armed_forces": 4585.0, "civilian_labor_force": 187087.0, "employed_pop": 170682.0, "unemployed_pop": 16405.0, "not_in_labor_force": 102467.0, "pop_16_over": 294139.0, "pop_in_labor_force": 191672.0, "asian_male_45_54": 550.0, "asian_male_55_64": 446.0, "black_male_45_54": 6827.0, "black_male_55_64": 5772.0, "hispanic_male_45_54": 969.0, "hispanic_male_55_64": 550.0, "white_male_45_54": 13897.0, "white_male_55_64": 13150.0, "bachelors_degree_2": 46798.0, "bachelors_degree_or_higher_25_64": 60392.0, "children": 87603.0, "children_in_single_female_hh": 28855.0, "commuters_by_bus": 3420.0, "commuters_by_car_truck_van": 154182.0, "commuters_by_carpool": 16544.0, "commuters_by_subway_or_elevated": 37.0, "commuters_drove_alone": 137638.0, "different_house_year_ago_different_city": 47864.0, "different_house_year_ago_same_city": 20271.0, "employed_agriculture_forestry_fishing_hunting_mining": 713.0, "employed_arts_entertainment_recreation_accommodation_food": 21591.0, "employed_construction": 11318.0, "employed_education_health_social": 37073.0, "employed_finance_insurance_real_estate": 8851.0, "employed_information": 2370.0, "employed_manufacturing": 17519.0, "employed_other_services_not_public_admin": 7657.0, "employed_public_administration": 10684.0, "employed_retail_trade": 20611.0, "employed_science_management_admin_waste": 15773.0, "employed_transportation_warehousing_utilities": 12751.0, "employed_wholesale_trade": 3771.0, "female_female_households": 264.0, "four_more_cars": 7956.0, "gini_index": 0.4609, "graduate_professional_degree": 28187.0, "group_quarters": 12620.0, "high_school_including_ged": 65257.0, "households_public_asst_or_food_stamps": 17120.0, "in_grades_1_to_4": 19570.0, "in_grades_5_to_8": 20039.0, "in_grades_9_to_12": 18837.0, "in_school": 104787.0, "in_undergrad_college": 28421.0, "less_than_high_school_graduate": 26952.0, "male_45_64_associates_degree": 2553.0, "male_45_64_bachelors_degree": 7967.0, "male_45_64_graduate_degree": 4004.0, "male_45_64_less_than_9_grade": 1223.0, "male_45_64_grade_9_12": 3198.0, "male_45_64_high_school": 13563.0, "male_45_64_some_college": 9957.0, "male_45_to_64": 42465.0, "male_male_households": 338.0, "management_business_sci_arts_employed": 59117.0, "no_car": 5443.0, "no_cars": 9825.0, "not_us_citizen_pop": 12109.0, "occupation_management_arts": 59117.0, "occupation_natural_resources_construction_maintenance": 16678.0, "occupation_production_transportation_material": 21577.0, "occupation_sales_office": 39748.0, "occupation_services": 33562.0, "one_car": 48677.0, "two_cars": 52380.0, "three_cars": 18394.0, "pop_25_64": 195897.0, "pop_determined_poverty_status": 359123.0, "population_1_year_and_over": 367318.0, "population_3_years_over": 357385.0, "poverty": 59044.0, "sales_office_employed": 39748.0, "some_college_and_associates_degree": 76424.0, "walked_to_work": 3939.0, "worked_at_home": 5860.0, "workers_16_and_over": 171353.0, "associates_degree": 17321.0, "bachelors_degree": 46798.0, "high_school_diploma": 53124.0, "less_one_year_college": 15591.0, "masters_degree": 20017.0, "one_year_more_college": 43512.0, "pop_25_years_over": 243618.0, "commute_35_44_mins": 10471.0, "commute_60_more_mins": 8019.0, "commute_less_10_mins": 19752.0, "commuters_16_over": 165493.0, "hispanic_any_race": 21349.0, "do_date": "20122016", "pop_5_years_over": NaN, "speak_only_english_at_home": NaN, "speak_spanish_at_home": NaN, "speak_spanish_at_home_low_english": NaN, "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN}, {"geo_id": "36260", "nonfamily_households": 43545.0, "family_households": 155843.0, "median_year_structure_built": 1985.0, "rent_burden_not_computed": 3284.0, "rent_over_50_percent": 8781.0, "rent_40_to_50_percent": 3765.0, "rent_35_to_40_percent": 2952.0, "rent_30_to_35_percent": 4760.0, "rent_25_to_30_percent": 5693.0, "rent_20_to_25_percent": 7121.0, "rent_15_to_20_percent": 7147.0, "rent_10_to_15_percent": 4822.0, "rent_under_10_percent": 1611.0, "total_pop": 632793.0, "male_pop": 318228.0, "female_pop": 314565.0, "median_age": 31.2, "white_pop": 520141.0, "black_pop": 6671.0, "asian_pop": 9621.0, "hispanic_pop": 77189.0, "amerindian_pop": 2882.0, "other_race_pop": 352.0, "two_or_more_races_pop": 13278.0, "not_hispanic_pop": 555604.0, "commuters_by_public_transportation": 6089.0, "households": 199388.0, "median_income": 65687.0, "income_per_capita": 25568.0, "housing_units": 212777.0, "vacant_housing_units": 13389.0, "vacant_housing_units_for_rent": 2690.0, "vacant_housing_units_for_sale": 2157.0, "median_rent": 738.0, "percent_income_spent_on_rent": 27.3, "owner_occupied_housing_units": 149452.0, "million_dollar_housing_units": 677.0, "mortgaged_housing_units": 109521.0, "families_with_young_children": 66422.0, "two_parent_families_with_young_children": 54294.0, "two_parents_in_labor_force_families_with_young_children": 24927.0, "two_parents_father_in_labor_force_families_with_young_children": 28016.0, "two_parents_mother_in_labor_force_families_with_young_children": 955.0, "two_parents_not_in_labor_force_families_with_young_children": 396.0, "one_parent_families_with_young_children": 12128.0, "father_one_parent_families_with_young_children": 3020.0, "father_in_labor_force_one_parent_families_with_young_children": 2781.0, "commute_10_14_mins": 44131.0, "commute_15_19_mins": 48758.0, "commute_20_24_mins": 43989.0, "commute_25_29_mins": 17311.0, "commute_30_34_mins": 29978.0, "commute_45_59_mins": 17179.0, "aggregate_travel_time_to_work": 6191155.0, "income_less_10000": 8648.0, "income_10000_14999": 6210.0, "income_15000_19999": 6331.0, "income_20000_24999": 7260.0, "income_25000_29999": 7607.0, "income_30000_34999": 8096.0, "income_35000_39999": 8737.0, "income_40000_44999": 9325.0, "income_45000_49999": 8957.0, "income_50000_59999": 18149.0, "income_60000_74999": 25881.0, "income_75000_99999": 32768.0, "income_100000_124999": 20886.0, "income_125000_149999": 12275.0, "income_150000_199999": 11109.0, "income_200000_or_more": 7149.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 856.0, "owner_occupied_housing_units_lower_value_quartile": 149000.0, "owner_occupied_housing_units_median_value": 206900.0, "owner_occupied_housing_units_upper_value_quartile": 292700.0, "married_households": 127892.0, "occupied_housing_units": 199388.0, "housing_units_renter_occupied": 49936.0, "dwellings_1_units_detached": 158247.0, "dwellings_1_units_attached": 10433.0, "dwellings_2_units": 6336.0, "dwellings_3_to_4_units": 9698.0, "dwellings_5_to_9_units": 5860.0, "dwellings_10_to_19_units": 6413.0, "dwellings_20_to_49_units": 5090.0, "dwellings_50_or_more_units": 3943.0, "mobile_homes": 6665.0, "housing_built_2005_or_later": 1620.0, "housing_built_2000_to_2004": 7933.0, "housing_built_1939_or_earlier": 9178.0, "male_under_5": 28331.0, "male_5_to_9": 29292.0, "male_10_to_14": 29550.0, "male_15_to_17": 16162.0, "male_18_to_19": 8596.0, "male_20": 4370.0, "male_21": 5129.0, "male_22_to_24": 13191.0, "male_25_to_29": 21873.0, "male_30_to_34": 24246.0, "male_35_to_39": 22785.0, "male_40_to_44": 20266.0, "male_45_to_49": 16862.0, "male_50_to_54": 17585.0, "male_55_to_59": 16946.0, "male_60_61": 6180.0, "male_62_64": 7261.0, "male_65_to_66": 4834.0, "male_67_to_69": 5680.0, "male_70_to_74": 7199.0, "male_75_to_79": 5451.0, "male_80_to_84": 3558.0, "male_85_and_over": 2881.0, "female_under_5": 26832.0, "female_5_to_9": 28101.0, "female_10_to_14": 27251.0, "female_15_to_17": 15364.0, "female_18_to_19": 7677.0, "female_20": 3583.0, "female_21": 4139.0, "female_22_to_24": 13005.0, "female_25_to_29": 22249.0, "female_30_to_34": 23971.0, "female_35_to_39": 23024.0, "female_40_to_44": 18639.0, "female_45_to_49": 16899.0, "female_50_to_54": 17841.0, "female_55_to_59": 17107.0, "female_60_to_61": 6137.0, "female_62_to_64": 7983.0, "female_65_to_66": 5270.0, "female_67_to_69": 5783.0, "female_70_to_74": 8184.0, "female_75_to_79": 6348.0, "female_80_to_84": 4651.0, "female_85_and_over": 4527.0, "white_including_hispanic": 568529.0, "black_including_hispanic": 7109.0, "amerindian_including_hispanic": 3937.0, "asian_including_hispanic": 9808.0, "commute_5_9_mins": 34906.0, "commute_35_39_mins": 6589.0, "commute_40_44_mins": 8225.0, "commute_60_89_mins": 10808.0, "commute_90_more_mins": 4372.0, "households_retirement_income": 39327.0, "armed_forces": 2600.0, "civilian_labor_force": 303797.0, "employed_pop": 290110.0, "unemployed_pop": 13687.0, "not_in_labor_force": 146720.0, "pop_16_over": 453117.0, "pop_in_labor_force": 306397.0, "asian_male_45_54": 524.0, "asian_male_55_64": 500.0, "black_male_45_54": 377.0, "black_male_55_64": 364.0, "hispanic_male_45_54": 3672.0, "hispanic_male_55_64": 2184.0, "white_male_45_54": 29358.0, "white_male_55_64": 26929.0, "bachelors_degree_2": 75859.0, "bachelors_degree_or_higher_25_64": 92232.0, "children": 200883.0, "children_in_single_female_hh": 25872.0, "commuters_by_bus": 4023.0, "commuters_by_car_truck_van": 260604.0, "commuters_by_carpool": 30868.0, "commuters_by_subway_or_elevated": 220.0, "commuters_drove_alone": 229736.0, "different_house_year_ago_different_city": 68409.0, "different_house_year_ago_same_city": 22109.0, "employed_agriculture_forestry_fishing_hunting_mining": 3463.0, "employed_arts_entertainment_recreation_accommodation_food": 23139.0, "employed_construction": 17615.0, "employed_education_health_social": 58624.0, "employed_finance_insurance_real_estate": 17339.0, "employed_information": 5116.0, "employed_manufacturing": 39756.0, "employed_other_services_not_public_admin": 12757.0, "employed_public_administration": 26174.0, "employed_retail_trade": 34980.0, "employed_science_management_admin_waste": 30764.0, "employed_transportation_warehousing_utilities": 13029.0, "employed_wholesale_trade": 7354.0, "female_female_households": 368.0, "four_more_cars": 21071.0, "gini_index": 0.3949, "graduate_professional_degree": 34036.0, "group_quarters": 5353.0, "high_school_including_ged": 94167.0, "households_public_asst_or_food_stamps": 18518.0, "in_grades_1_to_4": 44560.0, "in_grades_5_to_8": 44860.0, "in_grades_9_to_12": 42170.0, "in_school": 198022.0, "in_undergrad_college": 35479.0, "less_than_high_school_graduate": 25288.0, "male_45_64_associates_degree": 5674.0, "male_45_64_bachelors_degree": 13162.0, "male_45_64_graduate_degree": 8303.0, "male_45_64_less_than_9_grade": 1546.0, "male_45_64_grade_9_12": 3059.0, "male_45_64_high_school": 15189.0, "male_45_64_some_college": 17901.0, "male_45_to_64": 64834.0, "male_male_households": 98.0, "management_business_sci_arts_employed": 105922.0, "no_car": 4621.0, "no_cars": 8440.0, "not_us_citizen_pop": 19767.0, "occupation_management_arts": 105922.0, "occupation_natural_resources_construction_maintenance": 25605.0, "occupation_production_transportation_material": 42059.0, "occupation_sales_office": 75117.0, "occupation_services": 41407.0, "one_car": 45925.0, "two_cars": 83793.0, "three_cars": 40159.0, "pop_25_64": 307854.0, "pop_determined_poverty_status": 627233.0, "population_1_year_and_over": 622475.0, "population_3_years_over": 600694.0, "poverty": 58592.0, "sales_office_employed": 75117.0, "some_college_and_associates_degree": 142870.0, "walked_to_work": 4329.0, "worked_at_home": 13505.0, "workers_16_and_over": 288986.0, "associates_degree": 38022.0, "bachelors_degree": 75859.0, "high_school_diploma": 81565.0, "less_one_year_college": 34280.0, "masters_degree": 26567.0, "one_year_more_college": 70568.0, "pop_25_years_over": 372220.0, "commute_35_44_mins": 14814.0, "commute_60_more_mins": 15180.0, "commute_less_10_mins": 44141.0, "commuters_16_over": 275481.0, "hispanic_any_race": 77189.0, "do_date": "20122016", "pop_5_years_over": NaN, "speak_only_english_at_home": NaN, "speak_spanish_at_home": NaN, "speak_spanish_at_home_low_english": NaN, "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN}]}