{"table_name": "zcta5_2018_5yr", "table_fullname": "bigquery-public-data.census_bureau_acs.zcta5_2018_5yr", "column_names": ["geo_id", "do_date", "total_pop", "households", "male_pop", "female_pop", "median_age", "male_under_5", "male_5_to_9", "male_10_to_14", "male_15_to_17", "male_18_to_19", "male_20", "male_21", "male_22_to_24", "male_25_to_29", "male_30_to_34", "male_35_to_39", "male_40_to_44", "male_45_to_49", "male_50_to_54", "male_55_to_59", "male_60_to_61", "male_62_to_64", "male_65_to_66", "male_67_to_69", "male_70_to_74", "male_75_to_79", "male_80_to_84", "male_85_and_over", "female_under_5", "female_5_to_9", "female_10_to_14", "female_15_to_17", "female_18_to_19", "female_20", "female_21", "female_22_to_24", "female_25_to_29", "female_30_to_34", "female_35_to_39", "female_40_to_44", "female_45_to_49", "female_50_to_54", "female_55_to_59", "female_60_to_61", "female_62_to_64", "female_65_to_66", "female_67_to_69", "female_70_to_74", "female_75_to_79", "female_80_to_84", "female_85_and_over", "white_pop", "population_1_year_and_over", "population_3_years_over", "pop_5_years_over", "pop_15_and_over", "pop_16_over", "pop_25_years_over", "pop_25_64", "pop_never_married", "pop_now_married", "pop_separated", "pop_widowed", "pop_divorced", "not_us_citizen_pop", "black_pop", "asian_pop", "hispanic_pop", "amerindian_pop", "other_race_pop", "two_or_more_races_pop", "hispanic_any_race", "not_hispanic_pop", "asian_male_45_54", "asian_male_55_64", "black_male_45_54", "black_male_55_64", "hispanic_male_45_54", "hispanic_male_55_64", "white_male_45_54", "white_male_55_64", "median_income", "income_per_capita", "income_less_10000", "income_10000_14999", "income_15000_19999", "income_20000_24999", "income_25000_29999", "income_30000_34999", "income_35000_39999", "income_40000_44999", "income_45000_49999", "income_50000_59999", "income_60000_74999", "income_75000_99999", "income_100000_124999", "income_125000_149999", "income_150000_199999", "income_200000_or_more", "pop_determined_poverty_status", "poverty", "gini_index", "housing_units", "renter_occupied_housing_units_paying_cash_median_gross_rent", "owner_occupied_housing_units_lower_value_quartile", "owner_occupied_housing_units_median_value", "owner_occupied_housing_units_upper_value_quartile", "occupied_housing_units", "housing_units_renter_occupied", "vacant_housing_units", "vacant_housing_units_for_rent", "vacant_housing_units_for_sale", "dwellings_1_units_detached", "dwellings_1_units_attached", "dwellings_2_units", "dwellings_3_to_4_units", "dwellings_5_to_9_units", "dwellings_10_to_19_units", "dwellings_20_to_49_units", "dwellings_50_or_more_units", "mobile_homes", "housing_built_2005_or_later", "housing_built_2000_to_2004", "housing_built_1939_or_earlier", "median_year_structure_built", "married_households", "nonfamily_households", "family_households", "households_public_asst_or_food_stamps", "male_male_households", "female_female_households", "children", "children_in_single_female_hh", "median_rent", "percent_income_spent_on_rent", "rent_burden_not_computed", "rent_over_50_percent", "rent_40_to_50_percent", "rent_35_to_40_percent", "rent_30_to_35_percent", "rent_25_to_30_percent", "rent_20_to_25_percent", "rent_15_to_20_percent", "rent_10_to_15_percent", "rent_under_10_percent", "owner_occupied_housing_units", "million_dollar_housing_units", "mortgaged_housing_units", "different_house_year_ago_different_city", "different_house_year_ago_same_city", "families_with_young_children", "two_parent_families_with_young_children", "two_parents_in_labor_force_families_with_young_children", "two_parents_father_in_labor_force_families_with_young_children", "two_parents_mother_in_labor_force_families_with_young_children", "two_parents_not_in_labor_force_families_with_young_children", "one_parent_families_with_young_children", "father_one_parent_families_with_young_children", "father_in_labor_force_one_parent_families_with_young_children", "commute_less_10_mins", "commute_10_14_mins", "commute_15_19_mins", "commute_20_24_mins", "commute_25_29_mins", "commute_30_34_mins", "commute_35_44_mins", "commute_60_more_mins", "commute_45_59_mins", "commuters_16_over", "walked_to_work", "worked_at_home", "no_car", "no_cars", "one_car", "two_cars", "three_cars", "four_more_cars", "aggregate_travel_time_to_work", "commuters_by_public_transportation", "commuters_by_bus", "commuters_by_car_truck_van", "commuters_by_carpool", "commuters_by_subway_or_elevated", "commuters_drove_alone", "group_quarters", "associates_degree", "bachelors_degree", "high_school_diploma", "less_one_year_college", "masters_degree", "one_year_more_college", "less_than_high_school_graduate", "high_school_including_ged", "bachelors_degree_2", "bachelors_degree_or_higher_25_64", "graduate_professional_degree", "some_college_and_associates_degree", "male_45_64_associates_degree", "male_45_64_bachelors_degree", "male_45_64_graduate_degree", "male_45_64_less_than_9_grade", "male_45_64_grade_9_12", "male_45_64_high_school", "male_45_64_some_college", "male_45_to_64", "employed_pop", "unemployed_pop", "pop_in_labor_force", "not_in_labor_force", "workers_16_and_over", "armed_forces", "civilian_labor_force", "employed_agriculture_forestry_fishing_hunting_mining", "employed_arts_entertainment_recreation_accommodation_food", "employed_construction", "employed_education_health_social", "employed_finance_insurance_real_estate", "employed_information", "employed_manufacturing", "employed_other_services_not_public_admin", "employed_public_administration", "employed_retail_trade", "employed_science_management_admin_waste", "employed_transportation_warehousing_utilities", "employed_wholesale_trade", "occupation_management_arts", "occupation_natural_resources_construction_maintenance", "occupation_production_transportation_material", "occupation_sales_office", "occupation_services", "management_business_sci_arts_employed", "sales_office_employed", "in_grades_1_to_4", "in_grades_5_to_8", "in_grades_9_to_12", "in_school", "in_undergrad_college", "speak_only_english_at_home", "speak_spanish_at_home", "speak_spanish_at_home_low_english"], "column_types": ["STRING", "DATE", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64"], "nested_column_names": ["geo_id", "do_date", "total_pop", "households", "male_pop", "female_pop", "median_age", "male_under_5", "male_5_to_9", "male_10_to_14", "male_15_to_17", "male_18_to_19", "male_20", "male_21", "male_22_to_24", "male_25_to_29", "male_30_to_34", "male_35_to_39", "male_40_to_44", "male_45_to_49", "male_50_to_54", "male_55_to_59", "male_60_to_61", "male_62_to_64", "male_65_to_66", "male_67_to_69", "male_70_to_74", "male_75_to_79", "male_80_to_84", "male_85_and_over", "female_under_5", "female_5_to_9", "female_10_to_14", "female_15_to_17", "female_18_to_19", "female_20", "female_21", "female_22_to_24", "female_25_to_29", "female_30_to_34", "female_35_to_39", "female_40_to_44", "female_45_to_49", "female_50_to_54", "female_55_to_59", "female_60_to_61", "female_62_to_64", "female_65_to_66", "female_67_to_69", "female_70_to_74", "female_75_to_79", "female_80_to_84", "female_85_and_over", "white_pop", "population_1_year_and_over", "population_3_years_over", "pop_5_years_over", "pop_15_and_over", "pop_16_over", "pop_25_years_over", "pop_25_64", "pop_never_married", "pop_now_married", "pop_separated", "pop_widowed", "pop_divorced", "not_us_citizen_pop", "black_pop", "asian_pop", "hispanic_pop", "amerindian_pop", "other_race_pop", "two_or_more_races_pop", "hispanic_any_race", "not_hispanic_pop", "asian_male_45_54", "asian_male_55_64", "black_male_45_54", "black_male_55_64", "hispanic_male_45_54", "hispanic_male_55_64", "white_male_45_54", "white_male_55_64", "median_income", "income_per_capita", "income_less_10000", "income_10000_14999", "income_15000_19999", "income_20000_24999", "income_25000_29999", "income_30000_34999", "income_35000_39999", "income_40000_44999", "income_45000_49999", "income_50000_59999", "income_60000_74999", "income_75000_99999", "income_100000_124999", "income_125000_149999", "income_150000_199999", "income_200000_or_more", "pop_determined_poverty_status", "poverty", "gini_index", "housing_units", "renter_occupied_housing_units_paying_cash_median_gross_rent", "owner_occupied_housing_units_lower_value_quartile", "owner_occupied_housing_units_median_value", "owner_occupied_housing_units_upper_value_quartile", "occupied_housing_units", "housing_units_renter_occupied", "vacant_housing_units", "vacant_housing_units_for_rent", "vacant_housing_units_for_sale", "dwellings_1_units_detached", "dwellings_1_units_attached", "dwellings_2_units", "dwellings_3_to_4_units", "dwellings_5_to_9_units", "dwellings_10_to_19_units", "dwellings_20_to_49_units", "dwellings_50_or_more_units", "mobile_homes", "housing_built_2005_or_later", "housing_built_2000_to_2004", "housing_built_1939_or_earlier", "median_year_structure_built", "married_households", "nonfamily_households", "family_households", "households_public_asst_or_food_stamps", "male_male_households", "female_female_households", "children", "children_in_single_female_hh", "median_rent", "percent_income_spent_on_rent", "rent_burden_not_computed", "rent_over_50_percent", "rent_40_to_50_percent", "rent_35_to_40_percent", "rent_30_to_35_percent", "rent_25_to_30_percent", "rent_20_to_25_percent", "rent_15_to_20_percent", "rent_10_to_15_percent", "rent_under_10_percent", "owner_occupied_housing_units", "million_dollar_housing_units", "mortgaged_housing_units", "different_house_year_ago_different_city", "different_house_year_ago_same_city", "families_with_young_children", "two_parent_families_with_young_children", "two_parents_in_labor_force_families_with_young_children", "two_parents_father_in_labor_force_families_with_young_children", "two_parents_mother_in_labor_force_families_with_young_children", "two_parents_not_in_labor_force_families_with_young_children", "one_parent_families_with_young_children", "father_one_parent_families_with_young_children", "father_in_labor_force_one_parent_families_with_young_children", "commute_less_10_mins", "commute_10_14_mins", "commute_15_19_mins", "commute_20_24_mins", "commute_25_29_mins", "commute_30_34_mins", "commute_35_44_mins", "commute_60_more_mins", "commute_45_59_mins", "commuters_16_over", "walked_to_work", "worked_at_home", "no_car", "no_cars", "one_car", "two_cars", "three_cars", "four_more_cars", "aggregate_travel_time_to_work", "commuters_by_public_transportation", "commuters_by_bus", "commuters_by_car_truck_van", "commuters_by_carpool", "commuters_by_subway_or_elevated", "commuters_drove_alone", "group_quarters", "associates_degree", "bachelors_degree", "high_school_diploma", "less_one_year_college", "masters_degree", "one_year_more_college", "less_than_high_school_graduate", "high_school_including_ged", "bachelors_degree_2", "bachelors_degree_or_higher_25_64", "graduate_professional_degree", "some_college_and_associates_degree", "male_45_64_associates_degree", "male_45_64_bachelors_degree", "male_45_64_graduate_degree", "male_45_64_less_than_9_grade", "male_45_64_grade_9_12", "male_45_64_high_school", "male_45_64_some_college", "male_45_to_64", "employed_pop", "unemployed_pop", "pop_in_labor_force", "not_in_labor_force", "workers_16_and_over", "armed_forces", "civilian_labor_force", "employed_agriculture_forestry_fishing_hunting_mining", "employed_arts_entertainment_recreation_accommodation_food", "employed_construction", "employed_education_health_social", "employed_finance_insurance_real_estate", "employed_information", "employed_manufacturing", "employed_other_services_not_public_admin", "employed_public_administration", "employed_retail_trade", "employed_science_management_admin_waste", "employed_transportation_warehousing_utilities", "employed_wholesale_trade", "occupation_management_arts", "occupation_natural_resources_construction_maintenance", "occupation_production_transportation_material", "occupation_sales_office", "occupation_services", "management_business_sci_arts_employed", "sales_office_employed", "in_grades_1_to_4", "in_grades_5_to_8", "in_grades_9_to_12", "in_school", "in_undergrad_college", "speak_only_english_at_home", "speak_spanish_at_home", "speak_spanish_at_home_low_english"], "nested_column_types": ["STRING", "DATE", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64"], "description": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], "sample_rows": [{"geo_id": "87532", "do_date": "2014-01-01", "total_pop": 18756.0, "households": 6429.0, "male_pop": 9280.0, "female_pop": 9476.0, "median_age": 39.3, "male_under_5": 602.0, "male_5_to_9": 704.0, "male_10_to_14": 660.0, "male_15_to_17": 347.0, "male_18_to_19": 319.0, "male_20": 227.0, "male_21": 101.0, "male_22_to_24": 282.0, "male_25_to_29": 510.0, "male_30_to_34": 606.0, "male_35_to_39": 465.0, "male_40_to_44": 588.0, "male_45_to_49": 422.0, "male_50_to_54": 624.0, "male_55_to_59": 783.0, "male_60_to_61": 296.0, "male_62_to_64": 305.0, "male_65_to_66": 273.0, "male_67_to_69": 224.0, "male_70_to_74": 340.0, "male_75_to_79": 228.0, "male_80_to_84": 213.0, "male_85_and_over": 161.0, "female_under_5": 875.0, "female_5_to_9": 552.0, "female_10_to_14": 592.0, "female_15_to_17": 329.0, "female_18_to_19": 313.0, "female_20": 77.0, "female_21": 100.0, "female_22_to_24": 234.0, "female_25_to_29": 528.0, "female_30_to_34": 430.0, "female_35_to_39": 638.0, "female_40_to_44": 439.0, "female_45_to_49": 455.0, "female_50_to_54": 659.0, "female_55_to_59": 638.0, "female_60_to_61": 278.0, "female_62_to_64": 481.0, "female_65_to_66": 391.0, "female_67_to_69": 314.0, "female_70_to_74": 380.0, "female_75_to_79": 239.0, "female_80_to_84": 248.0, "female_85_and_over": 286.0, "white_pop": 2605.0, "population_1_year_and_over": NaN, "population_3_years_over": 17844.0, "pop_5_years_over": NaN, "pop_15_and_over": NaN, "pop_16_over": NaN, "pop_25_years_over": 12442.0, "pop_25_64": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "not_us_citizen_pop": 839.0, "black_pop": 104.0, "asian_pop": 164.0, "hispanic_pop": 14465.0, "amerindian_pop": 1272.0, "other_race_pop": 39.0, "two_or_more_races_pop": 107.0, "hispanic_any_race": 14465.0, "not_hispanic_pop": 4291.0, "asian_male_45_54": 6.0, "asian_male_55_64": 22.0, "black_male_45_54": 0.0, "black_male_55_64": 8.0, "hispanic_male_45_54": 824.0, "hispanic_male_55_64": 1072.0, "white_male_45_54": 145.0, "white_male_55_64": 193.0, "median_income": NaN, "income_per_capita": NaN, "income_less_10000": NaN, "income_10000_14999": NaN, "income_15000_19999": NaN, "income_20000_24999": NaN, "income_25000_29999": NaN, "income_30000_34999": NaN, "income_35000_39999": NaN, "income_40000_44999": NaN, "income_45000_49999": NaN, "income_50000_59999": NaN, "income_60000_74999": NaN, "income_75000_99999": NaN, "income_100000_124999": NaN, "income_125000_149999": NaN, "income_150000_199999": NaN, "income_200000_or_more": NaN, "pop_determined_poverty_status": NaN, "poverty": NaN, "gini_index": NaN, "housing_units": 8522.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 687.0, "owner_occupied_housing_units_lower_value_quartile": 109400.0, "owner_occupied_housing_units_median_value": 174500.0, "owner_occupied_housing_units_upper_value_quartile": 260900.0, "occupied_housing_units": 6429.0, "housing_units_renter_occupied": 2127.0, "vacant_housing_units": 2093.0, "vacant_housing_units_for_rent": 266.0, "vacant_housing_units_for_sale": 95.0, "dwellings_1_units_detached": 4685.0, "dwellings_1_units_attached": 97.0, "dwellings_2_units": 273.0, "dwellings_3_to_4_units": 123.0, "dwellings_5_to_9_units": 135.0, "dwellings_10_to_19_units": 15.0, "dwellings_20_to_49_units": 66.0, "dwellings_50_or_more_units": 14.0, "mobile_homes": 3095.0, "housing_built_2005_or_later": 30.0, "housing_built_2000_to_2004": 91.0, "housing_built_1939_or_earlier": 329.0, "median_year_structure_built": 1981.0, "married_households": 2427.0, "nonfamily_households": 2626.0, "family_households": 3803.0, "households_public_asst_or_food_stamps": NaN, "male_male_households": 0.0, "female_female_households": 19.0, "children": 4661.0, "children_in_single_female_hh": 2192.0, "median_rent": 536.0, "percent_income_spent_on_rent": NaN, "rent_burden_not_computed": NaN, "rent_over_50_percent": NaN, "rent_40_to_50_percent": NaN, "rent_35_to_40_percent": NaN, "rent_30_to_35_percent": NaN, "rent_25_to_30_percent": NaN, "rent_20_to_25_percent": NaN, "rent_15_to_20_percent": NaN, "rent_10_to_15_percent": NaN, "rent_under_10_percent": NaN, "owner_occupied_housing_units": 4302.0, "million_dollar_housing_units": 14.0, "mortgaged_housing_units": 1775.0, "different_house_year_ago_different_city": NaN, "different_house_year_ago_same_city": NaN, "families_with_young_children": NaN, "two_parent_families_with_young_children": NaN, "two_parents_in_labor_force_families_with_young_children": NaN, "two_parents_father_in_labor_force_families_with_young_children": NaN, "two_parents_mother_in_labor_force_families_with_young_children": NaN, "two_parents_not_in_labor_force_families_with_young_children": NaN, "one_parent_families_with_young_children": NaN, "father_one_parent_families_with_young_children": NaN, "father_in_labor_force_one_parent_families_with_young_children": NaN, "commute_less_10_mins": NaN, "commute_10_14_mins": NaN, "commute_15_19_mins": NaN, "commute_20_24_mins": NaN, "commute_25_29_mins": NaN, "commute_30_34_mins": NaN, "commute_35_44_mins": NaN, "commute_60_more_mins": NaN, "commute_45_59_mins": NaN, "commuters_16_over": NaN, "walked_to_work": NaN, "worked_at_home": NaN, "no_car": NaN, "no_cars": NaN, "one_car": NaN, "two_cars": NaN, "three_cars": NaN, "four_more_cars": NaN, "aggregate_travel_time_to_work": NaN, "commuters_by_public_transportation": NaN, "commuters_by_bus": NaN, "commuters_by_car_truck_van": NaN, "commuters_by_carpool": NaN, "commuters_by_subway_or_elevated": NaN, "commuters_drove_alone": NaN, "group_quarters": 96.0, "associates_degree": 1097.0, "bachelors_degree": 1587.0, "high_school_diploma": 3145.0, "less_one_year_college": 921.0, "masters_degree": 755.0, "one_year_more_college": 2170.0, "less_than_high_school_graduate": NaN, "high_school_including_ged": NaN, "bachelors_degree_2": NaN, "bachelors_degree_or_higher_25_64": NaN, "graduate_professional_degree": NaN, "some_college_and_associates_degree": NaN, "male_45_64_associates_degree": 212.0, "male_45_64_bachelors_degree": 264.0, "male_45_64_graduate_degree": 167.0, "male_45_64_less_than_9_grade": 188.0, "male_45_64_grade_9_12": 281.0, "male_45_64_high_school": 757.0, "male_45_64_some_college": 561.0, "male_45_to_64": 2430.0, "employed_pop": NaN, "unemployed_pop": NaN, "pop_in_labor_force": NaN, "not_in_labor_force": NaN, "workers_16_and_over": NaN, "armed_forces": NaN, "civilian_labor_force": NaN, "employed_agriculture_forestry_fishing_hunting_mining": NaN, "employed_arts_entertainment_recreation_accommodation_food": NaN, "employed_construction": NaN, "employed_education_health_social": NaN, "employed_finance_insurance_real_estate": NaN, "employed_information": NaN, "employed_manufacturing": NaN, "employed_other_services_not_public_admin": NaN, "employed_public_administration": NaN, "employed_retail_trade": NaN, "employed_science_management_admin_waste": NaN, "employed_transportation_warehousing_utilities": NaN, "employed_wholesale_trade": NaN, "occupation_management_arts": NaN, "occupation_natural_resources_construction_maintenance": NaN, "occupation_production_transportation_material": NaN, "occupation_sales_office": NaN, "occupation_services": NaN, "management_business_sci_arts_employed": NaN, "sales_office_employed": NaN, "in_grades_1_to_4": 1018.0, "in_grades_5_to_8": 1011.0, "in_grades_9_to_12": 877.0, "in_school": 4517.0, "in_undergrad_college": 882.0, "speak_only_english_at_home": NaN, "speak_spanish_at_home": NaN, "speak_spanish_at_home_low_english": NaN}, {"geo_id": "87533", "do_date": "2014-01-01", "total_pop": 133.0, "households": 58.0, "male_pop": 49.0, "female_pop": 84.0, "median_age": 25.8, "male_under_5": 0.0, "male_5_to_9": 12.0, "male_10_to_14": 8.0, "male_15_to_17": 4.0, "male_18_to_19": 8.0, "male_20": 0.0, "male_21": 0.0, "male_22_to_24": 0.0, "male_25_to_29": 3.0, "male_30_to_34": 3.0, "male_35_to_39": 3.0, "male_40_to_44": 5.0, "male_45_to_49": 0.0, "male_50_to_54": 0.0, "male_55_to_59": 3.0, "male_60_to_61": 0.0, "male_62_to_64": 0.0, "male_65_to_66": 0.0, "male_67_to_69": 0.0, "male_70_to_74": 0.0, "male_75_to_79": 0.0, "male_80_to_84": 0.0, "male_85_and_over": 0.0, "female_under_5": 6.0, "female_5_to_9": 22.0, "female_10_to_14": 0.0, "female_15_to_17": 3.0, "female_18_to_19": 0.0, "female_20": 0.0, "female_21": 0.0, "female_22_to_24": 2.0, "female_25_to_29": 2.0, "female_30_to_34": 6.0, "female_35_to_39": 14.0, "female_40_to_44": 9.0, "female_45_to_49": 0.0, "female_50_to_54": 0.0, "female_55_to_59": 7.0, "female_60_to_61": 0.0, "female_62_to_64": 3.0, "female_65_to_66": 3.0, "female_67_to_69": 0.0, "female_70_to_74": 0.0, "female_75_to_79": 0.0, "female_80_to_84": 3.0, "female_85_and_over": 4.0, "white_pop": 7.0, "population_1_year_and_over": NaN, "population_3_years_over": 133.0, "pop_5_years_over": NaN, "pop_15_and_over": NaN, "pop_16_over": NaN, "pop_25_years_over": 68.0, "pop_25_64": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "not_us_citizen_pop": 0.0, "black_pop": 0.0, "asian_pop": 0.0, "hispanic_pop": 126.0, "amerindian_pop": 0.0, "other_race_pop": 0.0, "two_or_more_races_pop": 0.0, "hispanic_any_race": 126.0, "not_hispanic_pop": 7.0, "asian_male_45_54": 0.0, "asian_male_55_64": 0.0, "black_male_45_54": 0.0, "black_male_55_64": 0.0, "hispanic_male_45_54": 0.0, "hispanic_male_55_64": 3.0, "white_male_45_54": 0.0, "white_male_55_64": 0.0, "median_income": NaN, "income_per_capita": NaN, "income_less_10000": NaN, "income_10000_14999": NaN, "income_15000_19999": NaN, "income_20000_24999": NaN, "income_25000_29999": NaN, "income_30000_34999": NaN, "income_35000_39999": NaN, "income_40000_44999": NaN, "income_45000_49999": NaN, "income_50000_59999": NaN, "income_60000_74999": NaN, "income_75000_99999": NaN, "income_100000_124999": NaN, "income_125000_149999": NaN, "income_150000_199999": NaN, "income_200000_or_more": NaN, "pop_determined_poverty_status": NaN, "poverty": NaN, "gini_index": NaN, "housing_units": 68.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 738.0, "owner_occupied_housing_units_lower_value_quartile": 13400.0, "owner_occupied_housing_units_median_value": 17900.0, "owner_occupied_housing_units_upper_value_quartile": NaN, "occupied_housing_units": 58.0, "housing_units_renter_occupied": 19.0, "vacant_housing_units": 10.0, "vacant_housing_units_for_rent": 0.0, "vacant_housing_units_for_sale": 0.0, "dwellings_1_units_detached": 8.0, "dwellings_1_units_attached": 0.0, "dwellings_2_units": 0.0, "dwellings_3_to_4_units": 0.0, "dwellings_5_to_9_units": 0.0, "dwellings_10_to_19_units": 0.0, "dwellings_20_to_49_units": 0.0, "dwellings_50_or_more_units": 0.0, "mobile_homes": 60.0, "housing_built_2005_or_later": 0.0, "housing_built_2000_to_2004": 0.0, "housing_built_1939_or_earlier": 2.0, "median_year_structure_built": 1993.0, "married_households": 8.0, "nonfamily_households": 36.0, "family_households": 22.0, "households_public_asst_or_food_stamps": NaN, "male_male_households": 0.0, "female_female_households": 0.0, "children": 55.0, "children_in_single_female_hh": 37.0, "median_rent": 347.0, "percent_income_spent_on_rent": NaN, "rent_burden_not_computed": NaN, "rent_over_50_percent": NaN, "rent_40_to_50_percent": NaN, "rent_35_to_40_percent": NaN, "rent_30_to_35_percent": NaN, "rent_25_to_30_percent": NaN, "rent_20_to_25_percent": NaN, "rent_15_to_20_percent": NaN, "rent_10_to_15_percent": NaN, "rent_under_10_percent": NaN, "owner_occupied_housing_units": 39.0, "million_dollar_housing_units": 0.0, "mortgaged_housing_units": 5.0, "different_house_year_ago_different_city": NaN, "different_house_year_ago_same_city": NaN, "families_with_young_children": NaN, "two_parent_families_with_young_children": NaN, "two_parents_in_labor_force_families_with_young_children": NaN, "two_parents_father_in_labor_force_families_with_young_children": NaN, "two_parents_mother_in_labor_force_families_with_young_children": NaN, "two_parents_not_in_labor_force_families_with_young_children": NaN, "one_parent_families_with_young_children": NaN, "father_one_parent_families_with_young_children": NaN, "father_in_labor_force_one_parent_families_with_young_children": NaN, "commute_less_10_mins": NaN, "commute_10_14_mins": NaN, "commute_15_19_mins": NaN, "commute_20_24_mins": NaN, "commute_25_29_mins": NaN, "commute_30_34_mins": NaN, "commute_35_44_mins": NaN, "commute_60_more_mins": NaN, "commute_45_59_mins": NaN, "commuters_16_over": NaN, "walked_to_work": NaN, "worked_at_home": NaN, "no_car": NaN, "no_cars": NaN, "one_car": NaN, "two_cars": NaN, "three_cars": NaN, "four_more_cars": NaN, "aggregate_travel_time_to_work": NaN, "commuters_by_public_transportation": NaN, "commuters_by_bus": NaN, "commuters_by_car_truck_van": NaN, "commuters_by_carpool": NaN, "commuters_by_subway_or_elevated": NaN, "commuters_drove_alone": NaN, "group_quarters": 0.0, "associates_degree": 6.0, "bachelors_degree": 9.0, "high_school_diploma": 12.0, "less_one_year_college": 15.0, "masters_degree": 3.0, "one_year_more_college": 13.0, "less_than_high_school_graduate": NaN, "high_school_including_ged": NaN, "bachelors_degree_2": NaN, "bachelors_degree_or_higher_25_64": NaN, "graduate_professional_degree": NaN, "some_college_and_associates_degree": NaN, "male_45_64_associates_degree": 0.0, "male_45_64_bachelors_degree": 3.0, "male_45_64_graduate_degree": 0.0, "male_45_64_less_than_9_grade": 0.0, "male_45_64_grade_9_12": 0.0, "male_45_64_high_school": 0.0, "male_45_64_some_college": 0.0, "male_45_to_64": 3.0, "employed_pop": NaN, "unemployed_pop": NaN, "pop_in_labor_force": NaN, "not_in_labor_force": NaN, "workers_16_and_over": NaN, "armed_forces": NaN, "civilian_labor_force": NaN, "employed_agriculture_forestry_fishing_hunting_mining": NaN, "employed_arts_entertainment_recreation_accommodation_food": NaN, "employed_construction": NaN, "employed_education_health_social": NaN, "employed_finance_insurance_real_estate": NaN, "employed_information": NaN, "employed_manufacturing": NaN, "employed_other_services_not_public_admin": NaN, "employed_public_administration": NaN, "employed_retail_trade": NaN, "employed_science_management_admin_waste": NaN, "employed_transportation_warehousing_utilities": NaN, "employed_wholesale_trade": NaN, "occupation_management_arts": NaN, "occupation_natural_resources_construction_maintenance": NaN, "occupation_production_transportation_material": NaN, "occupation_sales_office": NaN, "occupation_services": NaN, "management_business_sci_arts_employed": NaN, "sales_office_employed": NaN, "in_grades_1_to_4": 24.0, "in_grades_5_to_8": 8.0, "in_grades_9_to_12": 7.0, "in_school": 58.0, "in_undergrad_college": 3.0, "speak_only_english_at_home": NaN, "speak_spanish_at_home": NaN, "speak_spanish_at_home_low_english": NaN}, {"geo_id": "87520", "do_date": "2014-01-01", "total_pop": 1237.0, "households": 518.0, "male_pop": 633.0, "female_pop": 604.0, "median_age": 56.1, "male_under_5": 30.0, "male_5_to_9": 71.0, "male_10_to_14": 47.0, "male_15_to_17": 22.0, "male_18_to_19": 29.0, "male_20": 0.0, "male_21": 0.0, "male_22_to_24": 0.0, "male_25_to_29": 0.0, "male_30_to_34": 19.0, "male_35_to_39": 11.0, "male_40_to_44": 15.0, "male_45_to_49": 87.0, "male_50_to_54": 26.0, "male_55_to_59": 51.0, "male_60_to_61": 32.0, "male_62_to_64": 60.0, "male_65_to_66": 16.0, "male_67_to_69": 36.0, "male_70_to_74": 24.0, "male_75_to_79": 42.0, "male_80_to_84": 4.0, "male_85_and_over": 11.0, "female_under_5": 0.0, "female_5_to_9": 30.0, "female_10_to_14": 0.0, "female_15_to_17": 22.0, "female_18_to_19": 0.0, "female_20": 0.0, "female_21": 0.0, "female_22_to_24": 0.0, "female_25_to_29": 0.0, "female_30_to_34": 117.0, "female_35_to_39": 23.0, "female_40_to_44": 0.0, "female_45_to_49": 14.0, "female_50_to_54": 23.0, "female_55_to_59": 133.0, "female_60_to_61": 33.0, "female_62_to_64": 60.0, "female_65_to_66": 22.0, "female_67_to_69": 27.0, "female_70_to_74": 24.0, "female_75_to_79": 36.0, "female_80_to_84": 21.0, "female_85_and_over": 19.0, "white_pop": 299.0, "population_1_year_and_over": NaN, "population_3_years_over": 1207.0, "pop_5_years_over": NaN, "pop_15_and_over": NaN, "pop_16_over": NaN, "pop_25_years_over": 986.0, "pop_25_64": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "not_us_citizen_pop": 0.0, "black_pop": 0.0, "asian_pop": 0.0, "hispanic_pop": 863.0, "amerindian_pop": 61.0, "other_race_pop": 14.0, "two_or_more_races_pop": 0.0, "hispanic_any_race": 863.0, "not_hispanic_pop": 374.0, "asian_male_45_54": 0.0, "asian_male_55_64": 0.0, "black_male_45_54": 0.0, "black_male_55_64": 0.0, "hispanic_male_45_54": 90.0, "hispanic_male_55_64": 34.0, "white_male_45_54": 23.0, "white_male_55_64": 99.0, "median_income": NaN, "income_per_capita": NaN, "income_less_10000": NaN, "income_10000_14999": NaN, "income_15000_19999": NaN, "income_20000_24999": NaN, "income_25000_29999": NaN, "income_30000_34999": NaN, "income_35000_39999": NaN, "income_40000_44999": NaN, "income_45000_49999": NaN, "income_50000_59999": NaN, "income_60000_74999": NaN, "income_75000_99999": NaN, "income_100000_124999": NaN, "income_125000_149999": NaN, "income_150000_199999": NaN, "income_200000_or_more": NaN, "pop_determined_poverty_status": NaN, "poverty": NaN, "gini_index": NaN, "housing_units": 1275.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 720.0, "owner_occupied_housing_units_lower_value_quartile": 132400.0, "owner_occupied_housing_units_median_value": 240000.0, "owner_occupied_housing_units_upper_value_quartile": 328600.0, "occupied_housing_units": 518.0, "housing_units_renter_occupied": 66.0, "vacant_housing_units": 757.0, "vacant_housing_units_for_rent": 9.0, "vacant_housing_units_for_sale": 38.0, "dwellings_1_units_detached": 892.0, "dwellings_1_units_attached": 15.0, "dwellings_2_units": 9.0, "dwellings_3_to_4_units": 52.0, "dwellings_5_to_9_units": 82.0, "dwellings_10_to_19_units": 0.0, "dwellings_20_to_49_units": 0.0, "dwellings_50_or_more_units": 0.0, "mobile_homes": 225.0, "housing_built_2005_or_later": 45.0, "housing_built_2000_to_2004": 10.0, "housing_built_1939_or_earlier": 32.0, "median_year_structure_built": 1982.0, "married_households": 214.0, "nonfamily_households": 246.0, "family_households": 272.0, "households_public_asst_or_food_stamps": NaN, "male_male_households": 0.0, "female_female_households": 0.0, "children": 222.0, "children_in_single_female_hh": 62.0, "median_rent": 518.0, "percent_income_spent_on_rent": NaN, "rent_burden_not_computed": NaN, "rent_over_50_percent": NaN, "rent_40_to_50_percent": NaN, "rent_35_to_40_percent": NaN, "rent_30_to_35_percent": NaN, "rent_25_to_30_percent": NaN, "rent_20_to_25_percent": NaN, "rent_15_to_20_percent": NaN, "rent_10_to_15_percent": NaN, "rent_under_10_percent": NaN, "owner_occupied_housing_units": 452.0, "million_dollar_housing_units": 11.0, "mortgaged_housing_units": 205.0, "different_house_year_ago_different_city": NaN, "different_house_year_ago_same_city": NaN, "families_with_young_children": NaN, "two_parent_families_with_young_children": NaN, "two_parents_in_labor_force_families_with_young_children": NaN, "two_parents_father_in_labor_force_families_with_young_children": NaN, "two_parents_mother_in_labor_force_families_with_young_children": NaN, "two_parents_not_in_labor_force_families_with_young_children": NaN, "one_parent_families_with_young_children": NaN, "father_one_parent_families_with_young_children": NaN, "father_in_labor_force_one_parent_families_with_young_children": NaN, "commute_less_10_mins": NaN, "commute_10_14_mins": NaN, "commute_15_19_mins": NaN, "commute_20_24_mins": NaN, "commute_25_29_mins": NaN, "commute_30_34_mins": NaN, "commute_35_44_mins": NaN, "commute_60_more_mins": NaN, "commute_45_59_mins": NaN, "commuters_16_over": NaN, "walked_to_work": NaN, "worked_at_home": NaN, "no_car": NaN, "no_cars": NaN, "one_car": NaN, "two_cars": NaN, "three_cars": NaN, "four_more_cars": NaN, "aggregate_travel_time_to_work": NaN, "commuters_by_public_transportation": NaN, "commuters_by_bus": NaN, "commuters_by_car_truck_van": NaN, "commuters_by_carpool": NaN, "commuters_by_subway_or_elevated": NaN, "commuters_drove_alone": NaN, "group_quarters": 0.0, "associates_degree": 73.0, "bachelors_degree": 161.0, "high_school_diploma": 254.0, "less_one_year_college": 114.0, "masters_degree": 154.0, "one_year_more_college": 65.0, "less_than_high_school_graduate": NaN, "high_school_including_ged": NaN, "bachelors_degree_2": NaN, "bachelors_degree_or_higher_25_64": NaN, "graduate_professional_degree": NaN, "some_college_and_associates_degree": NaN, "male_45_64_associates_degree": 10.0, "male_45_64_bachelors_degree": 66.0, "male_45_64_graduate_degree": 45.0, "male_45_64_less_than_9_grade": 0.0, "male_45_64_grade_9_12": 23.0, "male_45_64_high_school": 112.0, "male_45_64_some_college": 0.0, "male_45_to_64": 256.0, "employed_pop": NaN, "unemployed_pop": NaN, "pop_in_labor_force": NaN, "not_in_labor_force": NaN, "workers_16_and_over": NaN, "armed_forces": NaN, "civilian_labor_force": NaN, "employed_agriculture_forestry_fishing_hunting_mining": NaN, "employed_arts_entertainment_recreation_accommodation_food": NaN, "employed_construction": NaN, "employed_education_health_social": NaN, "employed_finance_insurance_real_estate": NaN, "employed_information": NaN, "employed_manufacturing": NaN, "employed_other_services_not_public_admin": NaN, "employed_public_administration": NaN, "employed_retail_trade": NaN, "employed_science_management_admin_waste": NaN, "employed_transportation_warehousing_utilities": NaN, "employed_wholesale_trade": NaN, "occupation_management_arts": NaN, "occupation_natural_resources_construction_maintenance": NaN, "occupation_production_transportation_material": NaN, "occupation_sales_office": NaN, "occupation_services": NaN, "management_business_sci_arts_employed": NaN, "sales_office_employed": NaN, "in_grades_1_to_4": 83.0, "in_grades_5_to_8": 33.0, "in_grades_9_to_12": 73.0, "in_school": 230.0, "in_undergrad_college": 0.0, "speak_only_english_at_home": NaN, "speak_spanish_at_home": NaN, "speak_spanish_at_home_low_english": NaN}, {"geo_id": "87017", "do_date": "2014-01-01", "total_pop": 346.0, "households": 112.0, "male_pop": 150.0, "female_pop": 196.0, "median_age": 50.9, "male_under_5": 0.0, "male_5_to_9": 0.0, "male_10_to_14": 12.0, "male_15_to_17": 10.0, "male_18_to_19": 9.0, "male_20": 0.0, "male_21": 0.0, "male_22_to_24": 0.0, "male_25_to_29": 0.0, "male_30_to_34": 14.0, "male_35_to_39": 0.0, "male_40_to_44": 0.0, "male_45_to_49": 35.0, "male_50_to_54": 0.0, "male_55_to_59": 30.0, "male_60_to_61": 15.0, "male_62_to_64": 11.0, "male_65_to_66": 10.0, "male_67_to_69": 0.0, "male_70_to_74": 0.0, "male_75_to_79": 0.0, "male_80_to_84": 0.0, "male_85_and_over": 4.0, "female_under_5": 0.0, "female_5_to_9": 0.0, "female_10_to_14": 15.0, "female_15_to_17": 5.0, "female_18_to_19": 32.0, "female_20": 0.0, "female_21": 0.0, "female_22_to_24": 8.0, "female_25_to_29": 0.0, "female_30_to_34": 0.0, "female_35_to_39": 11.0, "female_40_to_44": 14.0, "female_45_to_49": 0.0, "female_50_to_54": 9.0, "female_55_to_59": 23.0, "female_60_to_61": 0.0, "female_62_to_64": 9.0, "female_65_to_66": 0.0, "female_67_to_69": 0.0, "female_70_to_74": 0.0, "female_75_to_79": 0.0, "female_80_to_84": 70.0, "female_85_and_over": 0.0, "white_pop": 33.0, "population_1_year_and_over": NaN, "population_3_years_over": 346.0, "pop_5_years_over": NaN, "pop_15_and_over": NaN, "pop_16_over": NaN, "pop_25_years_over": 255.0, "pop_25_64": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "not_us_citizen_pop": 61.0, "black_pop": 0.0, "asian_pop": 52.0, "hispanic_pop": 261.0, "amerindian_pop": 0.0, "other_race_pop": 0.0, "two_or_more_races_pop": 0.0, "hispanic_any_race": 261.0, "not_hispanic_pop": 85.0, "asian_male_45_54": 4.0, "asian_male_55_64": 0.0, "black_male_45_54": 0.0, "black_male_55_64": 0.0, "hispanic_male_45_54": 31.0, "hispanic_male_55_64": 56.0, "white_male_45_54": 0.0, "white_male_55_64": 0.0, "median_income": NaN, "income_per_capita": NaN, "income_less_10000": NaN, "income_10000_14999": NaN, "income_15000_19999": NaN, "income_20000_24999": NaN, "income_25000_29999": NaN, "income_30000_34999": NaN, "income_35000_39999": NaN, "income_40000_44999": NaN, "income_45000_49999": NaN, "income_50000_59999": NaN, "income_60000_74999": NaN, "income_75000_99999": NaN, "income_100000_124999": NaN, "income_125000_149999": NaN, "income_150000_199999": NaN, "income_200000_or_more": NaN, "pop_determined_poverty_status": NaN, "poverty": NaN, "gini_index": NaN, "housing_units": 240.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 1045.0, "owner_occupied_housing_units_lower_value_quartile": 114200.0, "owner_occupied_housing_units_median_value": 170800.0, "owner_occupied_housing_units_upper_value_quartile": 199700.0, "occupied_housing_units": 112.0, "housing_units_renter_occupied": 23.0, "vacant_housing_units": 128.0, "vacant_housing_units_for_rent": 6.0, "vacant_housing_units_for_sale": 5.0, "dwellings_1_units_detached": 111.0, "dwellings_1_units_attached": 0.0, "dwellings_2_units": 0.0, "dwellings_3_to_4_units": 0.0, "dwellings_5_to_9_units": 0.0, "dwellings_10_to_19_units": 0.0, "dwellings_20_to_49_units": 0.0, "dwellings_50_or_more_units": 0.0, "mobile_homes": 129.0, "housing_built_2005_or_later": 0.0, "housing_built_2000_to_2004": 0.0, "housing_built_1939_or_earlier": 0.0, "median_year_structure_built": 1976.0, "married_households": 45.0, "nonfamily_households": 36.0, "family_households": 76.0, "households_public_asst_or_food_stamps": NaN, "male_male_households": 0.0, "female_female_households": 0.0, "children": 42.0, "children_in_single_female_hh": 20.0, "median_rent": NaN, "percent_income_spent_on_rent": NaN, "rent_burden_not_computed": NaN, "rent_over_50_percent": NaN, "rent_40_to_50_percent": NaN, "rent_35_to_40_percent": NaN, "rent_30_to_35_percent": NaN, "rent_25_to_30_percent": NaN, "rent_20_to_25_percent": NaN, "rent_15_to_20_percent": NaN, "rent_10_to_15_percent": NaN, "rent_under_10_percent": NaN, "owner_occupied_housing_units": 89.0, "million_dollar_housing_units": 0.0, "mortgaged_housing_units": 31.0, "different_house_year_ago_different_city": NaN, "different_house_year_ago_same_city": NaN, "families_with_young_children": NaN, "two_parent_families_with_young_children": NaN, "two_parents_in_labor_force_families_with_young_children": NaN, "two_parents_father_in_labor_force_families_with_young_children": NaN, "two_parents_mother_in_labor_force_families_with_young_children": NaN, "two_parents_not_in_labor_force_families_with_young_children": NaN, "one_parent_families_with_young_children": NaN, "father_one_parent_families_with_young_children": NaN, "father_in_labor_force_one_parent_families_with_young_children": NaN, "commute_less_10_mins": NaN, "commute_10_14_mins": NaN, "commute_15_19_mins": NaN, "commute_20_24_mins": NaN, "commute_25_29_mins": NaN, "commute_30_34_mins": NaN, "commute_35_44_mins": NaN, "commute_60_more_mins": NaN, "commute_45_59_mins": NaN, "commuters_16_over": NaN, "walked_to_work": NaN, "worked_at_home": NaN, "no_car": NaN, "no_cars": NaN, "one_car": NaN, "two_cars": NaN, "three_cars": NaN, "four_more_cars": NaN, "aggregate_travel_time_to_work": NaN, "commuters_by_public_transportation": NaN, "commuters_by_bus": NaN, "commuters_by_car_truck_van": NaN, "commuters_by_carpool": NaN, "commuters_by_subway_or_elevated": NaN, "commuters_drove_alone": NaN, "group_quarters": 0.0, "associates_degree": 0.0, "bachelors_degree": 20.0, "high_school_diploma": 161.0, "less_one_year_college": 28.0, "masters_degree": 6.0, "one_year_more_college": 0.0, "less_than_high_school_graduate": NaN, "high_school_including_ged": NaN, "bachelors_degree_2": NaN, "bachelors_degree_or_higher_25_64": NaN, "graduate_professional_degree": NaN, "some_college_and_associates_degree": NaN, "male_45_64_associates_degree": 0.0, "male_45_64_bachelors_degree": 6.0, "male_45_64_graduate_degree": 0.0, "male_45_64_less_than_9_grade": 0.0, "male_45_64_grade_9_12": 11.0, "male_45_64_high_school": 60.0, "male_45_64_some_college": 14.0, "male_45_to_64": 91.0, "employed_pop": NaN, "unemployed_pop": NaN, "pop_in_labor_force": NaN, "not_in_labor_force": NaN, "workers_16_and_over": NaN, "armed_forces": NaN, "civilian_labor_force": NaN, "employed_agriculture_forestry_fishing_hunting_mining": NaN, "employed_arts_entertainment_recreation_accommodation_food": NaN, "employed_construction": NaN, "employed_education_health_social": NaN, "employed_finance_insurance_real_estate": NaN, "employed_information": NaN, "employed_manufacturing": NaN, "employed_other_services_not_public_admin": NaN, "employed_public_administration": NaN, "employed_retail_trade": NaN, "employed_science_management_admin_waste": NaN, "employed_transportation_warehousing_utilities": NaN, "employed_wholesale_trade": NaN, "occupation_management_arts": NaN, "occupation_natural_resources_construction_maintenance": NaN, "occupation_production_transportation_material": NaN, "occupation_sales_office": NaN, "occupation_services": NaN, "management_business_sci_arts_employed": NaN, "sales_office_employed": NaN, "in_grades_1_to_4": 0.0, "in_grades_5_to_8": 27.0, "in_grades_9_to_12": 15.0, "in_school": 96.0, "in_undergrad_college": 40.0, "speak_only_english_at_home": NaN, "speak_spanish_at_home": NaN, "speak_spanish_at_home_low_english": NaN}, {"geo_id": "87528", "do_date": "2014-01-01", "total_pop": 3505.0, "households": 727.0, "male_pop": 1758.0, "female_pop": 1747.0, "median_age": 27.9, "male_under_5": 260.0, "male_5_to_9": 194.0, "male_10_to_14": 159.0, "male_15_to_17": 121.0, "male_18_to_19": 38.0, "male_20": 5.0, "male_21": 0.0, "male_22_to_24": 112.0, "male_25_to_29": 202.0, "male_30_to_34": 56.0, "male_35_to_39": 106.0, "male_40_to_44": 66.0, "male_45_to_49": 37.0, "male_50_to_54": 111.0, "male_55_to_59": 89.0, "male_60_to_61": 58.0, "male_62_to_64": 17.0, "male_65_to_66": 12.0, "male_67_to_69": 11.0, "male_70_to_74": 85.0, "male_75_to_79": 10.0, "male_80_to_84": 9.0, "male_85_and_over": 0.0, "female_under_5": 151.0, "female_5_to_9": 165.0, "female_10_to_14": 189.0, "female_15_to_17": 82.0, "female_18_to_19": 45.0, "female_20": 1.0, "female_21": 57.0, "female_22_to_24": 44.0, "female_25_to_29": 106.0, "female_30_to_34": 110.0, "female_35_to_39": 103.0, "female_40_to_44": 35.0, "female_45_to_49": 173.0, "female_50_to_54": 181.0, "female_55_to_59": 104.0, "female_60_to_61": 22.0, "female_62_to_64": 41.0, "female_65_to_66": 21.0, "female_67_to_69": 44.0, "female_70_to_74": 55.0, "female_75_to_79": 13.0, "female_80_to_84": 5.0, "female_85_and_over": 0.0, "white_pop": 84.0, "population_1_year_and_over": NaN, "population_3_years_over": 3311.0, "pop_5_years_over": NaN, "pop_15_and_over": NaN, "pop_16_over": NaN, "pop_25_years_over": 1882.0, "pop_25_64": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "not_us_citizen_pop": 34.0, "black_pop": 16.0, "asian_pop": 0.0, "hispanic_pop": 775.0, "amerindian_pop": 2566.0, "other_race_pop": 10.0, "two_or_more_races_pop": 54.0, "hispanic_any_race": 775.0, "not_hispanic_pop": 2730.0, "asian_male_45_54": 0.0, "asian_male_55_64": 0.0, "black_male_45_54": 0.0, "black_male_55_64": 7.0, "hispanic_male_45_54": 10.0, "hispanic_male_55_64": 37.0, "white_male_45_54": 0.0, "white_male_55_64": 8.0, "median_income": NaN, "income_per_capita": NaN, "income_less_10000": NaN, "income_10000_14999": NaN, "income_15000_19999": NaN, "income_20000_24999": NaN, "income_25000_29999": NaN, "income_30000_34999": NaN, "income_35000_39999": NaN, "income_40000_44999": NaN, "income_45000_49999": NaN, "income_50000_59999": NaN, "income_60000_74999": NaN, "income_75000_99999": NaN, "income_100000_124999": NaN, "income_125000_149999": NaN, "income_150000_199999": NaN, "income_200000_or_more": NaN, "pop_determined_poverty_status": NaN, "poverty": NaN, "gini_index": NaN, "housing_units": 1024.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 501.0, "owner_occupied_housing_units_lower_value_quartile": 43800.0, "owner_occupied_housing_units_median_value": 92700.0, "owner_occupied_housing_units_upper_value_quartile": 163800.0, "occupied_housing_units": 727.0, "housing_units_renter_occupied": 227.0, "vacant_housing_units": 297.0, "vacant_housing_units_for_rent": 18.0, "vacant_housing_units_for_sale": 0.0, "dwellings_1_units_detached": 684.0, "dwellings_1_units_attached": 2.0, "dwellings_2_units": 64.0, "dwellings_3_to_4_units": 19.0, "dwellings_5_to_9_units": 5.0, "dwellings_10_to_19_units": 0.0, "dwellings_20_to_49_units": 0.0, "dwellings_50_or_more_units": 0.0, "mobile_homes": 250.0, "housing_built_2005_or_later": 23.0, "housing_built_2000_to_2004": 26.0, "housing_built_1939_or_earlier": 0.0, "median_year_structure_built": 1984.0, "married_households": 236.0, "nonfamily_households": 274.0, "family_households": 453.0, "households_public_asst_or_food_stamps": NaN, "male_male_households": 0.0, "female_female_households": 0.0, "children": 1321.0, "children_in_single_female_hh": 568.0, "median_rent": 364.0, "percent_income_spent_on_rent": NaN, "rent_burden_not_computed": NaN, "rent_over_50_percent": NaN, "rent_40_to_50_percent": NaN, "rent_35_to_40_percent": NaN, "rent_30_to_35_percent": NaN, "rent_25_to_30_percent": NaN, "rent_20_to_25_percent": NaN, "rent_15_to_20_percent": NaN, "rent_10_to_15_percent": NaN, "rent_under_10_percent": NaN, "owner_occupied_housing_units": 500.0, "million_dollar_housing_units": 0.0, "mortgaged_housing_units": 66.0, "different_house_year_ago_different_city": NaN, "different_house_year_ago_same_city": NaN, "families_with_young_children": NaN, "two_parent_families_with_young_children": NaN, "two_parents_in_labor_force_families_with_young_children": NaN, "two_parents_father_in_labor_force_families_with_young_children": NaN, "two_parents_mother_in_labor_force_families_with_young_children": NaN, "two_parents_not_in_labor_force_families_with_young_children": NaN, "one_parent_families_with_young_children": NaN, "father_one_parent_families_with_young_children": NaN, "father_in_labor_force_one_parent_families_with_young_children": NaN, "commute_less_10_mins": NaN, "commute_10_14_mins": NaN, "commute_15_19_mins": NaN, "commute_20_24_mins": NaN, "commute_25_29_mins": NaN, "commute_30_34_mins": NaN, "commute_35_44_mins": NaN, "commute_60_more_mins": NaN, "commute_45_59_mins": NaN, "commuters_16_over": NaN, "walked_to_work": NaN, "worked_at_home": NaN, "no_car": NaN, "no_cars": NaN, "one_car": NaN, "two_cars": NaN, "three_cars": NaN, "four_more_cars": NaN, "aggregate_travel_time_to_work": NaN, "commuters_by_public_transportation": NaN, "commuters_by_bus": NaN, "commuters_by_car_truck_van": NaN, "commuters_by_carpool": NaN, "commuters_by_subway_or_elevated": NaN, "commuters_drove_alone": NaN, "group_quarters": 41.0, "associates_degree": 142.0, "bachelors_degree": 115.0, "high_school_diploma": 678.0, "less_one_year_college": 86.0, "masters_degree": 78.0, "one_year_more_college": 511.0, "less_than_high_school_graduate": NaN, "high_school_including_ged": NaN, "bachelors_degree_2": NaN, "bachelors_degree_or_higher_25_64": NaN, "graduate_professional_degree": NaN, "some_college_and_associates_degree": NaN, "male_45_64_associates_degree": 22.0, "male_45_64_bachelors_degree": 37.0, "male_45_64_graduate_degree": 0.0, "male_45_64_less_than_9_grade": 3.0, "male_45_64_grade_9_12": 32.0, "male_45_64_high_school": 149.0, "male_45_64_some_college": 69.0, "male_45_to_64": 312.0, "employed_pop": NaN, "unemployed_pop": NaN, "pop_in_labor_force": NaN, "not_in_labor_force": NaN, "workers_16_and_over": NaN, "armed_forces": NaN, "civilian_labor_force": NaN, "employed_agriculture_forestry_fishing_hunting_mining": NaN, "employed_arts_entertainment_recreation_accommodation_food": NaN, "employed_construction": NaN, "employed_education_health_social": NaN, "employed_finance_insurance_real_estate": NaN, "employed_information": NaN, "employed_manufacturing": NaN, "employed_other_services_not_public_admin": NaN, "employed_public_administration": NaN, "employed_retail_trade": NaN, "employed_science_management_admin_waste": NaN, "employed_transportation_warehousing_utilities": NaN, "employed_wholesale_trade": NaN, "occupation_management_arts": NaN, "occupation_natural_resources_construction_maintenance": NaN, "occupation_production_transportation_material": NaN, "occupation_sales_office": NaN, "occupation_services": NaN, "management_business_sci_arts_employed": NaN, "sales_office_employed": NaN, "in_grades_1_to_4": 293.0, "in_grades_5_to_8": 266.0, "in_grades_9_to_12": 298.0, "in_school": 1237.0, "in_undergrad_college": 138.0, "speak_only_english_at_home": NaN, "speak_spanish_at_home": NaN, "speak_spanish_at_home_low_english": NaN}]}