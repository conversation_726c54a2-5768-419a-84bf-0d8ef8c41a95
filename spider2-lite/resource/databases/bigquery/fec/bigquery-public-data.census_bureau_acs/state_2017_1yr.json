{"table_name": "state_2017_1yr", "table_fullname": "bigquery-public-data.census_bureau_acs.state_2017_1yr", "column_names": ["geo_id", "nonfamily_households", "family_households", "median_year_structure_built", "rent_burden_not_computed", "rent_over_50_percent", "rent_40_to_50_percent", "rent_35_to_40_percent", "rent_30_to_35_percent", "rent_25_to_30_percent", "rent_20_to_25_percent", "rent_15_to_20_percent", "rent_10_to_15_percent", "rent_under_10_percent", "total_pop", "male_pop", "female_pop", "median_age", "white_pop", "black_pop", "asian_pop", "hispanic_pop", "amerindian_pop", "other_race_pop", "two_or_more_races_pop", "not_hispanic_pop", "commuters_by_public_transportation", "households", "median_income", "income_per_capita", "housing_units", "vacant_housing_units", "vacant_housing_units_for_rent", "vacant_housing_units_for_sale", "median_rent", "percent_income_spent_on_rent", "owner_occupied_housing_units", "million_dollar_housing_units", "mortgaged_housing_units", "families_with_young_children", "two_parent_families_with_young_children", "two_parents_in_labor_force_families_with_young_children", "two_parents_father_in_labor_force_families_with_young_children", "two_parents_mother_in_labor_force_families_with_young_children", "two_parents_not_in_labor_force_families_with_young_children", "one_parent_families_with_young_children", "father_one_parent_families_with_young_children", "father_in_labor_force_one_parent_families_with_young_children", "commute_10_14_mins", "commute_15_19_mins", "commute_20_24_mins", "commute_25_29_mins", "commute_30_34_mins", "commute_45_59_mins", "aggregate_travel_time_to_work", "income_less_10000", "income_10000_14999", "income_15000_19999", "income_20000_24999", "income_25000_29999", "income_30000_34999", "income_35000_39999", "income_40000_44999", "income_45000_49999", "income_50000_59999", "income_60000_74999", "income_75000_99999", "income_100000_124999", "income_125000_149999", "income_150000_199999", "income_200000_or_more", "renter_occupied_housing_units_paying_cash_median_gross_rent", "owner_occupied_housing_units_lower_value_quartile", "owner_occupied_housing_units_median_value", "owner_occupied_housing_units_upper_value_quartile", "married_households", "occupied_housing_units", "housing_units_renter_occupied", "dwellings_1_units_detached", "dwellings_1_units_attached", "dwellings_2_units", "dwellings_3_to_4_units", "dwellings_5_to_9_units", "dwellings_10_to_19_units", "dwellings_20_to_49_units", "dwellings_50_or_more_units", "mobile_homes", "housing_built_2005_or_later", "housing_built_2000_to_2004", "housing_built_1939_or_earlier", "male_under_5", "male_5_to_9", "male_10_to_14", "male_15_to_17", "male_18_to_19", "male_20", "male_21", "male_22_to_24", "male_25_to_29", "male_30_to_34", "male_35_to_39", "male_40_to_44", "male_45_to_49", "male_50_to_54", "male_55_to_59", "male_60_61", "male_62_64", "male_65_to_66", "male_67_to_69", "male_70_to_74", "male_75_to_79", "male_80_to_84", "male_85_and_over", "female_under_5", "female_5_to_9", "female_10_to_14", "female_15_to_17", "female_18_to_19", "female_20", "female_21", "female_22_to_24", "female_25_to_29", "female_30_to_34", "female_35_to_39", "female_40_to_44", "female_45_to_49", "female_50_to_54", "female_55_to_59", "female_60_to_61", "female_62_to_64", "female_65_to_66", "female_67_to_69", "female_70_to_74", "female_75_to_79", "female_80_to_84", "female_85_and_over", "white_including_hispanic", "black_including_hispanic", "amerindian_including_hispanic", "asian_including_hispanic", "commute_5_9_mins", "commute_35_39_mins", "commute_40_44_mins", "commute_60_89_mins", "commute_90_more_mins", "households_retirement_income", "armed_forces", "civilian_labor_force", "employed_pop", "unemployed_pop", "not_in_labor_force", "pop_16_over", "pop_in_labor_force", "asian_male_45_54", "asian_male_55_64", "black_male_45_54", "black_male_55_64", "hispanic_male_45_54", "hispanic_male_55_64", "white_male_45_54", "white_male_55_64", "bachelors_degree_2", "bachelors_degree_or_higher_25_64", "children", "children_in_single_female_hh", "commuters_by_bus", "commuters_by_car_truck_van", "commuters_by_carpool", "commuters_by_subway_or_elevated", "commuters_drove_alone", "different_house_year_ago_different_city", "different_house_year_ago_same_city", "employed_agriculture_forestry_fishing_hunting_mining", "employed_arts_entertainment_recreation_accommodation_food", "employed_construction", "employed_education_health_social", "employed_finance_insurance_real_estate", "employed_information", "employed_manufacturing", "employed_other_services_not_public_admin", "employed_public_administration", "employed_retail_trade", "employed_science_management_admin_waste", "employed_transportation_warehousing_utilities", "employed_wholesale_trade", "female_female_households", "four_more_cars", "gini_index", "graduate_professional_degree", "group_quarters", "high_school_including_ged", "households_public_asst_or_food_stamps", "in_grades_1_to_4", "in_grades_5_to_8", "in_grades_9_to_12", "in_school", "in_undergrad_college", "less_than_high_school_graduate", "male_45_64_associates_degree", "male_45_64_bachelors_degree", "male_45_64_graduate_degree", "male_45_64_less_than_9_grade", "male_45_64_grade_9_12", "male_45_64_high_school", "male_45_64_some_college", "male_45_to_64", "male_male_households", "management_business_sci_arts_employed", "no_car", "no_cars", "not_us_citizen_pop", "occupation_management_arts", "occupation_natural_resources_construction_maintenance", "occupation_production_transportation_material", "occupation_sales_office", "occupation_services", "one_car", "two_cars", "three_cars", "pop_25_64", "pop_determined_poverty_status", "population_1_year_and_over", "population_3_years_over", "poverty", "sales_office_employed", "some_college_and_associates_degree", "walked_to_work", "worked_at_home", "workers_16_and_over", "associates_degree", "bachelors_degree", "high_school_diploma", "less_one_year_college", "masters_degree", "one_year_more_college", "pop_25_years_over", "commute_35_44_mins", "commute_60_more_mins", "commute_less_10_mins", "commuters_16_over", "hispanic_any_race", "pop_5_years_over", "speak_only_english_at_home", "speak_spanish_at_home", "speak_spanish_at_home_low_english", "pop_15_and_over", "pop_never_married", "pop_now_married", "pop_separated", "pop_widowed", "pop_divorced", "do_date"], "column_types": ["STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "STRING"], "nested_column_names": ["geo_id", "nonfamily_households", "family_households", "median_year_structure_built", "rent_burden_not_computed", "rent_over_50_percent", "rent_40_to_50_percent", "rent_35_to_40_percent", "rent_30_to_35_percent", "rent_25_to_30_percent", "rent_20_to_25_percent", "rent_15_to_20_percent", "rent_10_to_15_percent", "rent_under_10_percent", "total_pop", "male_pop", "female_pop", "median_age", "white_pop", "black_pop", "asian_pop", "hispanic_pop", "amerindian_pop", "other_race_pop", "two_or_more_races_pop", "not_hispanic_pop", "commuters_by_public_transportation", "households", "median_income", "income_per_capita", "housing_units", "vacant_housing_units", "vacant_housing_units_for_rent", "vacant_housing_units_for_sale", "median_rent", "percent_income_spent_on_rent", "owner_occupied_housing_units", "million_dollar_housing_units", "mortgaged_housing_units", "families_with_young_children", "two_parent_families_with_young_children", "two_parents_in_labor_force_families_with_young_children", "two_parents_father_in_labor_force_families_with_young_children", "two_parents_mother_in_labor_force_families_with_young_children", "two_parents_not_in_labor_force_families_with_young_children", "one_parent_families_with_young_children", "father_one_parent_families_with_young_children", "father_in_labor_force_one_parent_families_with_young_children", "commute_10_14_mins", "commute_15_19_mins", "commute_20_24_mins", "commute_25_29_mins", "commute_30_34_mins", "commute_45_59_mins", "aggregate_travel_time_to_work", "income_less_10000", "income_10000_14999", "income_15000_19999", "income_20000_24999", "income_25000_29999", "income_30000_34999", "income_35000_39999", "income_40000_44999", "income_45000_49999", "income_50000_59999", "income_60000_74999", "income_75000_99999", "income_100000_124999", "income_125000_149999", "income_150000_199999", "income_200000_or_more", "renter_occupied_housing_units_paying_cash_median_gross_rent", "owner_occupied_housing_units_lower_value_quartile", "owner_occupied_housing_units_median_value", "owner_occupied_housing_units_upper_value_quartile", "married_households", "occupied_housing_units", "housing_units_renter_occupied", "dwellings_1_units_detached", "dwellings_1_units_attached", "dwellings_2_units", "dwellings_3_to_4_units", "dwellings_5_to_9_units", "dwellings_10_to_19_units", "dwellings_20_to_49_units", "dwellings_50_or_more_units", "mobile_homes", "housing_built_2005_or_later", "housing_built_2000_to_2004", "housing_built_1939_or_earlier", "male_under_5", "male_5_to_9", "male_10_to_14", "male_15_to_17", "male_18_to_19", "male_20", "male_21", "male_22_to_24", "male_25_to_29", "male_30_to_34", "male_35_to_39", "male_40_to_44", "male_45_to_49", "male_50_to_54", "male_55_to_59", "male_60_61", "male_62_64", "male_65_to_66", "male_67_to_69", "male_70_to_74", "male_75_to_79", "male_80_to_84", "male_85_and_over", "female_under_5", "female_5_to_9", "female_10_to_14", "female_15_to_17", "female_18_to_19", "female_20", "female_21", "female_22_to_24", "female_25_to_29", "female_30_to_34", "female_35_to_39", "female_40_to_44", "female_45_to_49", "female_50_to_54", "female_55_to_59", "female_60_to_61", "female_62_to_64", "female_65_to_66", "female_67_to_69", "female_70_to_74", "female_75_to_79", "female_80_to_84", "female_85_and_over", "white_including_hispanic", "black_including_hispanic", "amerindian_including_hispanic", "asian_including_hispanic", "commute_5_9_mins", "commute_35_39_mins", "commute_40_44_mins", "commute_60_89_mins", "commute_90_more_mins", "households_retirement_income", "armed_forces", "civilian_labor_force", "employed_pop", "unemployed_pop", "not_in_labor_force", "pop_16_over", "pop_in_labor_force", "asian_male_45_54", "asian_male_55_64", "black_male_45_54", "black_male_55_64", "hispanic_male_45_54", "hispanic_male_55_64", "white_male_45_54", "white_male_55_64", "bachelors_degree_2", "bachelors_degree_or_higher_25_64", "children", "children_in_single_female_hh", "commuters_by_bus", "commuters_by_car_truck_van", "commuters_by_carpool", "commuters_by_subway_or_elevated", "commuters_drove_alone", "different_house_year_ago_different_city", "different_house_year_ago_same_city", "employed_agriculture_forestry_fishing_hunting_mining", "employed_arts_entertainment_recreation_accommodation_food", "employed_construction", "employed_education_health_social", "employed_finance_insurance_real_estate", "employed_information", "employed_manufacturing", "employed_other_services_not_public_admin", "employed_public_administration", "employed_retail_trade", "employed_science_management_admin_waste", "employed_transportation_warehousing_utilities", "employed_wholesale_trade", "female_female_households", "four_more_cars", "gini_index", "graduate_professional_degree", "group_quarters", "high_school_including_ged", "households_public_asst_or_food_stamps", "in_grades_1_to_4", "in_grades_5_to_8", "in_grades_9_to_12", "in_school", "in_undergrad_college", "less_than_high_school_graduate", "male_45_64_associates_degree", "male_45_64_bachelors_degree", "male_45_64_graduate_degree", "male_45_64_less_than_9_grade", "male_45_64_grade_9_12", "male_45_64_high_school", "male_45_64_some_college", "male_45_to_64", "male_male_households", "management_business_sci_arts_employed", "no_car", "no_cars", "not_us_citizen_pop", "occupation_management_arts", "occupation_natural_resources_construction_maintenance", "occupation_production_transportation_material", "occupation_sales_office", "occupation_services", "one_car", "two_cars", "three_cars", "pop_25_64", "pop_determined_poverty_status", "population_1_year_and_over", "population_3_years_over", "poverty", "sales_office_employed", "some_college_and_associates_degree", "walked_to_work", "worked_at_home", "workers_16_and_over", "associates_degree", "bachelors_degree", "high_school_diploma", "less_one_year_college", "masters_degree", "one_year_more_college", "pop_25_years_over", "commute_35_44_mins", "commute_60_more_mins", "commute_less_10_mins", "commuters_16_over", "hispanic_any_race", "pop_5_years_over", "speak_only_english_at_home", "speak_spanish_at_home", "speak_spanish_at_home_low_english", "pop_15_and_over", "pop_never_married", "pop_now_married", "pop_separated", "pop_widowed", "pop_divorced", "do_date"], "nested_column_types": ["STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "STRING"], "description": ["US States Geoids", "Nonfamily Households. A householder living alone or with nonrelatives only. Unmarried couples households, whether opposite-sex or same-sex, with no relatives of the householder present are tabulated in nonfamily households.", "Family Households. A family consists of a householder and one or more other people living in the same household who are related to the householder by birth, marriage, or adoption. All people in a household who are related to the householder are regarded as members of his or her family. A family household may contain people not related to the householder, but those people are not included as part of the family of the householder in tabulations. Thus, the number of family households is equal to the number of families, but family households may include more members than do families. A household can contain only one family for purposes of tabulations. Not all households contain families since a household may be comprised of a group of unrelated people or of one person living alone -- these are called nonfamily households. Families are classified by type as either a \"married couple family\" or \"other family\" according to the sex of the householder and the presence of relatives. The data on family type are based ...", "Median Year Structure Built. Median Year Structure Built", "Housing units without rent burden computed. Units for which no rent is paid and units occupied by households that reported no income or a net loss comprise this category", "Housing units spending over 50% income on rent. Gross rent over 50 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 40 to 49.9% income on rent. Gross rent from 40.0 to 49.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 35 to 39.9% income on rent. Gross rent from 35.0 to 39.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 30 to 34.9% income on rent. Gross rent from 30.0 to 34.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 25 to 29.9% income on rent. Gross rent from 25.0 to 29.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 20 to 24.9% income on rent. Gross rent from 20.0 to 24.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 15 to 19.9% income on rent. Gross rent from 15.0 to 19.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 10 to 14.9% income on rent. Gross rent from 10.0 to 14.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending less than 10% on rent. Gross rent less than 10 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Total Population. The total number of all people living in a given geographic area.  This is a very useful catch-all denominator when calculating rates.", "Male Population. The number of people within each geography who are male.", "Female Population. The number of people within each geography who are female.", "Median Age. The median age of all people in a given geographic area.", "White Population. The number of people identifying as white, non-Hispanic in each geography.", "Black or African American Population. The number of people identifying as black or African American, non-Hispanic in each geography.", "Asian Population. The number of people identifying as Asian, non-Hispanic in each geography.", "Hispanic Population. The number of people identifying as Hispanic or Latino in each geography.", "American Indian and Alaska Native Population. The number of people identifying as American Indian or Alaska native in each geography.", "Other Race population. The number of people identifying as another race in each geography", "Two or more races population. The number of people identifying as two or more races in each geography", "Population not Hispanic. The number of people not identifying as Hispanic or Latino in each geography.", "Commuters by Public Transportation. The number of workers age 16 years and over within a geographic area who primarily traveled to work by public transportation.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Households. A count of the number of households in each geography. A household consists of one or more people who live in the same dwelling and also share at meals or living accommodation, and may consist of a single family or some other grouping of people. ", "Median Household Income in the past 12 Months. Within a geographic area, the median income received by every household on a regular basis before payments for personal income taxes, social security, union dues, medicare deductions, etc.  It includes income received from wages, salary, commissions, bonuses, and tips; self-employment income from own nonfarm or farm businesses, including proprietorships and partnerships; interest, dividends, net rental income, royalty income, or income from estates and trusts; Social Security or Railroad Retirement income; Supplemental Security Income (SSI); any cash public assistance or welfare payments from the state or local welfare office; retirement, survivor, or disability benefits; and any other sources of income received regularly such as Veterans' (VA) payments, unemployment and/or worker's compensation, child support, and alimony.", "Per Capita Income in the past 12 Months. Per capita income is the mean income computed for every man, woman, and child in a particular group. It is derived by dividing the total income of a particular group by the total population.", "Housing Units. A count of housing units in each geography.  A housing unit is a house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters.", "Vacant Housing Units. The count of vacant housing units in a geographic area. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Vacant Housing Units for Rent. The count of vacant housing units in a geographic area that are for rent. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Vacant Housing Units for Sale. The count of vacant housing units in a geographic area that are for sale. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Median Rent. The median contract rent within a geographic area. The contract rent is the monthly rent agreed to or contracted for, regardless of any furnishings, utilities, fees, meals, or services that may be included. For vacant units, it is the monthly rent asked for the rental unit at the time of interview.", "Percent of Household Income Spent on Rent. Within a geographic area, the median percentage of household income which was spent on gross rent.  Gross rent is the amount of the contract rent plus the estimated average monthly cost of utilities (electricity, gas, water, sewer etc.) and fuels (oil, coal, wood, etc.) if these are paid by the renter.  Household income is the sum of the income of all people 15 years and older living in the household.", "Owner-occupied Housing Units", "Owner-occupied Housing Units valued at $1,000,000 or more.. The count of owner occupied housing units in a geographic area that are valued at $1,000,000 or more.  Value is the respondent's estimate of how much the property (house and lot, mobile home and lot, or condominium unit) would sell for if it were for sale.", "Owner-occupied Housing Units with a Mortgage. The count of housing units within a geographic area that are mortagaged. \"Mortgage\" refers to all forms of debt where the property is pledged as security for repayment of the debt, including deeds of trust, trust deed, contracts to purchase, land contracts, junior mortgages, and home equity loans.", "Families with young children (under 6 years of age)", "Two-parent families with young children (under 6 years of age)", "Two-parent families, both parents in labor force with young children (under 6 years of age)", "Two-parent families, father only in labor force with young children (under 6 years of age)", "Two-parent families, mother only in labor force with young children (under 6 years of age)", "Two-parent families, neither parent in labor force with young children (under 6 years of age)", "One-parent families with young children (under 6 years of age)", "One-parent families, father, with young children (under 6 years of age)", "One-parent families, father in labor force, with young children (under 6 years of age)", "Number of workers with a commute between 10 and 14 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 10 and 14 minutes. ", "Number of workers with a commute between 15 and 19 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 15 and 19 minutes. ", "Number of workers with a commute between 20 and 24 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 20 and 24 minutes.", "Number of workers with a commute between 25 and 29 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 25 and 29 minutes. ", "Number of workers with a commute between 30 and 34 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 30 and 34 minutes. ", "Number of workers with a commute between 45 and 59 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 45 and 59 minutes. ", "Aggregate travel time to work. The total number of minutes every worker in a geographic area over the age of 16 who did not work from home spent spent commuting to work in one day", "Households with income less than $10,000. The number of households in a geographic area whose annual income was less than $10,000.", "Households with income of $10,000 to $14,999. The number of households in a geographic area whose annual income was between $10,000 and $14,999.", "Households with income of $15,000 to $19,999. The number of households in a geographic area whose annual income was between $15,000 and $19,999.", "Households with income of $20,000 to $24,999. The number of households in a geographic area whose annual income was between $20,000 and $24,999.", "Households with income of $25,000 to $29,999. The number of households in a geographic area whose annual income was between $20,000 and $24,999.", "Households with income of $30,000 to $34,999. The number of households in a geographic area whose annual income was between $30,000 and $34,999.", "Households with income of $35,000 to $39,999. The number of households in a geographic area whose annual income was between $35,000 and $39,999.", "Households with income of $40,000 to $44,999. The number of households in a geographic area whose annual income was between $40,000 and $44,999.", "Households with income of $45,000 to $49,999. The number of households in a geographic area whose annual income was between $45,000 and $49,999.", "Households with income of $50,000 to $59,999. The number of households in a geographic area whose annual income was between $50,000 and $59,999.", "Households with income of $60,000 to $74,999. The number of households in a geographic area whose annual income was between $60,000 and $74,999.", "Households with income of $75,000 to $99,999. The number of households in a geographic area whose annual income was between $75,000 and $99,999.", "Households with income of $100,000 to $124,999. The number of households in a geographic area whose annual income was between $100,000 and $124,999.", "Households with income of $125,000 to $149,999. The number of households in a geographic area whose annual income was between $125,000 and $149,999.", "Households with income of $150,000 to $199,999. The number of households in a geographic area whose annual income was between $150,000 and $1999,999.", "Households with income of $200,000 Or More. The number of households in a geographic area whose annual income was more than $200,000.", "Renter-Occupied Housing Units Paying Cash Rent Median Gross Rent", "Owner-Occupied Housing Units Lower Value Quartile", "Owner-Occupied Housing Units Median Value. The middle value (median) in a geographic area owner occupied housing units.", "Owner-Occupied Housing Units Upper Value Quartile", "Married households. People in formal marriages, as well as people in common-law marriages, are included. Does not include same-sex marriages.", "Occupied housing units. A housing unit is classified as occupied if it is the usual place of residence of the person or group of people living in it at the time of enumeration.", "Renter occupied housing units. All occupied units which are not owner occupied, whether they are rented for cash rent or occupied without payment of cash rent, are classified as renter-occupied.", "Single-family (one unit) detached dwellings. This is a 1-unit structure detached from any other house, that is, with open space on all four sides. Such structures are considered detached even if they have an adjoining shed or garage. A one-family house that contains a business is considered detached as long as the building has open space on all four sides. Mobile homes to which one or more permanent rooms have been added or built also are included.", "Single-family (one unit) attached dwellings. This is a 1-unit structure that has one or more walls extending from ground to roof separating it from adjoining structures. In row houses (sometimes called townhouses), double houses, or houses attached to nonresidential structures, each house is a separate, attached structure if the dividing or common wall goes from ground to roof.", "Two-family (two unit) dwellings", "Multifamily dwellings with three to 4 units", "Apartment buildings with 5 to 9 units", "Apartment buildings with 10 to 19 units", "Apartment buildings with 20 to 49 units", "Apartment buildings with 50 or more units", "Mobile homes. A manufactured home is defined as a movable dwelling, 8 feet or more wide and 40 feet or more long, designed to be towed on its own chassis, with transportation gear integral to the unit when it leaves the factory, and without need of a permanent foundation. These homes are built in accordance with the U.S. Department of Housing and Urban Development (HUD) building code.", "Housing units built in 2005 or later. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built in 2005 or later.", "Housing units built between 2000 and 2004. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built from 2000 to 2004.", "Housing units built before 1939. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built in 1939 or earlier.", "Male under 5 years. The male population over the age of five years within the specified area.", "Male age 5 to 9. The male population between the age of five years to nine years within the specified area.", "Male age 10 to 14. The male population between the age of ten years to fourteen years within the specified area.", "Male age 15 to 17. The male population between the age of fifteeen years to seventeen years within the specified area.", "Male age 18 and 19. The male population between the age of eighteen years to nineteen years within the specified area.", "Male age 20. The male population with an age of twenty years within the specified area.", "Male age 21. The male population with an age of twenty-one years within the specified area.", "Male age 22 to 24. The male population between the age of twenty-two years to twenty-four years within the specified area.", "Male age 25 to 29. The male population between the age of twenty-five years to twenty-nine years within the specified area.", "Male age 30 to 34. The male population between the age of thirty years to thirty-four years within the specified area.", "Male age 35 to 39. The male population between the age of thirty-five years to thirty-nine years within the specified area.", "Male age 40 to 44. The male population between the age of fourty years to fourty-four years within the specified area.", "Men age 45 to 49. The male population between the age of fourty-five years to fourty-nine years within the specified area.", "Men age 50 to 54. The male population between the age of fifty years to fifty-four years within the specified area.", "Men age 55 to 59. The male population between the age of fifty-five years to fifty-nine years within the specified area.", "Men age 60 to 61. The male population between the age of sixty years to sixty-one years within the specified area.", "Men age 62 to 64. The male population between the age of sixty-two years to sixty-four years within the specified area.", "Male age 65 to 66. The male population between the age of sixty-five years to sixty-six years within the specified area.", "Male age 67 to 69. The male population between the age of sixty-seven years to sixty-nine years within the specified area.", "Male age 70 to 74. The male population between the age of seventy years to seventy-four years within the specified area.", "Male age 75 to 79. The male population between the age of seventy-five years to seventy-nine years within the specified area.", "Male age 80 to 84. The male population between the age of eighty years to eighty-four years within the specified area.", "Male age 85 and over. The male population of the age of eighty-five years and over within the specified area.", "Female under 5 years. The female population over the age of five years within the specified area.", "Female age 5 to 9. The female population between the age of five years to nine years within the specified area.", "Female age 10 to 14. The female population between the age of ten years to fourteen years within the specified area.", "Female age 15 to 17. The female population between the age of fifteeen years to seventeen years within the specified area.", "Female age 18 and 19. The female population between the age of eighteen years to nineteen years within the specified area.", "Female age 20. The female population with an age of twenty years within the specified area.", "Female age 21. The female population with an age of twenty-one years within the specified area.", "Female age 22 to 24. The female population between the age of twenty-two years to twenty-four years within the specified area.", "Female age 25 to 29. The female population between the age of twenty-five years to twenty-nine years within the specified area.", "Female age 30 to 34. The female population between the age of thirty years to thirty-four years within the specified area.", "Female age 35 to 39. The female population between the age of thirty-five years to thirty-nine years within the specified area.", "Female age 40 to 44. The female population between the age of fourty years to fourty-four years within the specified area.", "Female age 45 to 49. The female population between the age of fourty-five years to fourty-nine years within the specified area.", "Female age 50 to 54. The female population between the age of fifty years to fifty-four years within the specified area.", "Female age 55 to 59. The female population between the age of fifty-five years to fifty-nine years within the specified area.", "Female age 60 and 61. The female population between the age of sixty years to sixty-one years within the specified area.", "Female age 62 to 64. The female population between the age of sixty-two years to sixty-four years within the specified area.", "Female age 65 to 66. The female population between the age of sixty-five years to sixty-six years within the specified area.", "Female age 67 to 69. The female population between the age of sixty-seven years to sixty-nine years within the specified area.", "Female age 70 to 74. The female population between the age of seventy years to seventy-four years within the specified area.", "Female age 75 to 79. The female population between the age of seventy-five years to seventy-nine years within the specified area.", "Female age 80 to 84. The female population between the age of eighty years to eighty-four years within the specified area.", "Female age 85 and over. The female population of the age of eighty-five years and over within the specified area.", "White including Hispanic", "Black including Hispanic", "American Indian including Hispanic", "Asian including Hispanic", "Number of workers with a commute between 5 and 9 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 5 and 9 minutes.", "Number of workers with a commute between 35 and 39 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 35 and 39 minutes. ", "Number of workers with a commute between 40 and 44 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 40 and 44 minutes. ", "Number of workers with a commute between 60 and 89 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 60 and 89 minutes .", "Number of workers with a commute of more than 90 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute more than 90 minutes.", "Households receiving retirement income", "Population in Armed Forces. The number of people in each geography who are members of the U.S. Armed Forces (people on active duty with the United States Army, Air Force, Navy, Marine Corps, or Coast Guard).", "Population in Civilian Labor Force. The number of civilians 16 years and over in each geography who can be classified as either \"employed\" or \"unemployed\" below.", "Employed Population. The number of civilians 16 years old and over in each geography who either (1) were \"at work,\" that is, those who did any work at all during the reference week as paid employees, worked in their own business or profession, worked on their own farm, or worked 15 hours or more as unpaid workers on a family farm or in a family business; or (2) were \"with a job but not at work,\" that is, those who did not work during the reference week but had jobs or businesses from which they were temporarily absent due to illness, bad weather, industrial dispute, vacation, or other personal reasons. Excluded from the employed are people whose only activity consisted of work around the house or unpaid volunteer work for religious, charitable, and similar organizations; also excluded are all institutionalized people and people on active duty in the United States Armed Forces.", "", "Population Not in Labor Force. The number of people in each geography who are 16 years old and over who are not classified as members of the labor force. This category consists mainly of students, homemakers, retired workers, seasonal workers interviewed in an off season who were not looking for work, institutionalized people, and people doing only incidental unpaid family work (less than 15 hours during the reference week).", "Population age 16 and over. The number of people in each geography who are age 16 or over.", "Population in Labor Force. The number of people in each geography who are either in the civilian labor force or are members of the U.S. Armed Forces (people on active duty with the United States Army, Air Force, Navy, Marine Corps, or Coast Guard).", "Asian Men age 45 to 54", "Asian Men age 55 to 64", "Black Men age 45 to 54", "Black Men age 55 to 64", "Hispanic Men age 45 to 54", "Hispanic Men age 55 to 64", "White Men age 45 to 54", "White Men age 55 to 64", "Population who completed a bachelor's degree. From mobility table.", "Population with Bachelors Degree or Higher, Ages 25 to 64. The number of people in each geography who are between the ages of 25 and 64 who have attained a bachelors degree or higher. ", "Children under 18 Years of Age. The number of people within each geography who are under 18 years of age.", "Children under 18 years of age in single female-led household", "Commuters by Bus. The number of workers age 16 years and over within a geographic area who primarily traveled to work by bus.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.  This is a subset of workers who commuted by public transport.", "Commuters by Car, Truck, or Van. The number of workers age 16 years and over within  a geographic area who primarily traveled to work by car, truck or  van.  This is the principal mode of travel or type of conveyance,  by distance rather than time, that the worker usually used to get  from home to work.", "Commuters by Carpool. The number of workers age 16 years and over within a geographic area who primarily traveled to work by carpool.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Commuters by Subway or Elevated. The number of workers age 16 years and over within a geographic area who primarily traveled to work by subway or elevated train.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.  This is a subset of workers who commuted by public transport.", "Commuters who drove alone. The number of workers age 16 years and over within a geographic area who primarily traveled by car driving alone. This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Lived in a different house one year ago in a different city. All people in a geographic area who lived in a different city within the year prior to the survey.", "Lived in a different house one year ago in the same city. All people in a geographic area who lived in the same city but moved to a different unit within the year prior to the survey.", "Workers employed in firms in agriculture, forestry, fishing, hunting, or mining. The Agriculture, Forestry, Fishing and Hunting sector comprises establishments primarily engaged in growing crops, raising animals, harvesting timber, and harvesting fish and other animals from a farm, ranch, or their natural habitats.", "Workers employed in firms in arts, entertainment, recreation, accommodation and food services. The Arts, Entertainment, and Recreation sector includes a wide range of establishments that operate facilities or provide services to meet varied cultural, entertainment, and recreational interests of their patrons. This sector comprises (1) establishments that are involved in producing, promoting, or participating in live performances, events, or exhibits intended for public viewing; (2) establishments that preserve and exhibit objects and sites of historical, cultural, or educational interest; and (3) establishments that operate facilities or provide services that enable patrons to participate in recreational activities or pursue amusement, hobby, and leisure-time interests.", "Workers employed in firms in construction. The Construction sector comprises establishments primarily engaged in the construction of buildings or engineering projects (e.g., highways and utility systems). Construction work done may include new work, additions, alterations, or maintenance and repairs.", "Workers employed in firms in educational services, health care, and social assistance. Outpatient health services, other than hospital care, including: public health administration; research and education; categorical health programs; treatment and immunization clinics; nursing; environmental health activities such as air and water pollution control; ambulance service if provided separately from fire protection services, and other general public health activities such as mosquito abatement. School health services provided by health agencies (rather than school agencies) are included here. Sewage treatment operations are classified under Sewerage.", "Workers employed in firms in finance, insurance, real estate and rental and leasing. The Real Estate and Rental and Leasing sector comprises establishments primarily engaged in renting, leasing, or otherwise allowing the use of tangible or intangible assets, and establishments providing related services. The major portion of this sector comprises establishments that rent, lease, or otherwise allow the use of their own assets by others. The assets may be tangible, as is the case of real estate and equipment, or intangible, as is the case with patents and trademarks.", "Workers employed in firms in information. The Information sector comprises establishments engaged in the following processes: (a) producing and distributing information and cultural products, (b) providing the means to transmit or distribute these products as well as data or communications, and (c) processing data. Included are the publishing industries, the motion picture and sound recording industries; the broadcasting industries, the telecommunications industries; Web search portals, data processing industries, and the information services industries.", "Workers employed in firms in manufacturing. The Manufacturing sector comprises establishments engaged in the mechanical, physical, or chemical transformation of materials, substances, or components into new products.", "Workers employed in firms in other services except public administration. The Other Services (Except Public Administration) sector comprises establishments engaged in providing services not specifically provided for elsewhere in the classification system. Establishments in this sector are primarily engaged in activities such as equipment and machinery repairing, promoting or administering religious activities, grantmaking, advocacy, and providing drycleaning and laundry services, personal care services, death care services, pet care services, photofinishing services, temporary parking services, and dating services. Private households that engage in employing workers on or about the premises in activities primarily concerned with the operation of the household are included in this sector.", "Workers employed in firms in public administration. The Public Administration sector consists of establishments of federal, state, and local government agencies that administer, oversee, and manage public programs and have executive, legislative, or judicial authority over other institutions within a given area. These agencies also set policy, create laws, adjudicate civil and criminal legal cases, provide for public safety and for national defense. In general, government establishments in the public administration sector oversee governmental programs and activities that are not performed by private establishments.", "Workers employed in firms in retail trade. The Retail Trade sector comprises establishments engaged in retailing merchandise, generally without transformation, and rendering services incidental to the sale of merchandise. The retailing process is the final step in the distribution of merchandise; retailers are, therefore, organized to sell merchandise in small quantities to the general public.", "Workers employed in firms in professional scientific, management, administrative and waste management services. The Administrative and Support and Waste Management and Remediation Services sector comprises establishments performing routine support activities for the day-to-day operations of other organizations. The establishments in this sector specialize in one or more of these support activities and provide these services to clients in a variety of industries and, in some cases, to households. Activities performed include office administration, hiring and placing of personnel, document preparation and similar clerical services, solicitation, collection, security and surveillance services, cleaning, and waste disposal services.", "Workers employed in firms in transportation, warehousing, and utilities. The Transportation and Warehousing sector includes industries providing transportation of passengers and cargo, warehousing and storage for goods, scenic and sightseeing transportation, and support activities related to modes of transportation. The modes of transportation are air, rail, water, road, and pipeline.", "Workers employed in firms in wholesale trade. The Wholesale Trade sector comprises establishments engaged in wholesaling merchandise, generally without transformation, and rendering services incidental to the sale of merchandise. The wholesaling process is an intermediate step in the distribution of merchandise. Wholesalers are organized to sell or arrange the purchase or sale of (a) goods for resale (i.e., goods sold to other wholesalers or retailers), (b) capital or durable nonconsumer goods, and (c) raw and intermediate materials and supplies used in production.", "Households with two female partners. An unmarried partner is a person age 15 years and over, who is not related to the householder, who shares living quarters, and who has an intimate relationship with the householder.", "Four car households. The number of households with four or more passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Gini Index. The Gini index, or index of income concentration, is a statistical measure of income inequality ranging from 0 to 1. A measure of 1 indicates perfect inequality, i.e., one household having all the income and rest having none. A measure of 0 indicates perfect equality, i.e., all households having an equal share of income.", "Population who completed a graduate or professional degree", "Population living in group quarters", "Population with high school degree, including GED. The number of people in a geographic area over the age of 25 who attained a high school degree or GED.", "Households on cash public assistance or receiving food stamps (SNAP)", "Students Enrolled in Grades 1 to 4. The total number of people in each geography currently enrolled in grades 1 through 4 inclusive.  This corresponds roughly to elementary school.", "Students Enrolled in Grades 5 to 8. The total number of people in each geography currently enrolled in grades 5 through 8 inclusive.  This corresponds roughly to middle school.", "Students Enrolled in Grades 9 to 12. The total number of people in each geography currently enrolled in grades 9 through 12 inclusive.  This corresponds roughly to high school.", "Students Enrolled in School. The total number of people in each geography currently enrolled at any level of school, from nursery or pre-school to advanced post-graduate education.  Only includes those over the age of 3.", "Students Enrolled as Undergraduate in College. The number of people in a geographic area who are enrolled in college at the undergraduate level. Enrollment refers to being registered or listed as a student in an educational program leading to a college degree. This may be a public school or college, a private school or college.", "Less than high school graduate. The number of people in a geographic area over the age of 25 who have not completed high school or any other advanced degree.", "Men age 45 to 64 who obtained an associate's degree", "Men age 45 to 64 who obtained a bachelor's degree", "Men age 45 to 64 who obtained a graduate or professional degree", "Men age 45 to 64 who attained less than a 9th grade education", "Men age 45 to 64 who attained between 9th and 12th grade, no diploma", "Men age 45 to 64 who completed high school or obtained GED", "Men age 45 to 64 who completed some college, no degree", "Men age 45 to 64 (\"middle aged\"). The male population between the age of fourty-five years to sixty-four years within the specified area.", "Households with two male partners. An unmarried partner is a person age 15 years and over, who is not related to the householder, who shares living quarters, and who has an intimate relationship with the householder.", "Civilian Employed Population in Management, Business, Science, and Arts Occupations. The number of employed civilians 16 years old and over in each geography in the Management, Business, Science, and Arts occupations. Occupation codes are based on the Standard Occupational Classification (SOC), published by the Executive Office of the President, Office of Management and Budget.", "Workers age 16 and over with no vehicle. All people in a geographic area over the age of 16 who do not own a car.", "Car-free households. The number of households without passenger cars, vans, and pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Not a U.S. Citizen Population. The number of people within each geography who indicated that they are not U.S. citizens.", "Workers employed in management business science and arts occupations", "Workers employed in natural resources, construction, and maintenance occupations", "Workers employed in production, transportation, and material moving", "Workers employed in sales and office occupations", "Workers employed in service occupations", "One car households. The number of households with one passenger car, van, , pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Two car households. The number of households with two passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Three car households. The number of households with one passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Population age 25 to 64. The number of people in each geography who are between the ages of 25 and 64. ", "Population for Whom Poverty Status Determined. The number of people in each geography who could be identified as either living in poverty or not.  This should be used as the denominator when calculating poverty rates, as it excludes people for whom it was not possible to determine poverty.", "Population 1 year and over. All people, male and female, child and adult, living in a given geographic area that are 1 year and older.", "Population 3 Years and Over. The total number of people in each geography age 3 years and over.  This denominator is mostly used to calculate rates of school enrollment.", "Income In The Past 12 Months Below Poverty Level. The number of people in a geographic area who are part of a family (which could be just them as an individual) determined to be \"in poverty\" following the Office of Management and Budget's Directive 14. (https://www.census.gov/hhes/povmeas/methodology/ombdir14.html)", "Civilian Employed Population in Sales and Office Occupations. The number of employed civilians 16 years old and over in each geography in the Sales and Office occupations. Occupation codes are based on the Standard Occupational Classification (SOC), published by the Executive Office of the President, Office of Management and Budget.", "Population who completed some college or obtained associate's degree. The number of people in a geographic area over the age of 25 who obtained an associate's degree, and did not complete a more advanced degree.", "Walked to Work. The number of workers age 16 years and over within a geographic area who primarily walked to work.  This would mean that of any way of getting to work, they travelled the most distance walking.", "Worked at Home. The count within a geographical area of workers over the age of 16 who worked at home.", "Workers over the Age of 16. The number of people in each geography who work. Workers include those employed at private for-profit companies, the self-employed, government workers and non-profit employees.", "Population Completed Associate's Degree. The number of people in a geographic area over the age of 25 who obtained a associate's degree, and did not complete a more advanced degree.", "Population Completed Bachelor's Degree. The number of people in a geographic area over the age of 25 who obtained a bachelor's degree, and did not complete a more advanced degree.", "Population Completed High School. The number of people in a geographic area over the age of 25 who completed high school, and did not complete a more advanced degree.", "Population completed less than one year of college, no degree. The number of people in a geographic area over the age of 25 who attended college for less than one year and no further.", "Population Completed Master's Degree. The number of people in a geographic area over the age of 25 who obtained a master's degree, but did not complete a more advanced degree.", "Population completed more than one year of college, no degree. The number of people in a geographic area over the age of 25 who attended college for more than one year but did not obtain a degree", "Population 25 Years and Over. The number of people in a geographic area who are over the age of 25.  This is used mostly as a denominator of educational attainment.", "Number of workers with a commute between 35 and 44 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 35 and 44 minutes. ", "Number of workers with a commute of over 60 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in over 60 minutes.", "Number of workers with less than 10 minute commute. The number of workers in a geographic area over the age of 16 who do not work from home and commute in less than 10 minutes.", "Workers age 16 and over who do not work from home. The number of workers in a geographic area over the age of 16 who do not work from home", "Hispanic of any race", "Population 5 Years and Over. The number of people in a geographic area who are over the age of 5.  This is primarily used as a denominator of measures of language spoken at home.", "Speaks only English at Home. The number of people in a geographic area over age 5 who speak only English at home.", "Speaks Spanish at Home. The number of people in a geographic area over age 5 who speak Spanish at home, possibly in addition to other languages.", "Speaks Spanish at Home, speaks English less than \"very well\"", "Population 15 Years and Over. The number of people in a geographic area who are over the age of 15.  This is used mostly as a denominator of marital status.", "Never Married. The number of people in a geographic area who have never been married.", "Currently married. The number of people in a geographic area who are currently married", "Married but separated. The number of people in a geographic area who are married but separated", "Widowed. The number of people in a geographic area who are widowed", "Divorced. The number of people in a geographic area who are divorced", null], "sample_rows": [{"geo_id": "72", "nonfamily_households": 385732.0, "family_households": 805573.0, "median_year_structure_built": 1978.0, "rent_burden_not_computed": 184063.0, "rent_over_50_percent": 61192.0, "rent_40_to_50_percent": 15317.0, "rent_35_to_40_percent": 13835.0, "rent_30_to_35_percent": 20897.0, "rent_25_to_30_percent": 26758.0, "rent_20_to_25_percent": 22911.0, "rent_15_to_20_percent": 20711.0, "rent_10_to_15_percent": 15470.0, "rent_under_10_percent": 8127.0, "total_pop": 3337177.0, "male_pop": 1591997.0, "female_pop": 1745180.0, "median_age": 41.4, "white_pop": 23379.0, "black_pop": 2454.0, "asian_pop": 1498.0, "hispanic_pop": 3306460.0, "amerindian_pop": 77.0, "other_race_pop": 1469.0, "two_or_more_races_pop": 1840.0, "not_hispanic_pop": 30717.0, "commuters_by_public_transportation": 17286.0, "households": 1191305.0, "median_income": 19343.0, "income_per_capita": 12279.0, "housing_units": 1561802.0, "vacant_housing_units": 370497.0, "vacant_housing_units_for_rent": 36122.0, "vacant_housing_units_for_sale": 30244.0, "median_rent": 371.0, "percent_income_spent_on_rent": 32.1, "owner_occupied_housing_units": 802024.0, "million_dollar_housing_units": 822.0, "mortgaged_housing_units": 312180.0, "families_with_young_children": 175157.0, "two_parent_families_with_young_children": 57905.0, "two_parents_in_labor_force_families_with_young_children": 38959.0, "two_parents_father_in_labor_force_families_with_young_children": 14267.0, "two_parents_mother_in_labor_force_families_with_young_children": 2378.0, "two_parents_not_in_labor_force_families_with_young_children": 2301.0, "one_parent_families_with_young_children": 117252.0, "father_one_parent_families_with_young_children": 27869.0, "father_in_labor_force_one_parent_families_with_young_children": 21737.0, "commute_10_14_mins": 121173.0, "commute_15_19_mins": 151004.0, "commute_20_24_mins": 137390.0, "commute_25_29_mins": 53970.0, "commute_30_34_mins": 151520.0, "commute_45_59_mins": 77568.0, "aggregate_travel_time_to_work": 27317920.0, "income_less_10000": 349478.0, "income_10000_14999": 139534.0, "income_15000_19999": 122356.0, "income_20000_24999": 98984.0, "income_25000_29999": 72554.0, "income_30000_34999": 65333.0, "income_35000_39999": 50837.0, "income_40000_44999": 45550.0, "income_45000_49999": 39152.0, "income_50000_59999": 62286.0, "income_60000_74999": 53520.0, "income_75000_99999": 44733.0, "income_100000_124999": 18963.0, "income_125000_149999": 10806.0, "income_150000_199999": 7293.0, "income_200000_or_more": 9926.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 453.0, "owner_occupied_housing_units_lower_value_quartile": 80000.0, "owner_occupied_housing_units_median_value": 109800.0, "owner_occupied_housing_units_upper_value_quartile": 154600.0, "married_households": 447425.0, "occupied_housing_units": 1191305.0, "housing_units_renter_occupied": 389281.0, "dwellings_1_units_detached": 1069670.0, "dwellings_1_units_attached": 179133.0, "dwellings_2_units": 54899.0, "dwellings_3_to_4_units": 52411.0, "dwellings_5_to_9_units": 65701.0, "dwellings_10_to_19_units": 43286.0, "dwellings_20_to_49_units": 29572.0, "dwellings_50_or_more_units": 60732.0, "mobile_homes": 6312.0, "housing_built_2005_or_later": 3669.0, "housing_built_2000_to_2004": 15528.0, "housing_built_1939_or_earlier": 61114.0, "male_under_5": 82207.0, "male_5_to_9": 88402.0, "male_10_to_14": 106966.0, "male_15_to_17": 67779.0, "male_18_to_19": 52033.0, "male_20": 23507.0, "male_21": 26857.0, "male_22_to_24": 70519.0, "male_25_to_29": 100098.0, "male_30_to_34": 91930.0, "male_35_to_39": 97265.0, "male_40_to_44": 102266.0, "male_45_to_49": 98331.0, "male_50_to_54": 102602.0, "male_55_to_59": 102315.0, "male_60_61": 38489.0, "male_62_64": 55540.0, "male_65_to_66": 34045.0, "male_67_to_69": 51566.0, "male_70_to_74": 80600.0, "male_75_to_79": 57117.0, "male_80_to_84": 34262.0, "male_85_and_over": 27301.0, "female_under_5": 64492.0, "female_5_to_9": 84432.0, "female_10_to_14": 98459.0, "female_15_to_17": 64057.0, "female_18_to_19": 47764.0, "female_20": 26259.0, "female_21": 24925.0, "female_22_to_24": 64844.0, "female_25_to_29": 109012.0, "female_30_to_34": 95617.0, "female_35_to_39": 118482.0, "female_40_to_44": 109831.0, "female_45_to_49": 111485.0, "female_50_to_54": 118185.0, "female_55_to_59": 114827.0, "female_60_to_61": 46959.0, "female_62_to_64": 71686.0, "female_65_to_66": 39941.0, "female_67_to_69": 67102.0, "female_70_to_74": 95620.0, "female_75_to_79": 72221.0, "female_80_to_84": 51553.0, "female_85_and_over": 47427.0, "white_including_hispanic": 2210728.0, "black_including_hispanic": 403659.0, "amerindian_including_hispanic": 7934.0, "asian_including_hispanic": 5424.0, "commute_5_9_mins": 58253.0, "commute_35_39_mins": 25183.0, "commute_40_44_mins": 48813.0, "commute_60_89_mins": 85397.0, "commute_90_more_mins": 29975.0, "households_retirement_income": 196206.0, "armed_forces": 1983.0, "civilian_labor_force": 1199622.0, "employed_pop": 1002763.0, "unemployed_pop": 196859.0, "not_in_labor_force": 1569918.0, "pop_16_over": 2771523.0, "pop_in_labor_force": 1201605.0, "asian_male_45_54": NaN, "asian_male_55_64": NaN, "black_male_45_54": 26665.0, "black_male_55_64": 25726.0, "hispanic_male_45_54": 198446.0, "hispanic_male_55_64": 193800.0, "white_male_45_54": 1917.0, "white_male_55_64": 1730.0, "bachelors_degree_2": NaN, "bachelors_degree_or_higher_25_64": 498047.0, "children": 656794.0, "children_in_single_female_hh": 305576.0, "commuters_by_bus": 7335.0, "commuters_by_car_truck_van": 894572.0, "commuters_by_carpool": 73790.0, "commuters_by_subway_or_elevated": 2949.0, "commuters_drove_alone": 820782.0, "different_house_year_ago_different_city": NaN, "different_house_year_ago_same_city": NaN, "employed_agriculture_forestry_fishing_hunting_mining": 14245.0, "employed_arts_entertainment_recreation_accommodation_food": 91605.0, "employed_construction": 51542.0, "employed_education_health_social": 244554.0, "employed_finance_insurance_real_estate": 57081.0, "employed_information": 16107.0, "employed_manufacturing": 82407.0, "employed_other_services_not_public_admin": 55734.0, "employed_public_administration": 89157.0, "employed_retail_trade": 133983.0, "employed_science_management_admin_waste": 98367.0, "employed_transportation_warehousing_utilities": 39495.0, "employed_wholesale_trade": 28486.0, "female_female_households": 950.0, "four_more_cars": 39500.0, "gini_index": 0.5512, "graduate_professional_degree": NaN, "group_quarters": 39360.0, "high_school_including_ged": NaN, "households_public_asst_or_food_stamps": 463625.0, "in_grades_1_to_4": 139242.0, "in_grades_5_to_8": 167886.0, "in_grades_9_to_12": 168446.0, "in_school": 804711.0, "in_undergrad_college": 219030.0, "less_than_high_school_graduate": NaN, "male_45_64_associates_degree": 36612.0, "male_45_64_bachelors_degree": 60485.0, "male_45_64_graduate_degree": 27327.0, "male_45_64_less_than_9_grade": 51406.0, "male_45_64_grade_9_12": 40629.0, "male_45_64_high_school": 131474.0, "male_45_64_some_college": 49344.0, "male_45_to_64": 397277.0, "male_male_households": 1849.0, "management_business_sci_arts_employed": 336173.0, "no_car": 35863.0, "no_cars": 193855.0, "not_us_citizen_pop": NaN, "occupation_management_arts": 336173.0, "occupation_natural_resources_construction_maintenance": 85723.0, "occupation_production_transportation_material": 102657.0, "occupation_sales_office": 273362.0, "occupation_services": 204848.0, "one_car": 512040.0, "two_cars": 342361.0, "three_cars": 103549.0, "pop_25_64": 1684920.0, "pop_determined_poverty_status": 3306715.0, "population_1_year_and_over": NaN, "population_3_years_over": 3259323.0, "poverty": 1468798.0, "sales_office_employed": 273362.0, "some_college_and_associates_degree": NaN, "walked_to_work": 27693.0, "worked_at_home": 21251.0, "workers_16_and_over": 978691.0, "associates_degree": 242776.0, "bachelors_degree": 428558.0, "high_school_diploma": 593528.0, "less_one_year_college": 35698.0, "masters_degree": 121293.0, "one_year_more_college": 249320.0, "pop_25_years_over": 2343675.0, "commute_35_44_mins": 73996.0, "commute_60_more_mins": 115372.0, "commute_less_10_mins": 75447.0, "commuters_16_over": 957440.0, "hispanic_any_race": 3306460.0, "pop_5_years_over": NaN, "speak_only_english_at_home": NaN, "speak_spanish_at_home": NaN, "speak_spanish_at_home_low_english": NaN, "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "do_date": "2017"}, {"geo_id": "15", "nonfamily_households": 137742.0, "family_households": 320336.0, "median_year_structure_built": 1979.0, "rent_burden_not_computed": 15122.0, "rent_over_50_percent": 48554.0, "rent_40_to_50_percent": 18569.0, "rent_35_to_40_percent": 14074.0, "rent_30_to_35_percent": 17033.0, "rent_25_to_30_percent": 19429.0, "rent_20_to_25_percent": 21142.0, "rent_15_to_20_percent": 17650.0, "rent_10_to_15_percent": 12340.0, "rent_under_10_percent": 6087.0, "total_pop": 1427538.0, "male_pop": 716350.0, "female_pop": 711188.0, "median_age": 39.2, "white_pop": 310584.0, "black_pop": 22629.0, "asian_pop": 532881.0, "hispanic_pop": 150125.0, "amerindian_pop": 1659.0, "other_race_pop": 2268.0, "two_or_more_races_pop": 274652.0, "not_hispanic_pop": 1277413.0, "commuters_by_public_transportation": 43263.0, "households": 458078.0, "median_income": 77765.0, "income_per_capita": 33882.0, "housing_units": 542955.0, "vacant_housing_units": 84877.0, "vacant_housing_units_for_rent": 19371.0, "vacant_housing_units_for_sale": 3439.0, "median_rent": 1434.0, "percent_income_spent_on_rent": 33.2, "owner_occupied_housing_units": 268078.0, "million_dollar_housing_units": 26863.0, "mortgaged_housing_units": 172214.0, "families_with_young_children": 101987.0, "two_parent_families_with_young_children": 68453.0, "two_parents_in_labor_force_families_with_young_children": 40594.0, "two_parents_father_in_labor_force_families_with_young_children": 24447.0, "two_parents_mother_in_labor_force_families_with_young_children": 1968.0, "two_parents_not_in_labor_force_families_with_young_children": 1444.0, "one_parent_families_with_young_children": 33534.0, "father_one_parent_families_with_young_children": 9374.0, "father_in_labor_force_one_parent_families_with_young_children": 8494.0, "commute_10_14_mins": 92487.0, "commute_15_19_mins": 95283.0, "commute_20_24_mins": 98862.0, "commute_25_29_mins": 33673.0, "commute_30_34_mins": 104576.0, "commute_45_59_mins": 62081.0, "aggregate_travel_time_to_work": 18177550.0, "income_less_10000": 28417.0, "income_10000_14999": 13429.0, "income_15000_19999": 13464.0, "income_20000_24999": 14330.0, "income_25000_29999": 14242.0, "income_30000_34999": 14594.0, "income_35000_39999": 15836.0, "income_40000_44999": 17186.0, "income_45000_49999": 15690.0, "income_50000_59999": 30533.0, "income_60000_74999": 43069.0, "income_75000_99999": 67589.0, "income_100000_124999": 51085.0, "income_125000_149999": 34245.0, "income_150000_199999": 42481.0, "income_200000_or_more": 41888.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 1573.0, "owner_occupied_housing_units_lower_value_quartile": 401900.0, "owner_occupied_housing_units_median_value": 617400.0, "owner_occupied_housing_units_upper_value_quartile": 870100.0, "married_households": 233373.0, "occupied_housing_units": 458078.0, "housing_units_renter_occupied": 190000.0, "dwellings_1_units_detached": 285067.0, "dwellings_1_units_attached": 51257.0, "dwellings_2_units": 10568.0, "dwellings_3_to_4_units": 21532.0, "dwellings_5_to_9_units": 36860.0, "dwellings_10_to_19_units": 29323.0, "dwellings_20_to_49_units": 29066.0, "dwellings_50_or_more_units": 77733.0, "mobile_homes": 1099.0, "housing_built_2005_or_later": 9545.0, "housing_built_2000_to_2004": 16565.0, "housing_built_1939_or_earlier": 18276.0, "male_under_5": 45962.0, "male_5_to_9": 43351.0, "male_10_to_14": 44044.0, "male_15_to_17": 24191.0, "male_18_to_19": 16545.0, "male_20": 7985.0, "male_21": 11650.0, "male_22_to_24": 32566.0, "male_25_to_29": 55236.0, "male_30_to_34": 51528.0, "male_35_to_39": 47044.0, "male_40_to_44": 45949.0, "male_45_to_49": 43059.0, "male_50_to_54": 43109.0, "male_55_to_59": 46012.0, "male_60_61": 18507.0, "male_62_64": 25480.0, "male_65_to_66": 16211.0, "male_67_to_69": 23866.0, "male_70_to_74": 29658.0, "male_75_to_79": 19490.0, "male_80_to_84": 12123.0, "male_85_and_over": 12784.0, "female_under_5": 43611.0, "female_5_to_9": 42730.0, "female_10_to_14": 40020.0, "female_15_to_17": 22088.0, "female_18_to_19": 13820.0, "female_20": 7075.0, "female_21": 8718.0, "female_22_to_24": 25438.0, "female_25_to_29": 49734.0, "female_30_to_34": 49566.0, "female_35_to_39": 48485.0, "female_40_to_44": 40359.0, "female_45_to_49": 43160.0, "female_50_to_54": 44280.0, "female_55_to_59": 48189.0, "female_60_to_61": 18735.0, "female_62_to_64": 25562.0, "female_65_to_66": 18688.0, "female_67_to_69": 25463.0, "female_70_to_74": 30975.0, "female_75_to_79": 21610.0, "female_80_to_84": 19139.0, "female_85_and_over": 23743.0, "white_including_hispanic": 357113.0, "black_including_hispanic": 23386.0, "amerindian_including_hispanic": 2661.0, "asian_including_hispanic": 545639.0, "commute_5_9_mins": 65617.0, "commute_35_39_mins": 14816.0, "commute_40_44_mins": 27826.0, "commute_60_89_mins": 47796.0, "commute_90_more_mins": 17827.0, "households_retirement_income": 109001.0, "armed_forces": 43073.0, "civilian_labor_force": 710267.0, "employed_pop": 680505.0, "unemployed_pop": 29762.0, "not_in_labor_force": 400117.0, "pop_16_over": 1153457.0, "pop_in_labor_force": 753340.0, "asian_male_45_54": 35584.0, "asian_male_55_64": 39765.0, "black_male_45_54": 1336.0, "black_male_55_64": 1456.0, "hispanic_male_45_54": 6069.0, "hispanic_male_55_64": 4819.0, "white_male_45_54": 20572.0, "white_male_55_64": 25390.0, "bachelors_degree_2": 216993.0, "bachelors_degree_or_higher_25_64": 247995.0, "children": 305997.0, "children_in_single_female_hh": 65583.0, "commuters_by_bus": 43257.0, "commuters_by_car_truck_van": 568962.0, "commuters_by_carpool": 93324.0, "commuters_by_subway_or_elevated": 6.0, "commuters_drove_alone": 475638.0, "different_house_year_ago_different_city": 115166.0, "different_house_year_ago_same_city": 46974.0, "employed_agriculture_forestry_fishing_hunting_mining": 12103.0, "employed_arts_entertainment_recreation_accommodation_food": 117563.0, "employed_construction": 49227.0, "employed_education_health_social": 137422.0, "employed_finance_insurance_real_estate": 43936.0, "employed_information": 10342.0, "employed_manufacturing": 20916.0, "employed_other_services_not_public_admin": 29311.0, "employed_public_administration": 61288.0, "employed_retail_trade": 76789.0, "employed_science_management_admin_waste": 68890.0, "employed_transportation_warehousing_utilities": 36663.0, "employed_wholesale_trade": 16055.0, "female_female_households": 796.0, "four_more_cars": 40087.0, "gini_index": 0.4455, "graduate_professional_degree": 111687.0, "group_quarters": 44435.0, "high_school_including_ged": 280494.0, "households_public_asst_or_food_stamps": 52771.0, "in_grades_1_to_4": 62754.0, "in_grades_5_to_8": 67349.0, "in_grades_9_to_12": 61609.0, "in_school": 324608.0, "in_undergrad_college": 75435.0, "less_than_high_school_graduate": 77131.0, "male_45_64_associates_degree": 17508.0, "male_45_64_bachelors_degree": 35082.0, "male_45_64_graduate_degree": 20183.0, "male_45_64_less_than_9_grade": 4794.0, "male_45_64_grade_9_12": 9637.0, "male_45_64_high_school": 51942.0, "male_45_64_some_college": 37021.0, "male_45_to_64": 176167.0, "male_male_households": 956.0, "management_business_sci_arts_employed": 231746.0, "no_car": 21088.0, "no_cars": 36257.0, "not_us_citizen_pop": 104502.0, "occupation_management_arts": 231746.0, "occupation_natural_resources_construction_maintenance": 65229.0, "occupation_production_transportation_material": 53336.0, "occupation_sales_office": 167880.0, "occupation_services": 162314.0, "one_car": 156035.0, "two_cars": 162390.0, "three_cars": 63309.0, "pop_25_64": 743994.0, "pop_determined_poverty_status": 1388550.0, "population_1_year_and_over": 1410750.0, "population_3_years_over": 1375101.0, "poverty": 132549.0, "sales_office_employed": 167880.0, "some_college_and_associates_degree": 311439.0, "walked_to_work": 38388.0, "worked_at_home": 30644.0, "workers_16_and_over": 703939.0, "associates_degree": 106987.0, "bachelors_degree": 216993.0, "high_school_diploma": 258133.0, "less_one_year_college": 56498.0, "masters_degree": 77325.0, "one_year_more_college": 147954.0, "pop_25_years_over": 997744.0, "commute_35_44_mins": 42642.0, "commute_60_more_mins": 65623.0, "commute_less_10_mins": 78068.0, "commuters_16_over": 673295.0, "hispanic_any_race": 150125.0, "pop_5_years_over": NaN, "speak_only_english_at_home": NaN, "speak_spanish_at_home": NaN, "speak_spanish_at_home_low_english": NaN, "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "do_date": "2017"}, {"geo_id": "16", "nonfamily_households": 205361.0, "family_households": 419774.0, "median_year_structure_built": 1988.0, "rent_burden_not_computed": 16884.0, "rent_over_50_percent": 34448.0, "rent_40_to_50_percent": 16589.0, "rent_35_to_40_percent": 13600.0, "rent_30_to_35_percent": 14517.0, "rent_25_to_30_percent": 22121.0, "rent_20_to_25_percent": 21853.0, "rent_15_to_20_percent": 22293.0, "rent_10_to_15_percent": 19532.0, "rent_under_10_percent": 7805.0, "total_pop": 1716943.0, "male_pop": 862720.0, "female_pop": 854223.0, "median_age": 36.3, "white_pop": 1407302.0, "black_pop": 10947.0, "asian_pop": 22698.0, "hispanic_pop": 213619.0, "amerindian_pop": 19136.0, "other_race_pop": 1724.0, "two_or_more_races_pop": 39204.0, "not_hispanic_pop": 1503324.0, "commuters_by_public_transportation": 4859.0, "households": 625135.0, "median_income": 52225.0, "income_per_capita": 26386.0, "housing_units": 721818.0, "vacant_housing_units": 96683.0, "vacant_housing_units_for_rent": 12732.0, "vacant_housing_units_for_sale": 6822.0, "median_rent": 698.0, "percent_income_spent_on_rent": 28.4, "owner_occupied_housing_units": 435493.0, "million_dollar_housing_units": 2152.0, "mortgaged_housing_units": 280297.0, "families_with_young_children": 134263.0, "two_parent_families_with_young_children": 101914.0, "two_parents_in_labor_force_families_with_young_children": 47931.0, "two_parents_father_in_labor_force_families_with_young_children": 49011.0, "two_parents_mother_in_labor_force_families_with_young_children": 2916.0, "two_parents_not_in_labor_force_families_with_young_children": 2056.0, "one_parent_families_with_young_children": 32349.0, "father_one_parent_families_with_young_children": 8753.0, "father_in_labor_force_one_parent_families_with_young_children": 7593.0, "commute_10_14_mins": 133358.0, "commute_15_19_mins": 130748.0, "commute_20_24_mins": 99075.0, "commute_25_29_mins": 42561.0, "commute_30_34_mins": 77388.0, "commute_45_59_mins": 32993.0, "aggregate_travel_time_to_work": 15362010.0, "income_less_10000": 37996.0, "income_10000_14999": 29599.0, "income_15000_19999": 26252.0, "income_20000_24999": 35162.0, "income_25000_29999": 33106.0, "income_30000_34999": 36094.0, "income_35000_39999": 32900.0, "income_40000_44999": 32472.0, "income_45000_49999": 30916.0, "income_50000_59999": 56060.0, "income_60000_74999": 71609.0, "income_75000_99999": 78785.0, "income_100000_124999": 50721.0, "income_125000_149999": 27918.0, "income_150000_199999": 25116.0, "income_200000_or_more": 20429.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 822.0, "owner_occupied_housing_units_lower_value_quartile": 141200.0, "owner_occupied_housing_units_median_value": 207100.0, "owner_occupied_housing_units_upper_value_quartile": 305400.0, "married_households": 338425.0, "occupied_housing_units": 625135.0, "housing_units_renter_occupied": 189642.0, "dwellings_1_units_detached": 540337.0, "dwellings_1_units_attached": 19243.0, "dwellings_2_units": 14993.0, "dwellings_3_to_4_units": 30957.0, "dwellings_5_to_9_units": 18485.0, "dwellings_10_to_19_units": 15151.0, "dwellings_20_to_49_units": 11522.0, "dwellings_50_or_more_units": 16668.0, "mobile_homes": 53556.0, "housing_built_2005_or_later": 29649.0, "housing_built_2000_to_2004": 27931.0, "housing_built_1939_or_earlier": 31407.0, "male_under_5": 60820.0, "male_5_to_9": 60630.0, "male_10_to_14": 67820.0, "male_15_to_17": 38532.0, "male_18_to_19": 26740.0, "male_20": 13103.0, "male_21": 12287.0, "male_22_to_24": 33909.0, "male_25_to_29": 57264.0, "male_30_to_34": 54832.0, "male_35_to_39": 55334.0, "male_40_to_44": 53838.0, "male_45_to_49": 49497.0, "male_50_to_54": 49102.0, "male_55_to_59": 55053.0, "male_60_61": 22401.0, "male_62_64": 27588.0, "male_65_to_66": 17600.0, "male_67_to_69": 27534.0, "male_70_to_74": 33052.0, "male_75_to_79": 19982.0, "male_80_to_84": 14858.0, "male_85_and_over": 10944.0, "female_under_5": 55406.0, "female_5_to_9": 58862.0, "female_10_to_14": 63956.0, "female_15_to_17": 37259.0, "female_18_to_19": 23859.0, "female_20": 9894.0, "female_21": 9938.0, "female_22_to_24": 32140.0, "female_25_to_29": 54758.0, "female_30_to_34": 54674.0, "female_35_to_39": 60488.0, "female_40_to_44": 45997.0, "female_45_to_49": 50929.0, "female_50_to_54": 48852.0, "female_55_to_59": 54568.0, "female_60_to_61": 24134.0, "female_62_to_64": 29885.0, "female_65_to_66": 20771.0, "female_67_to_69": 27130.0, "female_70_to_74": 34279.0, "female_75_to_79": 22107.0, "female_80_to_84": 15562.0, "female_85_and_over": 18775.0, "white_including_hispanic": 1546011.0, "black_including_hispanic": 11347.0, "amerindian_including_hispanic": 21589.0, "asian_including_hispanic": 23113.0, "commute_5_9_mins": 109328.0, "commute_35_39_mins": 14590.0, "commute_40_44_mins": 18052.0, "commute_60_89_mins": 21720.0, "commute_90_more_mins": 14228.0, "households_retirement_income": 117907.0, "armed_forces": 4317.0, "civilian_labor_force": 820913.0, "employed_pop": 786913.0, "unemployed_pop": 34000.0, "not_in_labor_force": 503319.0, "pop_16_over": 1328549.0, "pop_in_labor_force": 825230.0, "asian_male_45_54": 845.0, "asian_male_55_64": 1320.0, "black_male_45_54": 814.0, "black_male_55_64": 535.0, "hispanic_male_45_54": 11542.0, "hispanic_male_55_64": 7294.0, "white_male_45_54": 82669.0, "white_male_55_64": 92478.0, "bachelors_degree_2": 202800.0, "bachelors_degree_or_higher_25_64": 230085.0, "children": 443285.0, "children_in_single_female_hh": 71460.0, "commuters_by_bus": 4469.0, "commuters_by_car_truck_van": 690863.0, "commuters_by_carpool": 78853.0, "commuters_by_subway_or_elevated": 170.0, "commuters_drove_alone": 612010.0, "different_house_year_ago_different_city": 200667.0, "different_house_year_ago_same_city": 81542.0, "employed_agriculture_forestry_fishing_hunting_mining": 38706.0, "employed_arts_entertainment_recreation_accommodation_food": 70773.0, "employed_construction": 62540.0, "employed_education_health_social": 174335.0, "employed_finance_insurance_real_estate": 38192.0, "employed_information": 13658.0, "employed_manufacturing": 77682.0, "employed_other_services_not_public_admin": 40702.0, "employed_public_administration": 40306.0, "employed_retail_trade": 96615.0, "employed_science_management_admin_waste": 73981.0, "employed_transportation_warehousing_utilities": 39127.0, "employed_wholesale_trade": 20296.0, "female_female_households": 466.0, "four_more_cars": 69092.0, "gini_index": 0.4478, "graduate_professional_degree": 94858.0, "group_quarters": 29946.0, "high_school_including_ged": 313029.0, "households_public_asst_or_food_stamps": 66035.0, "in_grades_1_to_4": 98136.0, "in_grades_5_to_8": 103929.0, "in_grades_9_to_12": 102332.0, "in_school": 454019.0, "in_undergrad_college": 87793.0, "less_than_high_school_graduate": 101739.0, "male_45_64_associates_degree": 20173.0, "male_45_64_bachelors_degree": 34106.0, "male_45_64_graduate_degree": 18505.0, "male_45_64_less_than_9_grade": 6939.0, "male_45_64_grade_9_12": 13346.0, "male_45_64_high_school": 59891.0, "male_45_64_some_college": 50681.0, "male_45_to_64": 203641.0, "male_male_households": 414.0, "management_business_sci_arts_employed": 271899.0, "no_car": 14541.0, "no_cars": 24650.0, "not_us_citizen_pop": 60943.0, "occupation_management_arts": 271899.0, "occupation_natural_resources_construction_maintenance": 99652.0, "occupation_production_transportation_material": 104269.0, "occupation_sales_office": 175175.0, "occupation_services": 135918.0, "one_car": 172861.0, "two_cars": 241441.0, "three_cars": 117091.0, "pop_25_64": 849194.0, "pop_determined_poverty_status": 1686491.0, "population_1_year_and_over": 1694182.0, "population_3_years_over": 1650911.0, "poverty": 216309.0, "sales_office_employed": 175175.0, "some_college_and_associates_degree": 399362.0, "walked_to_work": 17473.0, "worked_at_home": 44006.0, "workers_16_and_over": 773999.0, "associates_degree": 107505.0, "bachelors_degree": 202800.0, "high_school_diploma": 248246.0, "less_one_year_college": 93270.0, "masters_degree": 66560.0, "one_year_more_college": 198587.0, "pop_25_years_over": 1111788.0, "commute_35_44_mins": 32642.0, "commute_60_more_mins": 35948.0, "commute_less_10_mins": 145280.0, "commuters_16_over": 729993.0, "hispanic_any_race": NaN, "pop_5_years_over": NaN, "speak_only_english_at_home": NaN, "speak_spanish_at_home": NaN, "speak_spanish_at_home_low_english": NaN, "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "do_date": "2017"}, {"geo_id": "02", "nonfamily_households": 83340.0, "family_households": 167401.0, "median_year_structure_built": 1984.0, "rent_burden_not_computed": 7610.0, "rent_over_50_percent": 17168.0, "rent_40_to_50_percent": 6910.0, "rent_35_to_40_percent": 5997.0, "rent_30_to_35_percent": 8323.0, "rent_25_to_30_percent": 9426.0, "rent_20_to_25_percent": 12551.0, "rent_15_to_20_percent": 11606.0, "rent_10_to_15_percent": 7493.0, "rent_under_10_percent": 4313.0, "total_pop": 739795.0, "male_pop": 385776.0, "female_pop": 354019.0, "median_age": 34.5, "white_pop": 448081.0, "black_pop": 21192.0, "asian_pop": 48569.0, "hispanic_pop": 51712.0, "amerindian_pop": 105146.0, "other_race_pop": 1210.0, "two_or_more_races_pop": 55186.0, "not_hispanic_pop": 688083.0, "commuters_by_public_transportation": 5827.0, "households": 250741.0, "median_income": 73181.0, "income_per_capita": 34222.0, "housing_units": 316968.0, "vacant_housing_units": 66227.0, "vacant_housing_units_for_rent": 8292.0, "vacant_housing_units_for_sale": 2768.0, "median_rent": 1085.0, "percent_income_spent_on_rent": 28.1, "owner_occupied_housing_units": 159344.0, "million_dollar_housing_units": 898.0, "mortgaged_housing_units": 102297.0, "families_with_young_children": 60763.0, "two_parent_families_with_young_children": 45026.0, "two_parents_in_labor_force_families_with_young_children": 22375.0, "two_parents_father_in_labor_force_families_with_young_children": 18552.0, "two_parents_mother_in_labor_force_families_with_young_children": 2665.0, "two_parents_not_in_labor_force_families_with_young_children": 1434.0, "one_parent_families_with_young_children": 15737.0, "father_one_parent_families_with_young_children": 4685.0, "father_in_labor_force_one_parent_families_with_young_children": 4120.0, "commute_10_14_mins": 65363.0, "commute_15_19_mins": 58568.0, "commute_20_24_mins": 47855.0, "commute_25_29_mins": 15369.0, "commute_30_34_mins": 25437.0, "commute_45_59_mins": 11159.0, "aggregate_travel_time_to_work": 6558400.0, "income_less_10000": 11015.0, "income_10000_14999": 7426.0, "income_15000_19999": 7176.0, "income_20000_24999": 9512.0, "income_25000_29999": 10415.0, "income_30000_34999": 9318.0, "income_35000_39999": 8583.0, "income_40000_44999": 9266.0, "income_45000_49999": 8733.0, "income_50000_59999": 20084.0, "income_60000_74999": 26500.0, "income_75000_99999": 33858.0, "income_100000_124999": 29474.0, "income_125000_149999": 19934.0, "income_150000_199999": 21169.0, "income_200000_or_more": 18278.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 1201.0, "owner_occupied_housing_units_lower_value_quartile": 180800.0, "owner_occupied_housing_units_median_value": 273100.0, "owner_occupied_housing_units_upper_value_quartile": 375100.0, "married_households": 123825.0, "occupied_housing_units": 250741.0, "housing_units_renter_occupied": 91397.0, "dwellings_1_units_detached": 198319.0, "dwellings_1_units_attached": 25568.0, "dwellings_2_units": 17076.0, "dwellings_3_to_4_units": 22198.0, "dwellings_5_to_9_units": 13478.0, "dwellings_10_to_19_units": 9002.0, "dwellings_20_to_49_units": 11067.0, "dwellings_50_or_more_units": 4850.0, "mobile_homes": 14685.0, "housing_built_2005_or_later": 4480.0, "housing_built_2000_to_2004": 8538.0, "housing_built_1939_or_earlier": 4575.0, "male_under_5": 26064.0, "male_5_to_9": 27390.0, "male_10_to_14": 25234.0, "male_15_to_17": 15373.0, "male_18_to_19": 10731.0, "male_20": 5167.0, "male_21": 7294.0, "male_22_to_24": 17918.0, "male_25_to_29": 33805.0, "male_30_to_34": 29715.0, "male_35_to_39": 28346.0, "male_40_to_44": 21871.0, "male_45_to_49": 22454.0, "male_50_to_54": 24117.0, "male_55_to_59": 25189.0, "male_60_61": 10980.0, "male_62_64": 12589.0, "male_65_to_66": 7785.0, "male_67_to_69": 9857.0, "male_70_to_74": 11896.0, "male_75_to_79": 5770.0, "male_80_to_84": 3728.0, "male_85_and_over": 2503.0, "female_under_5": 26855.0, "female_5_to_9": 27101.0, "female_10_to_14": 23512.0, "female_15_to_17": 13652.0, "female_18_to_19": 8002.0, "female_20": 4931.0, "female_21": 4427.0, "female_22_to_24": 14583.0, "female_25_to_29": 28902.0, "female_30_to_34": 25727.0, "female_35_to_39": 27420.0, "female_40_to_44": 19487.0, "female_45_to_49": 19454.0, "female_50_to_54": 23027.0, "female_55_to_59": 23814.0, "female_60_to_61": 9336.0, "female_62_to_64": 12287.0, "female_65_to_66": 6495.0, "female_67_to_69": 10367.0, "female_70_to_74": 10438.0, "female_75_to_79": 6322.0, "female_80_to_84": 4343.0, "female_85_and_over": 3537.0, "white_including_hispanic": 474856.0, "black_including_hispanic": 22107.0, "amerindian_including_hispanic": 109948.0, "asian_including_hispanic": 49385.0, "commute_5_9_mins": 62579.0, "commute_35_39_mins": 3031.0, "commute_40_44_mins": 5078.0, "commute_60_89_mins": 9096.0, "commute_90_more_mins": 7928.0, "households_retirement_income": 49553.0, "armed_forces": 16609.0, "civilian_labor_force": 373249.0, "employed_pop": 344982.0, "unemployed_pop": 28267.0, "not_in_labor_force": 183338.0, "pop_16_over": 573196.0, "pop_in_labor_force": 389858.0, "asian_male_45_54": 3468.0, "asian_male_55_64": 2774.0, "black_male_45_54": 956.0, "black_male_55_64": 1160.0, "hispanic_male_45_54": 2486.0, "hispanic_male_55_64": 1251.0, "white_male_45_54": 32552.0, "white_male_55_64": 36388.0, "bachelors_degree_2": 86444.0, "bachelors_degree_or_higher_25_64": 113311.0, "children": 185181.0, "children_in_single_female_hh": 36893.0, "commuters_by_bus": 5124.0, "commuters_by_car_truck_van": 285020.0, "commuters_by_carpool": 41554.0, "commuters_by_subway_or_elevated": 72.0, "commuters_drove_alone": 243466.0, "different_house_year_ago_different_city": 71827.0, "different_house_year_ago_same_city": 56540.0, "employed_agriculture_forestry_fishing_hunting_mining": 15248.0, "employed_arts_entertainment_recreation_accommodation_food": 32032.0, "employed_construction": 25180.0, "employed_education_health_social": 85599.0, "employed_finance_insurance_real_estate": 11111.0, "employed_information": 7449.0, "employed_manufacturing": 12344.0, "employed_other_services_not_public_admin": 15083.0, "employed_public_administration": 39400.0, "employed_retail_trade": 40381.0, "employed_science_management_admin_waste": 26392.0, "employed_transportation_warehousing_utilities": 28841.0, "employed_wholesale_trade": 5922.0, "female_female_households": 215.0, "four_more_cars": 18468.0, "gini_index": 0.4241, "graduate_professional_degree": 52240.0, "group_quarters": 27545.0, "high_school_including_ged": 133132.0, "households_public_asst_or_food_stamps": 33454.0, "in_grades_1_to_4": 41482.0, "in_grades_5_to_8": 39218.0, "in_grades_9_to_12": 40414.0, "in_school": 186461.0, "in_undergrad_college": 35247.0, "less_than_high_school_graduate": 40036.0, "male_45_64_associates_degree": 7980.0, "male_45_64_bachelors_degree": 15083.0, "male_45_64_graduate_degree": 10293.0, "male_45_64_less_than_9_grade": 2396.0, "male_45_64_grade_9_12": 4065.0, "male_45_64_high_school": 30842.0, "male_45_64_some_college": 24670.0, "male_45_to_64": 95329.0, "male_male_households": 75.0, "management_business_sci_arts_employed": 128922.0, "no_car": 18935.0, "no_cars": 25141.0, "not_us_citizen_pop": 27023.0, "occupation_management_arts": 128922.0, "occupation_natural_resources_construction_maintenance": 38802.0, "occupation_production_transportation_material": 37177.0, "occupation_sales_office": 80166.0, "occupation_services": 59915.0, "one_car": 74913.0, "two_cars": 95352.0, "three_cars": 36867.0, "pop_25_64": 398520.0, "pop_determined_poverty_status": 720472.0, "population_1_year_and_over": 730874.0, "population_3_years_over": 707412.0, "poverty": 80012.0, "sales_office_employed": 80166.0, "some_college_and_associates_degree": 169709.0, "walked_to_work": 25829.0, "worked_at_home": 15984.0, "workers_16_and_over": 351282.0, "associates_degree": 42724.0, "bachelors_degree": 86444.0, "high_school_diploma": 105599.0, "less_one_year_college": 36507.0, "masters_degree": 37005.0, "one_year_more_college": 90478.0, "pop_25_years_over": 481561.0, "commute_35_44_mins": 8109.0, "commute_60_more_mins": 17024.0, "commute_less_10_mins": 86414.0, "commuters_16_over": 335298.0, "hispanic_any_race": NaN, "pop_5_years_over": NaN, "speak_only_english_at_home": NaN, "speak_spanish_at_home": NaN, "speak_spanish_at_home_low_english": NaN, "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "do_date": "2017"}, {"geo_id": "54", "nonfamily_households": 253766.0, "family_households": 461542.0, "median_year_structure_built": 1974.0, "rent_burden_not_computed": 36304.0, "rent_over_50_percent": 40626.0, "rent_40_to_50_percent": 11105.0, "rent_35_to_40_percent": 10883.0, "rent_30_to_35_percent": 15262.0, "rent_25_to_30_percent": 19347.0, "rent_20_to_25_percent": 18186.0, "rent_15_to_20_percent": 18150.0, "rent_10_to_15_percent": 17697.0, "rent_under_10_percent": 8866.0, "total_pop": 1815857.0, "male_pop": 897166.0, "female_pop": 918691.0, "median_age": 42.4, "white_pop": 1670509.0, "black_pop": 71358.0, "asian_pop": 13695.0, "hispanic_pop": 22807.0, "amerindian_pop": 2391.0, "other_race_pop": 6730.0, "two_or_more_races_pop": 28023.0, "not_hispanic_pop": 1793050.0, "commuters_by_public_transportation": 6552.0, "households": 715308.0, "median_income": 43469.0, "income_per_capita": 24478.0, "housing_units": 892240.0, "vacant_housing_units": 176932.0, "vacant_housing_units_for_rent": 17949.0, "vacant_housing_units_for_sale": 12257.0, "median_rent": 523.0, "percent_income_spent_on_rent": 29.4, "owner_occupied_housing_units": 518882.0, "million_dollar_housing_units": 1297.0, "mortgaged_housing_units": 238355.0, "families_with_young_children": 110648.0, "two_parent_families_with_young_children": 64600.0, "two_parents_in_labor_force_families_with_young_children": 39350.0, "two_parents_father_in_labor_force_families_with_young_children": 21054.0, "two_parents_mother_in_labor_force_families_with_young_children": 2043.0, "two_parents_not_in_labor_force_families_with_young_children": 2153.0, "one_parent_families_with_young_children": 46048.0, "father_one_parent_families_with_young_children": 12836.0, "father_in_labor_force_one_parent_families_with_young_children": 10966.0, "commute_10_14_mins": 94390.0, "commute_15_19_mins": 114301.0, "commute_20_24_mins": 94392.0, "commute_25_29_mins": 41942.0, "commute_30_34_mins": 77360.0, "commute_45_59_mins": 49267.0, "aggregate_travel_time_to_work": 17873265.0, "income_less_10000": 70097.0, "income_10000_14999": 46980.0, "income_15000_19999": 50633.0, "income_20000_24999": 48781.0, "income_25000_29999": 39103.0, "income_30000_34999": 40101.0, "income_35000_39999": 35968.0, "income_40000_44999": 34256.0, "income_45000_49999": 28292.0, "income_50000_59999": 58907.0, "income_60000_74999": 69641.0, "income_75000_99999": 77770.0, "income_100000_124999": 46350.0, "income_125000_149999": 25578.0, "income_150000_199999": 23678.0, "income_200000_or_more": 19173.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 690.0, "owner_occupied_housing_units_lower_value_quartile": 68500.0, "owner_occupied_housing_units_median_value": 119800.0, "owner_occupied_housing_units_upper_value_quartile": 197100.0, "married_households": 346532.0, "occupied_housing_units": 715308.0, "housing_units_renter_occupied": 196426.0, "dwellings_1_units_detached": 626976.0, "dwellings_1_units_attached": 19857.0, "dwellings_2_units": 19699.0, "dwellings_3_to_4_units": 26410.0, "dwellings_5_to_9_units": 25517.0, "dwellings_10_to_19_units": 14678.0, "dwellings_20_to_49_units": 10845.0, "dwellings_50_or_more_units": 13582.0, "mobile_homes": 134178.0, "housing_built_2005_or_later": 13541.0, "housing_built_2000_to_2004": 20542.0, "housing_built_1939_or_earlier": 65374.0, "male_under_5": 51869.0, "male_5_to_9": 54008.0, "male_10_to_14": 54772.0, "male_15_to_17": 30944.0, "male_18_to_19": 27535.0, "male_20": 10660.0, "male_21": 13016.0, "male_22_to_24": 33285.0, "male_25_to_29": 54717.0, "male_30_to_34": 50540.0, "male_35_to_39": 56829.0, "male_40_to_44": 54069.0, "male_45_to_49": 58794.0, "male_50_to_54": 60123.0, "male_55_to_59": 61784.0, "male_60_61": 28193.0, "male_62_64": 37758.0, "male_65_to_66": 26046.0, "male_67_to_69": 33865.0, "male_70_to_74": 39544.0, "male_75_to_79": 29484.0, "male_80_to_84": 16365.0, "male_85_and_over": 12966.0, "female_under_5": 46018.0, "female_5_to_9": 50225.0, "female_10_to_14": 53134.0, "female_15_to_17": 32433.0, "female_18_to_19": 24034.0, "female_20": 12144.0, "female_21": 10501.0, "female_22_to_24": 31929.0, "female_25_to_29": 53520.0, "female_30_to_34": 49820.0, "female_35_to_39": 53013.0, "female_40_to_44": 56722.0, "female_45_to_49": 58586.0, "female_50_to_54": 60236.0, "female_55_to_59": 63062.0, "female_60_to_61": 30704.0, "female_62_to_64": 40308.0, "female_65_to_66": 26056.0, "female_67_to_69": 35287.0, "female_70_to_74": 46786.0, "female_75_to_79": 34879.0, "female_80_to_84": 23928.0, "female_85_and_over": 25366.0, "white_including_hispanic": 1684321.0, "black_including_hispanic": 71991.0, "amerindian_including_hispanic": 2438.0, "asian_including_hispanic": 13733.0, "commute_5_9_mins": 80076.0, "commute_35_39_mins": 18211.0, "commute_40_44_mins": 23318.0, "commute_60_89_mins": 39983.0, "commute_90_more_mins": 25678.0, "households_retirement_income": 178834.0, "armed_forces": 1487.0, "civilian_labor_force": 777186.0, "employed_pop": 725200.0, "unemployed_pop": 51986.0, "not_in_labor_force": 706379.0, "pop_16_over": 1485052.0, "pop_in_labor_force": 778673.0, "asian_male_45_54": 790.0, "asian_male_55_64": 519.0, "black_male_45_54": 4813.0, "black_male_55_64": 4783.0, "hispanic_male_45_54": 1130.0, "hispanic_male_55_64": 1288.0, "white_male_45_54": 111218.0, "white_male_55_64": 119960.0, "bachelors_degree_2": 156378.0, "bachelors_degree_or_higher_25_64": 198681.0, "children": 373403.0, "children_in_single_female_hh": 89577.0, "commuters_by_bus": 5831.0, "commuters_by_car_truck_van": 653334.0, "commuters_by_carpool": 64022.0, "commuters_by_subway_or_elevated": 208.0, "commuters_drove_alone": 589312.0, "different_house_year_ago_different_city": 167834.0, "different_house_year_ago_same_city": 37548.0, "employed_agriculture_forestry_fishing_hunting_mining": 27209.0, "employed_arts_entertainment_recreation_accommodation_food": 72040.0, "employed_construction": 47520.0, "employed_education_health_social": 195210.0, "employed_finance_insurance_real_estate": 31352.0, "employed_information": 9537.0, "employed_manufacturing": 57645.0, "employed_other_services_not_public_admin": 32618.0, "employed_public_administration": 48018.0, "employed_retail_trade": 93282.0, "employed_science_management_admin_waste": 54025.0, "employed_transportation_warehousing_utilities": 39942.0, "employed_wholesale_trade": 16802.0, "female_female_households": 1215.0, "four_more_cars": 40850.0, "gini_index": 0.4686, "graduate_professional_degree": 102078.0, "group_quarters": 47432.0, "high_school_including_ged": 526652.0, "households_public_asst_or_food_stamps": 124082.0, "in_grades_1_to_4": 85114.0, "in_grades_5_to_8": 86176.0, "in_grades_9_to_12": 83749.0, "in_school": 397036.0, "in_undergrad_college": 80085.0, "less_than_high_school_graduate": 165261.0, "male_45_64_associates_degree": 12234.0, "male_45_64_bachelors_degree": 27912.0, "male_45_64_graduate_degree": 17077.0, "male_45_64_less_than_9_grade": 11413.0, "male_45_64_grade_9_12": 24922.0, "male_45_64_high_school": 111578.0, "male_45_64_some_college": 41516.0, "male_45_to_64": 246652.0, "male_male_households": 555.0, "management_business_sci_arts_employed": 240792.0, "no_car": 17678.0, "no_cars": 60322.0, "not_us_citizen_pop": 14264.0, "occupation_management_arts": 240792.0, "occupation_natural_resources_construction_maintenance": 79635.0, "occupation_production_transportation_material": 94160.0, "occupation_sales_office": 168862.0, "occupation_services": 141751.0, "one_car": 248927.0, "two_cars": 266158.0, "three_cars": 99051.0, "pop_25_64": 928778.0, "pop_determined_poverty_status": 1762467.0, "population_1_year_and_over": 1798306.0, "population_3_years_over": 1760286.0, "poverty": 336301.0, "sales_office_employed": 168862.0, "some_college_and_associates_degree": 328981.0, "walked_to_work": 20921.0, "worked_at_home": 23521.0, "workers_16_and_over": 711997.0, "associates_degree": 90494.0, "bachelors_degree": 156378.0, "high_school_diploma": 443031.0, "less_one_year_college": 75084.0, "masters_degree": 77079.0, "one_year_more_college": 163403.0, "pop_25_years_over": 1279350.0, "commute_35_44_mins": 41529.0, "commute_60_more_mins": 65661.0, "commute_less_10_mins": 109634.0, "commuters_16_over": 688476.0, "hispanic_any_race": NaN, "pop_5_years_over": NaN, "speak_only_english_at_home": NaN, "speak_spanish_at_home": NaN, "speak_spanish_at_home_low_english": NaN, "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "do_date": "2017"}]}