{"table_name": "place_2018_1yr", "table_fullname": "bigquery-public-data.census_bureau_acs.place_2018_1yr", "column_names": ["geo_id", "nonfamily_households", "family_households", "median_year_structure_built", "rent_burden_not_computed", "rent_over_50_percent", "rent_40_to_50_percent", "rent_35_to_40_percent", "rent_30_to_35_percent", "rent_25_to_30_percent", "rent_20_to_25_percent", "rent_15_to_20_percent", "rent_10_to_15_percent", "rent_under_10_percent", "total_pop", "male_pop", "female_pop", "median_age", "white_pop", "black_pop", "asian_pop", "hispanic_pop", "amerindian_pop", "other_race_pop", "two_or_more_races_pop", "not_hispanic_pop", "commuters_by_public_transportation", "households", "median_income", "income_per_capita", "housing_units", "vacant_housing_units", "vacant_housing_units_for_rent", "vacant_housing_units_for_sale", "median_rent", "percent_income_spent_on_rent", "owner_occupied_housing_units", "million_dollar_housing_units", "mortgaged_housing_units", "families_with_young_children", "two_parent_families_with_young_children", "two_parents_in_labor_force_families_with_young_children", "two_parents_father_in_labor_force_families_with_young_children", "two_parents_mother_in_labor_force_families_with_young_children", "two_parents_not_in_labor_force_families_with_young_children", "one_parent_families_with_young_children", "father_one_parent_families_with_young_children", "father_in_labor_force_one_parent_families_with_young_children", "commute_10_14_mins", "commute_15_19_mins", "commute_20_24_mins", "commute_25_29_mins", "commute_30_34_mins", "commute_45_59_mins", "aggregate_travel_time_to_work", "income_less_10000", "income_10000_14999", "income_15000_19999", "income_20000_24999", "income_25000_29999", "income_30000_34999", "income_35000_39999", "income_40000_44999", "income_45000_49999", "income_50000_59999", "income_60000_74999", "income_75000_99999", "income_100000_124999", "income_125000_149999", "income_150000_199999", "income_200000_or_more", "renter_occupied_housing_units_paying_cash_median_gross_rent", "owner_occupied_housing_units_lower_value_quartile", "owner_occupied_housing_units_median_value", "owner_occupied_housing_units_upper_value_quartile", "married_households", "occupied_housing_units", "housing_units_renter_occupied", "dwellings_1_units_detached", "dwellings_1_units_attached", "dwellings_2_units", "dwellings_3_to_4_units", "dwellings_5_to_9_units", "dwellings_10_to_19_units", "dwellings_20_to_49_units", "dwellings_50_or_more_units", "mobile_homes", "housing_built_2005_or_later", "housing_built_2000_to_2004", "housing_built_1939_or_earlier", "male_under_5", "male_5_to_9", "male_10_to_14", "male_15_to_17", "male_18_to_19", "male_20", "male_21", "male_22_to_24", "male_25_to_29", "male_30_to_34", "male_35_to_39", "male_40_to_44", "male_45_to_49", "male_50_to_54", "male_55_to_59", "male_60_61", "male_62_64", "male_65_to_66", "male_67_to_69", "male_70_to_74", "male_75_to_79", "male_80_to_84", "male_85_and_over", "female_under_5", "female_5_to_9", "female_10_to_14", "female_15_to_17", "female_18_to_19", "female_20", "female_21", "female_22_to_24", "female_25_to_29", "female_30_to_34", "female_35_to_39", "female_40_to_44", "female_45_to_49", "female_50_to_54", "female_55_to_59", "female_60_to_61", "female_62_to_64", "female_65_to_66", "female_67_to_69", "female_70_to_74", "female_75_to_79", "female_80_to_84", "female_85_and_over", "white_including_hispanic", "black_including_hispanic", "amerindian_including_hispanic", "asian_including_hispanic", "commute_5_9_mins", "commute_35_39_mins", "commute_40_44_mins", "commute_60_89_mins", "commute_90_more_mins", "households_retirement_income", "armed_forces", "civilian_labor_force", "employed_pop", "unemployed_pop", "not_in_labor_force", "pop_16_over", "pop_in_labor_force", "asian_male_45_54", "asian_male_55_64", "black_male_45_54", "black_male_55_64", "hispanic_male_45_54", "hispanic_male_55_64", "white_male_45_54", "white_male_55_64", "bachelors_degree_2", "bachelors_degree_or_higher_25_64", "children", "children_in_single_female_hh", "commuters_by_bus", "commuters_by_car_truck_van", "commuters_by_carpool", "commuters_by_subway_or_elevated", "commuters_drove_alone", "different_house_year_ago_different_city", "different_house_year_ago_same_city", "employed_agriculture_forestry_fishing_hunting_mining", "employed_arts_entertainment_recreation_accommodation_food", "employed_construction", "employed_education_health_social", "employed_finance_insurance_real_estate", "employed_information", "employed_manufacturing", "employed_other_services_not_public_admin", "employed_public_administration", "employed_retail_trade", "employed_science_management_admin_waste", "employed_transportation_warehousing_utilities", "employed_wholesale_trade", "female_female_households", "four_more_cars", "gini_index", "graduate_professional_degree", "group_quarters", "high_school_including_ged", "households_public_asst_or_food_stamps", "in_grades_1_to_4", "in_grades_5_to_8", "in_grades_9_to_12", "in_school", "in_undergrad_college", "less_than_high_school_graduate", "male_45_64_associates_degree", "male_45_64_bachelors_degree", "male_45_64_graduate_degree", "male_45_64_less_than_9_grade", "male_45_64_grade_9_12", "male_45_64_high_school", "male_45_64_some_college", "male_45_to_64", "male_male_households", "management_business_sci_arts_employed", "no_car", "no_cars", "not_us_citizen_pop", "occupation_management_arts", "occupation_natural_resources_construction_maintenance", "occupation_production_transportation_material", "occupation_sales_office", "occupation_services", "one_car", "two_cars", "three_cars", "pop_25_64", "pop_determined_poverty_status", "population_1_year_and_over", "population_3_years_over", "poverty", "sales_office_employed", "some_college_and_associates_degree", "walked_to_work", "worked_at_home", "workers_16_and_over", "associates_degree", "bachelors_degree", "high_school_diploma", "less_one_year_college", "masters_degree", "one_year_more_college", "pop_25_years_over", "commute_35_44_mins", "commute_60_more_mins", "commute_less_10_mins", "commuters_16_over", "hispanic_any_race", "pop_5_years_over", "speak_only_english_at_home", "speak_spanish_at_home", "speak_spanish_at_home_low_english", "pop_15_and_over", "pop_never_married", "pop_now_married", "pop_separated", "pop_widowed", "pop_divorced", "do_date"], "column_types": ["STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "STRING"], "nested_column_names": ["geo_id", "nonfamily_households", "family_households", "median_year_structure_built", "rent_burden_not_computed", "rent_over_50_percent", "rent_40_to_50_percent", "rent_35_to_40_percent", "rent_30_to_35_percent", "rent_25_to_30_percent", "rent_20_to_25_percent", "rent_15_to_20_percent", "rent_10_to_15_percent", "rent_under_10_percent", "total_pop", "male_pop", "female_pop", "median_age", "white_pop", "black_pop", "asian_pop", "hispanic_pop", "amerindian_pop", "other_race_pop", "two_or_more_races_pop", "not_hispanic_pop", "commuters_by_public_transportation", "households", "median_income", "income_per_capita", "housing_units", "vacant_housing_units", "vacant_housing_units_for_rent", "vacant_housing_units_for_sale", "median_rent", "percent_income_spent_on_rent", "owner_occupied_housing_units", "million_dollar_housing_units", "mortgaged_housing_units", "families_with_young_children", "two_parent_families_with_young_children", "two_parents_in_labor_force_families_with_young_children", "two_parents_father_in_labor_force_families_with_young_children", "two_parents_mother_in_labor_force_families_with_young_children", "two_parents_not_in_labor_force_families_with_young_children", "one_parent_families_with_young_children", "father_one_parent_families_with_young_children", "father_in_labor_force_one_parent_families_with_young_children", "commute_10_14_mins", "commute_15_19_mins", "commute_20_24_mins", "commute_25_29_mins", "commute_30_34_mins", "commute_45_59_mins", "aggregate_travel_time_to_work", "income_less_10000", "income_10000_14999", "income_15000_19999", "income_20000_24999", "income_25000_29999", "income_30000_34999", "income_35000_39999", "income_40000_44999", "income_45000_49999", "income_50000_59999", "income_60000_74999", "income_75000_99999", "income_100000_124999", "income_125000_149999", "income_150000_199999", "income_200000_or_more", "renter_occupied_housing_units_paying_cash_median_gross_rent", "owner_occupied_housing_units_lower_value_quartile", "owner_occupied_housing_units_median_value", "owner_occupied_housing_units_upper_value_quartile", "married_households", "occupied_housing_units", "housing_units_renter_occupied", "dwellings_1_units_detached", "dwellings_1_units_attached", "dwellings_2_units", "dwellings_3_to_4_units", "dwellings_5_to_9_units", "dwellings_10_to_19_units", "dwellings_20_to_49_units", "dwellings_50_or_more_units", "mobile_homes", "housing_built_2005_or_later", "housing_built_2000_to_2004", "housing_built_1939_or_earlier", "male_under_5", "male_5_to_9", "male_10_to_14", "male_15_to_17", "male_18_to_19", "male_20", "male_21", "male_22_to_24", "male_25_to_29", "male_30_to_34", "male_35_to_39", "male_40_to_44", "male_45_to_49", "male_50_to_54", "male_55_to_59", "male_60_61", "male_62_64", "male_65_to_66", "male_67_to_69", "male_70_to_74", "male_75_to_79", "male_80_to_84", "male_85_and_over", "female_under_5", "female_5_to_9", "female_10_to_14", "female_15_to_17", "female_18_to_19", "female_20", "female_21", "female_22_to_24", "female_25_to_29", "female_30_to_34", "female_35_to_39", "female_40_to_44", "female_45_to_49", "female_50_to_54", "female_55_to_59", "female_60_to_61", "female_62_to_64", "female_65_to_66", "female_67_to_69", "female_70_to_74", "female_75_to_79", "female_80_to_84", "female_85_and_over", "white_including_hispanic", "black_including_hispanic", "amerindian_including_hispanic", "asian_including_hispanic", "commute_5_9_mins", "commute_35_39_mins", "commute_40_44_mins", "commute_60_89_mins", "commute_90_more_mins", "households_retirement_income", "armed_forces", "civilian_labor_force", "employed_pop", "unemployed_pop", "not_in_labor_force", "pop_16_over", "pop_in_labor_force", "asian_male_45_54", "asian_male_55_64", "black_male_45_54", "black_male_55_64", "hispanic_male_45_54", "hispanic_male_55_64", "white_male_45_54", "white_male_55_64", "bachelors_degree_2", "bachelors_degree_or_higher_25_64", "children", "children_in_single_female_hh", "commuters_by_bus", "commuters_by_car_truck_van", "commuters_by_carpool", "commuters_by_subway_or_elevated", "commuters_drove_alone", "different_house_year_ago_different_city", "different_house_year_ago_same_city", "employed_agriculture_forestry_fishing_hunting_mining", "employed_arts_entertainment_recreation_accommodation_food", "employed_construction", "employed_education_health_social", "employed_finance_insurance_real_estate", "employed_information", "employed_manufacturing", "employed_other_services_not_public_admin", "employed_public_administration", "employed_retail_trade", "employed_science_management_admin_waste", "employed_transportation_warehousing_utilities", "employed_wholesale_trade", "female_female_households", "four_more_cars", "gini_index", "graduate_professional_degree", "group_quarters", "high_school_including_ged", "households_public_asst_or_food_stamps", "in_grades_1_to_4", "in_grades_5_to_8", "in_grades_9_to_12", "in_school", "in_undergrad_college", "less_than_high_school_graduate", "male_45_64_associates_degree", "male_45_64_bachelors_degree", "male_45_64_graduate_degree", "male_45_64_less_than_9_grade", "male_45_64_grade_9_12", "male_45_64_high_school", "male_45_64_some_college", "male_45_to_64", "male_male_households", "management_business_sci_arts_employed", "no_car", "no_cars", "not_us_citizen_pop", "occupation_management_arts", "occupation_natural_resources_construction_maintenance", "occupation_production_transportation_material", "occupation_sales_office", "occupation_services", "one_car", "two_cars", "three_cars", "pop_25_64", "pop_determined_poverty_status", "population_1_year_and_over", "population_3_years_over", "poverty", "sales_office_employed", "some_college_and_associates_degree", "walked_to_work", "worked_at_home", "workers_16_and_over", "associates_degree", "bachelors_degree", "high_school_diploma", "less_one_year_college", "masters_degree", "one_year_more_college", "pop_25_years_over", "commute_35_44_mins", "commute_60_more_mins", "commute_less_10_mins", "commuters_16_over", "hispanic_any_race", "pop_5_years_over", "speak_only_english_at_home", "speak_spanish_at_home", "speak_spanish_at_home_low_english", "pop_15_and_over", "pop_never_married", "pop_now_married", "pop_separated", "pop_widowed", "pop_divorced", "do_date"], "nested_column_types": ["STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "STRING"], "description": ["Incorporated Places Geoids", "Nonfamily Households. A householder living alone or with nonrelatives only. Unmarried couples households, whether opposite-sex or same-sex, with no relatives of the householder present are tabulated in nonfamily households.", "Family Households. A family consists of a householder and one or more other people living in the same household who are related to the householder by birth, marriage, or adoption. All people in a household who are related to the householder are regarded as members of his or her family. A family household may contain people not related to the householder, but those people are not included as part of the family of the householder in tabulations. Thus, the number of family households is equal to the number of families, but family households may include more members than do families. A household can contain only one family for purposes of tabulations. Not all households contain families since a household may be comprised of a group of unrelated people or of one person living alone -- these are called nonfamily households. Families are classified by type as either a \"married couple family\" or \"other family\" according to the sex of the householder and the presence of relatives. The data on family type are based ...", "Median Year Structure Built. Median Year Structure Built", "Housing units without rent burden computed. Units for which no rent is paid and units occupied by households that reported no income or a net loss comprise this category", "Housing units spending over 50% income on rent. Gross rent over 50 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 40 to 49.9% income on rent. Gross rent from 40.0 to 49.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 35 to 39.9% income on rent. Gross rent from 35.0 to 39.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 30 to 34.9% income on rent. Gross rent from 30.0 to 34.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 25 to 29.9% income on rent. Gross rent from 25.0 to 29.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 20 to 24.9% income on rent. Gross rent from 20.0 to 24.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 15 to 19.9% income on rent. Gross rent from 15.0 to 19.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 10 to 14.9% income on rent. Gross rent from 10.0 to 14.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending less than 10% on rent. Gross rent less than 10 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Total Population. The total number of all people living in a given geographic area.  This is a very useful catch-all denominator when calculating rates.", "Male Population. The number of people within each geography who are male.", "Female Population. The number of people within each geography who are female.", "Median Age. The median age of all people in a given geographic area.", "White Population. The number of people identifying as white, non-Hispanic in each geography.", "Black or African American Population. The number of people identifying as black or African American, non-Hispanic in each geography.", "Asian Population. The number of people identifying as Asian, non-Hispanic in each geography.", "Hispanic Population. The number of people identifying as Hispanic or Latino in each geography.", "American Indian and Alaska Native Population. The number of people identifying as American Indian or Alaska native in each geography.", "Other Race population. The number of people identifying as another race in each geography", "Two or more races population. The number of people identifying as two or more races in each geography", "Population not Hispanic. The number of people not identifying as Hispanic or Latino in each geography.", "Commuters by Public Transportation. The number of workers age 16 years and over within a geographic area who primarily traveled to work by public transportation.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Households. A count of the number of households in each geography. A household consists of one or more people who live in the same dwelling and also share at meals or living accommodation, and may consist of a single family or some other grouping of people. ", "Median Household Income in the past 12 Months. Within a geographic area, the median income received by every household on a regular basis before payments for personal income taxes, social security, union dues, medicare deductions, etc.  It includes income received from wages, salary, commissions, bonuses, and tips; self-employment income from own nonfarm or farm businesses, including proprietorships and partnerships; interest, dividends, net rental income, royalty income, or income from estates and trusts; Social Security or Railroad Retirement income; Supplemental Security Income (SSI); any cash public assistance or welfare payments from the state or local welfare office; retirement, survivor, or disability benefits; and any other sources of income received regularly such as Veterans' (VA) payments, unemployment and/or worker's compensation, child support, and alimony.", "Per Capita Income in the past 12 Months. Per capita income is the mean income computed for every man, woman, and child in a particular group. It is derived by dividing the total income of a particular group by the total population.", "Housing Units. A count of housing units in each geography.  A housing unit is a house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters.", "Vacant Housing Units. The count of vacant housing units in a geographic area. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Vacant Housing Units for Rent. The count of vacant housing units in a geographic area that are for rent. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Vacant Housing Units for Sale. The count of vacant housing units in a geographic area that are for sale. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Median Rent. The median contract rent within a geographic area. The contract rent is the monthly rent agreed to or contracted for, regardless of any furnishings, utilities, fees, meals, or services that may be included. For vacant units, it is the monthly rent asked for the rental unit at the time of interview.", "Percent of Household Income Spent on Rent. Within a geographic area, the median percentage of household income which was spent on gross rent.  Gross rent is the amount of the contract rent plus the estimated average monthly cost of utilities (electricity, gas, water, sewer etc.) and fuels (oil, coal, wood, etc.) if these are paid by the renter.  Household income is the sum of the income of all people 15 years and older living in the household.", "Owner-occupied Housing Units", "Owner-occupied Housing Units valued at $1,000,000 or more.. The count of owner occupied housing units in a geographic area that are valued at $1,000,000 or more.  Value is the respondent's estimate of how much the property (house and lot, mobile home and lot, or condominium unit) would sell for if it were for sale.", "Owner-occupied Housing Units with a Mortgage. The count of housing units within a geographic area that are mortagaged. \"Mortgage\" refers to all forms of debt where the property is pledged as security for repayment of the debt, including deeds of trust, trust deed, contracts to purchase, land contracts, junior mortgages, and home equity loans.", "Families with young children (under 6 years of age)", "Two-parent families with young children (under 6 years of age)", "Two-parent families, both parents in labor force with young children (under 6 years of age)", "Two-parent families, father only in labor force with young children (under 6 years of age)", "Two-parent families, mother only in labor force with young children (under 6 years of age)", "Two-parent families, neither parent in labor force with young children (under 6 years of age)", "One-parent families with young children (under 6 years of age)", "One-parent families, father, with young children (under 6 years of age)", "One-parent families, father in labor force, with young children (under 6 years of age)", "Number of workers with a commute between 10 and 14 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 10 and 14 minutes. ", "Number of workers with a commute between 15 and 19 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 15 and 19 minutes. ", "Number of workers with a commute between 20 and 24 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 20 and 24 minutes.", "Number of workers with a commute between 25 and 29 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 25 and 29 minutes. ", "Number of workers with a commute between 30 and 34 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 30 and 34 minutes. ", "Number of workers with a commute between 45 and 59 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 45 and 59 minutes. ", "Aggregate travel time to work. The total number of minutes every worker in a geographic area over the age of 16 who did not work from home spent spent commuting to work in one day", "Households with income less than $10,000. The number of households in a geographic area whose annual income was less than $10,000.", "Households with income of $10,000 to $14,999. The number of households in a geographic area whose annual income was between $10,000 and $14,999.", "Households with income of $15,000 to $19,999. The number of households in a geographic area whose annual income was between $15,000 and $19,999.", "Households with income of $20,000 to $24,999. The number of households in a geographic area whose annual income was between $20,000 and $24,999.", "Households with income of $25,000 to $29,999. The number of households in a geographic area whose annual income was between $20,000 and $24,999.", "Households with income of $30,000 to $34,999. The number of households in a geographic area whose annual income was between $30,000 and $34,999.", "Households with income of $35,000 to $39,999. The number of households in a geographic area whose annual income was between $35,000 and $39,999.", "Households with income of $40,000 to $44,999. The number of households in a geographic area whose annual income was between $40,000 and $44,999.", "Households with income of $45,000 to $49,999. The number of households in a geographic area whose annual income was between $45,000 and $49,999.", "Households with income of $50,000 to $59,999. The number of households in a geographic area whose annual income was between $50,000 and $59,999.", "Households with income of $60,000 to $74,999. The number of households in a geographic area whose annual income was between $60,000 and $74,999.", "Households with income of $75,000 to $99,999. The number of households in a geographic area whose annual income was between $75,000 and $99,999.", "Households with income of $100,000 to $124,999. The number of households in a geographic area whose annual income was between $100,000 and $124,999.", "Households with income of $125,000 to $149,999. The number of households in a geographic area whose annual income was between $125,000 and $149,999.", "Households with income of $150,000 to $199,999. The number of households in a geographic area whose annual income was between $150,000 and $1999,999.", "Households with income of $200,000 Or More. The number of households in a geographic area whose annual income was more than $200,000.", "Renter-Occupied Housing Units Paying Cash Rent Median Gross Rent", "Owner-Occupied Housing Units Lower Value Quartile", "Owner-Occupied Housing Units Median Value. The middle value (median) in a geographic area owner occupied housing units.", "Owner-Occupied Housing Units Upper Value Quartile", "Married households. People in formal marriages, as well as people in common-law marriages, are included. Does not include same-sex marriages.", "Occupied housing units. A housing unit is classified as occupied if it is the usual place of residence of the person or group of people living in it at the time of enumeration.", "Renter occupied housing units. All occupied units which are not owner occupied, whether they are rented for cash rent or occupied without payment of cash rent, are classified as renter-occupied.", "Single-family (one unit) detached dwellings. This is a 1-unit structure detached from any other house, that is, with open space on all four sides. Such structures are considered detached even if they have an adjoining shed or garage. A one-family house that contains a business is considered detached as long as the building has open space on all four sides. Mobile homes to which one or more permanent rooms have been added or built also are included.", "Single-family (one unit) attached dwellings. This is a 1-unit structure that has one or more walls extending from ground to roof separating it from adjoining structures. In row houses (sometimes called townhouses), double houses, or houses attached to nonresidential structures, each house is a separate, attached structure if the dividing or common wall goes from ground to roof.", "Two-family (two unit) dwellings", "Multifamily dwellings with three to 4 units", "Apartment buildings with 5 to 9 units", "Apartment buildings with 10 to 19 units", "Apartment buildings with 20 to 49 units", "Apartment buildings with 50 or more units", "Mobile homes. A manufactured home is defined as a movable dwelling, 8 feet or more wide and 40 feet or more long, designed to be towed on its own chassis, with transportation gear integral to the unit when it leaves the factory, and without need of a permanent foundation. These homes are built in accordance with the U.S. Department of Housing and Urban Development (HUD) building code.", "Housing units built in 2005 or later. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built in 2005 or later.", "Housing units built between 2000 and 2004. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built from 2000 to 2004.", "Housing units built before 1939. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built in 1939 or earlier.", "Male under 5 years. The male population over the age of five years within the specified area.", "Male age 5 to 9. The male population between the age of five years to nine years within the specified area.", "Male age 10 to 14. The male population between the age of ten years to fourteen years within the specified area.", "Male age 15 to 17. The male population between the age of fifteeen years to seventeen years within the specified area.", "Male age 18 and 19. The male population between the age of eighteen years to nineteen years within the specified area.", "Male age 20. The male population with an age of twenty years within the specified area.", "Male age 21. The male population with an age of twenty-one years within the specified area.", "Male age 22 to 24. The male population between the age of twenty-two years to twenty-four years within the specified area.", "Male age 25 to 29. The male population between the age of twenty-five years to twenty-nine years within the specified area.", "Male age 30 to 34. The male population between the age of thirty years to thirty-four years within the specified area.", "Male age 35 to 39. The male population between the age of thirty-five years to thirty-nine years within the specified area.", "Male age 40 to 44. The male population between the age of fourty years to fourty-four years within the specified area.", "Men age 45 to 49. The male population between the age of fourty-five years to fourty-nine years within the specified area.", "Men age 50 to 54. The male population between the age of fifty years to fifty-four years within the specified area.", "Men age 55 to 59. The male population between the age of fifty-five years to fifty-nine years within the specified area.", "Men age 60 to 61. The male population between the age of sixty years to sixty-one years within the specified area.", "Men age 62 to 64. The male population between the age of sixty-two years to sixty-four years within the specified area.", "Male age 65 to 66. The male population between the age of sixty-five years to sixty-six years within the specified area.", "Male age 67 to 69. The male population between the age of sixty-seven years to sixty-nine years within the specified area.", "Male age 70 to 74. The male population between the age of seventy years to seventy-four years within the specified area.", "Male age 75 to 79. The male population between the age of seventy-five years to seventy-nine years within the specified area.", "Male age 80 to 84. The male population between the age of eighty years to eighty-four years within the specified area.", "Male age 85 and over. The male population of the age of eighty-five years and over within the specified area.", "Female under 5 years. The female population over the age of five years within the specified area.", "Female age 5 to 9. The female population between the age of five years to nine years within the specified area.", "Female age 10 to 14. The female population between the age of ten years to fourteen years within the specified area.", "Female age 15 to 17. The female population between the age of fifteeen years to seventeen years within the specified area.", "Female age 18 and 19. The female population between the age of eighteen years to nineteen years within the specified area.", "Female age 20. The female population with an age of twenty years within the specified area.", "Female age 21. The female population with an age of twenty-one years within the specified area.", "Female age 22 to 24. The female population between the age of twenty-two years to twenty-four years within the specified area.", "Female age 25 to 29. The female population between the age of twenty-five years to twenty-nine years within the specified area.", "Female age 30 to 34. The female population between the age of thirty years to thirty-four years within the specified area.", "Female age 35 to 39. The female population between the age of thirty-five years to thirty-nine years within the specified area.", "Female age 40 to 44. The female population between the age of fourty years to fourty-four years within the specified area.", "Female age 45 to 49. The female population between the age of fourty-five years to fourty-nine years within the specified area.", "Female age 50 to 54. The female population between the age of fifty years to fifty-four years within the specified area.", "Female age 55 to 59. The female population between the age of fifty-five years to fifty-nine years within the specified area.", "Female age 60 and 61. The female population between the age of sixty years to sixty-one years within the specified area.", "Female age 62 to 64. The female population between the age of sixty-two years to sixty-four years within the specified area.", "Female age 65 to 66. The female population between the age of sixty-five years to sixty-six years within the specified area.", "Female age 67 to 69. The female population between the age of sixty-seven years to sixty-nine years within the specified area.", "Female age 70 to 74. The female population between the age of seventy years to seventy-four years within the specified area.", "Female age 75 to 79. The female population between the age of seventy-five years to seventy-nine years within the specified area.", "Female age 80 to 84. The female population between the age of eighty years to eighty-four years within the specified area.", "Female age 85 and over. The female population of the age of eighty-five years and over within the specified area.", "White including Hispanic", "Black including Hispanic", "American Indian including Hispanic", "Asian including Hispanic", "Number of workers with a commute between 5 and 9 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 5 and 9 minutes.", "Number of workers with a commute between 35 and 39 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 35 and 39 minutes. ", "Number of workers with a commute between 40 and 44 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 40 and 44 minutes. ", "Number of workers with a commute between 60 and 89 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 60 and 89 minutes .", "Number of workers with a commute of more than 90 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute more than 90 minutes.", "Households receiving retirement income", "Population in Armed Forces. The number of people in each geography who are members of the U.S. Armed Forces (people on active duty with the United States Army, Air Force, Navy, Marine Corps, or Coast Guard).", "Population in Civilian Labor Force. The number of civilians 16 years and over in each geography who can be classified as either \"employed\" or \"unemployed\" below.", "Employed Population. The number of civilians 16 years old and over in each geography who either (1) were \"at work,\" that is, those who did any work at all during the reference week as paid employees, worked in their own business or profession, worked on their own farm, or worked 15 hours or more as unpaid workers on a family farm or in a family business; or (2) were \"with a job but not at work,\" that is, those who did not work during the reference week but had jobs or businesses from which they were temporarily absent due to illness, bad weather, industrial dispute, vacation, or other personal reasons. Excluded from the employed are people whose only activity consisted of work around the house or unpaid volunteer work for religious, charitable, and similar organizations; also excluded are all institutionalized people and people on active duty in the United States Armed Forces.", "", "Population Not in Labor Force. The number of people in each geography who are 16 years old and over who are not classified as members of the labor force. This category consists mainly of students, homemakers, retired workers, seasonal workers interviewed in an off season who were not looking for work, institutionalized people, and people doing only incidental unpaid family work (less than 15 hours during the reference week).", "Population age 16 and over. The number of people in each geography who are age 16 or over.", "Population in Labor Force. The number of people in each geography who are either in the civilian labor force or are members of the U.S. Armed Forces (people on active duty with the United States Army, Air Force, Navy, Marine Corps, or Coast Guard).", "Asian Men age 45 to 54", "Asian Men age 55 to 64", "Black Men age 45 to 54", "Black Men age 55 to 64", "Hispanic Men age 45 to 54", "Hispanic Men age 55 to 64", "White Men age 45 to 54", "White Men age 55 to 64", "Population who completed a bachelor's degree. From mobility table.", "Population with Bachelors Degree or Higher, Ages 25 to 64. The number of people in each geography who are between the ages of 25 and 64 who have attained a bachelors degree or higher. ", "Children under 18 Years of Age. The number of people within each geography who are under 18 years of age.", "Children under 18 years of age in single female-led household", "Commuters by Bus. The number of workers age 16 years and over within a geographic area who primarily traveled to work by bus.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.  This is a subset of workers who commuted by public transport.", "Commuters by Car, Truck, or Van. The number of workers age 16 years and over within  a geographic area who primarily traveled to work by car, truck or  van.  This is the principal mode of travel or type of conveyance,  by distance rather than time, that the worker usually used to get  from home to work.", "Commuters by Carpool. The number of workers age 16 years and over within a geographic area who primarily traveled to work by carpool.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Commuters by Subway or Elevated. The number of workers age 16 years and over within a geographic area who primarily traveled to work by subway or elevated train.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.  This is a subset of workers who commuted by public transport.", "Commuters who drove alone. The number of workers age 16 years and over within a geographic area who primarily traveled by car driving alone. This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Lived in a different house one year ago in a different city. All people in a geographic area who lived in a different city within the year prior to the survey.", "Lived in a different house one year ago in the same city. All people in a geographic area who lived in the same city but moved to a different unit within the year prior to the survey.", "Workers employed in firms in agriculture, forestry, fishing, hunting, or mining. The Agriculture, Forestry, Fishing and Hunting sector comprises establishments primarily engaged in growing crops, raising animals, harvesting timber, and harvesting fish and other animals from a farm, ranch, or their natural habitats.", "Workers employed in firms in arts, entertainment, recreation, accommodation and food services. The Arts, Entertainment, and Recreation sector includes a wide range of establishments that operate facilities or provide services to meet varied cultural, entertainment, and recreational interests of their patrons. This sector comprises (1) establishments that are involved in producing, promoting, or participating in live performances, events, or exhibits intended for public viewing; (2) establishments that preserve and exhibit objects and sites of historical, cultural, or educational interest; and (3) establishments that operate facilities or provide services that enable patrons to participate in recreational activities or pursue amusement, hobby, and leisure-time interests.", "Workers employed in firms in construction. The Construction sector comprises establishments primarily engaged in the construction of buildings or engineering projects (e.g., highways and utility systems). Construction work done may include new work, additions, alterations, or maintenance and repairs.", "Workers employed in firms in educational services, health care, and social assistance. Outpatient health services, other than hospital care, including: public health administration; research and education; categorical health programs; treatment and immunization clinics; nursing; environmental health activities such as air and water pollution control; ambulance service if provided separately from fire protection services, and other general public health activities such as mosquito abatement. School health services provided by health agencies (rather than school agencies) are included here. Sewage treatment operations are classified under Sewerage.", "Workers employed in firms in finance, insurance, real estate and rental and leasing. The Real Estate and Rental and Leasing sector comprises establishments primarily engaged in renting, leasing, or otherwise allowing the use of tangible or intangible assets, and establishments providing related services. The major portion of this sector comprises establishments that rent, lease, or otherwise allow the use of their own assets by others. The assets may be tangible, as is the case of real estate and equipment, or intangible, as is the case with patents and trademarks.", "Workers employed in firms in information. The Information sector comprises establishments engaged in the following processes: (a) producing and distributing information and cultural products, (b) providing the means to transmit or distribute these products as well as data or communications, and (c) processing data. Included are the publishing industries, the motion picture and sound recording industries; the broadcasting industries, the telecommunications industries; Web search portals, data processing industries, and the information services industries.", "Workers employed in firms in manufacturing. The Manufacturing sector comprises establishments engaged in the mechanical, physical, or chemical transformation of materials, substances, or components into new products.", "Workers employed in firms in other services except public administration. The Other Services (Except Public Administration) sector comprises establishments engaged in providing services not specifically provided for elsewhere in the classification system. Establishments in this sector are primarily engaged in activities such as equipment and machinery repairing, promoting or administering religious activities, grantmaking, advocacy, and providing drycleaning and laundry services, personal care services, death care services, pet care services, photofinishing services, temporary parking services, and dating services. Private households that engage in employing workers on or about the premises in activities primarily concerned with the operation of the household are included in this sector.", "Workers employed in firms in public administration. The Public Administration sector consists of establishments of federal, state, and local government agencies that administer, oversee, and manage public programs and have executive, legislative, or judicial authority over other institutions within a given area. These agencies also set policy, create laws, adjudicate civil and criminal legal cases, provide for public safety and for national defense. In general, government establishments in the public administration sector oversee governmental programs and activities that are not performed by private establishments.", "Workers employed in firms in retail trade. The Retail Trade sector comprises establishments engaged in retailing merchandise, generally without transformation, and rendering services incidental to the sale of merchandise. The retailing process is the final step in the distribution of merchandise; retailers are, therefore, organized to sell merchandise in small quantities to the general public.", "Workers employed in firms in professional scientific, management, administrative and waste management services. The Administrative and Support and Waste Management and Remediation Services sector comprises establishments performing routine support activities for the day-to-day operations of other organizations. The establishments in this sector specialize in one or more of these support activities and provide these services to clients in a variety of industries and, in some cases, to households. Activities performed include office administration, hiring and placing of personnel, document preparation and similar clerical services, solicitation, collection, security and surveillance services, cleaning, and waste disposal services.", "Workers employed in firms in transportation, warehousing, and utilities. The Transportation and Warehousing sector includes industries providing transportation of passengers and cargo, warehousing and storage for goods, scenic and sightseeing transportation, and support activities related to modes of transportation. The modes of transportation are air, rail, water, road, and pipeline.", "Workers employed in firms in wholesale trade. The Wholesale Trade sector comprises establishments engaged in wholesaling merchandise, generally without transformation, and rendering services incidental to the sale of merchandise. The wholesaling process is an intermediate step in the distribution of merchandise. Wholesalers are organized to sell or arrange the purchase or sale of (a) goods for resale (i.e., goods sold to other wholesalers or retailers), (b) capital or durable nonconsumer goods, and (c) raw and intermediate materials and supplies used in production.", "Households with two female partners. An unmarried partner is a person age 15 years and over, who is not related to the householder, who shares living quarters, and who has an intimate relationship with the householder.", "Four car households. The number of households with four or more passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Gini Index. The Gini index, or index of income concentration, is a statistical measure of income inequality ranging from 0 to 1. A measure of 1 indicates perfect inequality, i.e., one household having all the income and rest having none. A measure of 0 indicates perfect equality, i.e., all households having an equal share of income.", "Population who completed a graduate or professional degree", "Population living in group quarters", "Population with high school degree, including GED. The number of people in a geographic area over the age of 25 who attained a high school degree or GED.", "Households on cash public assistance or receiving food stamps (SNAP)", "Students Enrolled in Grades 1 to 4. The total number of people in each geography currently enrolled in grades 1 through 4 inclusive.  This corresponds roughly to elementary school.", "Students Enrolled in Grades 5 to 8. The total number of people in each geography currently enrolled in grades 5 through 8 inclusive.  This corresponds roughly to middle school.", "Students Enrolled in Grades 9 to 12. The total number of people in each geography currently enrolled in grades 9 through 12 inclusive.  This corresponds roughly to high school.", "Students Enrolled in School. The total number of people in each geography currently enrolled at any level of school, from nursery or pre-school to advanced post-graduate education.  Only includes those over the age of 3.", "Students Enrolled as Undergraduate in College. The number of people in a geographic area who are enrolled in college at the undergraduate level. Enrollment refers to being registered or listed as a student in an educational program leading to a college degree. This may be a public school or college, a private school or college.", "Less than high school graduate. The number of people in a geographic area over the age of 25 who have not completed high school or any other advanced degree.", "Men age 45 to 64 who obtained an associate's degree", "Men age 45 to 64 who obtained a bachelor's degree", "Men age 45 to 64 who obtained a graduate or professional degree", "Men age 45 to 64 who attained less than a 9th grade education", "Men age 45 to 64 who attained between 9th and 12th grade, no diploma", "Men age 45 to 64 who completed high school or obtained GED", "Men age 45 to 64 who completed some college, no degree", "Men age 45 to 64 (\"middle aged\"). The male population between the age of fourty-five years to sixty-four years within the specified area.", "Households with two male partners. An unmarried partner is a person age 15 years and over, who is not related to the householder, who shares living quarters, and who has an intimate relationship with the householder.", "Civilian Employed Population in Management, Business, Science, and Arts Occupations. The number of employed civilians 16 years old and over in each geography in the Management, Business, Science, and Arts occupations. Occupation codes are based on the Standard Occupational Classification (SOC), published by the Executive Office of the President, Office of Management and Budget.", "Workers age 16 and over with no vehicle. All people in a geographic area over the age of 16 who do not own a car.", "Car-free households. The number of households without passenger cars, vans, and pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Not a U.S. Citizen Population. The number of people within each geography who indicated that they are not U.S. citizens.", "Workers employed in management business science and arts occupations", "Workers employed in natural resources, construction, and maintenance occupations", "Workers employed in production, transportation, and material moving", "Workers employed in sales and office occupations", "Workers employed in service occupations", "One car households. The number of households with one passenger car, van, , pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Two car households. The number of households with two passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Three car households. The number of households with one passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Population age 25 to 64. The number of people in each geography who are between the ages of 25 and 64. ", "Population for Whom Poverty Status Determined. The number of people in each geography who could be identified as either living in poverty or not.  This should be used as the denominator when calculating poverty rates, as it excludes people for whom it was not possible to determine poverty.", "Population 1 year and over. All people, male and female, child and adult, living in a given geographic area that are 1 year and older.", "Population 3 Years and Over. The total number of people in each geography age 3 years and over.  This denominator is mostly used to calculate rates of school enrollment.", "Income In The Past 12 Months Below Poverty Level. The number of people in a geographic area who are part of a family (which could be just them as an individual) determined to be \"in poverty\" following the Office of Management and Budget's Directive 14. (https://www.census.gov/hhes/povmeas/methodology/ombdir14.html)", "Civilian Employed Population in Sales and Office Occupations. The number of employed civilians 16 years old and over in each geography in the Sales and Office occupations. Occupation codes are based on the Standard Occupational Classification (SOC), published by the Executive Office of the President, Office of Management and Budget.", "Population who completed some college or obtained associate's degree. The number of people in a geographic area over the age of 25 who obtained an associate's degree, and did not complete a more advanced degree.", "Walked to Work. The number of workers age 16 years and over within a geographic area who primarily walked to work.  This would mean that of any way of getting to work, they travelled the most distance walking.", "Worked at Home. The count within a geographical area of workers over the age of 16 who worked at home.", "Workers over the Age of 16. The number of people in each geography who work. Workers include those employed at private for-profit companies, the self-employed, government workers and non-profit employees.", "Population Completed Associate's Degree. The number of people in a geographic area over the age of 25 who obtained a associate's degree, and did not complete a more advanced degree.", "Population Completed Bachelor's Degree. The number of people in a geographic area over the age of 25 who obtained a bachelor's degree, and did not complete a more advanced degree.", "Population Completed High School. The number of people in a geographic area over the age of 25 who completed high school, and did not complete a more advanced degree.", "Population completed less than one year of college, no degree. The number of people in a geographic area over the age of 25 who attended college for less than one year and no further.", "Population Completed Master's Degree. The number of people in a geographic area over the age of 25 who obtained a master's degree, but did not complete a more advanced degree.", "Population completed more than one year of college, no degree. The number of people in a geographic area over the age of 25 who attended college for more than one year but did not obtain a degree", "Population 25 Years and Over. The number of people in a geographic area who are over the age of 25.  This is used mostly as a denominator of educational attainment.", "Number of workers with a commute between 35 and 44 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 35 and 44 minutes. ", "Number of workers with a commute of over 60 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in over 60 minutes.", "Number of workers with less than 10 minute commute. The number of workers in a geographic area over the age of 16 who do not work from home and commute in less than 10 minutes.", "Workers age 16 and over who do not work from home. The number of workers in a geographic area over the age of 16 who do not work from home", "Hispanic of any race", "Population 5 Years and Over. The number of people in a geographic area who are over the age of 5.  This is primarily used as a denominator of measures of language spoken at home.", "Speaks only English at Home. The number of people in a geographic area over age 5 who speak only English at home.", "Speaks Spanish at Home. The number of people in a geographic area over age 5 who speak Spanish at home, possibly in addition to other languages.", "Speaks Spanish at Home, speaks English less than \"very well\"", "Population 15 Years and Over. The number of people in a geographic area who are over the age of 15.  This is used mostly as a denominator of marital status.", "Never Married. The number of people in a geographic area who have never been married.", "Currently married. The number of people in a geographic area who are currently married", "Married but separated. The number of people in a geographic area who are married but separated", "Widowed. The number of people in a geographic area who are widowed", "Divorced. The number of people in a geographic area who are divorced", null], "sample_rows": [{"geo_id": "0816000", "nonfamily_households": 66315.0, "family_households": 115430.0, "median_year_structure_built": 1984.0, "rent_burden_not_computed": 3227.0, "rent_over_50_percent": 17614.0, "rent_40_to_50_percent": 7346.0, "rent_35_to_40_percent": 5289.0, "rent_30_to_35_percent": 7027.0, "rent_25_to_30_percent": 7103.0, "rent_20_to_25_percent": 9502.0, "rent_15_to_20_percent": 8412.0, "rent_10_to_15_percent": 4076.0, "rent_under_10_percent": 2068.0, "total_pop": 472666.0, "male_pop": 235360.0, "female_pop": 237306.0, "median_age": 34.6, "white_pop": 315200.0, "black_pop": 30890.0, "asian_pop": 16414.0, "hispanic_pop": 87084.0, "amerindian_pop": 1525.0, "other_race_pop": 776.0, "two_or_more_races_pop": 19668.0, "not_hispanic_pop": 385582.0, "commuters_by_public_transportation": 2189.0, "households": 181745.0, "median_income": 65331.0, "income_per_capita": 33194.0, "housing_units": 191810.0, "vacant_housing_units": 10065.0, "vacant_housing_units_for_rent": 2601.0, "vacant_housing_units_for_sale": 1386.0, "median_rent": 1028.0, "percent_income_spent_on_rent": 32.2, "owner_occupied_housing_units": 110081.0, "million_dollar_housing_units": 926.0, "mortgaged_housing_units": 77619.0, "families_with_young_children": 35666.0, "two_parent_families_with_young_children": 24190.0, "two_parents_in_labor_force_families_with_young_children": 12707.0, "two_parents_father_in_labor_force_families_with_young_children": 10260.0, "two_parents_mother_in_labor_force_families_with_young_children": 1223.0, "two_parents_not_in_labor_force_families_with_young_children": 0.0, "one_parent_families_with_young_children": 11476.0, "father_one_parent_families_with_young_children": 3440.0, "father_in_labor_force_one_parent_families_with_young_children": 3089.0, "commute_10_14_mins": 32064.0, "commute_15_19_mins": 47823.0, "commute_20_24_mins": 44792.0, "commute_25_29_mins": 19246.0, "commute_30_34_mins": 24831.0, "commute_45_59_mins": 7792.0, "aggregate_travel_time_to_work": 5074900.0, "income_less_10000": 9213.0, "income_10000_14999": 6224.0, "income_15000_19999": 6410.0, "income_20000_24999": 10690.0, "income_25000_29999": 6490.0, "income_30000_34999": 8315.0, "income_35000_39999": 9086.0, "income_40000_44999": 6957.0, "income_45000_49999": 7295.0, "income_50000_59999": 13067.0, "income_60000_74999": 20226.0, "income_75000_99999": 26598.0, "income_100000_124999": 17989.0, "income_125000_149999": 11829.0, "income_150000_199999": 11184.0, "income_200000_or_more": 10172.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 1156.0, "owner_occupied_housing_units_lower_value_quartile": 216700.0, "owner_occupied_housing_units_median_value": 288400.0, "owner_occupied_housing_units_upper_value_quartile": 391500.0, "married_households": 87306.0, "occupied_housing_units": 181745.0, "housing_units_renter_occupied": 71664.0, "dwellings_1_units_detached": 118370.0, "dwellings_1_units_attached": 14946.0, "dwellings_2_units": 2629.0, "dwellings_3_to_4_units": 9712.0, "dwellings_5_to_9_units": 10533.0, "dwellings_10_to_19_units": 11901.0, "dwellings_20_to_49_units": 11867.0, "dwellings_50_or_more_units": 8336.0, "mobile_homes": 3516.0, "housing_built_2005_or_later": 7369.0, "housing_built_2000_to_2004": 6286.0, "housing_built_1939_or_earlier": 2652.0, "male_under_5": 16075.0, "male_5_to_9": 16124.0, "male_10_to_14": 15118.0, "male_15_to_17": 8881.0, "male_18_to_19": 6734.0, "male_20": 3187.0, "male_21": 3060.0, "male_22_to_24": 12497.0, "male_25_to_29": 21449.0, "male_30_to_34": 20919.0, "male_35_to_39": 14752.0, "male_40_to_44": 15983.0, "male_45_to_49": 13784.0, "male_50_to_54": 13215.0, "male_55_to_59": 13803.0, "male_60_61": 5185.0, "male_62_64": 6692.0, "male_65_to_66": 4562.0, "male_67_to_69": 5118.0, "male_70_to_74": 8177.0, "male_75_to_79": 4122.0, "male_80_to_84": 3123.0, "male_85_and_over": 2800.0, "female_under_5": 15660.0, "female_5_to_9": 14012.0, "female_10_to_14": 16786.0, "female_15_to_17": 9288.0, "female_18_to_19": 6797.0, "female_20": 2339.0, "female_21": 2892.0, "female_22_to_24": 10394.0, "female_25_to_29": 20215.0, "female_30_to_34": 17341.0, "female_35_to_39": 15570.0, "female_40_to_44": 14414.0, "female_45_to_49": 13916.0, "female_50_to_54": 14775.0, "female_55_to_59": 14848.0, "female_60_to_61": 4709.0, "female_62_to_64": 8594.0, "female_65_to_66": 5495.0, "female_67_to_69": 6821.0, "female_70_to_74": 8120.0, "female_75_to_79": 5607.0, "female_80_to_84": 3874.0, "female_85_and_over": 4839.0, "white_including_hispanic": 363389.0, "black_including_hispanic": 32160.0, "amerindian_including_hispanic": 3788.0, "asian_including_hispanic": 16843.0, "commute_5_9_mins": 21707.0, "commute_35_39_mins": 2987.0, "commute_40_44_mins": 3889.0, "commute_60_89_mins": 7022.0, "commute_90_more_mins": 5481.0, "households_retirement_income": 38219.0, "armed_forces": 10121.0, "civilian_labor_force": 242815.0, "employed_pop": 232357.0, "unemployed_pop": 10458.0, "not_in_labor_force": 119643.0, "pop_16_over": 372579.0, "pop_in_labor_force": 252936.0, "asian_male_45_54": 1127.0, "asian_male_55_64": 709.0, "black_male_45_54": 2134.0, "black_male_55_64": 2059.0, "hispanic_male_45_54": 4873.0, "hispanic_male_55_64": 2467.0, "white_male_45_54": 18162.0, "white_male_55_64": 20333.0, "bachelors_degree_2": 72577.0, "bachelors_degree_or_higher_25_64": 102222.0, "children": 111944.0, "children_in_single_female_hh": 24364.0, "commuters_by_bus": 1734.0, "commuters_by_car_truck_van": 210030.0, "commuters_by_carpool": 25580.0, "commuters_by_subway_or_elevated": 455.0, "commuters_drove_alone": 184450.0, "different_house_year_ago_different_city": 51183.0, "different_house_year_ago_same_city": 52059.0, "employed_agriculture_forestry_fishing_hunting_mining": 1984.0, "employed_arts_entertainment_recreation_accommodation_food": 26143.0, "employed_construction": 19108.0, "employed_education_health_social": 53063.0, "employed_finance_insurance_real_estate": 18509.0, "employed_information": 7401.0, "employed_manufacturing": 11947.0, "employed_other_services_not_public_admin": 12024.0, "employed_public_administration": 11051.0, "employed_retail_trade": 26177.0, "employed_science_management_admin_waste": 33219.0, "employed_transportation_warehousing_utilities": 7885.0, "employed_wholesale_trade": 3846.0, "female_female_households": 174.0, "four_more_cars": 12422.0, "gini_index": 0.4421, "graduate_professional_degree": 52729.0, "group_quarters": 7492.0, "high_school_including_ged": 59793.0, "households_public_asst_or_food_stamps": 18102.0, "in_grades_1_to_4": 23739.0, "in_grades_5_to_8": 25863.0, "in_grades_9_to_12": 24635.0, "in_school": 127377.0, "in_undergrad_college": 32445.0, "less_than_high_school_graduate": 22320.0, "male_45_64_associates_degree": 4861.0, "male_45_64_bachelors_degree": 13453.0, "male_45_64_graduate_degree": 10664.0, "male_45_64_less_than_9_grade": 1088.0, "male_45_64_grade_9_12": 1763.0, "male_45_64_high_school": 10828.0, "male_45_64_some_college": 10022.0, "male_45_to_64": 52679.0, "male_male_households": 256.0, "management_business_sci_arts_employed": 94869.0, "no_car": 7256.0, "no_cars": 9532.0, "not_us_citizen_pop": 19775.0, "occupation_management_arts": 94869.0, "occupation_natural_resources_construction_maintenance": 21514.0, "occupation_production_transportation_material": 20638.0, "occupation_sales_office": 53174.0, "occupation_services": 42162.0, "one_car": 61044.0, "two_cars": 70359.0, "three_cars": 28388.0, "pop_25_64": 250164.0, "pop_determined_poverty_status": 464686.0, "population_1_year_and_over": 466933.0, "population_3_years_over": 454783.0, "poverty": 51057.0, "sales_office_employed": 53174.0, "some_college_and_associates_degree": 105403.0, "walked_to_work": 5664.0, "worked_at_home": 16311.0, "workers_16_and_over": 237698.0, "associates_degree": 33076.0, "bachelors_degree": 72577.0, "high_school_diploma": 48502.0, "less_one_year_college": 21837.0, "masters_degree": 40187.0, "one_year_more_college": 50490.0, "pop_25_years_over": 312822.0, "commute_35_44_mins": 6876.0, "commute_60_more_mins": 12503.0, "commute_less_10_mins": 25460.0, "commuters_16_over": 221387.0, "hispanic_any_race": NaN, "pop_5_years_over": NaN, "speak_only_english_at_home": NaN, "speak_spanish_at_home": NaN, "speak_spanish_at_home_low_english": NaN, "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "do_date": "2018"}, {"geo_id": "4967000", "nonfamily_households": 42262.0, "family_households": 38452.0, "median_year_structure_built": 1961.0, "rent_burden_not_computed": 1511.0, "rent_over_50_percent": 8681.0, "rent_40_to_50_percent": 2738.0, "rent_35_to_40_percent": 2155.0, "rent_30_to_35_percent": 3916.0, "rent_25_to_30_percent": 5683.0, "rent_20_to_25_percent": 5035.0, "rent_15_to_20_percent": 6329.0, "rent_10_to_15_percent": 4081.0, "rent_under_10_percent": 2161.0, "total_pop": 200576.0, "male_pop": 102709.0, "female_pop": 97867.0, "median_age": 32.2, "white_pop": 132404.0, "black_pop": 4491.0, "asian_pop": 9457.0, "hispanic_pop": 43131.0, "amerindian_pop": 2080.0, "other_race_pop": 1033.0, "two_or_more_races_pop": 6206.0, "not_hispanic_pop": 157445.0, "commuters_by_public_transportation": 8562.0, "households": 80714.0, "median_income": 61625.0, "income_per_capita": 38893.0, "housing_units": 86597.0, "vacant_housing_units": 5883.0, "vacant_housing_units_for_rent": 2196.0, "vacant_housing_units_for_sale": 0.0, "median_rent": 930.0, "percent_income_spent_on_rent": 27.4, "owner_occupied_housing_units": 38424.0, "million_dollar_housing_units": 1410.0, "mortgaged_housing_units": 26839.0, "families_with_young_children": 14750.0, "two_parent_families_with_young_children": 10331.0, "two_parents_in_labor_force_families_with_young_children": 6511.0, "two_parents_father_in_labor_force_families_with_young_children": 3769.0, "two_parents_mother_in_labor_force_families_with_young_children": 51.0, "two_parents_not_in_labor_force_families_with_young_children": 0.0, "one_parent_families_with_young_children": 4419.0, "father_one_parent_families_with_young_children": 1662.0, "father_in_labor_force_one_parent_families_with_young_children": 1538.0, "commute_10_14_mins": 23366.0, "commute_15_19_mins": 24268.0, "commute_20_24_mins": 17587.0, "commute_25_29_mins": 5531.0, "commute_30_34_mins": 11127.0, "commute_45_59_mins": 2759.0, "aggregate_travel_time_to_work": 1984525.0, "income_less_10000": 6027.0, "income_10000_14999": 3127.0, "income_15000_19999": 2491.0, "income_20000_24999": 3284.0, "income_25000_29999": 3571.0, "income_30000_34999": 3926.0, "income_35000_39999": 3062.0, "income_40000_44999": 3803.0, "income_45000_49999": 4037.0, "income_50000_59999": 5401.0, "income_60000_74999": 9566.0, "income_75000_99999": 7997.0, "income_100000_124999": 7225.0, "income_125000_149999": 4637.0, "income_150000_199999": 5323.0, "income_200000_or_more": 7237.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 1001.0, "owner_occupied_housing_units_lower_value_quartile": 234500.0, "owner_occupied_housing_units_median_value": 359800.0, "owner_occupied_housing_units_upper_value_quartile": 546000.0, "married_households": 28830.0, "occupied_housing_units": 80714.0, "housing_units_renter_occupied": 42290.0, "dwellings_1_units_detached": 38730.0, "dwellings_1_units_attached": 2821.0, "dwellings_2_units": 5631.0, "dwellings_3_to_4_units": 4756.0, "dwellings_5_to_9_units": 4864.0, "dwellings_10_to_19_units": 5598.0, "dwellings_20_to_49_units": 9130.0, "dwellings_50_or_more_units": 13695.0, "mobile_homes": 1289.0, "housing_built_2005_or_later": 4236.0, "housing_built_2000_to_2004": 2171.0, "housing_built_1939_or_earlier": 7295.0, "male_under_5": 6272.0, "male_5_to_9": 6214.0, "male_10_to_14": 4401.0, "male_15_to_17": 2264.0, "male_18_to_19": 3581.0, "male_20": 2605.0, "male_21": 2355.0, "male_22_to_24": 6491.0, "male_25_to_29": 13149.0, "male_30_to_34": 9847.0, "male_35_to_39": 9305.0, "male_40_to_44": 6582.0, "male_45_to_49": 4794.0, "male_50_to_54": 4263.0, "male_55_to_59": 5596.0, "male_60_61": 2004.0, "male_62_64": 3089.0, "male_65_to_66": 1474.0, "male_67_to_69": 2277.0, "male_70_to_74": 2617.0, "male_75_to_79": 1470.0, "male_80_to_84": 1247.0, "male_85_and_over": 812.0, "female_under_5": 6038.0, "female_5_to_9": 5907.0, "female_10_to_14": 6380.0, "female_15_to_17": 2276.0, "female_18_to_19": 2945.0, "female_20": 1771.0, "female_21": 1859.0, "female_22_to_24": 6841.0, "female_25_to_29": 12004.0, "female_30_to_34": 9067.0, "female_35_to_39": 7525.0, "female_40_to_44": 5421.0, "female_45_to_49": 3614.0, "female_50_to_54": 5362.0, "female_55_to_59": 4476.0, "female_60_to_61": 1966.0, "female_62_to_64": 3361.0, "female_65_to_66": 1432.0, "female_67_to_69": 1930.0, "female_70_to_74": 2857.0, "female_75_to_79": 2142.0, "female_80_to_84": 1632.0, "female_85_and_over": 1061.0, "white_including_hispanic": 148780.0, "black_including_hispanic": 4734.0, "amerindian_including_hispanic": 3252.0, "asian_including_hispanic": 9523.0, "commute_5_9_mins": 11270.0, "commute_35_39_mins": 1619.0, "commute_40_44_mins": 1566.0, "commute_60_89_mins": 2411.0, "commute_90_more_mins": 621.0, "households_retirement_income": 10265.0, "armed_forces": 56.0, "civilian_labor_force": 116010.0, "employed_pop": 112685.0, "unemployed_pop": 3325.0, "not_in_labor_force": 48164.0, "pop_16_over": 164230.0, "pop_in_labor_force": 116066.0, "asian_male_45_54": 371.0, "asian_male_55_64": 281.0, "black_male_45_54": NaN, "black_male_55_64": NaN, "hispanic_male_45_54": 2173.0, "hispanic_male_55_64": 2019.0, "white_male_45_54": 6198.0, "white_male_55_64": 7776.0, "bachelors_degree_2": 36110.0, "bachelors_degree_or_higher_25_64": 53738.0, "children": 39752.0, "children_in_single_female_hh": 7807.0, "commuters_by_bus": 5578.0, "commuters_by_car_truck_van": 84700.0, "commuters_by_carpool": 12093.0, "commuters_by_subway_or_elevated": 283.0, "commuters_drove_alone": 72607.0, "different_house_year_ago_different_city": 28043.0, "different_house_year_ago_same_city": 15261.0, "employed_agriculture_forestry_fishing_hunting_mining": 653.0, "employed_arts_entertainment_recreation_accommodation_food": 14717.0, "employed_construction": 5543.0, "employed_education_health_social": 30229.0, "employed_finance_insurance_real_estate": 8920.0, "employed_information": 1918.0, "employed_manufacturing": 8897.0, "employed_other_services_not_public_admin": 5623.0, "employed_public_administration": 2997.0, "employed_retail_trade": 10328.0, "employed_science_management_admin_waste": 16795.0, "employed_transportation_warehousing_utilities": 4340.0, "employed_wholesale_trade": 1725.0, "female_female_households": 108.0, "four_more_cars": 3524.0, "gini_index": 0.5067, "graduate_professional_degree": 27574.0, "group_quarters": 6409.0, "high_school_including_ged": 22486.0, "households_public_asst_or_food_stamps": 5351.0, "in_grades_1_to_4": 9799.0, "in_grades_5_to_8": 8082.0, "in_grades_9_to_12": 7349.0, "in_school": 58011.0, "in_undergrad_college": 22100.0, "less_than_high_school_graduate": 13458.0, "male_45_64_associates_degree": 865.0, "male_45_64_bachelors_degree": 3875.0, "male_45_64_graduate_degree": 4784.0, "male_45_64_less_than_9_grade": 1152.0, "male_45_64_grade_9_12": 1474.0, "male_45_64_high_school": 2896.0, "male_45_64_some_college": 4700.0, "male_45_to_64": 19746.0, "male_male_households": 194.0, "management_business_sci_arts_employed": 53781.0, "no_car": 4885.0, "no_cars": 8773.0, "not_us_citizen_pop": 21386.0, "occupation_management_arts": 53781.0, "occupation_natural_resources_construction_maintenance": 7139.0, "occupation_production_transportation_material": 12313.0, "occupation_sales_office": 20668.0, "occupation_services": 18784.0, "one_car": 32341.0, "two_cars": 27821.0, "three_cars": 8255.0, "pop_25_64": 111425.0, "pop_determined_poverty_status": 195020.0, "population_1_year_and_over": 197008.0, "population_3_years_over": 191651.0, "poverty": 31917.0, "sales_office_employed": 20668.0, "some_college_and_associates_degree": 32748.0, "walked_to_work": 6486.0, "worked_at_home": 6894.0, "workers_16_and_over": 110805.0, "associates_degree": 9469.0, "bachelors_degree": 36110.0, "high_school_diploma": 18955.0, "less_one_year_college": 6522.0, "masters_degree": 15451.0, "one_year_more_college": 16757.0, "pop_25_years_over": 132376.0, "commute_35_44_mins": 3185.0, "commute_60_more_mins": 3032.0, "commute_less_10_mins": 13056.0, "commuters_16_over": 103911.0, "hispanic_any_race": NaN, "pop_5_years_over": NaN, "speak_only_english_at_home": NaN, "speak_spanish_at_home": NaN, "speak_spanish_at_home_low_english": NaN, "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "do_date": "2018"}, {"geo_id": "3915000", "nonfamily_households": 73285.0, "family_households": 65482.0, "median_year_structure_built": 1950.0, "rent_burden_not_computed": 3754.0, "rent_over_50_percent": 20303.0, "rent_40_to_50_percent": 7748.0, "rent_35_to_40_percent": 3435.0, "rent_30_to_35_percent": 7237.0, "rent_25_to_30_percent": 10856.0, "rent_20_to_25_percent": 8308.0, "rent_15_to_20_percent": 13024.0, "rent_10_to_15_percent": 7627.0, "rent_under_10_percent": 3743.0, "total_pop": 302615.0, "male_pop": 146754.0, "female_pop": 155861.0, "median_age": 33.0, "white_pop": 150693.0, "black_pop": 122261.0, "asian_pop": 6999.0, "hispanic_pop": 10773.0, "amerindian_pop": 11.0, "other_race_pop": 791.0, "two_or_more_races_pop": 11032.0, "not_hispanic_pop": 291842.0, "commuters_by_public_transportation": 10103.0, "households": 138767.0, "median_income": 43585.0, "income_per_capita": 31874.0, "housing_units": 161429.0, "vacant_housing_units": 22662.0, "vacant_housing_units_for_rent": 6068.0, "vacant_housing_units_for_sale": 750.0, "median_rent": 633.0, "percent_income_spent_on_rent": 28.9, "owner_occupied_housing_units": 52732.0, "million_dollar_housing_units": 368.0, "mortgaged_housing_units": 33850.0, "families_with_young_children": 23119.0, "two_parent_families_with_young_children": 7878.0, "two_parents_in_labor_force_families_with_young_children": 4160.0, "two_parents_father_in_labor_force_families_with_young_children": 3261.0, "two_parents_mother_in_labor_force_families_with_young_children": 457.0, "two_parents_not_in_labor_force_families_with_young_children": 0.0, "one_parent_families_with_young_children": 15241.0, "father_one_parent_families_with_young_children": 2429.0, "father_in_labor_force_one_parent_families_with_young_children": 2256.0, "commute_10_14_mins": 19379.0, "commute_15_19_mins": 26969.0, "commute_20_24_mins": 24390.0, "commute_25_29_mins": 13251.0, "commute_30_34_mins": 20036.0, "commute_45_59_mins": 6659.0, "aggregate_travel_time_to_work": 3344255.0, "income_less_10000": 17646.0, "income_10000_14999": 11141.0, "income_15000_19999": 8801.0, "income_20000_24999": 7686.0, "income_25000_29999": 7094.0, "income_30000_34999": 6521.0, "income_35000_39999": 5788.0, "income_40000_44999": 6145.0, "income_45000_49999": 5421.0, "income_50000_59999": 10461.0, "income_60000_74999": 13639.0, "income_75000_99999": 12637.0, "income_100000_124999": 8929.0, "income_125000_149999": 4573.0, "income_150000_199999": 5511.0, "income_200000_or_more": 6774.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 745.0, "owner_occupied_housing_units_lower_value_quartile": 87700.0, "owner_occupied_housing_units_median_value": 147800.0, "owner_occupied_housing_units_upper_value_quartile": 273400.0, "married_households": 34136.0, "occupied_housing_units": 138767.0, "housing_units_renter_occupied": 86035.0, "dwellings_1_units_detached": 62393.0, "dwellings_1_units_attached": 9546.0, "dwellings_2_units": 14702.0, "dwellings_3_to_4_units": 18024.0, "dwellings_5_to_9_units": 14668.0, "dwellings_10_to_19_units": 17848.0, "dwellings_20_to_49_units": 8739.0, "dwellings_50_or_more_units": 14806.0, "mobile_homes": 446.0, "housing_built_2005_or_later": 2799.0, "housing_built_2000_to_2004": 1381.0, "housing_built_1939_or_earlier": 11513.0, "male_under_5": 10616.0, "male_5_to_9": 6963.0, "male_10_to_14": 9125.0, "male_15_to_17": 5474.0, "male_18_to_19": 5481.0, "male_20": 2668.0, "male_21": 1682.0, "male_22_to_24": 8867.0, "male_25_to_29": 16182.0, "male_30_to_34": 12388.0, "male_35_to_39": 9876.0, "male_40_to_44": 7446.0, "male_45_to_49": 8832.0, "male_50_to_54": 6596.0, "male_55_to_59": 9758.0, "male_60_61": 3905.0, "male_62_64": 5418.0, "male_65_to_66": 2092.0, "male_67_to_69": 3467.0, "male_70_to_74": 3813.0, "male_75_to_79": 3026.0, "male_80_to_84": 1780.0, "male_85_and_over": 1299.0, "female_under_5": 10624.0, "female_5_to_9": 8825.0, "female_10_to_14": 8605.0, "female_15_to_17": 4515.0, "female_18_to_19": 6523.0, "female_20": 2699.0, "female_21": 3319.0, "female_22_to_24": 7526.0, "female_25_to_29": 16592.0, "female_30_to_34": 13133.0, "female_35_to_39": 8904.0, "female_40_to_44": 7347.0, "female_45_to_49": 8105.0, "female_50_to_54": 7284.0, "female_55_to_59": 10174.0, "female_60_to_61": 3791.0, "female_62_to_64": 5623.0, "female_65_to_66": 3710.0, "female_67_to_69": 3519.0, "female_70_to_74": 5182.0, "female_75_to_79": 3543.0, "female_80_to_84": 1844.0, "female_85_and_over": 4474.0, "white_including_hispanic": 155452.0, "black_including_hispanic": 123267.0, "amerindian_including_hispanic": 244.0, "asian_including_hispanic": 7028.0, "commute_5_9_mins": 10937.0, "commute_35_39_mins": 3628.0, "commute_40_44_mins": 3916.0, "commute_60_89_mins": 4330.0, "commute_90_more_mins": 2964.0, "households_retirement_income": 15939.0, "armed_forces": 223.0, "civilian_labor_force": 159690.0, "employed_pop": 149764.0, "unemployed_pop": 9926.0, "not_in_labor_force": 84340.0, "pop_16_over": 244253.0, "pop_in_labor_force": 159913.0, "asian_male_45_54": NaN, "asian_male_55_64": NaN, "black_male_45_54": 6203.0, "black_male_55_64": 7363.0, "hispanic_male_45_54": 517.0, "hispanic_male_55_64": 460.0, "white_male_45_54": 8166.0, "white_male_55_64": 10791.0, "bachelors_degree_2": 45257.0, "bachelors_degree_or_higher_25_64": 65389.0, "children": 64747.0, "children_in_single_female_hh": 35283.0, "commuters_by_bus": 9945.0, "commuters_by_car_truck_van": 118335.0, "commuters_by_carpool": 14361.0, "commuters_by_subway_or_elevated": 43.0, "commuters_drove_alone": 103974.0, "different_house_year_ago_different_city": 29878.0, "different_house_year_ago_same_city": 39808.0, "employed_agriculture_forestry_fishing_hunting_mining": 302.0, "employed_arts_entertainment_recreation_accommodation_food": 13978.0, "employed_construction": 4433.0, "employed_education_health_social": 40358.0, "employed_finance_insurance_real_estate": 11093.0, "employed_information": 3946.0, "employed_manufacturing": 15146.0, "employed_other_services_not_public_admin": 6455.0, "employed_public_administration": 4778.0, "employed_retail_trade": 15511.0, "employed_science_management_admin_waste": 19740.0, "employed_transportation_warehousing_utilities": 10713.0, "employed_wholesale_trade": 3311.0, "female_female_households": 99.0, "four_more_cars": 4091.0, "gini_index": 0.5432, "graduate_professional_degree": 32545.0, "group_quarters": 12878.0, "high_school_including_ged": 46435.0, "households_public_asst_or_food_stamps": 25620.0, "in_grades_1_to_4": 13270.0, "in_grades_5_to_8": 14000.0, "in_grades_9_to_12": 13844.0, "in_school": 82290.0, "in_undergrad_college": 24792.0, "less_than_high_school_graduate": 25229.0, "male_45_64_associates_degree": 2259.0, "male_45_64_bachelors_degree": 6427.0, "male_45_64_graduate_degree": 3747.0, "male_45_64_less_than_9_grade": 2088.0, "male_45_64_grade_9_12": 3581.0, "male_45_64_high_school": 9277.0, "male_45_64_some_college": 7130.0, "male_45_to_64": 34509.0, "male_male_households": 74.0, "management_business_sci_arts_employed": 65182.0, "no_car": 10658.0, "no_cars": 25891.0, "not_us_citizen_pop": 11447.0, "occupation_management_arts": 65182.0, "occupation_natural_resources_construction_maintenance": 6063.0, "occupation_production_transportation_material": 22986.0, "occupation_sales_office": 29557.0, "occupation_services": 25976.0, "one_car": 59728.0, "two_cars": 39512.0, "three_cars": 9545.0, "pop_25_64": 161354.0, "pop_determined_poverty_status": 291634.0, "population_1_year_and_over": 296698.0, "population_3_years_over": 289018.0, "poverty": 73460.0, "sales_office_employed": 29557.0, "some_college_and_associates_degree": 49637.0, "walked_to_work": 7997.0, "worked_at_home": 6657.0, "workers_16_and_over": 146721.0, "associates_degree": 14914.0, "bachelors_degree": 45257.0, "high_school_diploma": 37776.0, "less_one_year_college": 8730.0, "masters_degree": 23102.0, "one_year_more_college": 25993.0, "pop_25_years_over": 199103.0, "commute_35_44_mins": 7544.0, "commute_60_more_mins": 7294.0, "commute_less_10_mins": 14542.0, "commuters_16_over": 140064.0, "hispanic_any_race": NaN, "pop_5_years_over": NaN, "speak_only_english_at_home": NaN, "speak_spanish_at_home": NaN, "speak_spanish_at_home_low_english": NaN, "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "do_date": "2018"}, {"geo_id": "2758000", "nonfamily_households": 56068.0, "family_households": 59790.0, "median_year_structure_built": 1950.0, "rent_burden_not_computed": 2753.0, "rent_over_50_percent": 13822.0, "rent_40_to_50_percent": 4024.0, "rent_35_to_40_percent": 3575.0, "rent_30_to_35_percent": 4758.0, "rent_25_to_30_percent": 7712.0, "rent_20_to_25_percent": 7782.0, "rent_15_to_20_percent": 6262.0, "rent_10_to_15_percent": 4904.0, "rent_under_10_percent": 3129.0, "total_pop": 307701.0, "male_pop": 151617.0, "female_pop": 156084.0, "median_age": 32.6, "white_pop": 159782.0, "black_pop": 44572.0, "asian_pop": 59395.0, "hispanic_pop": 26081.0, "amerindian_pop": 2590.0, "other_race_pop": 1241.0, "two_or_more_races_pop": 13954.0, "not_hispanic_pop": 281620.0, "commuters_by_public_transportation": 14722.0, "households": 115858.0, "median_income": 59033.0, "income_per_capita": 33489.0, "housing_units": 124176.0, "vacant_housing_units": 8318.0, "vacant_housing_units_for_rent": 4061.0, "vacant_housing_units_for_sale": 298.0, "median_rent": 860.0, "percent_income_spent_on_rent": 28.8, "owner_occupied_housing_units": 57137.0, "million_dollar_housing_units": 358.0, "mortgaged_housing_units": 40472.0, "families_with_young_children": 25435.0, "two_parent_families_with_young_children": 16021.0, "two_parents_in_labor_force_families_with_young_children": 11347.0, "two_parents_father_in_labor_force_families_with_young_children": 3921.0, "two_parents_mother_in_labor_force_families_with_young_children": 712.0, "two_parents_not_in_labor_force_families_with_young_children": 41.0, "one_parent_families_with_young_children": 9414.0, "father_one_parent_families_with_young_children": 1356.0, "father_in_labor_force_one_parent_families_with_young_children": 1050.0, "commute_10_14_mins": 20721.0, "commute_15_19_mins": 24662.0, "commute_20_24_mins": 26945.0, "commute_25_29_mins": 14788.0, "commute_30_34_mins": 19757.0, "commute_45_59_mins": 9916.0, "aggregate_travel_time_to_work": 3556175.0, "income_less_10000": 9598.0, "income_10000_14999": 6242.0, "income_15000_19999": 3941.0, "income_20000_24999": 5054.0, "income_25000_29999": 5946.0, "income_30000_34999": 5500.0, "income_35000_39999": 5555.0, "income_40000_44999": 5247.0, "income_45000_49999": 3567.0, "income_50000_59999": 7892.0, "income_60000_74999": 11320.0, "income_75000_99999": 14753.0, "income_100000_124999": 8402.0, "income_125000_149999": 7434.0, "income_150000_199999": 7690.0, "income_200000_or_more": 7717.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 947.0, "owner_occupied_housing_units_lower_value_quartile": 164500.0, "owner_occupied_housing_units_median_value": 216100.0, "owner_occupied_housing_units_upper_value_quartile": 326600.0, "married_households": 39152.0, "occupied_housing_units": 115858.0, "housing_units_renter_occupied": 58721.0, "dwellings_1_units_detached": 58541.0, "dwellings_1_units_attached": 5713.0, "dwellings_2_units": 8806.0, "dwellings_3_to_4_units": 4816.0, "dwellings_5_to_9_units": 5917.0, "dwellings_10_to_19_units": 9822.0, "dwellings_20_to_49_units": 9053.0, "dwellings_50_or_more_units": 21274.0, "mobile_homes": 234.0, "housing_built_2005_or_later": 3054.0, "housing_built_2000_to_2004": 1642.0, "housing_built_1939_or_earlier": 8776.0, "male_under_5": 10516.0, "male_5_to_9": 11429.0, "male_10_to_14": 10075.0, "male_15_to_17": 6326.0, "male_18_to_19": 4618.0, "male_20": 3138.0, "male_21": 2995.0, "male_22_to_24": 7401.0, "male_25_to_29": 13905.0, "male_30_to_34": 13499.0, "male_35_to_39": 9939.0, "male_40_to_44": 9350.0, "male_45_to_49": 9792.0, "male_50_to_54": 9128.0, "male_55_to_59": 8803.0, "male_60_61": 3556.0, "male_62_64": 3387.0, "male_65_to_66": 2872.0, "male_67_to_69": 2815.0, "male_70_to_74": 3345.0, "male_75_to_79": 2102.0, "male_80_to_84": 1085.0, "male_85_and_over": 1541.0, "female_under_5": 11651.0, "female_5_to_9": 9513.0, "female_10_to_14": 9854.0, "female_15_to_17": 5104.0, "female_18_to_19": 4308.0, "female_20": 2188.0, "female_21": 2336.0, "female_22_to_24": 8163.0, "female_25_to_29": 14783.0, "female_30_to_34": 14054.0, "female_35_to_39": 11884.0, "female_40_to_44": 9097.0, "female_45_to_49": 8821.0, "female_50_to_54": 7853.0, "female_55_to_59": 8876.0, "female_60_to_61": 3443.0, "female_62_to_64": 4657.0, "female_65_to_66": 2632.0, "female_67_to_69": 4190.0, "female_70_to_74": 4831.0, "female_75_to_79": 2970.0, "female_80_to_84": 2089.0, "female_85_and_over": 2787.0, "white_including_hispanic": 173556.0, "black_including_hispanic": 45362.0, "amerindian_including_hispanic": 3217.0, "asian_including_hispanic": 59746.0, "commute_5_9_mins": 12339.0, "commute_35_39_mins": 5965.0, "commute_40_44_mins": 5226.0, "commute_60_89_mins": 4056.0, "commute_90_more_mins": 2032.0, "households_retirement_income": 16373.0, "armed_forces": 0.0, "civilian_labor_force": 168587.0, "employed_pop": 161190.0, "unemployed_pop": 7397.0, "not_in_labor_force": 72326.0, "pop_16_over": 240913.0, "pop_in_labor_force": 168587.0, "asian_male_45_54": 2933.0, "asian_male_55_64": 1347.0, "black_male_45_54": 3245.0, "black_male_55_64": 1685.0, "hispanic_male_45_54": 1322.0, "hispanic_male_55_64": 782.0, "white_male_45_54": 10972.0, "white_male_55_64": 11684.0, "bachelors_degree_2": 49640.0, "bachelors_degree_or_higher_25_64": 71457.0, "children": 74468.0, "children_in_single_female_hh": 23330.0, "commuters_by_bus": 12044.0, "commuters_by_car_truck_van": 124259.0, "commuters_by_carpool": 18353.0, "commuters_by_subway_or_elevated": 942.0, "commuters_drove_alone": 105906.0, "different_house_year_ago_different_city": 26434.0, "different_house_year_ago_same_city": 29410.0, "employed_agriculture_forestry_fishing_hunting_mining": 898.0, "employed_arts_entertainment_recreation_accommodation_food": 15427.0, "employed_construction": 5697.0, "employed_education_health_social": 46410.0, "employed_finance_insurance_real_estate": 10098.0, "employed_information": 3140.0, "employed_manufacturing": 17863.0, "employed_other_services_not_public_admin": 8582.0, "employed_public_administration": 6411.0, "employed_retail_trade": 15588.0, "employed_science_management_admin_waste": 19438.0, "employed_transportation_warehousing_utilities": 8909.0, "employed_wholesale_trade": 2729.0, "female_female_households": 346.0, "four_more_cars": 5067.0, "gini_index": 0.5034, "graduate_professional_degree": 34680.0, "group_quarters": 8859.0, "high_school_including_ged": 39738.0, "households_public_asst_or_food_stamps": 19709.0, "in_grades_1_to_4": 17381.0, "in_grades_5_to_8": 15902.0, "in_grades_9_to_12": 15330.0, "in_school": 87821.0, "in_undergrad_college": 21641.0, "less_than_high_school_graduate": 24503.0, "male_45_64_associates_degree": 2771.0, "male_45_64_bachelors_degree": 7193.0, "male_45_64_graduate_degree": 4781.0, "male_45_64_less_than_9_grade": 3115.0, "male_45_64_grade_9_12": 1621.0, "male_45_64_high_school": 9301.0, "male_45_64_some_college": 5884.0, "male_45_to_64": 34666.0, "male_male_households": 158.0, "management_business_sci_arts_employed": 68662.0, "no_car": 9358.0, "no_cars": 17498.0, "not_us_citizen_pop": 25792.0, "occupation_management_arts": 68662.0, "occupation_natural_resources_construction_maintenance": 6969.0, "occupation_production_transportation_material": 22200.0, "occupation_sales_office": 33660.0, "occupation_services": 29699.0, "one_car": 46091.0, "two_cars": 37381.0, "three_cars": 9821.0, "pop_25_64": 164827.0, "pop_determined_poverty_status": 299631.0, "population_1_year_and_over": 303174.0, "population_3_years_over": 293768.0, "poverty": 57979.0, "sales_office_employed": 33660.0, "some_college_and_associates_degree": 49525.0, "walked_to_work": 5789.0, "worked_at_home": 9719.0, "workers_16_and_over": 157830.0, "associates_degree": 16240.0, "bachelors_degree": 49640.0, "high_school_diploma": 32053.0, "less_one_year_college": 10449.0, "masters_degree": 22806.0, "one_year_more_college": 22836.0, "pop_25_years_over": 198086.0, "commute_35_44_mins": 11191.0, "commute_60_more_mins": 6088.0, "commute_less_10_mins": 14043.0, "commuters_16_over": 148111.0, "hispanic_any_race": NaN, "pop_5_years_over": NaN, "speak_only_english_at_home": NaN, "speak_spanish_at_home": NaN, "speak_spanish_at_home_low_english": NaN, "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "do_date": "2018"}, {"geo_id": "4159000", "nonfamily_households": 135473.0, "family_households": 138134.0, "median_year_structure_built": 1963.0, "rent_burden_not_computed": 6395.0, "rent_over_50_percent": 26975.0, "rent_40_to_50_percent": 13357.0, "rent_35_to_40_percent": 6799.0, "rent_30_to_35_percent": 10379.0, "rent_25_to_30_percent": 18967.0, "rent_20_to_25_percent": 16383.0, "rent_15_to_20_percent": 16780.0, "rent_10_to_15_percent": 9610.0, "rent_under_10_percent": 3878.0, "total_pop": 652573.0, "male_pop": 322810.0, "female_pop": 329763.0, "median_age": 37.6, "white_pop": 457042.0, "black_pop": 39882.0, "asian_pop": 57526.0, "hispanic_pop": 60760.0, "amerindian_pop": 4938.0, "other_race_pop": 2422.0, "two_or_more_races_pop": 25427.0, "not_hispanic_pop": 591813.0, "commuters_by_public_transportation": 43800.0, "households": 273607.0, "median_income": 73097.0, "income_per_capita": 42814.0, "housing_units": 294678.0, "vacant_housing_units": 21071.0, "vacant_housing_units_for_rent": 9429.0, "vacant_housing_units_for_sale": 2529.0, "median_rent": 1172.0, "percent_income_spent_on_rent": 28.9, "owner_occupied_housing_units": 144084.0, "million_dollar_housing_units": 3894.0, "mortgaged_housing_units": 102586.0, "families_with_young_children": 39044.0, "two_parent_families_with_young_children": 30170.0, "two_parents_in_labor_force_families_with_young_children": 21824.0, "two_parents_father_in_labor_force_families_with_young_children": 6547.0, "two_parents_mother_in_labor_force_families_with_young_children": 1325.0, "two_parents_not_in_labor_force_families_with_young_children": 474.0, "one_parent_families_with_young_children": 8874.0, "father_one_parent_families_with_young_children": 2079.0, "father_in_labor_force_one_parent_families_with_young_children": 1779.0, "commute_10_14_mins": 33876.0, "commute_15_19_mins": 56720.0, "commute_20_24_mins": 60339.0, "commute_25_29_mins": 28653.0, "commute_30_34_mins": 56405.0, "commute_45_59_mins": 26163.0, "aggregate_travel_time_to_work": 8528250.0, "income_less_10000": 16475.0, "income_10000_14999": 10519.0, "income_15000_19999": 10267.0, "income_20000_24999": 10415.0, "income_25000_29999": 10431.0, "income_30000_34999": 10248.0, "income_35000_39999": 10301.0, "income_40000_44999": 10724.0, "income_45000_49999": 8720.0, "income_50000_59999": 16099.0, "income_60000_74999": 25066.0, "income_75000_99999": 34810.0, "income_100000_124999": 29955.0, "income_125000_149999": 19943.0, "income_150000_199999": 21732.0, "income_200000_or_more": 27902.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 1297.0, "owner_occupied_housing_units_lower_value_quartile": 334200.0, "owner_occupied_housing_units_median_value": 451000.0, "owner_occupied_housing_units_upper_value_quartile": 635900.0, "married_households": 106236.0, "occupied_housing_units": 273607.0, "housing_units_renter_occupied": 129523.0, "dwellings_1_units_detached": 157468.0, "dwellings_1_units_attached": 11063.0, "dwellings_2_units": 9395.0, "dwellings_3_to_4_units": 16244.0, "dwellings_5_to_9_units": 14364.0, "dwellings_10_to_19_units": 18281.0, "dwellings_20_to_49_units": 22350.0, "dwellings_50_or_more_units": 41154.0, "mobile_homes": 3630.0, "housing_built_2005_or_later": 15375.0, "housing_built_2000_to_2004": 8970.0, "housing_built_1939_or_earlier": 26281.0, "male_under_5": 17478.0, "male_5_to_9": 16463.0, "male_10_to_14": 16280.0, "male_15_to_17": 9580.0, "male_18_to_19": 6843.0, "male_20": 2163.0, "male_21": 4031.0, "male_22_to_24": 11775.0, "male_25_to_29": 30206.0, "male_30_to_34": 31852.0, "male_35_to_39": 30272.0, "male_40_to_44": 26122.0, "male_45_to_49": 24564.0, "male_50_to_54": 20621.0, "male_55_to_59": 18476.0, "male_60_61": 6741.0, "male_62_64": 10271.0, "male_65_to_66": 8239.0, "male_67_to_69": 7945.0, "male_70_to_74": 10276.0, "male_75_to_79": 6645.0, "male_80_to_84": 3545.0, "male_85_and_over": 2422.0, "female_under_5": 16459.0, "female_5_to_9": 16335.0, "female_10_to_14": 15217.0, "female_15_to_17": 8460.0, "female_18_to_19": 5964.0, "female_20": 3689.0, "female_21": 2762.0, "female_22_to_24": 12799.0, "female_25_to_29": 32786.0, "female_30_to_34": 32535.0, "female_35_to_39": 30671.0, "female_40_to_44": 24752.0, "female_45_to_49": 22890.0, "female_50_to_54": 19039.0, "female_55_to_59": 20109.0, "female_60_to_61": 6160.0, "female_62_to_64": 11079.0, "female_65_to_66": 7105.0, "female_67_to_69": 9359.0, "female_70_to_74": 12699.0, "female_75_to_79": 7609.0, "female_80_to_84": 4955.0, "female_85_and_over": 6330.0, "white_including_hispanic": 503158.0, "black_including_hispanic": 40763.0, "amerindian_including_hispanic": 6184.0, "asian_including_hispanic": 58246.0, "commute_5_9_mins": 21826.0, "commute_35_39_mins": 9381.0, "commute_40_44_mins": 14374.0, "commute_60_89_mins": 13113.0, "commute_90_more_mins": 4833.0, "households_retirement_income": 38575.0, "armed_forces": 233.0, "civilian_labor_force": 387516.0, "employed_pop": 372004.0, "unemployed_pop": 15512.0, "not_in_labor_force": 161012.0, "pop_16_over": 548761.0, "pop_in_labor_force": 387749.0, "asian_male_45_54": 3940.0, "asian_male_55_64": 2743.0, "black_male_45_54": 2191.0, "black_male_55_64": 2158.0, "hispanic_male_45_54": 4710.0, "hispanic_male_55_64": 2069.0, "white_male_45_54": 32397.0, "white_male_55_64": 27597.0, "bachelors_degree_2": 145252.0, "bachelors_degree_or_higher_25_64": 209141.0, "children": 116272.0, "children_in_single_female_hh": 24490.0, "commuters_by_bus": 36050.0, "commuters_by_car_truck_van": 242377.0, "commuters_by_carpool": 27972.0, "commuters_by_subway_or_elevated": 2544.0, "commuters_drove_alone": 214405.0, "different_house_year_ago_different_city": 49274.0, "different_house_year_ago_same_city": 63295.0, "employed_agriculture_forestry_fishing_hunting_mining": 2139.0, "employed_arts_entertainment_recreation_accommodation_food": 42281.0, "employed_construction": 18697.0, "employed_education_health_social": 84871.0, "employed_finance_insurance_real_estate": 23029.0, "employed_information": 8428.0, "employed_manufacturing": 35386.0, "employed_other_services_not_public_admin": 18174.0, "employed_public_administration": 14296.0, "employed_retail_trade": 37662.0, "employed_science_management_admin_waste": 60598.0, "employed_transportation_warehousing_utilities": 15130.0, "employed_wholesale_trade": 11313.0, "female_female_households": 1024.0, "four_more_cars": 11585.0, "gini_index": 0.4756, "graduate_professional_degree": 101338.0, "group_quarters": 16140.0, "high_school_including_ged": 72135.0, "households_public_asst_or_food_stamps": 37612.0, "in_grades_1_to_4": 25224.0, "in_grades_5_to_8": 25438.0, "in_grades_9_to_12": 24587.0, "in_school": 145173.0, "in_undergrad_college": 39590.0, "less_than_high_school_graduate": 34427.0, "male_45_64_associates_degree": 6126.0, "male_45_64_bachelors_degree": 19860.0, "male_45_64_graduate_degree": 15577.0, "male_45_64_less_than_9_grade": 2914.0, "male_45_64_grade_9_12": 5731.0, "male_45_64_high_school": 14422.0, "male_45_64_some_college": 16043.0, "male_45_to_64": 80673.0, "male_male_households": 1514.0, "management_business_sci_arts_employed": 188677.0, "no_car": 24414.0, "no_cars": 37320.0, "not_us_citizen_pop": 42262.0, "occupation_management_arts": 188677.0, "occupation_natural_resources_construction_maintenance": 21856.0, "occupation_production_transportation_material": 35701.0, "occupation_sales_office": 66972.0, "occupation_services": 58798.0, "one_car": 108760.0, "two_cars": 89796.0, "three_cars": 26146.0, "pop_25_64": 399146.0, "pop_determined_poverty_status": 640255.0, "population_1_year_and_over": 647558.0, "population_3_years_over": 634441.0, "poverty": 73582.0, "sales_office_employed": 66972.0, "some_college_and_associates_degree": 133123.0, "walked_to_work": 20859.0, "worked_at_home": 35313.0, "workers_16_and_over": 366445.0, "associates_degree": 34904.0, "bachelors_degree": 145252.0, "high_school_diploma": 58890.0, "less_one_year_college": 25738.0, "masters_degree": 70478.0, "one_year_more_college": 72481.0, "pop_25_years_over": 486275.0, "commute_35_44_mins": 23755.0, "commute_60_more_mins": 17946.0, "commute_less_10_mins": 27275.0, "commuters_16_over": 331132.0, "hispanic_any_race": 60760.0, "pop_5_years_over": NaN, "speak_only_english_at_home": NaN, "speak_spanish_at_home": NaN, "speak_spanish_at_home_low_english": NaN, "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "do_date": "2018"}]}