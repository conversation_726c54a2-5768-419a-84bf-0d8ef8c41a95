{"table_name": "congressionaldistrict_2009_3yr", "table_fullname": "bigquery-public-data.census_bureau_acs.congressionaldistrict_2009_3yr", "column_names": ["geo_id", "nonfamily_households", "family_households", "median_year_structure_built", "rent_burden_not_computed", "rent_over_50_percent", "rent_40_to_50_percent", "rent_35_to_40_percent", "rent_30_to_35_percent", "rent_25_to_30_percent", "rent_20_to_25_percent", "rent_15_to_20_percent", "rent_10_to_15_percent", "rent_under_10_percent", "total_pop", "male_pop", "female_pop", "median_age", "white_pop", "black_pop", "asian_pop", "hispanic_pop", "amerindian_pop", "other_race_pop", "two_or_more_races_pop", "not_hispanic_pop", "commuters_by_public_transportation", "households", "median_income", "income_per_capita", "housing_units", "vacant_housing_units", "vacant_housing_units_for_rent", "vacant_housing_units_for_sale", "median_rent", "percent_income_spent_on_rent", "owner_occupied_housing_units", "million_dollar_housing_units", "mortgaged_housing_units", "families_with_young_children", "two_parent_families_with_young_children", "two_parents_in_labor_force_families_with_young_children", "two_parents_father_in_labor_force_families_with_young_children", "two_parents_mother_in_labor_force_families_with_young_children", "two_parents_not_in_labor_force_families_with_young_children", "one_parent_families_with_young_children", "father_one_parent_families_with_young_children", "father_in_labor_force_one_parent_families_with_young_children", "commute_10_14_mins", "commute_15_19_mins", "commute_20_24_mins", "commute_25_29_mins", "commute_30_34_mins", "commute_45_59_mins", "aggregate_travel_time_to_work", "income_less_10000", "income_10000_14999", "income_15000_19999", "income_20000_24999", "income_25000_29999", "income_30000_34999", "income_35000_39999", "income_40000_44999", "income_45000_49999", "income_50000_59999", "income_60000_74999", "income_75000_99999", "income_100000_124999", "income_125000_149999", "income_150000_199999", "income_200000_or_more", "renter_occupied_housing_units_paying_cash_median_gross_rent", "owner_occupied_housing_units_lower_value_quartile", "owner_occupied_housing_units_median_value", "owner_occupied_housing_units_upper_value_quartile", "married_households", "occupied_housing_units", "housing_units_renter_occupied", "dwellings_1_units_detached", "dwellings_1_units_attached", "dwellings_2_units", "dwellings_3_to_4_units", "dwellings_5_to_9_units", "dwellings_10_to_19_units", "dwellings_20_to_49_units", "dwellings_50_or_more_units", "mobile_homes", "housing_built_2005_or_later", "housing_built_2000_to_2004", "housing_built_1939_or_earlier", "male_under_5", "male_5_to_9", "male_10_to_14", "male_15_to_17", "male_18_to_19", "male_20", "male_21", "male_22_to_24", "male_25_to_29", "male_30_to_34", "male_35_to_39", "male_40_to_44", "male_45_to_49", "male_50_to_54", "male_55_to_59", "male_60_61", "male_62_64", "male_65_to_66", "male_67_to_69", "male_70_to_74", "male_75_to_79", "male_80_to_84", "male_85_and_over", "female_under_5", "female_5_to_9", "female_10_to_14", "female_15_to_17", "female_18_to_19", "female_20", "female_21", "female_22_to_24", "female_25_to_29", "female_30_to_34", "female_35_to_39", "female_40_to_44", "female_45_to_49", "female_50_to_54", "female_55_to_59", "female_60_to_61", "female_62_to_64", "female_65_to_66", "female_67_to_69", "female_70_to_74", "female_75_to_79", "female_80_to_84", "female_85_and_over", "white_including_hispanic", "black_including_hispanic", "amerindian_including_hispanic", "asian_including_hispanic", "commute_5_9_mins", "commute_35_39_mins", "commute_40_44_mins", "commute_60_89_mins", "commute_90_more_mins", "households_retirement_income", "armed_forces", "civilian_labor_force", "employed_pop", "unemployed_pop", "not_in_labor_force", "pop_16_over", "pop_in_labor_force", "asian_male_45_54", "asian_male_55_64", "black_male_45_54", "black_male_55_64", "hispanic_male_45_54", "hispanic_male_55_64", "white_male_45_54", "white_male_55_64", "bachelors_degree_2", "bachelors_degree_or_higher_25_64", "children", "children_in_single_female_hh", "commuters_by_bus", "commuters_by_car_truck_van", "commuters_by_carpool", "commuters_by_subway_or_elevated", "commuters_drove_alone", "different_house_year_ago_different_city", "different_house_year_ago_same_city", "employed_agriculture_forestry_fishing_hunting_mining", "employed_arts_entertainment_recreation_accommodation_food", "employed_construction", "employed_education_health_social", "employed_finance_insurance_real_estate", "employed_information", "employed_manufacturing", "employed_other_services_not_public_admin", "employed_public_administration", "employed_retail_trade", "employed_science_management_admin_waste", "employed_transportation_warehousing_utilities", "employed_wholesale_trade", "female_female_households", "four_more_cars", "gini_index", "graduate_professional_degree", "group_quarters", "high_school_including_ged", "households_public_asst_or_food_stamps", "in_grades_1_to_4", "in_grades_5_to_8", "in_grades_9_to_12", "in_school", "in_undergrad_college", "less_than_high_school_graduate", "male_45_64_associates_degree", "male_45_64_bachelors_degree", "male_45_64_graduate_degree", "male_45_64_less_than_9_grade", "male_45_64_grade_9_12", "male_45_64_high_school", "male_45_64_some_college", "male_45_to_64", "male_male_households", "management_business_sci_arts_employed", "no_car", "no_cars", "not_us_citizen_pop", "occupation_management_arts", "occupation_natural_resources_construction_maintenance", "occupation_production_transportation_material", "occupation_sales_office", "occupation_services", "one_car", "two_cars", "three_cars", "pop_25_64", "pop_determined_poverty_status", "population_1_year_and_over", "population_3_years_over", "poverty", "sales_office_employed", "some_college_and_associates_degree", "walked_to_work", "worked_at_home", "workers_16_and_over", "associates_degree", "bachelors_degree", "high_school_diploma", "less_one_year_college", "masters_degree", "one_year_more_college", "pop_25_years_over", "commute_35_44_mins", "commute_60_more_mins", "commute_less_10_mins", "commuters_16_over", "hispanic_any_race", "pop_5_years_over", "speak_only_english_at_home", "speak_spanish_at_home", "speak_spanish_at_home_low_english", "pop_15_and_over", "pop_never_married", "pop_now_married", "pop_separated", "pop_widowed", "pop_divorced", "do_date"], "column_types": ["STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "STRING"], "nested_column_names": ["geo_id", "nonfamily_households", "family_households", "median_year_structure_built", "rent_burden_not_computed", "rent_over_50_percent", "rent_40_to_50_percent", "rent_35_to_40_percent", "rent_30_to_35_percent", "rent_25_to_30_percent", "rent_20_to_25_percent", "rent_15_to_20_percent", "rent_10_to_15_percent", "rent_under_10_percent", "total_pop", "male_pop", "female_pop", "median_age", "white_pop", "black_pop", "asian_pop", "hispanic_pop", "amerindian_pop", "other_race_pop", "two_or_more_races_pop", "not_hispanic_pop", "commuters_by_public_transportation", "households", "median_income", "income_per_capita", "housing_units", "vacant_housing_units", "vacant_housing_units_for_rent", "vacant_housing_units_for_sale", "median_rent", "percent_income_spent_on_rent", "owner_occupied_housing_units", "million_dollar_housing_units", "mortgaged_housing_units", "families_with_young_children", "two_parent_families_with_young_children", "two_parents_in_labor_force_families_with_young_children", "two_parents_father_in_labor_force_families_with_young_children", "two_parents_mother_in_labor_force_families_with_young_children", "two_parents_not_in_labor_force_families_with_young_children", "one_parent_families_with_young_children", "father_one_parent_families_with_young_children", "father_in_labor_force_one_parent_families_with_young_children", "commute_10_14_mins", "commute_15_19_mins", "commute_20_24_mins", "commute_25_29_mins", "commute_30_34_mins", "commute_45_59_mins", "aggregate_travel_time_to_work", "income_less_10000", "income_10000_14999", "income_15000_19999", "income_20000_24999", "income_25000_29999", "income_30000_34999", "income_35000_39999", "income_40000_44999", "income_45000_49999", "income_50000_59999", "income_60000_74999", "income_75000_99999", "income_100000_124999", "income_125000_149999", "income_150000_199999", "income_200000_or_more", "renter_occupied_housing_units_paying_cash_median_gross_rent", "owner_occupied_housing_units_lower_value_quartile", "owner_occupied_housing_units_median_value", "owner_occupied_housing_units_upper_value_quartile", "married_households", "occupied_housing_units", "housing_units_renter_occupied", "dwellings_1_units_detached", "dwellings_1_units_attached", "dwellings_2_units", "dwellings_3_to_4_units", "dwellings_5_to_9_units", "dwellings_10_to_19_units", "dwellings_20_to_49_units", "dwellings_50_or_more_units", "mobile_homes", "housing_built_2005_or_later", "housing_built_2000_to_2004", "housing_built_1939_or_earlier", "male_under_5", "male_5_to_9", "male_10_to_14", "male_15_to_17", "male_18_to_19", "male_20", "male_21", "male_22_to_24", "male_25_to_29", "male_30_to_34", "male_35_to_39", "male_40_to_44", "male_45_to_49", "male_50_to_54", "male_55_to_59", "male_60_61", "male_62_64", "male_65_to_66", "male_67_to_69", "male_70_to_74", "male_75_to_79", "male_80_to_84", "male_85_and_over", "female_under_5", "female_5_to_9", "female_10_to_14", "female_15_to_17", "female_18_to_19", "female_20", "female_21", "female_22_to_24", "female_25_to_29", "female_30_to_34", "female_35_to_39", "female_40_to_44", "female_45_to_49", "female_50_to_54", "female_55_to_59", "female_60_to_61", "female_62_to_64", "female_65_to_66", "female_67_to_69", "female_70_to_74", "female_75_to_79", "female_80_to_84", "female_85_and_over", "white_including_hispanic", "black_including_hispanic", "amerindian_including_hispanic", "asian_including_hispanic", "commute_5_9_mins", "commute_35_39_mins", "commute_40_44_mins", "commute_60_89_mins", "commute_90_more_mins", "households_retirement_income", "armed_forces", "civilian_labor_force", "employed_pop", "unemployed_pop", "not_in_labor_force", "pop_16_over", "pop_in_labor_force", "asian_male_45_54", "asian_male_55_64", "black_male_45_54", "black_male_55_64", "hispanic_male_45_54", "hispanic_male_55_64", "white_male_45_54", "white_male_55_64", "bachelors_degree_2", "bachelors_degree_or_higher_25_64", "children", "children_in_single_female_hh", "commuters_by_bus", "commuters_by_car_truck_van", "commuters_by_carpool", "commuters_by_subway_or_elevated", "commuters_drove_alone", "different_house_year_ago_different_city", "different_house_year_ago_same_city", "employed_agriculture_forestry_fishing_hunting_mining", "employed_arts_entertainment_recreation_accommodation_food", "employed_construction", "employed_education_health_social", "employed_finance_insurance_real_estate", "employed_information", "employed_manufacturing", "employed_other_services_not_public_admin", "employed_public_administration", "employed_retail_trade", "employed_science_management_admin_waste", "employed_transportation_warehousing_utilities", "employed_wholesale_trade", "female_female_households", "four_more_cars", "gini_index", "graduate_professional_degree", "group_quarters", "high_school_including_ged", "households_public_asst_or_food_stamps", "in_grades_1_to_4", "in_grades_5_to_8", "in_grades_9_to_12", "in_school", "in_undergrad_college", "less_than_high_school_graduate", "male_45_64_associates_degree", "male_45_64_bachelors_degree", "male_45_64_graduate_degree", "male_45_64_less_than_9_grade", "male_45_64_grade_9_12", "male_45_64_high_school", "male_45_64_some_college", "male_45_to_64", "male_male_households", "management_business_sci_arts_employed", "no_car", "no_cars", "not_us_citizen_pop", "occupation_management_arts", "occupation_natural_resources_construction_maintenance", "occupation_production_transportation_material", "occupation_sales_office", "occupation_services", "one_car", "two_cars", "three_cars", "pop_25_64", "pop_determined_poverty_status", "population_1_year_and_over", "population_3_years_over", "poverty", "sales_office_employed", "some_college_and_associates_degree", "walked_to_work", "worked_at_home", "workers_16_and_over", "associates_degree", "bachelors_degree", "high_school_diploma", "less_one_year_college", "masters_degree", "one_year_more_college", "pop_25_years_over", "commute_35_44_mins", "commute_60_more_mins", "commute_less_10_mins", "commuters_16_over", "hispanic_any_race", "pop_5_years_over", "speak_only_english_at_home", "speak_spanish_at_home", "speak_spanish_at_home_low_english", "pop_15_and_over", "pop_never_married", "pop_now_married", "pop_separated", "pop_widowed", "pop_divorced", "do_date"], "nested_column_types": ["STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "STRING"], "description": ["US Congressional Districts Geoids", "Nonfamily Households. A householder living alone or with nonrelatives only. Unmarried couples households, whether opposite-sex or same-sex, with no relatives of the householder present are tabulated in nonfamily households.", "Family Households. A family consists of a householder and one or more other people living in the same household who are related to the householder by birth, marriage, or adoption. All people in a household who are related to the householder are regarded as members of his or her family. A family household may contain people not related to the householder, but those people are not included as part of the family of the householder in tabulations. Thus, the number of family households is equal to the number of families, but family households may include more members than do families. A household can contain only one family for purposes of tabulations. Not all households contain families since a household may be comprised of a group of unrelated people or of one person living alone -- these are called nonfamily households. Families are classified by type as either a \"married couple family\" or \"other family\" according to the sex of the householder and the presence of relatives. The data on family type are based ...", "Median Year Structure Built. Median Year Structure Built", "Housing units without rent burden computed. Units for which no rent is paid and units occupied by households that reported no income or a net loss comprise this category", "Housing units spending over 50% income on rent. Gross rent over 50 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 40 to 49.9% income on rent. Gross rent from 40.0 to 49.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 35 to 39.9% income on rent. Gross rent from 35.0 to 39.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 30 to 34.9% income on rent. Gross rent from 30.0 to 34.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 25 to 29.9% income on rent. Gross rent from 25.0 to 29.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 20 to 24.9% income on rent. Gross rent from 20.0 to 24.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 15 to 19.9% income on rent. Gross rent from 15.0 to 19.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 10 to 14.9% income on rent. Gross rent from 10.0 to 14.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending less than 10% on rent. Gross rent less than 10 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Total Population. The total number of all people living in a given geographic area.  This is a very useful catch-all denominator when calculating rates.", "Male Population. The number of people within each geography who are male.", "Female Population. The number of people within each geography who are female.", "Median Age. The median age of all people in a given geographic area.", "White Population. The number of people identifying as white, non-Hispanic in each geography.", "Black or African American Population. The number of people identifying as black or African American, non-Hispanic in each geography.", "Asian Population. The number of people identifying as Asian, non-Hispanic in each geography.", "Hispanic Population. The number of people identifying as Hispanic or Latino in each geography.", "American Indian and Alaska Native Population. The number of people identifying as American Indian or Alaska native in each geography.", "Other Race population. The number of people identifying as another race in each geography", "Two or more races population. The number of people identifying as two or more races in each geography", "Population not Hispanic. The number of people not identifying as Hispanic or Latino in each geography.", "Commuters by Public Transportation. The number of workers age 16 years and over within a geographic area who primarily traveled to work by public transportation.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Households. A count of the number of households in each geography. A household consists of one or more people who live in the same dwelling and also share at meals or living accommodation, and may consist of a single family or some other grouping of people. ", "Median Household Income in the past 12 Months. Within a geographic area, the median income received by every household on a regular basis before payments for personal income taxes, social security, union dues, medicare deductions, etc.  It includes income received from wages, salary, commissions, bonuses, and tips; self-employment income from own nonfarm or farm businesses, including proprietorships and partnerships; interest, dividends, net rental income, royalty income, or income from estates and trusts; Social Security or Railroad Retirement income; Supplemental Security Income (SSI); any cash public assistance or welfare payments from the state or local welfare office; retirement, survivor, or disability benefits; and any other sources of income received regularly such as Veterans' (VA) payments, unemployment and/or worker's compensation, child support, and alimony.", "Per Capita Income in the past 12 Months. Per capita income is the mean income computed for every man, woman, and child in a particular group. It is derived by dividing the total income of a particular group by the total population.", "Housing Units. A count of housing units in each geography.  A housing unit is a house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters.", "Vacant Housing Units. The count of vacant housing units in a geographic area. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Vacant Housing Units for Rent. The count of vacant housing units in a geographic area that are for rent. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Vacant Housing Units for Sale. The count of vacant housing units in a geographic area that are for sale. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Median Rent. The median contract rent within a geographic area. The contract rent is the monthly rent agreed to or contracted for, regardless of any furnishings, utilities, fees, meals, or services that may be included. For vacant units, it is the monthly rent asked for the rental unit at the time of interview.", "Percent of Household Income Spent on Rent. Within a geographic area, the median percentage of household income which was spent on gross rent.  Gross rent is the amount of the contract rent plus the estimated average monthly cost of utilities (electricity, gas, water, sewer etc.) and fuels (oil, coal, wood, etc.) if these are paid by the renter.  Household income is the sum of the income of all people 15 years and older living in the household.", "Owner-occupied Housing Units", "Owner-occupied Housing Units valued at $1,000,000 or more.. The count of owner occupied housing units in a geographic area that are valued at $1,000,000 or more.  Value is the respondent's estimate of how much the property (house and lot, mobile home and lot, or condominium unit) would sell for if it were for sale.", "Owner-occupied Housing Units with a Mortgage. The count of housing units within a geographic area that are mortagaged. \"Mortgage\" refers to all forms of debt where the property is pledged as security for repayment of the debt, including deeds of trust, trust deed, contracts to purchase, land contracts, junior mortgages, and home equity loans.", "Families with young children (under 6 years of age)", "Two-parent families with young children (under 6 years of age)", "Two-parent families, both parents in labor force with young children (under 6 years of age)", "Two-parent families, father only in labor force with young children (under 6 years of age)", "Two-parent families, mother only in labor force with young children (under 6 years of age)", "Two-parent families, neither parent in labor force with young children (under 6 years of age)", "One-parent families with young children (under 6 years of age)", "One-parent families, father, with young children (under 6 years of age)", "One-parent families, father in labor force, with young children (under 6 years of age)", "Number of workers with a commute between 10 and 14 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 10 and 14 minutes. ", "Number of workers with a commute between 15 and 19 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 15 and 19 minutes. ", "Number of workers with a commute between 20 and 24 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 20 and 24 minutes.", "Number of workers with a commute between 25 and 29 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 25 and 29 minutes. ", "Number of workers with a commute between 30 and 34 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 30 and 34 minutes. ", "Number of workers with a commute between 45 and 59 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 45 and 59 minutes. ", "Aggregate travel time to work. The total number of minutes every worker in a geographic area over the age of 16 who did not work from home spent spent commuting to work in one day", "Households with income less than $10,000. The number of households in a geographic area whose annual income was less than $10,000.", "Households with income of $10,000 to $14,999. The number of households in a geographic area whose annual income was between $10,000 and $14,999.", "Households with income of $15,000 to $19,999. The number of households in a geographic area whose annual income was between $15,000 and $19,999.", "Households with income of $20,000 to $24,999. The number of households in a geographic area whose annual income was between $20,000 and $24,999.", "Households with income of $25,000 to $29,999. The number of households in a geographic area whose annual income was between $20,000 and $24,999.", "Households with income of $30,000 to $34,999. The number of households in a geographic area whose annual income was between $30,000 and $34,999.", "Households with income of $35,000 to $39,999. The number of households in a geographic area whose annual income was between $35,000 and $39,999.", "Households with income of $40,000 to $44,999. The number of households in a geographic area whose annual income was between $40,000 and $44,999.", "Households with income of $45,000 to $49,999. The number of households in a geographic area whose annual income was between $45,000 and $49,999.", "Households with income of $50,000 to $59,999. The number of households in a geographic area whose annual income was between $50,000 and $59,999.", "Households with income of $60,000 to $74,999. The number of households in a geographic area whose annual income was between $60,000 and $74,999.", "Households with income of $75,000 to $99,999. The number of households in a geographic area whose annual income was between $75,000 and $99,999.", "Households with income of $100,000 to $124,999. The number of households in a geographic area whose annual income was between $100,000 and $124,999.", "Households with income of $125,000 to $149,999. The number of households in a geographic area whose annual income was between $125,000 and $149,999.", "Households with income of $150,000 to $199,999. The number of households in a geographic area whose annual income was between $150,000 and $1999,999.", "Households with income of $200,000 Or More. The number of households in a geographic area whose annual income was more than $200,000.", "Renter-Occupied Housing Units Paying Cash Rent Median Gross Rent", "Owner-Occupied Housing Units Lower Value Quartile", "Owner-Occupied Housing Units Median Value. The middle value (median) in a geographic area owner occupied housing units.", "Owner-Occupied Housing Units Upper Value Quartile", "Married households. People in formal marriages, as well as people in common-law marriages, are included. Does not include same-sex marriages.", "Occupied housing units. A housing unit is classified as occupied if it is the usual place of residence of the person or group of people living in it at the time of enumeration.", "Renter occupied housing units. All occupied units which are not owner occupied, whether they are rented for cash rent or occupied without payment of cash rent, are classified as renter-occupied.", "Single-family (one unit) detached dwellings. This is a 1-unit structure detached from any other house, that is, with open space on all four sides. Such structures are considered detached even if they have an adjoining shed or garage. A one-family house that contains a business is considered detached as long as the building has open space on all four sides. Mobile homes to which one or more permanent rooms have been added or built also are included.", "Single-family (one unit) attached dwellings. This is a 1-unit structure that has one or more walls extending from ground to roof separating it from adjoining structures. In row houses (sometimes called townhouses), double houses, or houses attached to nonresidential structures, each house is a separate, attached structure if the dividing or common wall goes from ground to roof.", "Two-family (two unit) dwellings", "Multifamily dwellings with three to 4 units", "Apartment buildings with 5 to 9 units", "Apartment buildings with 10 to 19 units", "Apartment buildings with 20 to 49 units", "Apartment buildings with 50 or more units", "Mobile homes. A manufactured home is defined as a movable dwelling, 8 feet or more wide and 40 feet or more long, designed to be towed on its own chassis, with transportation gear integral to the unit when it leaves the factory, and without need of a permanent foundation. These homes are built in accordance with the U.S. Department of Housing and Urban Development (HUD) building code.", "Housing units built in 2005 or later. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built in 2005 or later.", "Housing units built between 2000 and 2004. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built from 2000 to 2004.", "Housing units built before 1939. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built in 1939 or earlier.", "Male under 5 years. The male population over the age of five years within the specified area.", "Male age 5 to 9. The male population between the age of five years to nine years within the specified area.", "Male age 10 to 14. The male population between the age of ten years to fourteen years within the specified area.", "Male age 15 to 17. The male population between the age of fifteeen years to seventeen years within the specified area.", "Male age 18 and 19. The male population between the age of eighteen years to nineteen years within the specified area.", "Male age 20. The male population with an age of twenty years within the specified area.", "Male age 21. The male population with an age of twenty-one years within the specified area.", "Male age 22 to 24. The male population between the age of twenty-two years to twenty-four years within the specified area.", "Male age 25 to 29. The male population between the age of twenty-five years to twenty-nine years within the specified area.", "Male age 30 to 34. The male population between the age of thirty years to thirty-four years within the specified area.", "Male age 35 to 39. The male population between the age of thirty-five years to thirty-nine years within the specified area.", "Male age 40 to 44. The male population between the age of fourty years to fourty-four years within the specified area.", "Men age 45 to 49. The male population between the age of fourty-five years to fourty-nine years within the specified area.", "Men age 50 to 54. The male population between the age of fifty years to fifty-four years within the specified area.", "Men age 55 to 59. The male population between the age of fifty-five years to fifty-nine years within the specified area.", "Men age 60 to 61. The male population between the age of sixty years to sixty-one years within the specified area.", "Men age 62 to 64. The male population between the age of sixty-two years to sixty-four years within the specified area.", "Male age 65 to 66. The male population between the age of sixty-five years to sixty-six years within the specified area.", "Male age 67 to 69. The male population between the age of sixty-seven years to sixty-nine years within the specified area.", "Male age 70 to 74. The male population between the age of seventy years to seventy-four years within the specified area.", "Male age 75 to 79. The male population between the age of seventy-five years to seventy-nine years within the specified area.", "Male age 80 to 84. The male population between the age of eighty years to eighty-four years within the specified area.", "Male age 85 and over. The male population of the age of eighty-five years and over within the specified area.", "Female under 5 years. The female population over the age of five years within the specified area.", "Female age 5 to 9. The female population between the age of five years to nine years within the specified area.", "Female age 10 to 14. The female population between the age of ten years to fourteen years within the specified area.", "Female age 15 to 17. The female population between the age of fifteeen years to seventeen years within the specified area.", "Female age 18 and 19. The female population between the age of eighteen years to nineteen years within the specified area.", "Female age 20. The female population with an age of twenty years within the specified area.", "Female age 21. The female population with an age of twenty-one years within the specified area.", "Female age 22 to 24. The female population between the age of twenty-two years to twenty-four years within the specified area.", "Female age 25 to 29. The female population between the age of twenty-five years to twenty-nine years within the specified area.", "Female age 30 to 34. The female population between the age of thirty years to thirty-four years within the specified area.", "Female age 35 to 39. The female population between the age of thirty-five years to thirty-nine years within the specified area.", "Female age 40 to 44. The female population between the age of fourty years to fourty-four years within the specified area.", "Female age 45 to 49. The female population between the age of fourty-five years to fourty-nine years within the specified area.", "Female age 50 to 54. The female population between the age of fifty years to fifty-four years within the specified area.", "Female age 55 to 59. The female population between the age of fifty-five years to fifty-nine years within the specified area.", "Female age 60 and 61. The female population between the age of sixty years to sixty-one years within the specified area.", "Female age 62 to 64. The female population between the age of sixty-two years to sixty-four years within the specified area.", "Female age 65 to 66. The female population between the age of sixty-five years to sixty-six years within the specified area.", "Female age 67 to 69. The female population between the age of sixty-seven years to sixty-nine years within the specified area.", "Female age 70 to 74. The female population between the age of seventy years to seventy-four years within the specified area.", "Female age 75 to 79. The female population between the age of seventy-five years to seventy-nine years within the specified area.", "Female age 80 to 84. The female population between the age of eighty years to eighty-four years within the specified area.", "Female age 85 and over. The female population of the age of eighty-five years and over within the specified area.", "White including Hispanic", "Black including Hispanic", "American Indian including Hispanic", "Asian including Hispanic", "Number of workers with a commute between 5 and 9 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 5 and 9 minutes.", "Number of workers with a commute between 35 and 39 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 35 and 39 minutes. ", "Number of workers with a commute between 40 and 44 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 40 and 44 minutes. ", "Number of workers with a commute between 60 and 89 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 60 and 89 minutes .", "Number of workers with a commute of more than 90 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute more than 90 minutes.", "Households receiving retirement income", "Population in Armed Forces. The number of people in each geography who are members of the U.S. Armed Forces (people on active duty with the United States Army, Air Force, Navy, Marine Corps, or Coast Guard).", "Population in Civilian Labor Force. The number of civilians 16 years and over in each geography who can be classified as either \"employed\" or \"unemployed\" below.", "Employed Population. The number of civilians 16 years old and over in each geography who either (1) were \"at work,\" that is, those who did any work at all during the reference week as paid employees, worked in their own business or profession, worked on their own farm, or worked 15 hours or more as unpaid workers on a family farm or in a family business; or (2) were \"with a job but not at work,\" that is, those who did not work during the reference week but had jobs or businesses from which they were temporarily absent due to illness, bad weather, industrial dispute, vacation, or other personal reasons. Excluded from the employed are people whose only activity consisted of work around the house or unpaid volunteer work for religious, charitable, and similar organizations; also excluded are all institutionalized people and people on active duty in the United States Armed Forces.", "", "Population Not in Labor Force. The number of people in each geography who are 16 years old and over who are not classified as members of the labor force. This category consists mainly of students, homemakers, retired workers, seasonal workers interviewed in an off season who were not looking for work, institutionalized people, and people doing only incidental unpaid family work (less than 15 hours during the reference week).", "Population age 16 and over. The number of people in each geography who are age 16 or over.", "Population in Labor Force. The number of people in each geography who are either in the civilian labor force or are members of the U.S. Armed Forces (people on active duty with the United States Army, Air Force, Navy, Marine Corps, or Coast Guard).", "Asian Men age 45 to 54", "Asian Men age 55 to 64", "Black Men age 45 to 54", "Black Men age 55 to 64", "Hispanic Men age 45 to 54", "Hispanic Men age 55 to 64", "White Men age 45 to 54", "White Men age 55 to 64", "Population who completed a bachelor's degree. From mobility table.", "Population with Bachelors Degree or Higher, Ages 25 to 64. The number of people in each geography who are between the ages of 25 and 64 who have attained a bachelors degree or higher. ", "Children under 18 Years of Age. The number of people within each geography who are under 18 years of age.", "Children under 18 years of age in single female-led household", "Commuters by Bus. The number of workers age 16 years and over within a geographic area who primarily traveled to work by bus.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.  This is a subset of workers who commuted by public transport.", "Commuters by Car, Truck, or Van. The number of workers age 16 years and over within  a geographic area who primarily traveled to work by car, truck or  van.  This is the principal mode of travel or type of conveyance,  by distance rather than time, that the worker usually used to get  from home to work.", "Commuters by Carpool. The number of workers age 16 years and over within a geographic area who primarily traveled to work by carpool.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Commuters by Subway or Elevated. The number of workers age 16 years and over within a geographic area who primarily traveled to work by subway or elevated train.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.  This is a subset of workers who commuted by public transport.", "Commuters who drove alone. The number of workers age 16 years and over within a geographic area who primarily traveled by car driving alone. This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Lived in a different house one year ago in a different city. All people in a geographic area who lived in a different city within the year prior to the survey.", "Lived in a different house one year ago in the same city. All people in a geographic area who lived in the same city but moved to a different unit within the year prior to the survey.", "Workers employed in firms in agriculture, forestry, fishing, hunting, or mining. The Agriculture, Forestry, Fishing and Hunting sector comprises establishments primarily engaged in growing crops, raising animals, harvesting timber, and harvesting fish and other animals from a farm, ranch, or their natural habitats.", "Workers employed in firms in arts, entertainment, recreation, accommodation and food services. The Arts, Entertainment, and Recreation sector includes a wide range of establishments that operate facilities or provide services to meet varied cultural, entertainment, and recreational interests of their patrons. This sector comprises (1) establishments that are involved in producing, promoting, or participating in live performances, events, or exhibits intended for public viewing; (2) establishments that preserve and exhibit objects and sites of historical, cultural, or educational interest; and (3) establishments that operate facilities or provide services that enable patrons to participate in recreational activities or pursue amusement, hobby, and leisure-time interests.", "Workers employed in firms in construction. The Construction sector comprises establishments primarily engaged in the construction of buildings or engineering projects (e.g., highways and utility systems). Construction work done may include new work, additions, alterations, or maintenance and repairs.", "Workers employed in firms in educational services, health care, and social assistance. Outpatient health services, other than hospital care, including: public health administration; research and education; categorical health programs; treatment and immunization clinics; nursing; environmental health activities such as air and water pollution control; ambulance service if provided separately from fire protection services, and other general public health activities such as mosquito abatement. School health services provided by health agencies (rather than school agencies) are included here. Sewage treatment operations are classified under Sewerage.", "Workers employed in firms in finance, insurance, real estate and rental and leasing. The Real Estate and Rental and Leasing sector comprises establishments primarily engaged in renting, leasing, or otherwise allowing the use of tangible or intangible assets, and establishments providing related services. The major portion of this sector comprises establishments that rent, lease, or otherwise allow the use of their own assets by others. The assets may be tangible, as is the case of real estate and equipment, or intangible, as is the case with patents and trademarks.", "Workers employed in firms in information. The Information sector comprises establishments engaged in the following processes: (a) producing and distributing information and cultural products, (b) providing the means to transmit or distribute these products as well as data or communications, and (c) processing data. Included are the publishing industries, the motion picture and sound recording industries; the broadcasting industries, the telecommunications industries; Web search portals, data processing industries, and the information services industries.", "Workers employed in firms in manufacturing. The Manufacturing sector comprises establishments engaged in the mechanical, physical, or chemical transformation of materials, substances, or components into new products.", "Workers employed in firms in other services except public administration. The Other Services (Except Public Administration) sector comprises establishments engaged in providing services not specifically provided for elsewhere in the classification system. Establishments in this sector are primarily engaged in activities such as equipment and machinery repairing, promoting or administering religious activities, grantmaking, advocacy, and providing drycleaning and laundry services, personal care services, death care services, pet care services, photofinishing services, temporary parking services, and dating services. Private households that engage in employing workers on or about the premises in activities primarily concerned with the operation of the household are included in this sector.", "Workers employed in firms in public administration. The Public Administration sector consists of establishments of federal, state, and local government agencies that administer, oversee, and manage public programs and have executive, legislative, or judicial authority over other institutions within a given area. These agencies also set policy, create laws, adjudicate civil and criminal legal cases, provide for public safety and for national defense. In general, government establishments in the public administration sector oversee governmental programs and activities that are not performed by private establishments.", "Workers employed in firms in retail trade. The Retail Trade sector comprises establishments engaged in retailing merchandise, generally without transformation, and rendering services incidental to the sale of merchandise. The retailing process is the final step in the distribution of merchandise; retailers are, therefore, organized to sell merchandise in small quantities to the general public.", "Workers employed in firms in professional scientific, management, administrative and waste management services. The Administrative and Support and Waste Management and Remediation Services sector comprises establishments performing routine support activities for the day-to-day operations of other organizations. The establishments in this sector specialize in one or more of these support activities and provide these services to clients in a variety of industries and, in some cases, to households. Activities performed include office administration, hiring and placing of personnel, document preparation and similar clerical services, solicitation, collection, security and surveillance services, cleaning, and waste disposal services.", "Workers employed in firms in transportation, warehousing, and utilities. The Transportation and Warehousing sector includes industries providing transportation of passengers and cargo, warehousing and storage for goods, scenic and sightseeing transportation, and support activities related to modes of transportation. The modes of transportation are air, rail, water, road, and pipeline.", "Workers employed in firms in wholesale trade. The Wholesale Trade sector comprises establishments engaged in wholesaling merchandise, generally without transformation, and rendering services incidental to the sale of merchandise. The wholesaling process is an intermediate step in the distribution of merchandise. Wholesalers are organized to sell or arrange the purchase or sale of (a) goods for resale (i.e., goods sold to other wholesalers or retailers), (b) capital or durable nonconsumer goods, and (c) raw and intermediate materials and supplies used in production.", "Households with two female partners. An unmarried partner is a person age 15 years and over, who is not related to the householder, who shares living quarters, and who has an intimate relationship with the householder.", "Four car households. The number of households with four or more passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Gini Index. The Gini index, or index of income concentration, is a statistical measure of income inequality ranging from 0 to 1. A measure of 1 indicates perfect inequality, i.e., one household having all the income and rest having none. A measure of 0 indicates perfect equality, i.e., all households having an equal share of income.", "Population who completed a graduate or professional degree", "Population living in group quarters", "Population with high school degree, including GED. The number of people in a geographic area over the age of 25 who attained a high school degree or GED.", "Households on cash public assistance or receiving food stamps (SNAP)", "Students Enrolled in Grades 1 to 4. The total number of people in each geography currently enrolled in grades 1 through 4 inclusive.  This corresponds roughly to elementary school.", "Students Enrolled in Grades 5 to 8. The total number of people in each geography currently enrolled in grades 5 through 8 inclusive.  This corresponds roughly to middle school.", "Students Enrolled in Grades 9 to 12. The total number of people in each geography currently enrolled in grades 9 through 12 inclusive.  This corresponds roughly to high school.", "Students Enrolled in School. The total number of people in each geography currently enrolled at any level of school, from nursery or pre-school to advanced post-graduate education.  Only includes those over the age of 3.", "Students Enrolled as Undergraduate in College. The number of people in a geographic area who are enrolled in college at the undergraduate level. Enrollment refers to being registered or listed as a student in an educational program leading to a college degree. This may be a public school or college, a private school or college.", "Less than high school graduate. The number of people in a geographic area over the age of 25 who have not completed high school or any other advanced degree.", "Men age 45 to 64 who obtained an associate's degree", "Men age 45 to 64 who obtained a bachelor's degree", "Men age 45 to 64 who obtained a graduate or professional degree", "Men age 45 to 64 who attained less than a 9th grade education", "Men age 45 to 64 who attained between 9th and 12th grade, no diploma", "Men age 45 to 64 who completed high school or obtained GED", "Men age 45 to 64 who completed some college, no degree", "Men age 45 to 64 (\"middle aged\"). The male population between the age of fourty-five years to sixty-four years within the specified area.", "Households with two male partners. An unmarried partner is a person age 15 years and over, who is not related to the householder, who shares living quarters, and who has an intimate relationship with the householder.", "Civilian Employed Population in Management, Business, Science, and Arts Occupations. The number of employed civilians 16 years old and over in each geography in the Management, Business, Science, and Arts occupations. Occupation codes are based on the Standard Occupational Classification (SOC), published by the Executive Office of the President, Office of Management and Budget.", "Workers age 16 and over with no vehicle. All people in a geographic area over the age of 16 who do not own a car.", "Car-free households. The number of households without passenger cars, vans, and pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Not a U.S. Citizen Population. The number of people within each geography who indicated that they are not U.S. citizens.", "Workers employed in management business science and arts occupations", "Workers employed in natural resources, construction, and maintenance occupations", "Workers employed in production, transportation, and material moving", "Workers employed in sales and office occupations", "Workers employed in service occupations", "One car households. The number of households with one passenger car, van, , pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Two car households. The number of households with two passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Three car households. The number of households with one passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Population age 25 to 64. The number of people in each geography who are between the ages of 25 and 64. ", "Population for Whom Poverty Status Determined. The number of people in each geography who could be identified as either living in poverty or not.  This should be used as the denominator when calculating poverty rates, as it excludes people for whom it was not possible to determine poverty.", "Population 1 year and over. All people, male and female, child and adult, living in a given geographic area that are 1 year and older.", "Population 3 Years and Over. The total number of people in each geography age 3 years and over.  This denominator is mostly used to calculate rates of school enrollment.", "Income In The Past 12 Months Below Poverty Level. The number of people in a geographic area who are part of a family (which could be just them as an individual) determined to be \"in poverty\" following the Office of Management and Budget's Directive 14. (https://www.census.gov/hhes/povmeas/methodology/ombdir14.html)", "Civilian Employed Population in Sales and Office Occupations. The number of employed civilians 16 years old and over in each geography in the Sales and Office occupations. Occupation codes are based on the Standard Occupational Classification (SOC), published by the Executive Office of the President, Office of Management and Budget.", "Population who completed some college or obtained associate's degree. The number of people in a geographic area over the age of 25 who obtained an associate's degree, and did not complete a more advanced degree.", "Walked to Work. The number of workers age 16 years and over within a geographic area who primarily walked to work.  This would mean that of any way of getting to work, they travelled the most distance walking.", "Worked at Home. The count within a geographical area of workers over the age of 16 who worked at home.", "Workers over the Age of 16. The number of people in each geography who work. Workers include those employed at private for-profit companies, the self-employed, government workers and non-profit employees.", "Population Completed Associate's Degree. The number of people in a geographic area over the age of 25 who obtained a associate's degree, and did not complete a more advanced degree.", "Population Completed Bachelor's Degree. The number of people in a geographic area over the age of 25 who obtained a bachelor's degree, and did not complete a more advanced degree.", "Population Completed High School. The number of people in a geographic area over the age of 25 who completed high school, and did not complete a more advanced degree.", "Population completed less than one year of college, no degree. The number of people in a geographic area over the age of 25 who attended college for less than one year and no further.", "Population Completed Master's Degree. The number of people in a geographic area over the age of 25 who obtained a master's degree, but did not complete a more advanced degree.", "Population completed more than one year of college, no degree. The number of people in a geographic area over the age of 25 who attended college for more than one year but did not obtain a degree", "Population 25 Years and Over. The number of people in a geographic area who are over the age of 25.  This is used mostly as a denominator of educational attainment.", "Number of workers with a commute between 35 and 44 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 35 and 44 minutes. ", "Number of workers with a commute of over 60 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in over 60 minutes.", "Number of workers with less than 10 minute commute. The number of workers in a geographic area over the age of 16 who do not work from home and commute in less than 10 minutes.", "Workers age 16 and over who do not work from home. The number of workers in a geographic area over the age of 16 who do not work from home", "Hispanic of any race", "Population 5 Years and Over. The number of people in a geographic area who are over the age of 5.  This is primarily used as a denominator of measures of language spoken at home.", "Speaks only English at Home. The number of people in a geographic area over age 5 who speak only English at home.", "Speaks Spanish at Home. The number of people in a geographic area over age 5 who speak Spanish at home, possibly in addition to other languages.", "Speaks Spanish at Home, speaks English less than \"very well\"", "Population 15 Years and Over. The number of people in a geographic area who are over the age of 15.  This is used mostly as a denominator of marital status.", "Never Married. The number of people in a geographic area who have never been married.", "Currently married. The number of people in a geographic area who are currently married", "Married but separated. The number of people in a geographic area who are married but separated", "Widowed. The number of people in a geographic area who are widowed", "Divorced. The number of people in a geographic area who are divorced", null], "sample_rows": [{"geo_id": "1209", "nonfamily_households": 102760.0, "family_households": 190599.0, "median_year_structure_built": 1984.0, "rent_burden_not_computed": 4788.0, "rent_over_50_percent": 18300.0, "rent_40_to_50_percent": 7861.0, "rent_35_to_40_percent": 5384.0, "rent_30_to_35_percent": 7503.0, "rent_25_to_30_percent": 9929.0, "rent_20_to_25_percent": 10800.0, "rent_15_to_20_percent": 7762.0, "rent_10_to_15_percent": 4365.0, "rent_under_10_percent": 2154.0, "total_pop": 746306.0, "male_pop": 365917.0, "female_pop": 380389.0, "median_age": 41.5, "white_pop": 581974.0, "black_pop": 38630.0, "asian_pop": 23406.0, "hispanic_pop": 87906.0, "amerindian_pop": 1270.0, "other_race_pop": 1749.0, "two_or_more_races_pop": 10375.0, "not_hispanic_pop": 658400.0, "commuters_by_public_transportation": 4023.0, "households": 293359.0, "median_income": 50706.0, "income_per_capita": 29365.0, "housing_units": 349407.0, "vacant_housing_units": 56048.0, "vacant_housing_units_for_rent": 10970.0, "vacant_housing_units_for_sale": 10367.0, "median_rent": 780.0, "percent_income_spent_on_rent": 31.3, "owner_occupied_housing_units": 214513.0, "million_dollar_housing_units": 2810.0, "mortgaged_housing_units": 140990.0, "families_with_young_children": 51143.0, "two_parent_families_with_young_children": 37379.0, "two_parents_in_labor_force_families_with_young_children": 22045.0, "two_parents_father_in_labor_force_families_with_young_children": 13414.0, "two_parents_mother_in_labor_force_families_with_young_children": 1180.0, "two_parents_not_in_labor_force_families_with_young_children": 740.0, "one_parent_families_with_young_children": 13764.0, "father_one_parent_families_with_young_children": 3204.0, "father_in_labor_force_one_parent_families_with_young_children": 2552.0, "commute_10_14_mins": 38724.0, "commute_15_19_mins": 39318.0, "commute_20_24_mins": 47712.0, "commute_25_29_mins": 21074.0, "commute_30_34_mins": 49795.0, "commute_45_59_mins": 33857.0, "aggregate_travel_time_to_work": 8637855.0, "income_less_10000": 16701.0, "income_10000_14999": 15472.0, "income_15000_19999": 16319.0, "income_20000_24999": 17262.0, "income_25000_29999": 17355.0, "income_30000_34999": 16450.0, "income_35000_39999": 15759.0, "income_40000_44999": 15900.0, "income_45000_49999": 13303.0, "income_50000_59999": 24338.0, "income_60000_74999": 28465.0, "income_75000_99999": 34762.0, "income_100000_124999": 22419.0, "income_125000_149999": 13155.0, "income_150000_199999": 12493.0, "income_200000_or_more": 13206.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 930.0, "owner_occupied_housing_units_lower_value_quartile": 123800.0, "owner_occupied_housing_units_median_value": 203400.0, "owner_occupied_housing_units_upper_value_quartile": 317100.0, "married_households": 149295.0, "occupied_housing_units": 293359.0, "housing_units_renter_occupied": 78846.0, "dwellings_1_units_detached": 207956.0, "dwellings_1_units_attached": 22764.0, "dwellings_2_units": 4761.0, "dwellings_3_to_4_units": 12913.0, "dwellings_5_to_9_units": 17313.0, "dwellings_10_to_19_units": 22082.0, "dwellings_20_to_49_units": 13727.0, "dwellings_50_or_more_units": 12067.0, "mobile_homes": 35410.0, "housing_built_2005_or_later": 12094.0, "housing_built_2000_to_2004": 42749.0, "housing_built_1939_or_earlier": 3719.0, "male_under_5": 22475.0, "male_5_to_9": 22141.0, "male_10_to_14": 26102.0, "male_15_to_17": 14049.0, "male_18_to_19": 7781.0, "male_20": 3962.0, "male_21": 4856.0, "male_22_to_24": 13477.0, "male_25_to_29": 22868.0, "male_30_to_34": 21467.0, "male_35_to_39": 23917.0, "male_40_to_44": 27291.0, "male_45_to_49": 29198.0, "male_50_to_54": 25875.0, "male_55_to_59": 24301.0, "male_60_61": 8574.0, "male_62_64": 10755.0, "male_65_to_66": 6363.0, "male_67_to_69": 8596.0, "male_70_to_74": 11640.0, "male_75_to_79": 12838.0, "male_80_to_84": 9801.0, "male_85_and_over": 7590.0, "female_under_5": 21999.0, "female_5_to_9": 20666.0, "female_10_to_14": 24539.0, "female_15_to_17": 12894.0, "female_18_to_19": 7876.0, "female_20": 4665.0, "female_21": 4202.0, "female_22_to_24": 11546.0, "female_25_to_29": 21824.0, "female_30_to_34": 19846.0, "female_35_to_39": 23879.0, "female_40_to_44": 29020.0, "female_45_to_49": 29870.0, "female_50_to_54": 27807.0, "female_55_to_59": 24164.0, "female_60_to_61": 9909.0, "female_62_to_64": 12797.0, "female_65_to_66": 7171.0, "female_67_to_69": 10168.0, "female_70_to_74": 13377.0, "female_75_to_79": 16141.0, "female_80_to_84": 12317.0, "female_85_and_over": 13712.0, "white_including_hispanic": 656430.0, "black_including_hispanic": 40116.0, "amerindian_including_hispanic": 1598.0, "asian_including_hispanic": 23812.0, "commute_5_9_mins": 27090.0, "commute_35_39_mins": 11220.0, "commute_40_44_mins": 14616.0, "commute_60_89_mins": 19562.0, "commute_90_more_mins": 5790.0, "households_retirement_income": 59747.0, "armed_forces": NaN, "civilian_labor_force": NaN, "employed_pop": NaN, "unemployed_pop": NaN, "not_in_labor_force": NaN, "pop_16_over": NaN, "pop_in_labor_force": NaN, "asian_male_45_54": 1600.0, "asian_male_55_64": 855.0, "black_male_45_54": 2111.0, "black_male_55_64": 2025.0, "hispanic_male_45_54": 4882.0, "hispanic_male_55_64": 2678.0, "white_male_45_54": 45707.0, "white_male_55_64": 37557.0, "bachelors_degree_2": 99583.0, "bachelors_degree_or_higher_25_64": 127304.0, "children": 164865.0, "children_in_single_female_hh": 34625.0, "commuters_by_bus": 3944.0, "commuters_by_car_truck_van": 300207.0, "commuters_by_carpool": 30245.0, "commuters_by_subway_or_elevated": 46.0, "commuters_drove_alone": 269962.0, "different_house_year_ago_different_city": 88099.0, "different_house_year_ago_same_city": 19375.0, "employed_agriculture_forestry_fishing_hunting_mining": 3013.0, "employed_arts_entertainment_recreation_accommodation_food": 30579.0, "employed_construction": 25057.0, "employed_education_health_social": 72620.0, "employed_finance_insurance_real_estate": 33853.0, "employed_information": 10104.0, "employed_manufacturing": 20899.0, "employed_other_services_not_public_admin": 19561.0, "employed_public_administration": 12769.0, "employed_retail_trade": 43759.0, "employed_science_management_admin_waste": 45827.0, "employed_transportation_warehousing_utilities": 12817.0, "employed_wholesale_trade": 11885.0, "female_female_households": 975.0, "four_more_cars": 8410.0, "gini_index": 0.469, "graduate_professional_degree": 51648.0, "group_quarters": 12635.0, "high_school_including_ged": 155832.0, "households_public_asst_or_food_stamps": 16298.0, "in_grades_1_to_4": 34664.0, "in_grades_5_to_8": 40576.0, "in_grades_9_to_12": 34993.0, "in_school": 174578.0, "in_undergrad_college": 35449.0, "less_than_high_school_graduate": 56839.0, "male_45_64_associates_degree": 8536.0, "male_45_64_bachelors_degree": 21423.0, "male_45_64_graduate_degree": 12997.0, "male_45_64_less_than_9_grade": 1977.0, "male_45_64_grade_9_12": 5335.0, "male_45_64_high_school": 27488.0, "male_45_64_some_college": 20947.0, "male_45_to_64": 98703.0, "male_male_households": 970.0, "management_business_sci_arts_employed": 135756.0, "no_car": 6369.0, "no_cars": 13803.0, "not_us_citizen_pop": 42334.0, "occupation_management_arts": 135756.0, "occupation_natural_resources_construction_maintenance": 1917.0, "occupation_production_transportation_material": 27047.0, "occupation_sales_office": 101344.0, "occupation_services": 52500.0, "one_car": 119335.0, "two_cars": 119778.0, "three_cars": 32033.0, "pop_25_64": 393362.0, "pop_determined_poverty_status": 738324.0, "population_1_year_and_over": 738456.0, "population_3_years_over": 720709.0, "poverty": 76567.0, "sales_office_employed": 40863.0, "some_college_and_associates_degree": 159174.0, "walked_to_work": 3690.0, "worked_at_home": 21193.0, "workers_16_and_over": 336427.0, "associates_degree": NaN, "bachelors_degree": NaN, "high_school_diploma": NaN, "less_one_year_college": NaN, "masters_degree": NaN, "one_year_more_college": NaN, "pop_25_years_over": NaN, "commute_35_44_mins": 25836.0, "commute_60_more_mins": 25352.0, "commute_less_10_mins": 33566.0, "commuters_16_over": 315234.0, "hispanic_any_race": 87906.0, "pop_5_years_over": 701832.0, "speak_only_english_at_home": 592416.0, "speak_spanish_at_home": 61790.0, "speak_spanish_at_home_low_english": 25194.0, "pop_15_and_over": 608384.0, "pop_never_married": 153519.0, "pop_now_married": 319428.0, "pop_separated": 11455.0, "pop_widowed": 42266.0, "pop_divorced": 69252.0, "do_date": "20072009"}, {"geo_id": "1713", "nonfamily_households": 70427.0, "family_households": 194365.0, "median_year_structure_built": 1984.0, "rent_burden_not_computed": 1796.0, "rent_over_50_percent": 9227.0, "rent_40_to_50_percent": 3432.0, "rent_35_to_40_percent": 3398.0, "rent_30_to_35_percent": 3980.0, "rent_25_to_30_percent": 5478.0, "rent_20_to_25_percent": 7219.0, "rent_15_to_20_percent": 7209.0, "rent_10_to_15_percent": 4353.0, "rent_under_10_percent": 1696.0, "total_pop": 764213.0, "male_pop": 379875.0, "female_pop": 384338.0, "median_age": 36.7, "white_pop": 560919.0, "black_pop": 46346.0, "asian_pop": 66861.0, "hispanic_pop": 78187.0, "amerindian_pop": 421.0, "other_race_pop": 2055.0, "two_or_more_races_pop": 9382.0, "not_hispanic_pop": 686026.0, "commuters_by_public_transportation": 24742.0, "households": 264792.0, "median_income": 81365.0, "income_per_capita": 38344.0, "housing_units": 281519.0, "vacant_housing_units": 16727.0, "vacant_housing_units_for_rent": 4588.0, "vacant_housing_units_for_sale": 5851.0, "median_rent": 929.0, "percent_income_spent_on_rent": 27.3, "owner_occupied_housing_units": 217004.0, "million_dollar_housing_units": 6736.0, "mortgaged_housing_units": 169424.0, "families_with_young_children": 60092.0, "two_parent_families_with_young_children": 50544.0, "two_parents_in_labor_force_families_with_young_children": 28797.0, "two_parents_father_in_labor_force_families_with_young_children": 20144.0, "two_parents_mother_in_labor_force_families_with_young_children": 881.0, "two_parents_not_in_labor_force_families_with_young_children": 722.0, "one_parent_families_with_young_children": 9548.0, "father_one_parent_families_with_young_children": 2370.0, "father_in_labor_force_one_parent_families_with_young_children": 2219.0, "commute_10_14_mins": 39231.0, "commute_15_19_mins": 40819.0, "commute_20_24_mins": 43236.0, "commute_25_29_mins": 22439.0, "commute_30_34_mins": 46026.0, "commute_45_59_mins": 41911.0, "aggregate_travel_time_to_work": 11690765.0, "income_less_10000": 6286.0, "income_10000_14999": 5565.0, "income_15000_19999": 6862.0, "income_20000_24999": 7616.0, "income_25000_29999": 9477.0, "income_30000_34999": 8798.0, "income_35000_39999": 9067.0, "income_40000_44999": 10073.0, "income_45000_49999": 9378.0, "income_50000_59999": 19729.0, "income_60000_74999": 28472.0, "income_75000_99999": 39636.0, "income_100000_124999": 31559.0, "income_125000_149999": 21434.0, "income_150000_199999": 24608.0, "income_200000_or_more": 26232.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 1038.0, "owner_occupied_housing_units_lower_value_quartile": 219300.0, "owner_occupied_housing_units_median_value": 317800.0, "owner_occupied_housing_units_upper_value_quartile": 444300.0, "married_households": 163232.0, "occupied_housing_units": 264792.0, "housing_units_renter_occupied": 47788.0, "dwellings_1_units_detached": 182103.0, "dwellings_1_units_attached": 38577.0, "dwellings_2_units": 2533.0, "dwellings_3_to_4_units": 6721.0, "dwellings_5_to_9_units": 15142.0, "dwellings_10_to_19_units": 14501.0, "dwellings_20_to_49_units": 9854.0, "dwellings_50_or_more_units": 11549.0, "mobile_homes": 508.0, "housing_built_2005_or_later": 11689.0, "housing_built_2000_to_2004": 38969.0, "housing_built_1939_or_earlier": 11243.0, "male_under_5": 26267.0, "male_5_to_9": 29764.0, "male_10_to_14": 32291.0, "male_15_to_17": 18330.0, "male_18_to_19": 9895.0, "male_20": 5631.0, "male_21": 4381.0, "male_22_to_24": 13132.0, "male_25_to_29": 24154.0, "male_30_to_34": 22799.0, "male_35_to_39": 30334.0, "male_40_to_44": 30361.0, "male_45_to_49": 30934.0, "male_50_to_54": 28358.0, "male_55_to_59": 23377.0, "male_60_61": 8299.0, "male_62_64": 8764.0, "male_65_to_66": 5768.0, "male_67_to_69": 6748.0, "male_70_to_74": 7648.0, "male_75_to_79": 5342.0, "male_80_to_84": 4147.0, "male_85_and_over": 3151.0, "female_under_5": 23851.0, "female_5_to_9": 28643.0, "female_10_to_14": 29583.0, "female_15_to_17": 17070.0, "female_18_to_19": 10009.0, "female_20": 4484.0, "female_21": 4374.0, "female_22_to_24": 12227.0, "female_25_to_29": 21892.0, "female_30_to_34": 23184.0, "female_35_to_39": 30792.0, "female_40_to_44": 30443.0, "female_45_to_49": 30421.0, "female_50_to_54": 29252.0, "female_55_to_59": 24866.0, "female_60_to_61": 8145.0, "female_62_to_64": 9540.0, "female_65_to_66": 6327.0, "female_67_to_69": 7076.0, "female_70_to_74": 9182.0, "female_75_to_79": 7534.0, "female_80_to_84": 7219.0, "female_85_and_over": 8224.0, "white_including_hispanic": 604870.0, "black_including_hispanic": 47306.0, "amerindian_including_hispanic": 1082.0, "asian_including_hispanic": 67183.0, "commute_5_9_mins": 27360.0, "commute_35_39_mins": 13138.0, "commute_40_44_mins": 21625.0, "commute_60_89_mins": 43414.0, "commute_90_more_mins": 14488.0, "households_retirement_income": 40043.0, "armed_forces": NaN, "civilian_labor_force": NaN, "employed_pop": NaN, "unemployed_pop": NaN, "not_in_labor_force": NaN, "pop_16_over": NaN, "pop_in_labor_force": NaN, "asian_male_45_54": 4487.0, "asian_male_55_64": 3028.0, "black_male_45_54": 3137.0, "black_male_55_64": 1620.0, "hispanic_male_45_54": 3104.0, "hispanic_male_55_64": 2000.0, "white_male_45_54": 48167.0, "white_male_55_64": 33440.0, "bachelors_degree_2": 135682.0, "bachelors_degree_or_higher_25_64": 203836.0, "children": 205799.0, "children_in_single_female_hh": 26518.0, "commuters_by_bus": 1346.0, "commuters_by_car_truck_van": 326131.0, "commuters_by_carpool": 25174.0, "commuters_by_subway_or_elevated": 1409.0, "commuters_drove_alone": 300957.0, "different_house_year_ago_different_city": 63318.0, "different_house_year_ago_same_city": 14722.0, "employed_agriculture_forestry_fishing_hunting_mining": 1099.0, "employed_arts_entertainment_recreation_accommodation_food": 31547.0, "employed_construction": 22087.0, "employed_education_health_social": 80242.0, "employed_finance_insurance_real_estate": 39197.0, "employed_information": 9792.0, "employed_manufacturing": 43570.0, "employed_other_services_not_public_admin": 17019.0, "employed_public_administration": 10081.0, "employed_retail_trade": 43312.0, "employed_science_management_admin_waste": 51337.0, "employed_transportation_warehousing_utilities": 21607.0, "employed_wholesale_trade": 16821.0, "female_female_households": 548.0, "four_more_cars": 17334.0, "gini_index": 0.437, "graduate_professional_degree": 88760.0, "group_quarters": 11808.0, "high_school_including_ged": 102379.0, "households_public_asst_or_food_stamps": 8611.0, "in_grades_1_to_4": 46308.0, "in_grades_5_to_8": 49169.0, "in_grades_9_to_12": 47089.0, "in_school": 229263.0, "in_undergrad_college": 45204.0, "less_than_high_school_graduate": 34035.0, "male_45_64_associates_degree": 6774.0, "male_45_64_bachelors_degree": 27080.0, "male_45_64_graduate_degree": 24088.0, "male_45_64_less_than_9_grade": 1581.0, "male_45_64_grade_9_12": 3167.0, "male_45_64_high_school": 18705.0, "male_45_64_some_college": 18337.0, "male_45_to_64": 99732.0, "male_male_households": 439.0, "management_business_sci_arts_employed": 176368.0, "no_car": 4727.0, "no_cars": 8630.0, "not_us_citizen_pop": 54046.0, "occupation_management_arts": 176368.0, "occupation_natural_resources_construction_maintenance": 455.0, "occupation_production_transportation_material": 24768.0, "occupation_sales_office": 103906.0, "occupation_services": 48957.0, "one_car": 72347.0, "two_cars": 124987.0, "three_cars": 41494.0, "pop_25_64": 415915.0, "pop_determined_poverty_status": 752654.0, "population_1_year_and_over": 754807.0, "population_3_years_over": 735790.0, "poverty": 34050.0, "sales_office_employed": 51899.0, "some_college_and_associates_degree": 133425.0, "walked_to_work": 5272.0, "worked_at_home": 17668.0, "workers_16_and_over": 378057.0, "associates_degree": NaN, "bachelors_degree": NaN, "high_school_diploma": NaN, "less_one_year_college": NaN, "masters_degree": NaN, "one_year_more_college": NaN, "pop_25_years_over": NaN, "commute_35_44_mins": 34763.0, "commute_60_more_mins": 57902.0, "commute_less_10_mins": 34062.0, "commuters_16_over": 360389.0, "hispanic_any_race": 78187.0, "pop_5_years_over": 714095.0, "speak_only_english_at_home": 552009.0, "speak_spanish_at_home": 54798.0, "speak_spanish_at_home_low_english": 23676.0, "pop_15_and_over": 593814.0, "pop_never_married": 166831.0, "pop_now_married": 343345.0, "pop_separated": 6124.0, "pop_widowed": 24235.0, "pop_divorced": 41622.0, "do_date": "20072009"}, {"geo_id": "4804", "nonfamily_households": 74037.0, "family_households": 210678.0, "median_year_structure_built": 1984.0, "rent_burden_not_computed": 9905.0, "rent_over_50_percent": 12904.0, "rent_40_to_50_percent": 5364.0, "rent_35_to_40_percent": 4741.0, "rent_30_to_35_percent": 5039.0, "rent_25_to_30_percent": 7837.0, "rent_20_to_25_percent": 8370.0, "rent_15_to_20_percent": 8363.0, "rent_10_to_15_percent": 6008.0, "rent_under_10_percent": 2845.0, "total_pop": 807760.0, "male_pop": 403356.0, "female_pop": 404404.0, "median_age": 36.5, "white_pop": 594326.0, "black_pop": 82423.0, "asian_pop": 13607.0, "hispanic_pop": 97529.0, "amerindian_pop": 5635.0, "other_race_pop": 808.0, "two_or_more_races_pop": 12431.0, "not_hispanic_pop": 710231.0, "commuters_by_public_transportation": 1705.0, "households": 284715.0, "median_income": 51112.0, "income_per_capita": 24291.0, "housing_units": 327454.0, "vacant_housing_units": 42739.0, "vacant_housing_units_for_rent": 7333.0, "vacant_housing_units_for_sale": 5848.0, "median_rent": 488.0, "percent_income_spent_on_rent": 28.3, "owner_occupied_housing_units": 213339.0, "million_dollar_housing_units": 1164.0, "mortgaged_housing_units": 132966.0, "families_with_young_children": 68642.0, "two_parent_families_with_young_children": 47784.0, "two_parents_in_labor_force_families_with_young_children": 27894.0, "two_parents_father_in_labor_force_families_with_young_children": 18087.0, "two_parents_mother_in_labor_force_families_with_young_children": 1110.0, "two_parents_not_in_labor_force_families_with_young_children": 693.0, "one_parent_families_with_young_children": 20858.0, "father_one_parent_families_with_young_children": 4217.0, "father_in_labor_force_one_parent_families_with_young_children": 3519.0, "commute_10_14_mins": 50749.0, "commute_15_19_mins": 47386.0, "commute_20_24_mins": 40613.0, "commute_25_29_mins": 17654.0, "commute_30_34_mins": 41184.0, "commute_45_59_mins": 32785.0, "aggregate_travel_time_to_work": 9074865.0, "income_less_10000": 21075.0, "income_10000_14999": 15966.0, "income_15000_19999": 15550.0, "income_20000_24999": 15383.0, "income_25000_29999": 15954.0, "income_30000_34999": 13756.0, "income_35000_39999": 14835.0, "income_40000_44999": 14217.0, "income_45000_49999": 12624.0, "income_50000_59999": 24265.0, "income_60000_74999": 29754.0, "income_75000_99999": 38025.0, "income_100000_124999": 22192.0, "income_125000_149999": 11804.0, "income_150000_199999": 11187.0, "income_200000_or_more": 8128.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 683.0, "owner_occupied_housing_units_lower_value_quartile": 66900.0, "owner_occupied_housing_units_median_value": 122200.0, "owner_occupied_housing_units_upper_value_quartile": 189600.0, "married_households": 164332.0, "occupied_housing_units": 284715.0, "housing_units_renter_occupied": 71376.0, "dwellings_1_units_detached": 251371.0, "dwellings_1_units_attached": 3816.0, "dwellings_2_units": 6991.0, "dwellings_3_to_4_units": 6595.0, "dwellings_5_to_9_units": 8321.0, "dwellings_10_to_19_units": 5614.0, "dwellings_20_to_49_units": 3563.0, "dwellings_50_or_more_units": 2519.0, "mobile_homes": 38358.0, "housing_built_2005_or_later": 28034.0, "housing_built_2000_to_2004": 47656.0, "housing_built_1939_or_earlier": 20550.0, "male_under_5": 31367.0, "male_5_to_9": 32046.0, "male_10_to_14": 30674.0, "male_15_to_17": 18462.0, "male_18_to_19": 11980.0, "male_20": 5468.0, "male_21": 5433.0, "male_22_to_24": 12869.0, "male_25_to_29": 25503.0, "male_30_to_34": 26888.0, "male_35_to_39": 28399.0, "male_40_to_44": 30832.0, "male_45_to_49": 30493.0, "male_50_to_54": 26274.0, "male_55_to_59": 21677.0, "male_60_61": 9364.0, "male_62_64": 11619.0, "male_65_to_66": 6972.0, "male_67_to_69": 8268.0, "male_70_to_74": 11773.0, "male_75_to_79": 7772.0, "male_80_to_84": 5508.0, "male_85_and_over": 3715.0, "female_under_5": 28874.0, "female_5_to_9": 28815.0, "female_10_to_14": 28985.0, "female_15_to_17": 16660.0, "female_18_to_19": 10488.0, "female_20": 4737.0, "female_21": 4662.0, "female_22_to_24": 11813.0, "female_25_to_29": 23379.0, "female_30_to_34": 26439.0, "female_35_to_39": 29344.0, "female_40_to_44": 28354.0, "female_45_to_49": 30387.0, "female_50_to_54": 27318.0, "female_55_to_59": 24796.0, "female_60_to_61": 8457.0, "female_62_to_64": 12055.0, "female_65_to_66": 7450.0, "female_67_to_69": 8974.0, "female_70_to_74": 13636.0, "female_75_to_79": 10214.0, "female_80_to_84": 9543.0, "female_85_and_over": 9024.0, "white_including_hispanic": 659629.0, "black_including_hispanic": 83168.0, "amerindian_including_hispanic": 6758.0, "asian_including_hispanic": 13620.0, "commute_5_9_mins": 43710.0, "commute_35_39_mins": 9134.0, "commute_40_44_mins": 12537.0, "commute_60_89_mins": 25164.0, "commute_90_more_mins": 9748.0, "households_retirement_income": 48478.0, "armed_forces": NaN, "civilian_labor_force": NaN, "employed_pop": NaN, "unemployed_pop": NaN, "not_in_labor_force": NaN, "pop_16_over": NaN, "pop_in_labor_force": NaN, "asian_male_45_54": 723.0, "asian_male_55_64": 330.0, "black_male_45_54": 5834.0, "black_male_55_64": 3481.0, "hispanic_male_45_54": 4943.0, "hispanic_male_55_64": 2623.0, "white_male_45_54": 44520.0, "white_male_55_64": 35513.0, "bachelors_degree_2": 81448.0, "bachelors_degree_or_higher_25_64": 99255.0, "children": 215883.0, "children_in_single_female_hh": 46073.0, "commuters_by_bus": 967.0, "commuters_by_car_truck_van": 331654.0, "commuters_by_carpool": 41445.0, "commuters_by_subway_or_elevated": 343.0, "commuters_drove_alone": 290209.0, "different_house_year_ago_different_city": 95258.0, "different_house_year_ago_same_city": 34156.0, "employed_agriculture_forestry_fishing_hunting_mining": 8007.0, "employed_arts_entertainment_recreation_accommodation_food": 23145.0, "employed_construction": 30640.0, "employed_education_health_social": 78256.0, "employed_finance_insurance_real_estate": 23965.0, "employed_information": 9373.0, "employed_manufacturing": 50015.0, "employed_other_services_not_public_admin": 18897.0, "employed_public_administration": 17033.0, "employed_retail_trade": 46260.0, "employed_science_management_admin_waste": 34591.0, "employed_transportation_warehousing_utilities": 18282.0, "employed_wholesale_trade": 10281.0, "female_female_households": 635.0, "four_more_cars": 18296.0, "gini_index": 0.439, "graduate_professional_degree": 35839.0, "group_quarters": 19232.0, "high_school_including_ged": 157889.0, "households_public_asst_or_food_stamps": 26820.0, "in_grades_1_to_4": 49832.0, "in_grades_5_to_8": 48003.0, "in_grades_9_to_12": 45765.0, "in_school": 212127.0, "in_undergrad_college": 36314.0, "less_than_high_school_graduate": 82143.0, "male_45_64_associates_degree": 6925.0, "male_45_64_bachelors_degree": 16059.0, "male_45_64_graduate_degree": 8048.0, "male_45_64_less_than_9_grade": 5682.0, "male_45_64_grade_9_12": 9091.0, "male_45_64_high_school": 29307.0, "male_45_64_some_college": 24315.0, "male_45_to_64": 99427.0, "male_male_households": 673.0, "management_business_sci_arts_employed": 123716.0, "no_car": 5865.0, "no_cars": 13155.0, "not_us_citizen_pop": 40187.0, "occupation_management_arts": 123716.0, "occupation_natural_resources_construction_maintenance": 3088.0, "occupation_production_transportation_material": 38447.0, "occupation_sales_office": 94401.0, "occupation_services": 58158.0, "one_car": 84733.0, "two_cars": 123598.0, "three_cars": 44933.0, "pop_25_64": 421578.0, "pop_determined_poverty_status": 787208.0, "population_1_year_and_over": 796220.0, "population_3_years_over": 773192.0, "poverty": 99171.0, "sales_office_employed": 35608.0, "some_college_and_associates_degree": 167108.0, "walked_to_work": 4380.0, "worked_at_home": 15676.0, "workers_16_and_over": 359619.0, "associates_degree": NaN, "bachelors_degree": NaN, "high_school_diploma": NaN, "less_one_year_college": NaN, "masters_degree": NaN, "one_year_more_college": NaN, "pop_25_years_over": NaN, "commute_35_44_mins": 21671.0, "commute_60_more_mins": 34912.0, "commute_less_10_mins": 56989.0, "commuters_16_over": 343943.0, "hispanic_any_race": 97529.0, "pop_5_years_over": 747519.0, "speak_only_english_at_home": 653870.0, "speak_spanish_at_home": 72810.0, "speak_spanish_at_home_low_english": 32208.0, "pop_15_and_over": 626999.0, "pop_never_married": 145222.0, "pop_now_married": 350694.0, "pop_separated": 13497.0, "pop_widowed": 40235.0, "pop_divorced": 72560.0, "do_date": "20072009"}, {"geo_id": "3706", "nonfamily_households": 84804.0, "family_households": 190887.0, "median_year_structure_built": 1984.0, "rent_burden_not_computed": 7739.0, "rent_over_50_percent": 13542.0, "rent_40_to_50_percent": 5313.0, "rent_35_to_40_percent": 3431.0, "rent_30_to_35_percent": 5276.0, "rent_25_to_30_percent": 6573.0, "rent_20_to_25_percent": 9334.0, "rent_15_to_20_percent": 10193.0, "rent_10_to_15_percent": 7051.0, "rent_under_10_percent": 2339.0, "total_pop": 702349.0, "male_pop": 342718.0, "female_pop": 359631.0, "median_age": 39.8, "white_pop": 562415.0, "black_pop": 70193.0, "asian_pop": 11439.0, "hispanic_pop": 47449.0, "amerindian_pop": 2594.0, "other_race_pop": 1697.0, "two_or_more_races_pop": 6252.0, "not_hispanic_pop": 654900.0, "commuters_by_public_transportation": 1046.0, "households": 275691.0, "median_income": 49140.0, "income_per_capita": 25897.0, "housing_units": 309560.0, "vacant_housing_units": 33869.0, "vacant_housing_units_for_rent": 8435.0, "vacant_housing_units_for_sale": 4567.0, "median_rent": 529.0, "percent_income_spent_on_rent": 27.0, "owner_occupied_housing_units": 204900.0, "million_dollar_housing_units": 1434.0, "mortgaged_housing_units": 137487.0, "families_with_young_children": 49853.0, "two_parent_families_with_young_children": 34461.0, "two_parents_in_labor_force_families_with_young_children": 21062.0, "two_parents_father_in_labor_force_families_with_young_children": 12044.0, "two_parents_mother_in_labor_force_families_with_young_children": 1001.0, "two_parents_not_in_labor_force_families_with_young_children": 354.0, "one_parent_families_with_young_children": 15392.0, "father_one_parent_families_with_young_children": 3630.0, "father_in_labor_force_one_parent_families_with_young_children": 3198.0, "commute_10_14_mins": 52326.0, "commute_15_19_mins": 58362.0, "commute_20_24_mins": 50147.0, "commute_25_29_mins": 24904.0, "commute_30_34_mins": 40098.0, "commute_45_59_mins": 17076.0, "aggregate_travel_time_to_work": 7142460.0, "income_less_10000": 18308.0, "income_10000_14999": 15371.0, "income_15000_19999": 15236.0, "income_20000_24999": 16197.0, "income_25000_29999": 17069.0, "income_30000_34999": 16449.0, "income_35000_39999": 14817.0, "income_40000_44999": 14190.0, "income_45000_49999": 12289.0, "income_50000_59999": 24660.0, "income_60000_74999": 29793.0, "income_75000_99999": 35428.0, "income_100000_124999": 18950.0, "income_125000_149999": 10184.0, "income_150000_199999": 8393.0, "income_200000_or_more": 8357.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 676.0, "owner_occupied_housing_units_lower_value_quartile": 99200.0, "owner_occupied_housing_units_median_value": 152100.0, "owner_occupied_housing_units_upper_value_quartile": 233100.0, "married_households": 151024.0, "occupied_housing_units": 275691.0, "housing_units_renter_occupied": 70791.0, "dwellings_1_units_detached": 214757.0, "dwellings_1_units_attached": 8855.0, "dwellings_2_units": 4719.0, "dwellings_3_to_4_units": 6913.0, "dwellings_5_to_9_units": 12687.0, "dwellings_10_to_19_units": 8710.0, "dwellings_20_to_49_units": 4108.0, "dwellings_50_or_more_units": 3124.0, "mobile_homes": 45596.0, "housing_built_2005_or_later": 15681.0, "housing_built_2000_to_2004": 35063.0, "housing_built_1939_or_earlier": 17286.0, "male_under_5": 21439.0, "male_5_to_9": 23129.0, "male_10_to_14": 24856.0, "male_15_to_17": 14720.0, "male_18_to_19": 7772.0, "male_20": 4491.0, "male_21": 4314.0, "male_22_to_24": 11742.0, "male_25_to_29": 19894.0, "male_30_to_34": 20216.0, "male_35_to_39": 25023.0, "male_40_to_44": 25805.0, "male_45_to_49": 27530.0, "male_50_to_54": 25586.0, "male_55_to_59": 22709.0, "male_60_61": 9052.0, "male_62_64": 10980.0, "male_65_to_66": 6821.0, "male_67_to_69": 8060.0, "male_70_to_74": 10273.0, "male_75_to_79": 8624.0, "male_80_to_84": 5861.0, "male_85_and_over": 3821.0, "female_under_5": 21487.0, "female_5_to_9": 21668.0, "female_10_to_14": 24573.0, "female_15_to_17": 14485.0, "female_18_to_19": 8267.0, "female_20": 3740.0, "female_21": 3909.0, "female_22_to_24": 10344.0, "female_25_to_29": 21037.0, "female_30_to_34": 21379.0, "female_35_to_39": 24832.0, "female_40_to_44": 24850.0, "female_45_to_49": 27989.0, "female_50_to_54": 26083.0, "female_55_to_59": 24075.0, "female_60_to_61": 9851.0, "female_62_to_64": 11655.0, "female_65_to_66": 7126.0, "female_67_to_69": 8631.0, "female_70_to_74": 13268.0, "female_75_to_79": 11602.0, "female_80_to_84": 9242.0, "female_85_and_over": 9538.0, "white_including_hispanic": 583132.0, "black_including_hispanic": 71067.0, "amerindian_including_hispanic": 2755.0, "asian_including_hispanic": 11606.0, "commute_5_9_mins": 34092.0, "commute_35_39_mins": 10440.0, "commute_40_44_mins": 8331.0, "commute_60_89_mins": 8481.0, "commute_90_more_mins": 4970.0, "households_retirement_income": 51239.0, "armed_forces": NaN, "civilian_labor_force": NaN, "employed_pop": NaN, "unemployed_pop": NaN, "not_in_labor_force": NaN, "pop_16_over": NaN, "pop_in_labor_force": NaN, "asian_male_45_54": 677.0, "asian_male_55_64": 445.0, "black_male_45_54": 5362.0, "black_male_55_64": 3270.0, "hispanic_male_45_54": 2020.0, "hispanic_male_55_64": 1314.0, "white_male_45_54": 44383.0, "white_male_55_64": 37335.0, "bachelors_degree_2": 83951.0, "bachelors_degree_or_higher_25_64": 101464.0, "children": 166357.0, "children_in_single_female_hh": 33321.0, "commuters_by_bus": 1027.0, "commuters_by_car_truck_van": 310787.0, "commuters_by_carpool": 32447.0, "commuters_by_subway_or_elevated": 19.0, "commuters_drove_alone": 278340.0, "different_house_year_ago_different_city": 73096.0, "different_house_year_ago_same_city": 17603.0, "employed_agriculture_forestry_fishing_hunting_mining": 4079.0, "employed_arts_entertainment_recreation_accommodation_food": 25671.0, "employed_construction": 26047.0, "employed_education_health_social": 69635.0, "employed_finance_insurance_real_estate": 22186.0, "employed_information": 6845.0, "employed_manufacturing": 61082.0, "employed_other_services_not_public_admin": 16949.0, "employed_public_administration": 10755.0, "employed_retail_trade": 38542.0, "employed_science_management_admin_waste": 26914.0, "employed_transportation_warehousing_utilities": 16613.0, "employed_wholesale_trade": 10801.0, "female_female_households": 811.0, "four_more_cars": 22641.0, "gini_index": 0.445, "graduate_professional_degree": 38941.0, "group_quarters": 12330.0, "high_school_including_ged": 145072.0, "households_public_asst_or_food_stamps": 21374.0, "in_grades_1_to_4": 37319.0, "in_grades_5_to_8": 39439.0, "in_grades_9_to_12": 38311.0, "in_school": 168021.0, "in_undergrad_college": 30756.0, "less_than_high_school_graduate": 78603.0, "male_45_64_associates_degree": 7488.0, "male_45_64_bachelors_degree": 17141.0, "male_45_64_graduate_degree": 8942.0, "male_45_64_less_than_9_grade": 4276.0, "male_45_64_grade_9_12": 10149.0, "male_45_64_high_school": 28789.0, "male_45_64_some_college": 19072.0, "male_45_to_64": 95857.0, "male_male_households": 700.0, "management_business_sci_arts_employed": 110395.0, "no_car": 5168.0, "no_cars": 12086.0, "not_us_citizen_pop": 29411.0, "occupation_management_arts": 110395.0, "occupation_natural_resources_construction_maintenance": 1826.0, "occupation_production_transportation_material": 33651.0, "occupation_sales_office": 85230.0, "occupation_services": 48577.0, "one_car": 79401.0, "two_cars": 108886.0, "three_cars": 52677.0, "pop_25_64": 378546.0, "pop_determined_poverty_status": 692082.0, "population_1_year_and_over": 694047.0, "population_3_years_over": 677329.0, "poverty": 86536.0, "sales_office_employed": 32882.0, "some_college_and_associates_degree": 134846.0, "walked_to_work": 3993.0, "worked_at_home": 10545.0, "workers_16_and_over": 329301.0, "associates_degree": NaN, "bachelors_degree": NaN, "high_school_diploma": NaN, "less_one_year_college": NaN, "masters_degree": NaN, "one_year_more_college": NaN, "pop_25_years_over": NaN, "commute_35_44_mins": 18771.0, "commute_60_more_mins": 13451.0, "commute_less_10_mins": 43621.0, "commuters_16_over": 318756.0, "hispanic_any_race": 47449.0, "pop_5_years_over": 659423.0, "speak_only_english_at_home": 601935.0, "speak_spanish_at_home": 39523.0, "speak_spanish_at_home_low_english": 20269.0, "pop_15_and_over": 565197.0, "pop_never_married": 132899.0, "pop_now_married": 317857.0, "pop_separated": 16988.0, "pop_widowed": 37763.0, "pop_divorced": 56741.0, "do_date": "20072009"}, {"geo_id": "4825", "nonfamily_households": 116699.0, "family_households": 173370.0, "median_year_structure_built": 1984.0, "rent_burden_not_computed": 7356.0, "rent_over_50_percent": 31362.0, "rent_40_to_50_percent": 10011.0, "rent_35_to_40_percent": 7968.0, "rent_30_to_35_percent": 10300.0, "rent_25_to_30_percent": 13891.0, "rent_20_to_25_percent": 15644.0, "rent_15_to_20_percent": 13894.0, "rent_10_to_15_percent": 9273.0, "rent_under_10_percent": 4858.0, "total_pop": 790672.0, "male_pop": 405236.0, "female_pop": 385436.0, "median_age": 31.5, "white_pop": 403346.0, "black_pop": 60281.0, "asian_pop": 16436.0, "hispanic_pop": 298176.0, "amerindian_pop": 1771.0, "other_race_pop": 1427.0, "two_or_more_races_pop": 8974.0, "not_hispanic_pop": 492496.0, "commuters_by_public_transportation": 15350.0, "households": 290069.0, "median_income": 48554.0, "income_per_capita": 25229.0, "housing_units": 323358.0, "vacant_housing_units": 33289.0, "vacant_housing_units_for_rent": 8714.0, "vacant_housing_units_for_sale": 3358.0, "median_rent": 689.0, "percent_income_spent_on_rent": 30.5, "owner_occupied_housing_units": 165512.0, "million_dollar_housing_units": 1584.0, "mortgaged_housing_units": 112616.0, "families_with_young_children": 70919.0, "two_parent_families_with_young_children": 45041.0, "two_parents_in_labor_force_families_with_young_children": 23735.0, "two_parents_father_in_labor_force_families_with_young_children": 19469.0, "two_parents_mother_in_labor_force_families_with_young_children": 1434.0, "two_parents_not_in_labor_force_families_with_young_children": 403.0, "one_parent_families_with_young_children": 25878.0, "father_one_parent_families_with_young_children": 6100.0, "father_in_labor_force_one_parent_families_with_young_children": 5778.0, "commute_10_14_mins": 51410.0, "commute_15_19_mins": 59741.0, "commute_20_24_mins": 57693.0, "commute_25_29_mins": 21423.0, "commute_30_34_mins": 57720.0, "commute_45_59_mins": 28902.0, "aggregate_travel_time_to_work": 9872415.0, "income_less_10000": 23620.0, "income_10000_14999": 15809.0, "income_15000_19999": 14663.0, "income_20000_24999": 17383.0, "income_25000_29999": 16744.0, "income_30000_34999": 16501.0, "income_35000_39999": 15561.0, "income_40000_44999": 15265.0, "income_45000_49999": 13240.0, "income_50000_59999": 24892.0, "income_60000_74999": 28233.0, "income_75000_99999": 34027.0, "income_100000_124999": 22407.0, "income_125000_149999": 11141.0, "income_150000_199999": 10741.0, "income_200000_or_more": 9842.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 840.0, "owner_occupied_housing_units_lower_value_quartile": 105400.0, "owner_occupied_housing_units_median_value": 164200.0, "owner_occupied_housing_units_upper_value_quartile": 255000.0, "married_households": 124125.0, "occupied_housing_units": 290069.0, "housing_units_renter_occupied": 124557.0, "dwellings_1_units_detached": 189394.0, "dwellings_1_units_attached": 9120.0, "dwellings_2_units": 12972.0, "dwellings_3_to_4_units": 11681.0, "dwellings_5_to_9_units": 14737.0, "dwellings_10_to_19_units": 25042.0, "dwellings_20_to_49_units": 19419.0, "dwellings_50_or_more_units": 15441.0, "mobile_homes": 24965.0, "housing_built_2005_or_later": 23244.0, "housing_built_2000_to_2004": 46379.0, "housing_built_1939_or_earlier": 17697.0, "male_under_5": 32724.0, "male_5_to_9": 27897.0, "male_10_to_14": 26188.0, "male_15_to_17": 15305.0, "male_18_to_19": 11349.0, "male_20": 6571.0, "male_21": 7595.0, "male_22_to_24": 21342.0, "male_25_to_29": 43818.0, "male_30_to_34": 38035.0, "male_35_to_39": 32209.0, "male_40_to_44": 27457.0, "male_45_to_49": 26886.0, "male_50_to_54": 23890.0, "male_55_to_59": 20704.0, "male_60_61": 6648.0, "male_62_64": 8271.0, "male_65_to_66": 5080.0, "male_67_to_69": 4773.0, "male_70_to_74": 6881.0, "male_75_to_79": 5368.0, "male_80_to_84": 3713.0, "male_85_and_over": 2532.0, "female_under_5": 29976.0, "female_5_to_9": 27059.0, "female_10_to_14": 23168.0, "female_15_to_17": 13659.0, "female_18_to_19": 11006.0, "female_20": 7327.0, "female_21": 7965.0, "female_22_to_24": 19446.0, "female_25_to_29": 39293.0, "female_30_to_34": 32193.0, "female_35_to_39": 26571.0, "female_40_to_44": 24359.0, "female_45_to_49": 25344.0, "female_50_to_54": 24051.0, "female_55_to_59": 19684.0, "female_60_to_61": 6779.0, "female_62_to_64": 9103.0, "female_65_to_66": 4364.0, "female_67_to_69": 6171.0, "female_70_to_74": 8647.0, "female_75_to_79": 6776.0, "female_80_to_84": 5646.0, "female_85_and_over": 6849.0, "white_including_hispanic": 543504.0, "black_including_hispanic": 61390.0, "amerindian_including_hispanic": 4761.0, "asian_including_hispanic": 16762.0, "commute_5_9_mins": 35265.0, "commute_35_39_mins": 10606.0, "commute_40_44_mins": 14456.0, "commute_60_89_mins": 20027.0, "commute_90_more_mins": 9980.0, "households_retirement_income": 34692.0, "armed_forces": NaN, "civilian_labor_force": NaN, "employed_pop": NaN, "unemployed_pop": NaN, "not_in_labor_force": NaN, "pop_16_over": NaN, "pop_in_labor_force": NaN, "asian_male_45_54": 501.0, "asian_male_55_64": 419.0, "black_male_45_54": 4128.0, "black_male_55_64": 2647.0, "hispanic_male_45_54": 15437.0, "hispanic_male_55_64": 8594.0, "white_male_45_54": 30321.0, "white_male_55_64": 23738.0, "bachelors_degree_2": 106879.0, "bachelors_degree_or_higher_25_64": 145477.0, "children": 195976.0, "children_in_single_female_hh": 52573.0, "commuters_by_bus": 15305.0, "commuters_by_car_truck_van": 340107.0, "commuters_by_carpool": 51791.0, "commuters_by_subway_or_elevated": 0.0, "commuters_drove_alone": 288316.0, "different_house_year_ago_different_city": 84754.0, "different_house_year_ago_same_city": 80200.0, "employed_agriculture_forestry_fishing_hunting_mining": 5475.0, "employed_arts_entertainment_recreation_accommodation_food": 45722.0, "employed_construction": 45313.0, "employed_education_health_social": 82046.0, "employed_finance_insurance_real_estate": 25385.0, "employed_information": 9389.0, "employed_manufacturing": 31175.0, "employed_other_services_not_public_admin": 20886.0, "employed_public_administration": 26404.0, "employed_retail_trade": 44066.0, "employed_science_management_admin_waste": 47746.0, "employed_transportation_warehousing_utilities": 14883.0, "employed_wholesale_trade": 9245.0, "female_female_households": 1539.0, "four_more_cars": 11354.0, "gini_index": 0.457, "graduate_professional_degree": 53679.0, "group_quarters": 20174.0, "high_school_including_ged": 115354.0, "households_public_asst_or_food_stamps": 26500.0, "in_grades_1_to_4": 43236.0, "in_grades_5_to_8": 39387.0, "in_grades_9_to_12": 39155.0, "in_school": 217701.0, "in_undergrad_college": 59433.0, "less_than_high_school_graduate": 94273.0, "male_45_64_associates_degree": 5496.0, "male_45_64_bachelors_degree": 18699.0, "male_45_64_graduate_degree": 10328.0, "male_45_64_less_than_9_grade": 7786.0, "male_45_64_grade_9_12": 6436.0, "male_45_64_high_school": 20719.0, "male_45_64_some_college": 16935.0, "male_45_to_64": 86399.0, "male_male_households": 1195.0, "management_business_sci_arts_employed": 148916.0, "no_car": 13648.0, "no_cars": 19101.0, "not_us_citizen_pop": 89454.0, "occupation_management_arts": 148916.0, "occupation_natural_resources_construction_maintenance": 1718.0, "occupation_production_transportation_material": 50030.0, "occupation_sales_office": 97143.0, "occupation_services": 74349.0, "one_car": 108067.0, "two_cars": 115793.0, "three_cars": 35754.0, "pop_25_64": 435295.0, "pop_determined_poverty_status": 771384.0, "population_1_year_and_over": 777325.0, "population_3_years_over": 752349.0, "poverty": 136368.0, "sales_office_employed": 39699.0, "some_college_and_associates_degree": 131910.0, "walked_to_work": 7232.0, "worked_at_home": 20586.0, "workers_16_and_over": 399084.0, "associates_degree": NaN, "bachelors_degree": NaN, "high_school_diploma": NaN, "less_one_year_college": NaN, "masters_degree": NaN, "one_year_more_college": NaN, "pop_25_years_over": NaN, "commute_35_44_mins": 25062.0, "commute_60_more_mins": 30007.0, "commute_less_10_mins": 46540.0, "commuters_16_over": 378498.0, "hispanic_any_race": 298176.0, "pop_5_years_over": 727972.0, "speak_only_english_at_home": 497080.0, "speak_spanish_at_home": 204571.0, "speak_spanish_at_home_low_english": 89776.0, "pop_15_and_over": 623660.0, "pop_never_married": 239090.0, "pop_now_married": 279893.0, "pop_separated": 12621.0, "pop_widowed": 24849.0, "pop_divorced": 58908.0, "do_date": "20072009"}]}