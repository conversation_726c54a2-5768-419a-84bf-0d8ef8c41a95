{"table_name": "zip_codes_2018_5yr", "table_fullname": "bigquery-public-data.census_bureau_acs.zip_codes_2018_5yr", "column_names": ["geo_id", "do_date", "total_pop", "households", "male_pop", "female_pop", "median_age", "male_under_5", "male_5_to_9", "male_10_to_14", "male_15_to_17", "male_18_to_19", "male_20", "male_21", "male_22_to_24", "male_25_to_29", "male_30_to_34", "male_35_to_39", "male_40_to_44", "male_45_to_49", "male_50_to_54", "male_55_to_59", "male_65_to_66", "male_67_to_69", "male_70_to_74", "male_75_to_79", "male_80_to_84", "male_85_and_over", "female_under_5", "female_5_to_9", "female_10_to_14", "female_15_to_17", "female_18_to_19", "female_20", "female_21", "female_22_to_24", "female_25_to_29", "female_30_to_34", "female_35_to_39", "female_40_to_44", "female_45_to_49", "female_50_to_54", "female_55_to_59", "female_60_to_61", "female_62_to_64", "female_65_to_66", "female_67_to_69", "female_70_to_74", "female_75_to_79", "female_80_to_84", "female_85_and_over", "white_pop", "population_1_year_and_over", "population_3_years_over", "pop_5_years_over", "pop_15_and_over", "pop_16_over", "pop_25_years_over", "pop_25_64", "pop_never_married", "pop_now_married", "pop_separated", "pop_widowed", "pop_divorced", "not_us_citizen_pop", "black_pop", "asian_pop", "hispanic_pop", "amerindian_pop", "other_race_pop", "two_or_more_races_pop", "hispanic_any_race", "not_hispanic_pop", "asian_male_45_54", "asian_male_55_64", "black_male_45_54", "black_male_55_64", "hispanic_male_45_54", "hispanic_male_55_64", "white_male_45_54", "white_male_55_64", "median_income", "income_per_capita", "income_less_10000", "income_10000_14999", "income_15000_19999", "income_20000_24999", "income_25000_29999", "income_30000_34999", "income_35000_39999", "income_40000_44999", "income_45000_49999", "income_50000_59999", "income_60000_74999", "income_75000_99999", "income_100000_124999", "income_125000_149999", "income_150000_199999", "income_200000_or_more", "pop_determined_poverty_status", "poverty", "gini_index", "housing_units", "renter_occupied_housing_units_paying_cash_median_gross_rent", "owner_occupied_housing_units_lower_value_quartile", "owner_occupied_housing_units_median_value", "owner_occupied_housing_units_upper_value_quartile", "occupied_housing_units", "housing_units_renter_occupied", "vacant_housing_units", "vacant_housing_units_for_rent", "vacant_housing_units_for_sale", "dwellings_1_units_detached", "dwellings_1_units_attached", "dwellings_2_units", "dwellings_3_to_4_units", "dwellings_5_to_9_units", "dwellings_10_to_19_units", "dwellings_20_to_49_units", "dwellings_50_or_more_units", "mobile_homes", "housing_built_2005_or_later", "housing_built_2000_to_2004", "housing_built_1939_or_earlier", "median_year_structure_built", "married_households", "nonfamily_households", "family_households", "households_public_asst_or_food_stamps", "male_male_households", "female_female_households", "children", "children_in_single_female_hh", "median_rent", "percent_income_spent_on_rent", "rent_burden_not_computed", "rent_over_50_percent", "rent_40_to_50_percent", "rent_35_to_40_percent", "rent_30_to_35_percent", "rent_25_to_30_percent", "rent_20_to_25_percent", "rent_15_to_20_percent", "rent_10_to_15_percent", "rent_under_10_percent", "owner_occupied_housing_units", "million_dollar_housing_units", "mortgaged_housing_units", "different_house_year_ago_different_city", "different_house_year_ago_same_city", "families_with_young_children", "two_parent_families_with_young_children", "two_parents_in_labor_force_families_with_young_children", "two_parents_father_in_labor_force_families_with_young_children", "two_parents_mother_in_labor_force_families_with_young_children", "two_parents_not_in_labor_force_families_with_young_children", "one_parent_families_with_young_children", "father_one_parent_families_with_young_children", "father_in_labor_force_one_parent_families_with_young_children", "commute_less_10_mins", "commute_10_14_mins", "commute_15_19_mins", "commute_20_24_mins", "commute_25_29_mins", "commute_30_34_mins", "commute_35_44_mins", "commute_60_more_mins", "commute_45_59_mins", "commuters_16_over", "walked_to_work", "worked_at_home", "no_car", "no_cars", "one_car", "two_cars", "three_cars", "four_more_cars", "aggregate_travel_time_to_work", "commuters_by_public_transportation", "commuters_by_bus", "commuters_by_car_truck_van", "commuters_by_carpool", "commuters_by_subway_or_elevated", "commuters_drove_alone", "group_quarters", "associates_degree", "bachelors_degree", "high_school_diploma", "less_one_year_college", "masters_degree", "one_year_more_college", "less_than_high_school_graduate", "high_school_including_ged", "bachelors_degree_2", "bachelors_degree_or_higher_25_64", "graduate_professional_degree", "some_college_and_associates_degree", "male_45_64_associates_degree", "male_45_64_bachelors_degree", "male_45_64_graduate_degree", "male_45_64_less_than_9_grade", "male_45_64_grade_9_12", "male_45_64_high_school", "male_45_64_some_college", "male_45_to_64", "employed_pop", "unemployed_pop", "pop_in_labor_force", "not_in_labor_force", "workers_16_and_over", "armed_forces", "civilian_labor_force", "employed_agriculture_forestry_fishing_hunting_mining", "employed_arts_entertainment_recreation_accommodation_food", "employed_construction", "employed_education_health_social", "employed_finance_insurance_real_estate", "employed_information", "employed_manufacturing", "employed_other_services_not_public_admin", "employed_public_administration", "employed_retail_trade", "employed_science_management_admin_waste", "employed_transportation_warehousing_utilities", "employed_wholesale_trade", "occupation_management_arts", "occupation_natural_resources_construction_maintenance", "occupation_production_transportation_material", "occupation_sales_office", "occupation_services", "management_business_sci_arts_employed", "sales_office_employed", "in_grades_1_to_4", "in_grades_5_to_8", "in_grades_9_to_12", "in_school", "in_undergrad_college", "speak_only_english_at_home", "speak_spanish_at_home", "speak_spanish_at_home_low_english"], "column_types": ["STRING", "DATE", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64"], "nested_column_names": ["geo_id", "do_date", "total_pop", "households", "male_pop", "female_pop", "median_age", "male_under_5", "male_5_to_9", "male_10_to_14", "male_15_to_17", "male_18_to_19", "male_20", "male_21", "male_22_to_24", "male_25_to_29", "male_30_to_34", "male_35_to_39", "male_40_to_44", "male_45_to_49", "male_50_to_54", "male_55_to_59", "male_65_to_66", "male_67_to_69", "male_70_to_74", "male_75_to_79", "male_80_to_84", "male_85_and_over", "female_under_5", "female_5_to_9", "female_10_to_14", "female_15_to_17", "female_18_to_19", "female_20", "female_21", "female_22_to_24", "female_25_to_29", "female_30_to_34", "female_35_to_39", "female_40_to_44", "female_45_to_49", "female_50_to_54", "female_55_to_59", "female_60_to_61", "female_62_to_64", "female_65_to_66", "female_67_to_69", "female_70_to_74", "female_75_to_79", "female_80_to_84", "female_85_and_over", "white_pop", "population_1_year_and_over", "population_3_years_over", "pop_5_years_over", "pop_15_and_over", "pop_16_over", "pop_25_years_over", "pop_25_64", "pop_never_married", "pop_now_married", "pop_separated", "pop_widowed", "pop_divorced", "not_us_citizen_pop", "black_pop", "asian_pop", "hispanic_pop", "amerindian_pop", "other_race_pop", "two_or_more_races_pop", "hispanic_any_race", "not_hispanic_pop", "asian_male_45_54", "asian_male_55_64", "black_male_45_54", "black_male_55_64", "hispanic_male_45_54", "hispanic_male_55_64", "white_male_45_54", "white_male_55_64", "median_income", "income_per_capita", "income_less_10000", "income_10000_14999", "income_15000_19999", "income_20000_24999", "income_25000_29999", "income_30000_34999", "income_35000_39999", "income_40000_44999", "income_45000_49999", "income_50000_59999", "income_60000_74999", "income_75000_99999", "income_100000_124999", "income_125000_149999", "income_150000_199999", "income_200000_or_more", "pop_determined_poverty_status", "poverty", "gini_index", "housing_units", "renter_occupied_housing_units_paying_cash_median_gross_rent", "owner_occupied_housing_units_lower_value_quartile", "owner_occupied_housing_units_median_value", "owner_occupied_housing_units_upper_value_quartile", "occupied_housing_units", "housing_units_renter_occupied", "vacant_housing_units", "vacant_housing_units_for_rent", "vacant_housing_units_for_sale", "dwellings_1_units_detached", "dwellings_1_units_attached", "dwellings_2_units", "dwellings_3_to_4_units", "dwellings_5_to_9_units", "dwellings_10_to_19_units", "dwellings_20_to_49_units", "dwellings_50_or_more_units", "mobile_homes", "housing_built_2005_or_later", "housing_built_2000_to_2004", "housing_built_1939_or_earlier", "median_year_structure_built", "married_households", "nonfamily_households", "family_households", "households_public_asst_or_food_stamps", "male_male_households", "female_female_households", "children", "children_in_single_female_hh", "median_rent", "percent_income_spent_on_rent", "rent_burden_not_computed", "rent_over_50_percent", "rent_40_to_50_percent", "rent_35_to_40_percent", "rent_30_to_35_percent", "rent_25_to_30_percent", "rent_20_to_25_percent", "rent_15_to_20_percent", "rent_10_to_15_percent", "rent_under_10_percent", "owner_occupied_housing_units", "million_dollar_housing_units", "mortgaged_housing_units", "different_house_year_ago_different_city", "different_house_year_ago_same_city", "families_with_young_children", "two_parent_families_with_young_children", "two_parents_in_labor_force_families_with_young_children", "two_parents_father_in_labor_force_families_with_young_children", "two_parents_mother_in_labor_force_families_with_young_children", "two_parents_not_in_labor_force_families_with_young_children", "one_parent_families_with_young_children", "father_one_parent_families_with_young_children", "father_in_labor_force_one_parent_families_with_young_children", "commute_less_10_mins", "commute_10_14_mins", "commute_15_19_mins", "commute_20_24_mins", "commute_25_29_mins", "commute_30_34_mins", "commute_35_44_mins", "commute_60_more_mins", "commute_45_59_mins", "commuters_16_over", "walked_to_work", "worked_at_home", "no_car", "no_cars", "one_car", "two_cars", "three_cars", "four_more_cars", "aggregate_travel_time_to_work", "commuters_by_public_transportation", "commuters_by_bus", "commuters_by_car_truck_van", "commuters_by_carpool", "commuters_by_subway_or_elevated", "commuters_drove_alone", "group_quarters", "associates_degree", "bachelors_degree", "high_school_diploma", "less_one_year_college", "masters_degree", "one_year_more_college", "less_than_high_school_graduate", "high_school_including_ged", "bachelors_degree_2", "bachelors_degree_or_higher_25_64", "graduate_professional_degree", "some_college_and_associates_degree", "male_45_64_associates_degree", "male_45_64_bachelors_degree", "male_45_64_graduate_degree", "male_45_64_less_than_9_grade", "male_45_64_grade_9_12", "male_45_64_high_school", "male_45_64_some_college", "male_45_to_64", "employed_pop", "unemployed_pop", "pop_in_labor_force", "not_in_labor_force", "workers_16_and_over", "armed_forces", "civilian_labor_force", "employed_agriculture_forestry_fishing_hunting_mining", "employed_arts_entertainment_recreation_accommodation_food", "employed_construction", "employed_education_health_social", "employed_finance_insurance_real_estate", "employed_information", "employed_manufacturing", "employed_other_services_not_public_admin", "employed_public_administration", "employed_retail_trade", "employed_science_management_admin_waste", "employed_transportation_warehousing_utilities", "employed_wholesale_trade", "occupation_management_arts", "occupation_natural_resources_construction_maintenance", "occupation_production_transportation_material", "occupation_sales_office", "occupation_services", "management_business_sci_arts_employed", "sales_office_employed", "in_grades_1_to_4", "in_grades_5_to_8", "in_grades_9_to_12", "in_school", "in_undergrad_college", "speak_only_english_at_home", "speak_spanish_at_home", "speak_spanish_at_home_low_english"], "nested_column_types": ["STRING", "DATE", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64"], "description": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], "sample_rows": [{"geo_id": "15537", "do_date": "2014-01-01", "total_pop": 7882.0, "households": 3228.0, "male_pop": 3969.0, "female_pop": 3913.0, "median_age": 47.3, "male_under_5": 159.0, "male_5_to_9": 305.0, "male_10_to_14": 146.0, "male_15_to_17": 191.0, "male_18_to_19": 82.0, "male_20": 52.0, "male_21": 51.0, "male_22_to_24": 166.0, "male_25_to_29": 203.0, "male_30_to_34": 179.0, "male_35_to_39": 171.0, "male_40_to_44": 291.0, "male_45_to_49": 277.0, "male_50_to_54": 277.0, "male_55_to_59": 261.0, "male_65_to_66": 69.0, "male_67_to_69": 189.0, "male_70_to_74": 247.0, "male_75_to_79": 235.0, "male_80_to_84": 75.0, "male_85_and_over": 104.0, "female_under_5": 136.0, "female_5_to_9": 116.0, "female_10_to_14": 172.0, "female_15_to_17": 226.0, "female_18_to_19": 48.0, "female_20": 49.0, "female_21": 49.0, "female_22_to_24": 121.0, "female_25_to_29": 206.0, "female_30_to_34": 144.0, "female_35_to_39": 187.0, "female_40_to_44": 320.0, "female_45_to_49": 185.0, "female_50_to_54": 235.0, "female_55_to_59": 257.0, "female_60_to_61": 144.0, "female_62_to_64": 210.0, "female_65_to_66": 97.0, "female_67_to_69": 147.0, "female_70_to_74": 272.0, "female_75_to_79": 194.0, "female_80_to_84": 154.0, "female_85_and_over": 244.0, "white_pop": 7704.0, "population_1_year_and_over": 7847.0, "population_3_years_over": 7722.0, "pop_5_years_over": NaN, "pop_15_and_over": NaN, "pop_16_over": 6695.0, "pop_25_years_over": 5813.0, "pop_25_64": 3786.0, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "not_us_citizen_pop": 45.0, "black_pop": 31.0, "asian_pop": 41.0, "hispanic_pop": 89.0, "amerindian_pop": 5.0, "other_race_pop": 0.0, "two_or_more_races_pop": 12.0, "hispanic_any_race": 89.0, "not_hispanic_pop": 7793.0, "asian_male_45_54": 0.0, "asian_male_55_64": 0.0, "black_male_45_54": 0.0, "black_male_55_64": 0.0, "hispanic_male_45_54": 17.0, "hispanic_male_55_64": 0.0, "white_male_45_54": 537.0, "white_male_55_64": 500.0, "median_income": 46265.0, "income_per_capita": 25528.0, "income_less_10000": 206.0, "income_10000_14999": 213.0, "income_15000_19999": 181.0, "income_20000_24999": 205.0, "income_25000_29999": 192.0, "income_30000_34999": 198.0, "income_35000_39999": 224.0, "income_40000_44999": 152.0, "income_45000_49999": 177.0, "income_50000_59999": 220.0, "income_60000_74999": 329.0, "income_75000_99999": 453.0, "income_100000_124999": 226.0, "income_125000_149999": 98.0, "income_150000_199999": 107.0, "income_200000_or_more": 47.0, "pop_determined_poverty_status": 7727.0, "poverty": 976.0, "gini_index": 0.422, "housing_units": 3841.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 658.0, "owner_occupied_housing_units_lower_value_quartile": 74100.0, "owner_occupied_housing_units_median_value": 120900.0, "owner_occupied_housing_units_upper_value_quartile": 188200.0, "occupied_housing_units": 3228.0, "housing_units_renter_occupied": 847.0, "vacant_housing_units": 613.0, "vacant_housing_units_for_rent": 93.0, "vacant_housing_units_for_sale": 106.0, "dwellings_1_units_detached": 2797.0, "dwellings_1_units_attached": 136.0, "dwellings_2_units": 102.0, "dwellings_3_to_4_units": 110.0, "dwellings_5_to_9_units": 33.0, "dwellings_10_to_19_units": 61.0, "dwellings_20_to_49_units": 86.0, "dwellings_50_or_more_units": 16.0, "mobile_homes": 500.0, "housing_built_2005_or_later": 31.0, "housing_built_2000_to_2004": 86.0, "housing_built_1939_or_earlier": 226.0, "median_year_structure_built": 1965.0, "married_households": 1677.0, "nonfamily_households": 1183.0, "family_households": 2045.0, "households_public_asst_or_food_stamps": 492.0, "male_male_households": 3.0, "female_female_households": 0.0, "children": 1451.0, "children_in_single_female_hh": 289.0, "median_rent": 493.0, "percent_income_spent_on_rent": 30.2, "rent_burden_not_computed": 144.0, "rent_over_50_percent": 184.0, "rent_40_to_50_percent": 84.0, "rent_35_to_40_percent": 41.0, "rent_30_to_35_percent": 44.0, "rent_25_to_30_percent": 54.0, "rent_20_to_25_percent": 60.0, "rent_15_to_20_percent": 86.0, "rent_10_to_15_percent": 136.0, "rent_under_10_percent": 14.0, "owner_occupied_housing_units": 2381.0, "million_dollar_housing_units": 4.0, "mortgaged_housing_units": 1163.0, "different_house_year_ago_different_city": 419.0, "different_house_year_ago_same_city": 57.0, "families_with_young_children": 348.0, "two_parent_families_with_young_children": 187.0, "two_parents_in_labor_force_families_with_young_children": 81.0, "two_parents_father_in_labor_force_families_with_young_children": 106.0, "two_parents_mother_in_labor_force_families_with_young_children": 0.0, "two_parents_not_in_labor_force_families_with_young_children": 0.0, "one_parent_families_with_young_children": 161.0, "father_one_parent_families_with_young_children": 102.0, "father_in_labor_force_one_parent_families_with_young_children": 36.0, "commute_less_10_mins": 379.0, "commute_10_14_mins": 633.0, "commute_15_19_mins": 730.0, "commute_20_24_mins": 362.0, "commute_25_29_mins": 107.0, "commute_30_34_mins": 315.0, "commute_35_44_mins": 115.0, "commute_60_more_mins": 385.0, "commute_45_59_mins": 278.0, "commuters_16_over": 3304.0, "walked_to_work": 95.0, "worked_at_home": 140.0, "no_car": 29.0, "no_cars": 189.0, "one_car": 919.0, "two_cars": 1177.0, "three_cars": 565.0, "four_more_cars": 378.0, "aggregate_travel_time_to_work": 86820.0, "commuters_by_public_transportation": 29.0, "commuters_by_bus": 29.0, "commuters_by_car_truck_van": 3167.0, "commuters_by_carpool": 438.0, "commuters_by_subway_or_elevated": 0.0, "commuters_drove_alone": 2729.0, "group_quarters": 155.0, "associates_degree": 343.0, "bachelors_degree": 474.0, "high_school_diploma": 2775.0, "less_one_year_college": 408.0, "masters_degree": 229.0, "one_year_more_college": 352.0, "less_than_high_school_graduate": 870.0, "high_school_including_ged": 3010.0, "bachelors_degree_2": 474.0, "bachelors_degree_or_higher_25_64": 604.0, "graduate_professional_degree": 356.0, "some_college_and_associates_degree": 1103.0, "male_45_64_associates_degree": 26.0, "male_45_64_bachelors_degree": 45.0, "male_45_64_graduate_degree": 83.0, "male_45_64_less_than_9_grade": 45.0, "male_45_64_grade_9_12": 76.0, "male_45_64_high_school": 650.0, "male_45_64_some_college": 129.0, "male_45_to_64": 1054.0, "employed_pop": 3529.0, "unemployed_pop": 183.0, "pop_in_labor_force": 3712.0, "not_in_labor_force": 2983.0, "workers_16_and_over": 3444.0, "armed_forces": 0.0, "civilian_labor_force": 3712.0, "employed_agriculture_forestry_fishing_hunting_mining": 104.0, "employed_arts_entertainment_recreation_accommodation_food": 372.0, "employed_construction": 538.0, "employed_education_health_social": 624.0, "employed_finance_insurance_real_estate": 76.0, "employed_information": 10.0, "employed_manufacturing": 455.0, "employed_other_services_not_public_admin": 210.0, "employed_public_administration": 136.0, "employed_retail_trade": 574.0, "employed_science_management_admin_waste": 158.0, "employed_transportation_warehousing_utilities": 190.0, "employed_wholesale_trade": 82.0, "occupation_management_arts": 789.0, "occupation_natural_resources_construction_maintenance": 612.0, "occupation_production_transportation_material": 600.0, "occupation_sales_office": 807.0, "occupation_services": 721.0, "management_business_sci_arts_employed": 789.0, "sales_office_employed": 807.0, "in_grades_1_to_4": 310.0, "in_grades_5_to_8": 260.0, "in_grades_9_to_12": 471.0, "in_school": 1404.0, "in_undergrad_college": 120.0, "speak_only_english_at_home": NaN, "speak_spanish_at_home": NaN, "speak_spanish_at_home_low_english": NaN}, {"geo_id": "48215", "do_date": "2014-01-01", "total_pop": 11979.0, "households": 4895.0, "male_pop": 5827.0, "female_pop": 6152.0, "median_age": 39.3, "male_under_5": 711.0, "male_5_to_9": 531.0, "male_10_to_14": 403.0, "male_15_to_17": 241.0, "male_18_to_19": 148.0, "male_20": 39.0, "male_21": 77.0, "male_22_to_24": 150.0, "male_25_to_29": 315.0, "male_30_to_34": 218.0, "male_35_to_39": 213.0, "male_40_to_44": 425.0, "male_45_to_49": 388.0, "male_50_to_54": 394.0, "male_55_to_59": 501.0, "male_65_to_66": 116.0, "male_67_to_69": 129.0, "male_70_to_74": 193.0, "male_75_to_79": 60.0, "male_80_to_84": 87.0, "male_85_and_over": 82.0, "female_under_5": 338.0, "female_5_to_9": 352.0, "female_10_to_14": 433.0, "female_15_to_17": 180.0, "female_18_to_19": 128.0, "female_20": 46.0, "female_21": 49.0, "female_22_to_24": 316.0, "female_25_to_29": 474.0, "female_30_to_34": 352.0, "female_35_to_39": 354.0, "female_40_to_44": 418.0, "female_45_to_49": 215.0, "female_50_to_54": 405.0, "female_55_to_59": 492.0, "female_60_to_61": 197.0, "female_62_to_64": 295.0, "female_65_to_66": 145.0, "female_67_to_69": 173.0, "female_70_to_74": 292.0, "female_75_to_79": 148.0, "female_80_to_84": 134.0, "female_85_and_over": 216.0, "white_pop": 858.0, "population_1_year_and_over": 11779.0, "population_3_years_over": 11330.0, "pop_5_years_over": NaN, "pop_15_and_over": NaN, "pop_16_over": 9101.0, "pop_25_years_over": 7837.0, "pop_25_64": 6062.0, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "not_us_citizen_pop": 37.0, "black_pop": 10740.0, "asian_pop": 26.0, "hispanic_pop": 163.0, "amerindian_pop": 0.0, "other_race_pop": 0.0, "two_or_more_races_pop": 192.0, "hispanic_any_race": 163.0, "not_hispanic_pop": 11816.0, "asian_male_45_54": 0.0, "asian_male_55_64": 0.0, "black_male_45_54": 649.0, "black_male_55_64": 726.0, "hispanic_male_45_54": 53.0, "hispanic_male_55_64": 0.0, "white_male_45_54": 80.0, "white_male_55_64": 166.0, "median_income": 22721.0, "income_per_capita": 18672.0, "income_less_10000": 1312.0, "income_10000_14999": 493.0, "income_15000_19999": 467.0, "income_20000_24999": 346.0, "income_25000_29999": 188.0, "income_30000_34999": 313.0, "income_35000_39999": 209.0, "income_40000_44999": 168.0, "income_45000_49999": 124.0, "income_50000_59999": 298.0, "income_60000_74999": 267.0, "income_75000_99999": 268.0, "income_100000_124999": 155.0, "income_125000_149999": 105.0, "income_150000_199999": 62.0, "income_200000_or_more": 120.0, "pop_determined_poverty_status": 11917.0, "poverty": 5009.0, "gini_index": 0.5847, "housing_units": 7379.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 743.0, "owner_occupied_housing_units_lower_value_quartile": 30000.0, "owner_occupied_housing_units_median_value": 56400.0, "owner_occupied_housing_units_upper_value_quartile": 118500.0, "occupied_housing_units": 4895.0, "housing_units_renter_occupied": 2769.0, "vacant_housing_units": 2484.0, "vacant_housing_units_for_rent": 131.0, "vacant_housing_units_for_sale": 0.0, "dwellings_1_units_detached": 4037.0, "dwellings_1_units_attached": 1610.0, "dwellings_2_units": 179.0, "dwellings_3_to_4_units": 144.0, "dwellings_5_to_9_units": 88.0, "dwellings_10_to_19_units": 261.0, "dwellings_20_to_49_units": 245.0, "dwellings_50_or_more_units": 815.0, "mobile_homes": 0.0, "housing_built_2005_or_later": 26.0, "housing_built_2000_to_2004": 154.0, "housing_built_1939_or_earlier": 1013.0, "median_year_structure_built": 1948.0, "married_households": 837.0, "nonfamily_households": 2471.0, "family_households": 2424.0, "households_public_asst_or_food_stamps": 2242.0, "male_male_households": 1.0, "female_female_households": 3.0, "children": 3189.0, "children_in_single_female_hh": 2143.0, "median_rent": 567.0, "percent_income_spent_on_rent": 39.7, "rent_burden_not_computed": 378.0, "rent_over_50_percent": 955.0, "rent_40_to_50_percent": 230.0, "rent_35_to_40_percent": 176.0, "rent_30_to_35_percent": 215.0, "rent_25_to_30_percent": 225.0, "rent_20_to_25_percent": 176.0, "rent_15_to_20_percent": 163.0, "rent_10_to_15_percent": 172.0, "rent_under_10_percent": 79.0, "owner_occupied_housing_units": 2126.0, "million_dollar_housing_units": 18.0, "mortgaged_housing_units": 748.0, "different_house_year_ago_different_city": 232.0, "different_house_year_ago_same_city": 1020.0, "families_with_young_children": 1097.0, "two_parent_families_with_young_children": 54.0, "two_parents_in_labor_force_families_with_young_children": 0.0, "two_parents_father_in_labor_force_families_with_young_children": 27.0, "two_parents_mother_in_labor_force_families_with_young_children": 11.0, "two_parents_not_in_labor_force_families_with_young_children": 16.0, "one_parent_families_with_young_children": 1043.0, "father_one_parent_families_with_young_children": 110.0, "father_in_labor_force_one_parent_families_with_young_children": 62.0, "commute_less_10_mins": 301.0, "commute_10_14_mins": 241.0, "commute_15_19_mins": 579.0, "commute_20_24_mins": 830.0, "commute_25_29_mins": 328.0, "commute_30_34_mins": 460.0, "commute_35_44_mins": 242.0, "commute_60_more_mins": 267.0, "commute_45_59_mins": 282.0, "commuters_16_over": 3530.0, "walked_to_work": 111.0, "worked_at_home": 189.0, "no_car": 548.0, "no_cars": 1582.0, "one_car": 2070.0, "two_cars": 961.0, "three_cars": 221.0, "four_more_cars": 61.0, "aggregate_travel_time_to_work": 98185.0, "commuters_by_public_transportation": 257.0, "commuters_by_bus": 257.0, "commuters_by_car_truck_van": 3075.0, "commuters_by_carpool": 571.0, "commuters_by_subway_or_elevated": 0.0, "commuters_drove_alone": 2504.0, "group_quarters": 119.0, "associates_degree": 428.0, "bachelors_degree": 661.0, "high_school_diploma": 2228.0, "less_one_year_college": 331.0, "masters_degree": 421.0, "one_year_more_college": 1648.0, "less_than_high_school_graduate": 1587.0, "high_school_including_ged": 2701.0, "bachelors_degree_2": 661.0, "bachelors_degree_or_higher_25_64": 904.0, "graduate_professional_degree": 481.0, "some_college_and_associates_degree": 2407.0, "male_45_64_associates_degree": 60.0, "male_45_64_bachelors_degree": 94.0, "male_45_64_graduate_degree": 127.0, "male_45_64_less_than_9_grade": 87.0, "male_45_64_grade_9_12": 363.0, "male_45_64_high_school": 599.0, "male_45_64_some_college": 359.0, "male_45_to_64": 1689.0, "employed_pop": 3746.0, "unemployed_pop": 606.0, "pop_in_labor_force": 4352.0, "not_in_labor_force": 4749.0, "workers_16_and_over": 3719.0, "armed_forces": 0.0, "civilian_labor_force": 4352.0, "employed_agriculture_forestry_fishing_hunting_mining": 0.0, "employed_arts_entertainment_recreation_accommodation_food": 594.0, "employed_construction": 109.0, "employed_education_health_social": 1062.0, "employed_finance_insurance_real_estate": 212.0, "employed_information": 93.0, "employed_manufacturing": 409.0, "employed_other_services_not_public_admin": 146.0, "employed_public_administration": 164.0, "employed_retail_trade": 273.0, "employed_science_management_admin_waste": 336.0, "employed_transportation_warehousing_utilities": 336.0, "employed_wholesale_trade": 12.0, "occupation_management_arts": 852.0, "occupation_natural_resources_construction_maintenance": 155.0, "occupation_production_transportation_material": 789.0, "occupation_sales_office": 725.0, "occupation_services": 1225.0, "management_business_sci_arts_employed": 852.0, "sales_office_employed": 725.0, "in_grades_1_to_4": 759.0, "in_grades_5_to_8": 594.0, "in_grades_9_to_12": 643.0, "in_school": 2652.0, "in_undergrad_college": 384.0, "speak_only_english_at_home": NaN, "speak_spanish_at_home": NaN, "speak_spanish_at_home_low_english": NaN}, {"geo_id": "29571", "do_date": "2014-01-01", "total_pop": 14140.0, "households": 5209.0, "male_pop": 6612.0, "female_pop": 7528.0, "median_age": 40.7, "male_under_5": 622.0, "male_5_to_9": 647.0, "male_10_to_14": 400.0, "male_15_to_17": 189.0, "male_18_to_19": 142.0, "male_20": 128.0, "male_21": 113.0, "male_22_to_24": 134.0, "male_25_to_29": 345.0, "male_30_to_34": 328.0, "male_35_to_39": 525.0, "male_40_to_44": 304.0, "male_45_to_49": 416.0, "male_50_to_54": 329.0, "male_55_to_59": 341.0, "male_65_to_66": 178.0, "male_67_to_69": 273.0, "male_70_to_74": 323.0, "male_75_to_79": 258.0, "male_80_to_84": 61.0, "male_85_and_over": 15.0, "female_under_5": 441.0, "female_5_to_9": 246.0, "female_10_to_14": 512.0, "female_15_to_17": 225.0, "female_18_to_19": 130.0, "female_20": 151.0, "female_21": 23.0, "female_22_to_24": 304.0, "female_25_to_29": 409.0, "female_30_to_34": 470.0, "female_35_to_39": 415.0, "female_40_to_44": 522.0, "female_45_to_49": 527.0, "female_50_to_54": 419.0, "female_55_to_59": 510.0, "female_60_to_61": 200.0, "female_62_to_64": 425.0, "female_65_to_66": 304.0, "female_67_to_69": 370.0, "female_70_to_74": 364.0, "female_75_to_79": 201.0, "female_80_to_84": 200.0, "female_85_and_over": 160.0, "white_pop": 4880.0, "population_1_year_and_over": 13886.0, "population_3_years_over": 13416.0, "pop_5_years_over": NaN, "pop_15_and_over": NaN, "pop_16_over": 11142.0, "pop_25_years_over": 9733.0, "pop_25_64": 7026.0, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "not_us_citizen_pop": 37.0, "black_pop": 8799.0, "asian_pop": 173.0, "hispanic_pop": 56.0, "amerindian_pop": 151.0, "other_race_pop": 0.0, "two_or_more_races_pop": 81.0, "hispanic_any_race": 56.0, "not_hispanic_pop": 14084.0, "asian_male_45_54": 0.0, "asian_male_55_64": 30.0, "black_male_45_54": 441.0, "black_male_55_64": 516.0, "hispanic_male_45_54": 0.0, "hispanic_male_55_64": 0.0, "white_male_45_54": 282.0, "white_male_55_64": 336.0, "median_income": 32764.0, "income_per_capita": 20157.0, "income_less_10000": 947.0, "income_10000_14999": 357.0, "income_15000_19999": 306.0, "income_20000_24999": 286.0, "income_25000_29999": 440.0, "income_30000_34999": 349.0, "income_35000_39999": 332.0, "income_40000_44999": 308.0, "income_45000_49999": 149.0, "income_50000_59999": 332.0, "income_60000_74999": 337.0, "income_75000_99999": 470.0, "income_100000_124999": 291.0, "income_125000_149999": 99.0, "income_150000_199999": 154.0, "income_200000_or_more": 52.0, "pop_determined_poverty_status": 13937.0, "poverty": 3698.0, "gini_index": 0.5231, "housing_units": 6637.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 580.0, "owner_occupied_housing_units_lower_value_quartile": 51800.0, "owner_occupied_housing_units_median_value": 86700.0, "owner_occupied_housing_units_upper_value_quartile": 152100.0, "occupied_housing_units": 5209.0, "housing_units_renter_occupied": 1693.0, "vacant_housing_units": 1428.0, "vacant_housing_units_for_rent": 180.0, "vacant_housing_units_for_sale": 81.0, "dwellings_1_units_detached": 3893.0, "dwellings_1_units_attached": 69.0, "dwellings_2_units": 338.0, "dwellings_3_to_4_units": 250.0, "dwellings_5_to_9_units": 184.0, "dwellings_10_to_19_units": 0.0, "dwellings_20_to_49_units": 0.0, "dwellings_50_or_more_units": 16.0, "mobile_homes": 1880.0, "housing_built_2005_or_later": 37.0, "housing_built_2000_to_2004": 21.0, "housing_built_1939_or_earlier": 289.0, "median_year_structure_built": 1982.0, "married_households": 2009.0, "nonfamily_households": 1718.0, "family_households": 3491.0, "households_public_asst_or_food_stamps": 1469.0, "male_male_households": 0.0, "female_female_households": 0.0, "children": 3282.0, "children_in_single_female_hh": 2262.0, "median_rent": 388.0, "percent_income_spent_on_rent": 35.9, "rent_burden_not_computed": 415.0, "rent_over_50_percent": 435.0, "rent_40_to_50_percent": 73.0, "rent_35_to_40_percent": 158.0, "rent_30_to_35_percent": 67.0, "rent_25_to_30_percent": 93.0, "rent_20_to_25_percent": 198.0, "rent_15_to_20_percent": 45.0, "rent_10_to_15_percent": 100.0, "rent_under_10_percent": 109.0, "owner_occupied_housing_units": 3516.0, "million_dollar_housing_units": 0.0, "mortgaged_housing_units": 1735.0, "different_house_year_ago_different_city": 1182.0, "different_house_year_ago_same_city": 100.0, "families_with_young_children": 1064.0, "two_parent_families_with_young_children": 331.0, "two_parents_in_labor_force_families_with_young_children": 192.0, "two_parents_father_in_labor_force_families_with_young_children": 108.0, "two_parents_mother_in_labor_force_families_with_young_children": 0.0, "two_parents_not_in_labor_force_families_with_young_children": 31.0, "one_parent_families_with_young_children": 733.0, "father_one_parent_families_with_young_children": 67.0, "father_in_labor_force_one_parent_families_with_young_children": 35.0, "commute_less_10_mins": 566.0, "commute_10_14_mins": 639.0, "commute_15_19_mins": 764.0, "commute_20_24_mins": 464.0, "commute_25_29_mins": 158.0, "commute_30_34_mins": 607.0, "commute_35_44_mins": 373.0, "commute_60_more_mins": 500.0, "commute_45_59_mins": 599.0, "commuters_16_over": 4670.0, "walked_to_work": 0.0, "worked_at_home": 50.0, "no_car": 257.0, "no_cars": 707.0, "one_car": 1735.0, "two_cars": 1733.0, "three_cars": 622.0, "four_more_cars": 412.0, "aggregate_travel_time_to_work": 133175.0, "commuters_by_public_transportation": 54.0, "commuters_by_bus": 54.0, "commuters_by_car_truck_van": 4578.0, "commuters_by_carpool": 672.0, "commuters_by_subway_or_elevated": 0.0, "commuters_drove_alone": 3906.0, "group_quarters": 38.0, "associates_degree": 747.0, "bachelors_degree": 888.0, "high_school_diploma": 3302.0, "less_one_year_college": 582.0, "masters_degree": 450.0, "one_year_more_college": 1608.0, "less_than_high_school_graduate": 1656.0, "high_school_including_ged": 3646.0, "bachelors_degree_2": 888.0, "bachelors_degree_or_higher_25_64": 1064.0, "graduate_professional_degree": 606.0, "some_college_and_associates_degree": 2937.0, "male_45_64_associates_degree": 69.0, "male_45_64_bachelors_degree": 90.0, "male_45_64_graduate_degree": 106.0, "male_45_64_less_than_9_grade": 25.0, "male_45_64_grade_9_12": 365.0, "male_45_64_high_school": 538.0, "male_45_64_some_college": 434.0, "male_45_to_64": 1627.0, "employed_pop": 4807.0, "unemployed_pop": 825.0, "pop_in_labor_force": 5632.0, "not_in_labor_force": 5510.0, "workers_16_and_over": 4720.0, "armed_forces": 0.0, "civilian_labor_force": 5632.0, "employed_agriculture_forestry_fishing_hunting_mining": 46.0, "employed_arts_entertainment_recreation_accommodation_food": 295.0, "employed_construction": 168.0, "employed_education_health_social": 1135.0, "employed_finance_insurance_real_estate": 232.0, "employed_information": 18.0, "employed_manufacturing": 1142.0, "employed_other_services_not_public_admin": 197.0, "employed_public_administration": 246.0, "employed_retail_trade": 492.0, "employed_science_management_admin_waste": 276.0, "employed_transportation_warehousing_utilities": 489.0, "employed_wholesale_trade": 71.0, "occupation_management_arts": 1347.0, "occupation_natural_resources_construction_maintenance": 498.0, "occupation_production_transportation_material": 1466.0, "occupation_sales_office": 813.0, "occupation_services": 683.0, "management_business_sci_arts_employed": 1347.0, "sales_office_employed": 813.0, "in_grades_1_to_4": 724.0, "in_grades_5_to_8": 805.0, "in_grades_9_to_12": 603.0, "in_school": 3395.0, "in_undergrad_college": 844.0, "speak_only_english_at_home": NaN, "speak_spanish_at_home": NaN, "speak_spanish_at_home_low_english": NaN}, {"geo_id": "88063", "do_date": "2014-01-01", "total_pop": 13961.0, "households": 4042.0, "male_pop": 6463.0, "female_pop": 7498.0, "median_age": 30.1, "male_under_5": 579.0, "male_5_to_9": 614.0, "male_10_to_14": 622.0, "male_15_to_17": 356.0, "male_18_to_19": 174.0, "male_20": 149.0, "male_21": 191.0, "male_22_to_24": 244.0, "male_25_to_29": 354.0, "male_30_to_34": 528.0, "male_35_to_39": 323.0, "male_40_to_44": 321.0, "male_45_to_49": 313.0, "male_50_to_54": 435.0, "male_55_to_59": 270.0, "male_65_to_66": 65.0, "male_67_to_69": 233.0, "male_70_to_74": 156.0, "male_75_to_79": 131.0, "male_80_to_84": 71.0, "male_85_and_over": 97.0, "female_under_5": 558.0, "female_5_to_9": 545.0, "female_10_to_14": 759.0, "female_15_to_17": 312.0, "female_18_to_19": 541.0, "female_20": 74.0, "female_21": 174.0, "female_22_to_24": 287.0, "female_25_to_29": 427.0, "female_30_to_34": 475.0, "female_35_to_39": 408.0, "female_40_to_44": 572.0, "female_45_to_49": 296.0, "female_50_to_54": 368.0, "female_55_to_59": 359.0, "female_60_to_61": 99.0, "female_62_to_64": 319.0, "female_65_to_66": 136.0, "female_67_to_69": 129.0, "female_70_to_74": 152.0, "female_75_to_79": 199.0, "female_80_to_84": 161.0, "female_85_and_over": 148.0, "white_pop": 340.0, "population_1_year_and_over": 13829.0, "population_3_years_over": 13315.0, "pop_5_years_over": NaN, "pop_15_and_over": NaN, "pop_16_over": 10045.0, "pop_25_years_over": 7782.0, "pop_25_64": 6104.0, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "not_us_citizen_pop": 2896.0, "black_pop": 9.0, "asian_pop": 0.0, "hispanic_pop": 13597.0, "amerindian_pop": 15.0, "other_race_pop": 0.0, "two_or_more_races_pop": 0.0, "hispanic_any_race": 13597.0, "not_hispanic_pop": 364.0, "asian_male_45_54": 0.0, "asian_male_55_64": 0.0, "black_male_45_54": 0.0, "black_male_55_64": 0.0, "hispanic_male_45_54": 742.0, "hispanic_male_55_64": 507.0, "white_male_45_54": 0.0, "white_male_55_64": 0.0, "median_income": 24875.0, "income_per_capita": 12434.0, "income_less_10000": 567.0, "income_10000_14999": 442.0, "income_15000_19999": 511.0, "income_20000_24999": 509.0, "income_25000_29999": 256.0, "income_30000_34999": 295.0, "income_35000_39999": 164.0, "income_40000_44999": 61.0, "income_45000_49999": 168.0, "income_50000_59999": 254.0, "income_60000_74999": 294.0, "income_75000_99999": 245.0, "income_100000_124999": 163.0, "income_125000_149999": 46.0, "income_150000_199999": 28.0, "income_200000_or_more": 39.0, "pop_determined_poverty_status": 13937.0, "poverty": 5616.0, "gini_index": 0.494, "housing_units": 4356.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 585.0, "owner_occupied_housing_units_lower_value_quartile": 67900.0, "owner_occupied_housing_units_median_value": 94900.0, "owner_occupied_housing_units_upper_value_quartile": 123900.0, "occupied_housing_units": 4042.0, "housing_units_renter_occupied": 1350.0, "vacant_housing_units": 314.0, "vacant_housing_units_for_rent": 39.0, "vacant_housing_units_for_sale": 0.0, "dwellings_1_units_detached": 2587.0, "dwellings_1_units_attached": 117.0, "dwellings_2_units": 108.0, "dwellings_3_to_4_units": 154.0, "dwellings_5_to_9_units": 84.0, "dwellings_10_to_19_units": 21.0, "dwellings_20_to_49_units": 59.0, "dwellings_50_or_more_units": 8.0, "mobile_homes": 1197.0, "housing_built_2005_or_later": 99.0, "housing_built_2000_to_2004": 308.0, "housing_built_1939_or_earlier": 0.0, "median_year_structure_built": 1990.0, "married_households": 2069.0, "nonfamily_households": 710.0, "family_households": 3332.0, "households_public_asst_or_food_stamps": 1382.0, "male_male_households": 0.0, "female_female_households": 0.0, "children": 4345.0, "children_in_single_female_hh": 1471.0, "median_rent": 465.0, "percent_income_spent_on_rent": 33.7, "rent_burden_not_computed": 135.0, "rent_over_50_percent": 358.0, "rent_40_to_50_percent": 55.0, "rent_35_to_40_percent": 127.0, "rent_30_to_35_percent": 261.0, "rent_25_to_30_percent": 114.0, "rent_20_to_25_percent": 83.0, "rent_15_to_20_percent": 111.0, "rent_10_to_15_percent": 95.0, "rent_under_10_percent": 11.0, "owner_occupied_housing_units": 2692.0, "million_dollar_housing_units": 0.0, "mortgaged_housing_units": 1177.0, "different_house_year_ago_different_city": 675.0, "different_house_year_ago_same_city": 325.0, "families_with_young_children": 1333.0, "two_parent_families_with_young_children": 644.0, "two_parents_in_labor_force_families_with_young_children": 336.0, "two_parents_father_in_labor_force_families_with_young_children": 296.0, "two_parents_mother_in_labor_force_families_with_young_children": 12.0, "two_parents_not_in_labor_force_families_with_young_children": 0.0, "one_parent_families_with_young_children": 689.0, "father_one_parent_families_with_young_children": 143.0, "father_in_labor_force_one_parent_families_with_young_children": 143.0, "commute_less_10_mins": 606.0, "commute_10_14_mins": 567.0, "commute_15_19_mins": 937.0, "commute_20_24_mins": 937.0, "commute_25_29_mins": 539.0, "commute_30_34_mins": 740.0, "commute_35_44_mins": 308.0, "commute_60_more_mins": 172.0, "commute_45_59_mins": 323.0, "commuters_16_over": 5129.0, "walked_to_work": 58.0, "worked_at_home": 229.0, "no_car": 144.0, "no_cars": 364.0, "one_car": 1063.0, "two_cars": 1559.0, "three_cars": 691.0, "four_more_cars": 365.0, "aggregate_travel_time_to_work": 118405.0, "commuters_by_public_transportation": 58.0, "commuters_by_bus": 58.0, "commuters_by_car_truck_van": 4947.0, "commuters_by_carpool": 484.0, "commuters_by_subway_or_elevated": 0.0, "commuters_drove_alone": 4463.0, "group_quarters": 0.0, "associates_degree": 385.0, "bachelors_degree": 648.0, "high_school_diploma": 1621.0, "less_one_year_college": 275.0, "masters_degree": 85.0, "one_year_more_college": 1071.0, "less_than_high_school_graduate": 3153.0, "high_school_including_ged": 2121.0, "bachelors_degree_2": 648.0, "bachelors_degree_or_higher_25_64": 684.0, "graduate_professional_degree": 129.0, "some_college_and_associates_degree": 1731.0, "male_45_64_associates_degree": 55.0, "male_45_64_bachelors_degree": 50.0, "male_45_64_graduate_degree": 20.0, "male_45_64_less_than_9_grade": 299.0, "male_45_64_grade_9_12": 180.0, "male_45_64_high_school": 458.0, "male_45_64_some_college": 193.0, "male_45_to_64": 1255.0, "employed_pop": 5395.0, "unemployed_pop": 493.0, "pop_in_labor_force": 5888.0, "not_in_labor_force": 4157.0, "workers_16_and_over": 5358.0, "armed_forces": 0.0, "civilian_labor_force": 5888.0, "employed_agriculture_forestry_fishing_hunting_mining": 203.0, "employed_arts_entertainment_recreation_accommodation_food": 679.0, "employed_construction": 634.0, "employed_education_health_social": 1278.0, "employed_finance_insurance_real_estate": 235.0, "employed_information": 26.0, "employed_manufacturing": 481.0, "employed_other_services_not_public_admin": 212.0, "employed_public_administration": 206.0, "employed_retail_trade": 456.0, "employed_science_management_admin_waste": 538.0, "employed_transportation_warehousing_utilities": 277.0, "employed_wholesale_trade": 170.0, "occupation_management_arts": 871.0, "occupation_natural_resources_construction_maintenance": 924.0, "occupation_production_transportation_material": 960.0, "occupation_sales_office": 1150.0, "occupation_services": 1490.0, "management_business_sci_arts_employed": 871.0, "sales_office_employed": 1150.0, "in_grades_1_to_4": 839.0, "in_grades_5_to_8": 1191.0, "in_grades_9_to_12": 1114.0, "in_school": 4653.0, "in_undergrad_college": 853.0, "speak_only_english_at_home": NaN, "speak_spanish_at_home": NaN, "speak_spanish_at_home_low_english": NaN}, {"geo_id": "32310", "do_date": "2014-01-01", "total_pop": 15864.0, "households": 5662.0, "male_pop": 7659.0, "female_pop": 8205.0, "median_age": 31.1, "male_under_5": 522.0, "male_5_to_9": 455.0, "male_10_to_14": 542.0, "male_15_to_17": 287.0, "male_18_to_19": 443.0, "male_20": 177.0, "male_21": 388.0, "male_22_to_24": 680.0, "male_25_to_29": 381.0, "male_30_to_34": 605.0, "male_35_to_39": 371.0, "male_40_to_44": 473.0, "male_45_to_49": 130.0, "male_50_to_54": 486.0, "male_55_to_59": 477.0, "male_65_to_66": 122.0, "male_67_to_69": 196.0, "male_70_to_74": 170.0, "male_75_to_79": 192.0, "male_80_to_84": 34.0, "male_85_and_over": 48.0, "female_under_5": 460.0, "female_5_to_9": 534.0, "female_10_to_14": 403.0, "female_15_to_17": 479.0, "female_18_to_19": 413.0, "female_20": 394.0, "female_21": 170.0, "female_22_to_24": 572.0, "female_25_to_29": 340.0, "female_30_to_34": 669.0, "female_35_to_39": 396.0, "female_40_to_44": 373.0, "female_45_to_49": 424.0, "female_50_to_54": 495.0, "female_55_to_59": 543.0, "female_60_to_61": 169.0, "female_62_to_64": 319.0, "female_65_to_66": 115.0, "female_67_to_69": 137.0, "female_70_to_74": 314.0, "female_75_to_79": 253.0, "female_80_to_84": 86.0, "female_85_and_over": 147.0, "white_pop": 6273.0, "population_1_year_and_over": 15685.0, "population_3_years_over": 15272.0, "pop_5_years_over": NaN, "pop_15_and_over": NaN, "pop_16_over": 12718.0, "pop_25_years_over": 8945.0, "pop_25_64": 7131.0, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN, "not_us_citizen_pop": 420.0, "black_pop": 7960.0, "asian_pop": 189.0, "hispanic_pop": 1071.0, "amerindian_pop": 35.0, "other_race_pop": 38.0, "two_or_more_races_pop": 298.0, "hispanic_any_race": 1071.0, "not_hispanic_pop": 14793.0, "asian_male_45_54": 0.0, "asian_male_55_64": 10.0, "black_male_45_54": 204.0, "black_male_55_64": 378.0, "hispanic_male_45_54": 19.0, "hispanic_male_55_64": 43.0, "white_male_45_54": 393.0, "white_male_55_64": 517.0, "median_income": 36653.0, "income_per_capita": 18784.0, "income_less_10000": 751.0, "income_10000_14999": 428.0, "income_15000_19999": 448.0, "income_20000_24999": 435.0, "income_25000_29999": 402.0, "income_30000_34999": 248.0, "income_35000_39999": 359.0, "income_40000_44999": 266.0, "income_45000_49999": 348.0, "income_50000_59999": 426.0, "income_60000_74999": 263.0, "income_75000_99999": 626.0, "income_100000_124999": 325.0, "income_125000_149999": 170.0, "income_150000_199999": 42.0, "income_200000_or_more": 125.0, "pop_determined_poverty_status": 15189.0, "poverty": 4401.0, "gini_index": 0.4746, "housing_units": 7761.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 841.0, "owner_occupied_housing_units_lower_value_quartile": 60200.0, "owner_occupied_housing_units_median_value": 96500.0, "owner_occupied_housing_units_upper_value_quartile": 168100.0, "occupied_housing_units": 5662.0, "housing_units_renter_occupied": 2943.0, "vacant_housing_units": 2099.0, "vacant_housing_units_for_rent": 283.0, "vacant_housing_units_for_sale": 64.0, "dwellings_1_units_detached": 3504.0, "dwellings_1_units_attached": 147.0, "dwellings_2_units": 261.0, "dwellings_3_to_4_units": 294.0, "dwellings_5_to_9_units": 172.0, "dwellings_10_to_19_units": 272.0, "dwellings_20_to_49_units": 367.0, "dwellings_50_or_more_units": 407.0, "mobile_homes": 2337.0, "housing_built_2005_or_later": 18.0, "housing_built_2000_to_2004": 157.0, "housing_built_1939_or_earlier": 282.0, "median_year_structure_built": 1979.0, "married_households": 1880.0, "nonfamily_households": 2259.0, "family_households": 3403.0, "households_public_asst_or_food_stamps": 1753.0, "male_male_households": 11.0, "female_female_households": 0.0, "children": 3682.0, "children_in_single_female_hh": 1465.0, "median_rent": 608.0, "percent_income_spent_on_rent": 39.1, "rent_burden_not_computed": 254.0, "rent_over_50_percent": 1073.0, "rent_40_to_50_percent": 212.0, "rent_35_to_40_percent": 331.0, "rent_30_to_35_percent": 214.0, "rent_25_to_30_percent": 205.0, "rent_20_to_25_percent": 225.0, "rent_15_to_20_percent": 272.0, "rent_10_to_15_percent": 126.0, "rent_under_10_percent": 31.0, "owner_occupied_housing_units": 2719.0, "million_dollar_housing_units": 0.0, "mortgaged_housing_units": 1470.0, "different_house_year_ago_different_city": 2247.0, "different_house_year_ago_same_city": 1781.0, "families_with_young_children": 1168.0, "two_parent_families_with_young_children": 612.0, "two_parents_in_labor_force_families_with_young_children": 254.0, "two_parents_father_in_labor_force_families_with_young_children": 228.0, "two_parents_mother_in_labor_force_families_with_young_children": 65.0, "two_parents_not_in_labor_force_families_with_young_children": 65.0, "one_parent_families_with_young_children": 556.0, "father_one_parent_families_with_young_children": 36.0, "father_in_labor_force_one_parent_families_with_young_children": 36.0, "commute_less_10_mins": 535.0, "commute_10_14_mins": 398.0, "commute_15_19_mins": 1532.0, "commute_20_24_mins": 968.0, "commute_25_29_mins": 391.0, "commute_30_34_mins": 1317.0, "commute_35_44_mins": 494.0, "commute_60_more_mins": 258.0, "commute_45_59_mins": 431.0, "commuters_16_over": 6324.0, "walked_to_work": 74.0, "worked_at_home": 197.0, "no_car": 456.0, "no_cars": 712.0, "one_car": 2114.0, "two_cars": 1710.0, "three_cars": 835.0, "four_more_cars": 291.0, "aggregate_travel_time_to_work": 159860.0, "commuters_by_public_transportation": 191.0, "commuters_by_bus": 191.0, "commuters_by_car_truck_van": 5942.0, "commuters_by_carpool": 1099.0, "commuters_by_subway_or_elevated": 0.0, "commuters_drove_alone": 4843.0, "group_quarters": 712.0, "associates_degree": 713.0, "bachelors_degree": 1216.0, "high_school_diploma": 2497.0, "less_one_year_college": 708.0, "masters_degree": 457.0, "one_year_more_college": 1046.0, "less_than_high_school_graduate": 1159.0, "high_school_including_ged": 3482.0, "bachelors_degree_2": 1216.0, "bachelors_degree_or_higher_25_64": 1356.0, "graduate_professional_degree": 621.0, "some_college_and_associates_degree": 2467.0, "male_45_64_associates_degree": 144.0, "male_45_64_bachelors_degree": 193.0, "male_45_64_graduate_degree": 62.0, "male_45_64_less_than_9_grade": 99.0, "male_45_64_grade_9_12": 163.0, "male_45_64_high_school": 611.0, "male_45_64_some_college": 301.0, "male_45_to_64": 1573.0, "employed_pop": 6614.0, "unemployed_pop": 554.0, "pop_in_labor_force": 7168.0, "not_in_labor_force": 5550.0, "workers_16_and_over": 6521.0, "armed_forces": 0.0, "civilian_labor_force": 7168.0, "employed_agriculture_forestry_fishing_hunting_mining": 77.0, "employed_arts_entertainment_recreation_accommodation_food": 850.0, "employed_construction": 512.0, "employed_education_health_social": 1238.0, "employed_finance_insurance_real_estate": 476.0, "employed_information": 32.0, "employed_manufacturing": 70.0, "employed_other_services_not_public_admin": 241.0, "employed_public_administration": 663.0, "employed_retail_trade": 1170.0, "employed_science_management_admin_waste": 880.0, "employed_transportation_warehousing_utilities": 260.0, "employed_wholesale_trade": 145.0, "occupation_management_arts": 1646.0, "occupation_natural_resources_construction_maintenance": 690.0, "occupation_production_transportation_material": 569.0, "occupation_sales_office": 2354.0, "occupation_services": 1355.0, "management_business_sci_arts_employed": 1646.0, "sales_office_employed": 2354.0, "in_grades_1_to_4": 803.0, "in_grades_5_to_8": 856.0, "in_grades_9_to_12": 1164.0, "in_school": 5387.0, "in_undergrad_college": 1902.0, "speak_only_english_at_home": NaN, "speak_spanish_at_home": NaN, "speak_spanish_at_home_low_english": NaN}]}