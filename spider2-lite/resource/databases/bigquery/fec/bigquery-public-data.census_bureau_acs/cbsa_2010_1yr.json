{"table_name": "cbsa_2010_1yr", "table_fullname": "bigquery-public-data.census_bureau_acs.cbsa_2010_1yr", "column_names": ["geo_id", "nonfamily_households", "family_households", "median_year_structure_built", "rent_burden_not_computed", "rent_over_50_percent", "rent_40_to_50_percent", "rent_35_to_40_percent", "rent_30_to_35_percent", "rent_25_to_30_percent", "rent_20_to_25_percent", "rent_15_to_20_percent", "rent_10_to_15_percent", "rent_under_10_percent", "total_pop", "male_pop", "female_pop", "median_age", "white_pop", "black_pop", "asian_pop", "hispanic_pop", "amerindian_pop", "other_race_pop", "two_or_more_races_pop", "not_hispanic_pop", "commuters_by_public_transportation", "households", "median_income", "income_per_capita", "housing_units", "vacant_housing_units", "vacant_housing_units_for_rent", "vacant_housing_units_for_sale", "median_rent", "percent_income_spent_on_rent", "owner_occupied_housing_units", "million_dollar_housing_units", "mortgaged_housing_units", "families_with_young_children", "two_parent_families_with_young_children", "two_parents_in_labor_force_families_with_young_children", "two_parents_father_in_labor_force_families_with_young_children", "two_parents_mother_in_labor_force_families_with_young_children", "two_parents_not_in_labor_force_families_with_young_children", "one_parent_families_with_young_children", "father_one_parent_families_with_young_children", "father_in_labor_force_one_parent_families_with_young_children", "commute_10_14_mins", "commute_15_19_mins", "commute_20_24_mins", "commute_25_29_mins", "commute_30_34_mins", "commute_45_59_mins", "aggregate_travel_time_to_work", "income_less_10000", "income_10000_14999", "income_15000_19999", "income_20000_24999", "income_25000_29999", "income_30000_34999", "income_35000_39999", "income_40000_44999", "income_45000_49999", "income_50000_59999", "income_60000_74999", "income_75000_99999", "income_100000_124999", "income_125000_149999", "income_150000_199999", "income_200000_or_more", "renter_occupied_housing_units_paying_cash_median_gross_rent", "owner_occupied_housing_units_lower_value_quartile", "owner_occupied_housing_units_median_value", "owner_occupied_housing_units_upper_value_quartile", "married_households", "occupied_housing_units", "housing_units_renter_occupied", "dwellings_1_units_detached", "dwellings_1_units_attached", "dwellings_2_units", "dwellings_3_to_4_units", "dwellings_5_to_9_units", "dwellings_10_to_19_units", "dwellings_20_to_49_units", "dwellings_50_or_more_units", "mobile_homes", "housing_built_2005_or_later", "housing_built_2000_to_2004", "housing_built_1939_or_earlier", "male_under_5", "male_5_to_9", "male_10_to_14", "male_15_to_17", "male_18_to_19", "male_20", "male_21", "male_22_to_24", "male_25_to_29", "male_30_to_34", "male_35_to_39", "male_40_to_44", "male_45_to_49", "male_50_to_54", "male_55_to_59", "male_60_61", "male_62_64", "male_65_to_66", "male_67_to_69", "male_70_to_74", "male_75_to_79", "male_80_to_84", "male_85_and_over", "female_under_5", "female_5_to_9", "female_10_to_14", "female_15_to_17", "female_18_to_19", "female_20", "female_21", "female_22_to_24", "female_25_to_29", "female_30_to_34", "female_35_to_39", "female_40_to_44", "female_45_to_49", "female_50_to_54", "female_55_to_59", "female_60_to_61", "female_62_to_64", "female_65_to_66", "female_67_to_69", "female_70_to_74", "female_75_to_79", "female_80_to_84", "female_85_and_over", "white_including_hispanic", "black_including_hispanic", "amerindian_including_hispanic", "asian_including_hispanic", "commute_5_9_mins", "commute_35_39_mins", "commute_40_44_mins", "commute_60_89_mins", "commute_90_more_mins", "households_retirement_income", "asian_male_45_54", "asian_male_55_64", "black_male_45_54", "black_male_55_64", "hispanic_male_45_54", "hispanic_male_55_64", "white_male_45_54", "white_male_55_64", "bachelors_degree_2", "bachelors_degree_or_higher_25_64", "children", "children_in_single_female_hh", "commuters_by_bus", "commuters_by_car_truck_van", "commuters_by_carpool", "commuters_by_subway_or_elevated", "commuters_drove_alone", "different_house_year_ago_different_city", "different_house_year_ago_same_city", "employed_agriculture_forestry_fishing_hunting_mining", "employed_arts_entertainment_recreation_accommodation_food", "employed_construction", "employed_education_health_social", "employed_finance_insurance_real_estate", "employed_information", "employed_manufacturing", "employed_other_services_not_public_admin", "employed_public_administration", "employed_retail_trade", "employed_science_management_admin_waste", "employed_transportation_warehousing_utilities", "employed_wholesale_trade", "female_female_households", "four_more_cars", "gini_index", "graduate_professional_degree", "group_quarters", "high_school_including_ged", "households_public_asst_or_food_stamps", "in_grades_1_to_4", "in_grades_5_to_8", "in_grades_9_to_12", "in_school", "in_undergrad_college", "less_than_high_school_graduate", "male_45_64_associates_degree", "male_45_64_bachelors_degree", "male_45_64_graduate_degree", "male_45_64_less_than_9_grade", "male_45_64_grade_9_12", "male_45_64_high_school", "male_45_64_some_college", "male_45_to_64", "male_male_households", "management_business_sci_arts_employed", "no_car", "no_cars", "not_us_citizen_pop", "occupation_management_arts", "occupation_natural_resources_construction_maintenance", "occupation_production_transportation_material", "occupation_sales_office", "occupation_services", "one_car", "two_cars", "three_cars", "pop_25_64", "pop_determined_poverty_status", "population_1_year_and_over", "population_3_years_over", "poverty", "sales_office_employed", "some_college_and_associates_degree", "walked_to_work", "worked_at_home", "workers_16_and_over", "associates_degree", "bachelors_degree", "high_school_diploma", "less_one_year_college", "masters_degree", "one_year_more_college", "pop_25_years_over", "commute_35_44_mins", "commute_60_more_mins", "commute_less_10_mins", "commuters_16_over", "hispanic_any_race", "pop_5_years_over", "speak_only_english_at_home", "speak_spanish_at_home", "speak_spanish_at_home_low_english", "pop_15_and_over", "pop_never_married", "pop_now_married", "pop_separated", "pop_widowed", "pop_divorced", "do_date", "armed_forces", "civilian_labor_force", "employed_pop", "unemployed_pop", "not_in_labor_force", "pop_16_over", "pop_in_labor_force"], "column_types": ["STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64"], "nested_column_names": ["geo_id", "nonfamily_households", "family_households", "median_year_structure_built", "rent_burden_not_computed", "rent_over_50_percent", "rent_40_to_50_percent", "rent_35_to_40_percent", "rent_30_to_35_percent", "rent_25_to_30_percent", "rent_20_to_25_percent", "rent_15_to_20_percent", "rent_10_to_15_percent", "rent_under_10_percent", "total_pop", "male_pop", "female_pop", "median_age", "white_pop", "black_pop", "asian_pop", "hispanic_pop", "amerindian_pop", "other_race_pop", "two_or_more_races_pop", "not_hispanic_pop", "commuters_by_public_transportation", "households", "median_income", "income_per_capita", "housing_units", "vacant_housing_units", "vacant_housing_units_for_rent", "vacant_housing_units_for_sale", "median_rent", "percent_income_spent_on_rent", "owner_occupied_housing_units", "million_dollar_housing_units", "mortgaged_housing_units", "families_with_young_children", "two_parent_families_with_young_children", "two_parents_in_labor_force_families_with_young_children", "two_parents_father_in_labor_force_families_with_young_children", "two_parents_mother_in_labor_force_families_with_young_children", "two_parents_not_in_labor_force_families_with_young_children", "one_parent_families_with_young_children", "father_one_parent_families_with_young_children", "father_in_labor_force_one_parent_families_with_young_children", "commute_10_14_mins", "commute_15_19_mins", "commute_20_24_mins", "commute_25_29_mins", "commute_30_34_mins", "commute_45_59_mins", "aggregate_travel_time_to_work", "income_less_10000", "income_10000_14999", "income_15000_19999", "income_20000_24999", "income_25000_29999", "income_30000_34999", "income_35000_39999", "income_40000_44999", "income_45000_49999", "income_50000_59999", "income_60000_74999", "income_75000_99999", "income_100000_124999", "income_125000_149999", "income_150000_199999", "income_200000_or_more", "renter_occupied_housing_units_paying_cash_median_gross_rent", "owner_occupied_housing_units_lower_value_quartile", "owner_occupied_housing_units_median_value", "owner_occupied_housing_units_upper_value_quartile", "married_households", "occupied_housing_units", "housing_units_renter_occupied", "dwellings_1_units_detached", "dwellings_1_units_attached", "dwellings_2_units", "dwellings_3_to_4_units", "dwellings_5_to_9_units", "dwellings_10_to_19_units", "dwellings_20_to_49_units", "dwellings_50_or_more_units", "mobile_homes", "housing_built_2005_or_later", "housing_built_2000_to_2004", "housing_built_1939_or_earlier", "male_under_5", "male_5_to_9", "male_10_to_14", "male_15_to_17", "male_18_to_19", "male_20", "male_21", "male_22_to_24", "male_25_to_29", "male_30_to_34", "male_35_to_39", "male_40_to_44", "male_45_to_49", "male_50_to_54", "male_55_to_59", "male_60_61", "male_62_64", "male_65_to_66", "male_67_to_69", "male_70_to_74", "male_75_to_79", "male_80_to_84", "male_85_and_over", "female_under_5", "female_5_to_9", "female_10_to_14", "female_15_to_17", "female_18_to_19", "female_20", "female_21", "female_22_to_24", "female_25_to_29", "female_30_to_34", "female_35_to_39", "female_40_to_44", "female_45_to_49", "female_50_to_54", "female_55_to_59", "female_60_to_61", "female_62_to_64", "female_65_to_66", "female_67_to_69", "female_70_to_74", "female_75_to_79", "female_80_to_84", "female_85_and_over", "white_including_hispanic", "black_including_hispanic", "amerindian_including_hispanic", "asian_including_hispanic", "commute_5_9_mins", "commute_35_39_mins", "commute_40_44_mins", "commute_60_89_mins", "commute_90_more_mins", "households_retirement_income", "asian_male_45_54", "asian_male_55_64", "black_male_45_54", "black_male_55_64", "hispanic_male_45_54", "hispanic_male_55_64", "white_male_45_54", "white_male_55_64", "bachelors_degree_2", "bachelors_degree_or_higher_25_64", "children", "children_in_single_female_hh", "commuters_by_bus", "commuters_by_car_truck_van", "commuters_by_carpool", "commuters_by_subway_or_elevated", "commuters_drove_alone", "different_house_year_ago_different_city", "different_house_year_ago_same_city", "employed_agriculture_forestry_fishing_hunting_mining", "employed_arts_entertainment_recreation_accommodation_food", "employed_construction", "employed_education_health_social", "employed_finance_insurance_real_estate", "employed_information", "employed_manufacturing", "employed_other_services_not_public_admin", "employed_public_administration", "employed_retail_trade", "employed_science_management_admin_waste", "employed_transportation_warehousing_utilities", "employed_wholesale_trade", "female_female_households", "four_more_cars", "gini_index", "graduate_professional_degree", "group_quarters", "high_school_including_ged", "households_public_asst_or_food_stamps", "in_grades_1_to_4", "in_grades_5_to_8", "in_grades_9_to_12", "in_school", "in_undergrad_college", "less_than_high_school_graduate", "male_45_64_associates_degree", "male_45_64_bachelors_degree", "male_45_64_graduate_degree", "male_45_64_less_than_9_grade", "male_45_64_grade_9_12", "male_45_64_high_school", "male_45_64_some_college", "male_45_to_64", "male_male_households", "management_business_sci_arts_employed", "no_car", "no_cars", "not_us_citizen_pop", "occupation_management_arts", "occupation_natural_resources_construction_maintenance", "occupation_production_transportation_material", "occupation_sales_office", "occupation_services", "one_car", "two_cars", "three_cars", "pop_25_64", "pop_determined_poverty_status", "population_1_year_and_over", "population_3_years_over", "poverty", "sales_office_employed", "some_college_and_associates_degree", "walked_to_work", "worked_at_home", "workers_16_and_over", "associates_degree", "bachelors_degree", "high_school_diploma", "less_one_year_college", "masters_degree", "one_year_more_college", "pop_25_years_over", "commute_35_44_mins", "commute_60_more_mins", "commute_less_10_mins", "commuters_16_over", "hispanic_any_race", "pop_5_years_over", "speak_only_english_at_home", "speak_spanish_at_home", "speak_spanish_at_home_low_english", "pop_15_and_over", "pop_never_married", "pop_now_married", "pop_separated", "pop_widowed", "pop_divorced", "do_date", "armed_forces", "civilian_labor_force", "employed_pop", "unemployed_pop", "not_in_labor_force", "pop_16_over", "pop_in_labor_force"], "nested_column_types": ["STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64"], "description": ["Core Based Statistical Area (CBSA)_2015 Geoids", "Nonfamily Households. A householder living alone or with nonrelatives only. Unmarried couples households, whether opposite-sex or same-sex, with no relatives of the householder present are tabulated in nonfamily households.", "Family Households. A family consists of a householder and one or more other people living in the same household who are related to the householder by birth, marriage, or adoption. All people in a household who are related to the householder are regarded as members of his or her family. A family household may contain people not related to the householder, but those people are not included as part of the family of the householder in tabulations. Thus, the number of family households is equal to the number of families, but family households may include more members than do families. A household can contain only one family for purposes of tabulations. Not all households contain families since a household may be comprised of a group of unrelated people or of one person living alone -- these are called nonfamily households. Families are classified by type as either a \"married couple family\" or \"other family\" according to the sex of the householder and the presence of relatives. The data on family type are based ...", "Median Year Structure Built. Median Year Structure Built", "Housing units without rent burden computed. Units for which no rent is paid and units occupied by households that reported no income or a net loss comprise this category", "Housing units spending over 50% income on rent. Gross rent over 50 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 40 to 49.9% income on rent. Gross rent from 40.0 to 49.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 35 to 39.9% income on rent. Gross rent from 35.0 to 39.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 30 to 34.9% income on rent. Gross rent from 30.0 to 34.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 25 to 29.9% income on rent. Gross rent from 25.0 to 29.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 20 to 24.9% income on rent. Gross rent from 20.0 to 24.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 15 to 19.9% income on rent. Gross rent from 15.0 to 19.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 10 to 14.9% income on rent. Gross rent from 10.0 to 14.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending less than 10% on rent. Gross rent less than 10 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Total Population. The total number of all people living in a given geographic area.  This is a very useful catch-all denominator when calculating rates.", "Male Population. The number of people within each geography who are male.", "Female Population. The number of people within each geography who are female.", "Median Age. The median age of all people in a given geographic area.", "White Population. The number of people identifying as white, non-Hispanic in each geography.", "Black or African American Population. The number of people identifying as black or African American, non-Hispanic in each geography.", "Asian Population. The number of people identifying as Asian, non-Hispanic in each geography.", "Hispanic Population. The number of people identifying as Hispanic or Latino in each geography.", "American Indian and Alaska Native Population. The number of people identifying as American Indian or Alaska native in each geography.", "Other Race population. The number of people identifying as another race in each geography", "Two or more races population. The number of people identifying as two or more races in each geography", "Population not Hispanic. The number of people not identifying as Hispanic or Latino in each geography.", "Commuters by Public Transportation. The number of workers age 16 years and over within a geographic area who primarily traveled to work by public transportation.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Households. A count of the number of households in each geography. A household consists of one or more people who live in the same dwelling and also share at meals or living accommodation, and may consist of a single family or some other grouping of people. ", "Median Household Income in the past 12 Months. Within a geographic area, the median income received by every household on a regular basis before payments for personal income taxes, social security, union dues, medicare deductions, etc.  It includes income received from wages, salary, commissions, bonuses, and tips; self-employment income from own nonfarm or farm businesses, including proprietorships and partnerships; interest, dividends, net rental income, royalty income, or income from estates and trusts; Social Security or Railroad Retirement income; Supplemental Security Income (SSI); any cash public assistance or welfare payments from the state or local welfare office; retirement, survivor, or disability benefits; and any other sources of income received regularly such as Veterans' (VA) payments, unemployment and/or worker's compensation, child support, and alimony.", "Per Capita Income in the past 12 Months. Per capita income is the mean income computed for every man, woman, and child in a particular group. It is derived by dividing the total income of a particular group by the total population.", "Housing Units. A count of housing units in each geography.  A housing unit is a house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters.", "Vacant Housing Units. The count of vacant housing units in a geographic area. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Vacant Housing Units for Rent. The count of vacant housing units in a geographic area that are for rent. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Vacant Housing Units for Sale. The count of vacant housing units in a geographic area that are for sale. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Median Rent. The median contract rent within a geographic area. The contract rent is the monthly rent agreed to or contracted for, regardless of any furnishings, utilities, fees, meals, or services that may be included. For vacant units, it is the monthly rent asked for the rental unit at the time of interview.", "Percent of Household Income Spent on Rent. Within a geographic area, the median percentage of household income which was spent on gross rent.  Gross rent is the amount of the contract rent plus the estimated average monthly cost of utilities (electricity, gas, water, sewer etc.) and fuels (oil, coal, wood, etc.) if these are paid by the renter.  Household income is the sum of the income of all people 15 years and older living in the household.", "Owner-occupied Housing Units", "Owner-occupied Housing Units valued at $1,000,000 or more.. The count of owner occupied housing units in a geographic area that are valued at $1,000,000 or more.  Value is the respondent's estimate of how much the property (house and lot, mobile home and lot, or condominium unit) would sell for if it were for sale.", "Owner-occupied Housing Units with a Mortgage. The count of housing units within a geographic area that are mortagaged. \"Mortgage\" refers to all forms of debt where the property is pledged as security for repayment of the debt, including deeds of trust, trust deed, contracts to purchase, land contracts, junior mortgages, and home equity loans.", "Families with young children (under 6 years of age)", "Two-parent families with young children (under 6 years of age)", "Two-parent families, both parents in labor force with young children (under 6 years of age)", "Two-parent families, father only in labor force with young children (under 6 years of age)", "Two-parent families, mother only in labor force with young children (under 6 years of age)", "Two-parent families, neither parent in labor force with young children (under 6 years of age)", "One-parent families with young children (under 6 years of age)", "One-parent families, father, with young children (under 6 years of age)", "One-parent families, father in labor force, with young children (under 6 years of age)", "Number of workers with a commute between 10 and 14 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 10 and 14 minutes. ", "Number of workers with a commute between 15 and 19 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 15 and 19 minutes. ", "Number of workers with a commute between 20 and 24 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 20 and 24 minutes.", "Number of workers with a commute between 25 and 29 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 25 and 29 minutes. ", "Number of workers with a commute between 30 and 34 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 30 and 34 minutes. ", "Number of workers with a commute between 45 and 59 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 45 and 59 minutes. ", "Aggregate travel time to work. The total number of minutes every worker in a geographic area over the age of 16 who did not work from home spent spent commuting to work in one day", "Households with income less than $10,000. The number of households in a geographic area whose annual income was less than $10,000.", "Households with income of $10,000 to $14,999. The number of households in a geographic area whose annual income was between $10,000 and $14,999.", "Households with income of $15,000 to $19,999. The number of households in a geographic area whose annual income was between $15,000 and $19,999.", "Households with income of $20,000 to $24,999. The number of households in a geographic area whose annual income was between $20,000 and $24,999.", "Households with income of $25,000 to $29,999. The number of households in a geographic area whose annual income was between $20,000 and $24,999.", "Households with income of $30,000 to $34,999. The number of households in a geographic area whose annual income was between $30,000 and $34,999.", "Households with income of $35,000 to $39,999. The number of households in a geographic area whose annual income was between $35,000 and $39,999.", "Households with income of $40,000 to $44,999. The number of households in a geographic area whose annual income was between $40,000 and $44,999.", "Households with income of $45,000 to $49,999. The number of households in a geographic area whose annual income was between $45,000 and $49,999.", "Households with income of $50,000 to $59,999. The number of households in a geographic area whose annual income was between $50,000 and $59,999.", "Households with income of $60,000 to $74,999. The number of households in a geographic area whose annual income was between $60,000 and $74,999.", "Households with income of $75,000 to $99,999. The number of households in a geographic area whose annual income was between $75,000 and $99,999.", "Households with income of $100,000 to $124,999. The number of households in a geographic area whose annual income was between $100,000 and $124,999.", "Households with income of $125,000 to $149,999. The number of households in a geographic area whose annual income was between $125,000 and $149,999.", "Households with income of $150,000 to $199,999. The number of households in a geographic area whose annual income was between $150,000 and $1999,999.", "Households with income of $200,000 Or More. The number of households in a geographic area whose annual income was more than $200,000.", "Renter-Occupied Housing Units Paying Cash Rent Median Gross Rent", "Owner-Occupied Housing Units Lower Value Quartile", "Owner-Occupied Housing Units Median Value. The middle value (median) in a geographic area owner occupied housing units.", "Owner-Occupied Housing Units Upper Value Quartile", "Married households. People in formal marriages, as well as people in common-law marriages, are included. Does not include same-sex marriages.", "Occupied housing units. A housing unit is classified as occupied if it is the usual place of residence of the person or group of people living in it at the time of enumeration.", "Renter occupied housing units. All occupied units which are not owner occupied, whether they are rented for cash rent or occupied without payment of cash rent, are classified as renter-occupied.", "Single-family (one unit) detached dwellings. This is a 1-unit structure detached from any other house, that is, with open space on all four sides. Such structures are considered detached even if they have an adjoining shed or garage. A one-family house that contains a business is considered detached as long as the building has open space on all four sides. Mobile homes to which one or more permanent rooms have been added or built also are included.", "Single-family (one unit) attached dwellings. This is a 1-unit structure that has one or more walls extending from ground to roof separating it from adjoining structures. In row houses (sometimes called townhouses), double houses, or houses attached to nonresidential structures, each house is a separate, attached structure if the dividing or common wall goes from ground to roof.", "Two-family (two unit) dwellings", "Multifamily dwellings with three to 4 units", "Apartment buildings with 5 to 9 units", "Apartment buildings with 10 to 19 units", "Apartment buildings with 20 to 49 units", "Apartment buildings with 50 or more units", "Mobile homes. A manufactured home is defined as a movable dwelling, 8 feet or more wide and 40 feet or more long, designed to be towed on its own chassis, with transportation gear integral to the unit when it leaves the factory, and without need of a permanent foundation. These homes are built in accordance with the U.S. Department of Housing and Urban Development (HUD) building code.", "Housing units built in 2005 or later. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built in 2005 or later.", "Housing units built between 2000 and 2004. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built from 2000 to 2004.", "Housing units built before 1939. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built in 1939 or earlier.", "Male under 5 years. The male population over the age of five years within the specified area.", "Male age 5 to 9. The male population between the age of five years to nine years within the specified area.", "Male age 10 to 14. The male population between the age of ten years to fourteen years within the specified area.", "Male age 15 to 17. The male population between the age of fifteeen years to seventeen years within the specified area.", "Male age 18 and 19. The male population between the age of eighteen years to nineteen years within the specified area.", "Male age 20. The male population with an age of twenty years within the specified area.", "Male age 21. The male population with an age of twenty-one years within the specified area.", "Male age 22 to 24. The male population between the age of twenty-two years to twenty-four years within the specified area.", "Male age 25 to 29. The male population between the age of twenty-five years to twenty-nine years within the specified area.", "Male age 30 to 34. The male population between the age of thirty years to thirty-four years within the specified area.", "Male age 35 to 39. The male population between the age of thirty-five years to thirty-nine years within the specified area.", "Male age 40 to 44. The male population between the age of fourty years to fourty-four years within the specified area.", "Men age 45 to 49. The male population between the age of fourty-five years to fourty-nine years within the specified area.", "Men age 50 to 54. The male population between the age of fifty years to fifty-four years within the specified area.", "Men age 55 to 59. The male population between the age of fifty-five years to fifty-nine years within the specified area.", "Men age 60 to 61. The male population between the age of sixty years to sixty-one years within the specified area.", "Men age 62 to 64. The male population between the age of sixty-two years to sixty-four years within the specified area.", "Male age 65 to 66. The male population between the age of sixty-five years to sixty-six years within the specified area.", "Male age 67 to 69. The male population between the age of sixty-seven years to sixty-nine years within the specified area.", "Male age 70 to 74. The male population between the age of seventy years to seventy-four years within the specified area.", "Male age 75 to 79. The male population between the age of seventy-five years to seventy-nine years within the specified area.", "Male age 80 to 84. The male population between the age of eighty years to eighty-four years within the specified area.", "Male age 85 and over. The male population of the age of eighty-five years and over within the specified area.", "Female under 5 years. The female population over the age of five years within the specified area.", "Female age 5 to 9. The female population between the age of five years to nine years within the specified area.", "Female age 10 to 14. The female population between the age of ten years to fourteen years within the specified area.", "Female age 15 to 17. The female population between the age of fifteeen years to seventeen years within the specified area.", "Female age 18 and 19. The female population between the age of eighteen years to nineteen years within the specified area.", "Female age 20. The female population with an age of twenty years within the specified area.", "Female age 21. The female population with an age of twenty-one years within the specified area.", "Female age 22 to 24. The female population between the age of twenty-two years to twenty-four years within the specified area.", "Female age 25 to 29. The female population between the age of twenty-five years to twenty-nine years within the specified area.", "Female age 30 to 34. The female population between the age of thirty years to thirty-four years within the specified area.", "Female age 35 to 39. The female population between the age of thirty-five years to thirty-nine years within the specified area.", "Female age 40 to 44. The female population between the age of fourty years to fourty-four years within the specified area.", "Female age 45 to 49. The female population between the age of fourty-five years to fourty-nine years within the specified area.", "Female age 50 to 54. The female population between the age of fifty years to fifty-four years within the specified area.", "Female age 55 to 59. The female population between the age of fifty-five years to fifty-nine years within the specified area.", "Female age 60 and 61. The female population between the age of sixty years to sixty-one years within the specified area.", "Female age 62 to 64. The female population between the age of sixty-two years to sixty-four years within the specified area.", "Female age 65 to 66. The female population between the age of sixty-five years to sixty-six years within the specified area.", "Female age 67 to 69. The female population between the age of sixty-seven years to sixty-nine years within the specified area.", "Female age 70 to 74. The female population between the age of seventy years to seventy-four years within the specified area.", "Female age 75 to 79. The female population between the age of seventy-five years to seventy-nine years within the specified area.", "Female age 80 to 84. The female population between the age of eighty years to eighty-four years within the specified area.", "Female age 85 and over. The female population of the age of eighty-five years and over within the specified area.", "White including Hispanic", "Black including Hispanic", "American Indian including Hispanic", "Asian including Hispanic", "Number of workers with a commute between 5 and 9 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 5 and 9 minutes.", "Number of workers with a commute between 35 and 39 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 35 and 39 minutes. ", "Number of workers with a commute between 40 and 44 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 40 and 44 minutes. ", "Number of workers with a commute between 60 and 89 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 60 and 89 minutes .", "Number of workers with a commute of more than 90 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute more than 90 minutes.", "Households receiving retirement income", "Asian Men age 45 to 54", "Asian Men age 55 to 64", "Black Men age 45 to 54", "Black Men age 55 to 64", "Hispanic Men age 45 to 54", "Hispanic Men age 55 to 64", "White Men age 45 to 54", "White Men age 55 to 64", "Population who completed a bachelor's degree. From mobility table.", "Population with Bachelors Degree or Higher, Ages 25 to 64. The number of people in each geography who are between the ages of 25 and 64 who have attained a bachelors degree or higher. ", "Children under 18 Years of Age. The number of people within each geography who are under 18 years of age.", "Children under 18 years of age in single female-led household", "Commuters by Bus. The number of workers age 16 years and over within a geographic area who primarily traveled to work by bus.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.  This is a subset of workers who commuted by public transport.", "Commuters by Car, Truck, or Van. The number of workers age 16 years and over within  a geographic area who primarily traveled to work by car, truck or  van.  This is the principal mode of travel or type of conveyance,  by distance rather than time, that the worker usually used to get  from home to work.", "Commuters by Carpool. The number of workers age 16 years and over within a geographic area who primarily traveled to work by carpool.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Commuters by Subway or Elevated. The number of workers age 16 years and over within a geographic area who primarily traveled to work by subway or elevated train.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.  This is a subset of workers who commuted by public transport.", "Commuters who drove alone. The number of workers age 16 years and over within a geographic area who primarily traveled by car driving alone. This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Lived in a different house one year ago in a different city. All people in a geographic area who lived in a different city within the year prior to the survey.", "Lived in a different house one year ago in the same city. All people in a geographic area who lived in the same city but moved to a different unit within the year prior to the survey.", "Workers employed in firms in agriculture, forestry, fishing, hunting, or mining. The Agriculture, Forestry, Fishing and Hunting sector comprises establishments primarily engaged in growing crops, raising animals, harvesting timber, and harvesting fish and other animals from a farm, ranch, or their natural habitats.", "Workers employed in firms in arts, entertainment, recreation, accommodation and food services. The Arts, Entertainment, and Recreation sector includes a wide range of establishments that operate facilities or provide services to meet varied cultural, entertainment, and recreational interests of their patrons. This sector comprises (1) establishments that are involved in producing, promoting, or participating in live performances, events, or exhibits intended for public viewing; (2) establishments that preserve and exhibit objects and sites of historical, cultural, or educational interest; and (3) establishments that operate facilities or provide services that enable patrons to participate in recreational activities or pursue amusement, hobby, and leisure-time interests.", "Workers employed in firms in construction. The Construction sector comprises establishments primarily engaged in the construction of buildings or engineering projects (e.g., highways and utility systems). Construction work done may include new work, additions, alterations, or maintenance and repairs.", "Workers employed in firms in educational services, health care, and social assistance. Outpatient health services, other than hospital care, including: public health administration; research and education; categorical health programs; treatment and immunization clinics; nursing; environmental health activities such as air and water pollution control; ambulance service if provided separately from fire protection services, and other general public health activities such as mosquito abatement. School health services provided by health agencies (rather than school agencies) are included here. Sewage treatment operations are classified under Sewerage.", "Workers employed in firms in finance, insurance, real estate and rental and leasing. The Real Estate and Rental and Leasing sector comprises establishments primarily engaged in renting, leasing, or otherwise allowing the use of tangible or intangible assets, and establishments providing related services. The major portion of this sector comprises establishments that rent, lease, or otherwise allow the use of their own assets by others. The assets may be tangible, as is the case of real estate and equipment, or intangible, as is the case with patents and trademarks.", "Workers employed in firms in information. The Information sector comprises establishments engaged in the following processes: (a) producing and distributing information and cultural products, (b) providing the means to transmit or distribute these products as well as data or communications, and (c) processing data. Included are the publishing industries, the motion picture and sound recording industries; the broadcasting industries, the telecommunications industries; Web search portals, data processing industries, and the information services industries.", "Workers employed in firms in manufacturing. The Manufacturing sector comprises establishments engaged in the mechanical, physical, or chemical transformation of materials, substances, or components into new products.", "Workers employed in firms in other services except public administration. The Other Services (Except Public Administration) sector comprises establishments engaged in providing services not specifically provided for elsewhere in the classification system. Establishments in this sector are primarily engaged in activities such as equipment and machinery repairing, promoting or administering religious activities, grantmaking, advocacy, and providing drycleaning and laundry services, personal care services, death care services, pet care services, photofinishing services, temporary parking services, and dating services. Private households that engage in employing workers on or about the premises in activities primarily concerned with the operation of the household are included in this sector.", "Workers employed in firms in public administration. The Public Administration sector consists of establishments of federal, state, and local government agencies that administer, oversee, and manage public programs and have executive, legislative, or judicial authority over other institutions within a given area. These agencies also set policy, create laws, adjudicate civil and criminal legal cases, provide for public safety and for national defense. In general, government establishments in the public administration sector oversee governmental programs and activities that are not performed by private establishments.", "Workers employed in firms in retail trade. The Retail Trade sector comprises establishments engaged in retailing merchandise, generally without transformation, and rendering services incidental to the sale of merchandise. The retailing process is the final step in the distribution of merchandise; retailers are, therefore, organized to sell merchandise in small quantities to the general public.", "Workers employed in firms in professional scientific, management, administrative and waste management services. The Administrative and Support and Waste Management and Remediation Services sector comprises establishments performing routine support activities for the day-to-day operations of other organizations. The establishments in this sector specialize in one or more of these support activities and provide these services to clients in a variety of industries and, in some cases, to households. Activities performed include office administration, hiring and placing of personnel, document preparation and similar clerical services, solicitation, collection, security and surveillance services, cleaning, and waste disposal services.", "Workers employed in firms in transportation, warehousing, and utilities. The Transportation and Warehousing sector includes industries providing transportation of passengers and cargo, warehousing and storage for goods, scenic and sightseeing transportation, and support activities related to modes of transportation. The modes of transportation are air, rail, water, road, and pipeline.", "Workers employed in firms in wholesale trade. The Wholesale Trade sector comprises establishments engaged in wholesaling merchandise, generally without transformation, and rendering services incidental to the sale of merchandise. The wholesaling process is an intermediate step in the distribution of merchandise. Wholesalers are organized to sell or arrange the purchase or sale of (a) goods for resale (i.e., goods sold to other wholesalers or retailers), (b) capital or durable nonconsumer goods, and (c) raw and intermediate materials and supplies used in production.", "Households with two female partners. An unmarried partner is a person age 15 years and over, who is not related to the householder, who shares living quarters, and who has an intimate relationship with the householder.", "Four car households. The number of households with four or more passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Gini Index. The Gini index, or index of income concentration, is a statistical measure of income inequality ranging from 0 to 1. A measure of 1 indicates perfect inequality, i.e., one household having all the income and rest having none. A measure of 0 indicates perfect equality, i.e., all households having an equal share of income.", "Population who completed a graduate or professional degree", "Population living in group quarters", "Population with high school degree, including GED. The number of people in a geographic area over the age of 25 who attained a high school degree or GED.", "Households on cash public assistance or receiving food stamps (SNAP)", "Students Enrolled in Grades 1 to 4. The total number of people in each geography currently enrolled in grades 1 through 4 inclusive.  This corresponds roughly to elementary school.", "Students Enrolled in Grades 5 to 8. The total number of people in each geography currently enrolled in grades 5 through 8 inclusive.  This corresponds roughly to middle school.", "Students Enrolled in Grades 9 to 12. The total number of people in each geography currently enrolled in grades 9 through 12 inclusive.  This corresponds roughly to high school.", "Students Enrolled in School. The total number of people in each geography currently enrolled at any level of school, from nursery or pre-school to advanced post-graduate education.  Only includes those over the age of 3.", "Students Enrolled as Undergraduate in College. The number of people in a geographic area who are enrolled in college at the undergraduate level. Enrollment refers to being registered or listed as a student in an educational program leading to a college degree. This may be a public school or college, a private school or college.", "Less than high school graduate. The number of people in a geographic area over the age of 25 who have not completed high school or any other advanced degree.", "Men age 45 to 64 who obtained an associate's degree", "Men age 45 to 64 who obtained a bachelor's degree", "Men age 45 to 64 who obtained a graduate or professional degree", "Men age 45 to 64 who attained less than a 9th grade education", "Men age 45 to 64 who attained between 9th and 12th grade, no diploma", "Men age 45 to 64 who completed high school or obtained GED", "Men age 45 to 64 who completed some college, no degree", "Men age 45 to 64 (\"middle aged\"). The male population between the age of fourty-five years to sixty-four years within the specified area.", "Households with two male partners. An unmarried partner is a person age 15 years and over, who is not related to the householder, who shares living quarters, and who has an intimate relationship with the householder.", "Civilian Employed Population in Management, Business, Science, and Arts Occupations. The number of employed civilians 16 years old and over in each geography in the Management, Business, Science, and Arts occupations. Occupation codes are based on the Standard Occupational Classification (SOC), published by the Executive Office of the President, Office of Management and Budget.", "Workers age 16 and over with no vehicle. All people in a geographic area over the age of 16 who do not own a car.", "Car-free households. The number of households without passenger cars, vans, and pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Not a U.S. Citizen Population. The number of people within each geography who indicated that they are not U.S. citizens.", "Workers employed in management business science and arts occupations", "Workers employed in natural resources, construction, and maintenance occupations", "Workers employed in production, transportation, and material moving", "Workers employed in sales and office occupations", "Workers employed in service occupations", "One car households. The number of households with one passenger car, van, , pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Two car households. The number of households with two passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Three car households. The number of households with one passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Population age 25 to 64. The number of people in each geography who are between the ages of 25 and 64. ", "Population for Whom Poverty Status Determined. The number of people in each geography who could be identified as either living in poverty or not.  This should be used as the denominator when calculating poverty rates, as it excludes people for whom it was not possible to determine poverty.", "Population 1 year and over. All people, male and female, child and adult, living in a given geographic area that are 1 year and older.", "Population 3 Years and Over. The total number of people in each geography age 3 years and over.  This denominator is mostly used to calculate rates of school enrollment.", "Income In The Past 12 Months Below Poverty Level. The number of people in a geographic area who are part of a family (which could be just them as an individual) determined to be \"in poverty\" following the Office of Management and Budget's Directive 14. (https://www.census.gov/hhes/povmeas/methodology/ombdir14.html)", "Civilian Employed Population in Sales and Office Occupations. The number of employed civilians 16 years old and over in each geography in the Sales and Office occupations. Occupation codes are based on the Standard Occupational Classification (SOC), published by the Executive Office of the President, Office of Management and Budget.", "Population who completed some college or obtained associate's degree. The number of people in a geographic area over the age of 25 who obtained an associate's degree, and did not complete a more advanced degree.", "Walked to Work. The number of workers age 16 years and over within a geographic area who primarily walked to work.  This would mean that of any way of getting to work, they travelled the most distance walking.", "Worked at Home. The count within a geographical area of workers over the age of 16 who worked at home.", "Workers over the Age of 16. The number of people in each geography who work. Workers include those employed at private for-profit companies, the self-employed, government workers and non-profit employees.", "Population Completed Associate's Degree. The number of people in a geographic area over the age of 25 who obtained a associate's degree, and did not complete a more advanced degree.", "Population Completed Bachelor's Degree. The number of people in a geographic area over the age of 25 who obtained a bachelor's degree, and did not complete a more advanced degree.", "Population Completed High School. The number of people in a geographic area over the age of 25 who completed high school, and did not complete a more advanced degree.", "Population completed less than one year of college, no degree. The number of people in a geographic area over the age of 25 who attended college for less than one year and no further.", "Population Completed Master's Degree. The number of people in a geographic area over the age of 25 who obtained a master's degree, but did not complete a more advanced degree.", "Population completed more than one year of college, no degree. The number of people in a geographic area over the age of 25 who attended college for more than one year but did not obtain a degree", "Population 25 Years and Over. The number of people in a geographic area who are over the age of 25.  This is used mostly as a denominator of educational attainment.", "Number of workers with a commute between 35 and 44 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 35 and 44 minutes. ", "Number of workers with a commute of over 60 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in over 60 minutes.", "Number of workers with less than 10 minute commute. The number of workers in a geographic area over the age of 16 who do not work from home and commute in less than 10 minutes.", "Workers age 16 and over who do not work from home. The number of workers in a geographic area over the age of 16 who do not work from home", "Hispanic of any race", "Population 5 Years and Over. The number of people in a geographic area who are over the age of 5.  This is primarily used as a denominator of measures of language spoken at home.", "Speaks only English at Home. The number of people in a geographic area over age 5 who speak only English at home.", "Speaks Spanish at Home. The number of people in a geographic area over age 5 who speak Spanish at home, possibly in addition to other languages.", "Speaks Spanish at Home, speaks English less than \"very well\"", "Population 15 Years and Over. The number of people in a geographic area who are over the age of 15.  This is used mostly as a denominator of marital status.", "Never Married. The number of people in a geographic area who have never been married.", "Currently married. The number of people in a geographic area who are currently married", "Married but separated. The number of people in a geographic area who are married but separated", "Widowed. The number of people in a geographic area who are widowed", "Divorced. The number of people in a geographic area who are divorced", null, "Population in Armed Forces. The number of people in each geography who are members of the U.S. Armed Forces (people on active duty with the United States Army, Air Force, Navy, Marine Corps, or Coast Guard).", "Population in Civilian Labor Force. The number of civilians 16 years and over in each geography who can be classified as either \"employed\" or \"unemployed\" below.", "Employed Population. The number of civilians 16 years old and over in each geography who either (1) were \"at work,\" that is, those who did any work at all during the reference week as paid employees, worked in their own business or profession, worked on their own farm, or worked 15 hours or more as unpaid workers on a family farm or in a family business; or (2) were \"with a job but not at work,\" that is, those who did not work during the reference week but had jobs or businesses from which they were temporarily absent due to illness, bad weather, industrial dispute, vacation, or other personal reasons. Excluded from the employed are people whose only activity consisted of work around the house or unpaid volunteer work for religious, charitable, and similar organizations; also excluded are all institutionalized people and people on active duty in the United States Armed Forces.", "", "Population Not in Labor Force. The number of people in each geography who are 16 years old and over who are not classified as members of the labor force. This category consists mainly of students, homemakers, retired workers, seasonal workers interviewed in an off season who were not looking for work, institutionalized people, and people doing only incidental unpaid family work (less than 15 hours during the reference week).", "Population age 16 and over. The number of people in each geography who are age 16 or over.", "Population in Labor Force. The number of people in each geography who are either in the civilian labor force or are members of the U.S. Armed Forces (people on active duty with the United States Army, Air Force, Navy, Marine Corps, or Coast Guard)."], "sample_rows": [{"geo_id": "44140", "nonfamily_households": 98496.0, "family_households": 171764.0, "median_year_structure_built": 1957.0, "rent_burden_not_computed": 5812.0, "rent_over_50_percent": 29148.0, "rent_40_to_50_percent": 6472.0, "rent_35_to_40_percent": 6092.0, "rent_30_to_35_percent": 9281.0, "rent_25_to_30_percent": 10827.0, "rent_20_to_25_percent": 10828.0, "rent_15_to_20_percent": 9031.0, "rent_10_to_15_percent": 8556.0, "rent_under_10_percent": 3243.0, "total_pop": 693141.0, "male_pop": 331085.0, "female_pop": 362056.0, "median_age": 38.7, "white_pop": 515202.0, "black_pop": 40385.0, "asian_pop": 17400.0, "hispanic_pop": 106942.0, "amerindian_pop": 1249.0, "other_race_pop": 1202.0, "two_or_more_races_pop": 10673.0, "not_hispanic_pop": 586199.0, "commuters_by_public_transportation": 6283.0, "households": 270260.0, "median_income": 49209.0, "income_per_capita": 25198.0, "housing_units": 288521.0, "vacant_housing_units": 18261.0, "vacant_housing_units_for_rent": 4509.0, "vacant_housing_units_for_sale": 1148.0, "median_rent": 685.0, "percent_income_spent_on_rent": 32.3, "owner_occupied_housing_units": 170970.0, "million_dollar_housing_units": 654.0, "mortgaged_housing_units": 116745.0, "families_with_young_children": 42935.0, "two_parent_families_with_young_children": 23251.0, "two_parents_in_labor_force_families_with_young_children": 16192.0, "two_parents_father_in_labor_force_families_with_young_children": 5825.0, "two_parents_mother_in_labor_force_families_with_young_children": 688.0, "two_parents_not_in_labor_force_families_with_young_children": 546.0, "one_parent_families_with_young_children": 19684.0, "father_one_parent_families_with_young_children": 2496.0, "father_in_labor_force_one_parent_families_with_young_children": 2293.0, "commute_10_14_mins": 48623.0, "commute_15_19_mins": 47704.0, "commute_20_24_mins": 50585.0, "commute_25_29_mins": 19727.0, "commute_30_34_mins": 36815.0, "commute_45_59_mins": 13886.0, "aggregate_travel_time_to_work": 6824695.0, "income_less_10000": 27582.0, "income_10000_14999": 15663.0, "income_15000_19999": 17173.0, "income_20000_24999": 13380.0, "income_25000_29999": 13411.0, "income_30000_34999": 12762.0, "income_35000_39999": 13136.0, "income_40000_44999": 11469.0, "income_45000_49999": 12169.0, "income_50000_59999": 20254.0, "income_60000_74999": 26883.0, "income_75000_99999": 34333.0, "income_100000_124999": 22954.0, "income_125000_149999": 12345.0, "income_150000_199999": 9864.0, "income_200000_or_more": 6882.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 780.0, "owner_occupied_housing_units_lower_value_quartile": 164600.0, "owner_occupied_housing_units_median_value": 220700.0, "owner_occupied_housing_units_upper_value_quartile": 299900.0, "married_households": 116728.0, "occupied_housing_units": 270260.0, "housing_units_renter_occupied": 99290.0, "dwellings_1_units_detached": 169170.0, "dwellings_1_units_attached": 11454.0, "dwellings_2_units": 29078.0, "dwellings_3_to_4_units": 23216.0, "dwellings_5_to_9_units": 21281.0, "dwellings_10_to_19_units": 9992.0, "dwellings_20_to_49_units": 9486.0, "dwellings_50_or_more_units": 10402.0, "mobile_homes": 4387.0, "housing_built_2005_or_later": 6978.0, "housing_built_2000_to_2004": 7084.0, "housing_built_1939_or_earlier": 91028.0, "male_under_5": 18762.0, "male_5_to_9": 20952.0, "male_10_to_14": 21523.0, "male_15_to_17": 14726.0, "male_18_to_19": 13575.0, "male_20": 8693.0, "male_21": 6924.0, "male_22_to_24": 14727.0, "male_25_to_29": 19857.0, "male_30_to_34": 17556.0, "male_35_to_39": 20295.0, "male_40_to_44": 19758.0, "male_45_to_49": 24749.0, "male_50_to_54": 25630.0, "male_55_to_59": 23329.0, "male_60_61": 8028.0, "male_62_64": 11724.0, "male_65_to_66": 5928.0, "male_67_to_69": 7437.0, "male_70_to_74": 8669.0, "male_75_to_79": 7222.0, "male_80_to_84": 5980.0, "male_85_and_over": 5041.0, "female_under_5": 18222.0, "female_5_to_9": 18854.0, "female_10_to_14": 22872.0, "female_15_to_17": 14541.0, "female_18_to_19": 15323.0, "female_20": 8610.0, "female_21": 7188.0, "female_22_to_24": 14656.0, "female_25_to_29": 20745.0, "female_30_to_34": 19191.0, "female_35_to_39": 19872.0, "female_40_to_44": 24347.0, "female_45_to_49": 26517.0, "female_50_to_54": 27435.0, "female_55_to_59": 24576.0, "female_60_to_61": 9262.0, "female_62_to_64": 13431.0, "female_65_to_66": 5442.0, "female_67_to_69": 8603.0, "female_70_to_74": 11849.0, "female_75_to_79": 11953.0, "female_80_to_84": 8799.0, "female_85_and_over": 9768.0, "white_including_hispanic": 569820.0, "black_including_hispanic": 44989.0, "amerindian_including_hispanic": 2619.0, "asian_including_hispanic": 17573.0, "commute_5_9_mins": 32281.0, "commute_35_39_mins": 6656.0, "commute_40_44_mins": 9019.0, "commute_60_89_mins": 10216.0, "commute_90_more_mins": 7218.0, "households_retirement_income": 47983.0, "asian_male_45_54": 746.0, "asian_male_55_64": 789.0, "black_male_45_54": 2922.0, "black_male_55_64": 1823.0, "hispanic_male_45_54": 5080.0, "hispanic_male_55_64": 2762.0, "white_male_45_54": 41075.0, "white_male_55_64": 37509.0, "bachelors_degree_2": 76307.0, "bachelors_degree_or_higher_25_64": 111389.0, "children": 150452.0, "children_in_single_female_hh": 51569.0, "commuters_by_bus": 6052.0, "commuters_by_car_truck_van": 274807.0, "commuters_by_carpool": 25197.0, "commuters_by_subway_or_elevated": 231.0, "commuters_drove_alone": 249610.0, "different_house_year_ago_different_city": 63121.0, "different_house_year_ago_same_city": 27001.0, "employed_agriculture_forestry_fishing_hunting_mining": 2466.0, "employed_arts_entertainment_recreation_accommodation_food": 25119.0, "employed_construction": 16991.0, "employed_education_health_social": 100758.0, "employed_finance_insurance_real_estate": 18392.0, "employed_information": 7446.0, "employed_manufacturing": 36648.0, "employed_other_services_not_public_admin": 14441.0, "employed_public_administration": 14832.0, "employed_retail_trade": 36471.0, "employed_science_management_admin_waste": 22640.0, "employed_transportation_warehousing_utilities": 14167.0, "employed_wholesale_trade": 6676.0, "female_female_households": 1659.0, "four_more_cars": 10735.0, "gini_index": 0.452, "graduate_professional_degree": 55662.0, "group_quarters": 41434.0, "high_school_including_ged": 134801.0, "households_public_asst_or_food_stamps": 50157.0, "in_grades_1_to_4": 32661.0, "in_grades_5_to_8": 35684.0, "in_grades_9_to_12": 36961.0, "in_school": 199728.0, "in_undergrad_college": 65866.0, "less_than_high_school_graduate": 61517.0, "male_45_64_associates_degree": 7451.0, "male_45_64_bachelors_degree": 15943.0, "male_45_64_graduate_degree": 10815.0, "male_45_64_less_than_9_grade": 3981.0, "male_45_64_grade_9_12": 6403.0, "male_45_64_high_school": 31406.0, "male_45_64_some_college": 17461.0, "male_45_to_64": 93460.0, "male_male_households": 653.0, "management_business_sci_arts_employed": 115291.0, "no_car": 10848.0, "no_cars": 32954.0, "not_us_citizen_pop": 31443.0, "occupation_management_arts": 115291.0, "occupation_natural_resources_construction_maintenance": 25570.0, "occupation_production_transportation_material": 40080.0, "occupation_sales_office": 73516.0, "occupation_services": 62590.0, "one_car": 99852.0, "two_cars": 97269.0, "three_cars": 29450.0, "pop_25_64": 356302.0, "pop_determined_poverty_status": 652365.0, "population_1_year_and_over": 686991.0, "population_3_years_over": 672768.0, "poverty": 102810.0, "sales_office_employed": 73516.0, "some_college_and_associates_degree": 124706.0, "walked_to_work": 10458.0, "worked_at_home": 16952.0, "workers_16_and_over": 310722.0, "associates_degree": 40550.0, "bachelors_degree": 76307.0, "high_school_diploma": 115976.0, "less_one_year_college": 27660.0, "masters_degree": 40029.0, "one_year_more_college": 56496.0, "pop_25_years_over": 452993.0, "commute_35_44_mins": 15675.0, "commute_60_more_mins": 17434.0, "commute_less_10_mins": 43321.0, "commuters_16_over": 293770.0, "hispanic_any_race": 106942.0, "pop_5_years_over": NaN, "speak_only_english_at_home": NaN, "speak_spanish_at_home": NaN, "speak_spanish_at_home_low_english": NaN, "pop_15_and_over": 571956.0, "pop_never_married": 215268.0, "pop_now_married": 248305.0, "pop_separated": 13612.0, "pop_widowed": 30510.0, "pop_divorced": 57133.0, "do_date": "2010", "armed_forces": NaN, "civilian_labor_force": NaN, "employed_pop": NaN, "unemployed_pop": NaN, "not_in_labor_force": NaN, "pop_16_over": NaN, "pop_in_labor_force": NaN}, {"geo_id": "25420", "nonfamily_households": 80045.0, "family_households": 141622.0, "median_year_structure_built": 1970.0, "rent_burden_not_computed": 4409.0, "rent_over_50_percent": 13207.0, "rent_40_to_50_percent": 6349.0, "rent_35_to_40_percent": 2271.0, "rent_30_to_35_percent": 4739.0, "rent_25_to_30_percent": 8811.0, "rent_20_to_25_percent": 8822.0, "rent_15_to_20_percent": 9335.0, "rent_10_to_15_percent": 5108.0, "rent_under_10_percent": 2933.0, "total_pop": 550076.0, "male_pop": 267838.0, "female_pop": 282238.0, "median_age": 39.7, "white_pop": 441758.0, "black_pop": 54202.0, "asian_pop": 14398.0, "hispanic_pop": 25958.0, "amerindian_pop": 573.0, "other_race_pop": 1201.0, "two_or_more_races_pop": 11483.0, "not_hispanic_pop": 524118.0, "commuters_by_public_transportation": 4944.0, "households": 221667.0, "median_income": 54009.0, "income_per_capita": 27953.0, "housing_units": 241035.0, "vacant_housing_units": 19368.0, "vacant_housing_units_for_rent": 6122.0, "vacant_housing_units_for_sale": 3092.0, "median_rent": 659.0, "percent_income_spent_on_rent": 27.6, "owner_occupied_housing_units": 155683.0, "million_dollar_housing_units": 962.0, "mortgaged_housing_units": 101693.0, "families_with_young_children": 37540.0, "two_parent_families_with_young_children": 23979.0, "two_parents_in_labor_force_families_with_young_children": 13949.0, "two_parents_father_in_labor_force_families_with_young_children": 8958.0, "two_parents_mother_in_labor_force_families_with_young_children": 759.0, "two_parents_not_in_labor_force_families_with_young_children": 313.0, "one_parent_families_with_young_children": 13561.0, "father_one_parent_families_with_young_children": 2910.0, "father_in_labor_force_one_parent_families_with_young_children": 2594.0, "commute_10_14_mins": 38902.0, "commute_15_19_mins": 50186.0, "commute_20_24_mins": 43321.0, "commute_25_29_mins": 15078.0, "commute_30_34_mins": 26832.0, "commute_45_59_mins": 14962.0, "aggregate_travel_time_to_work": 5711010.0, "income_less_10000": 12534.0, "income_10000_14999": 11546.0, "income_15000_19999": 9139.0, "income_20000_24999": 11525.0, "income_25000_29999": 11578.0, "income_30000_34999": 13174.0, "income_35000_39999": 10953.0, "income_40000_44999": 12007.0, "income_45000_49999": 10400.0, "income_50000_59999": 17661.0, "income_60000_74999": 25757.0, "income_75000_99999": 31178.0, "income_100000_124999": 19017.0, "income_125000_149999": 10181.0, "income_150000_199999": 9012.0, "income_200000_or_more": 6005.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 788.0, "owner_occupied_housing_units_lower_value_quartile": 116800.0, "owner_occupied_housing_units_median_value": 168100.0, "owner_occupied_housing_units_upper_value_quartile": 237100.0, "married_households": 103674.0, "occupied_housing_units": 221667.0, "housing_units_renter_occupied": 65984.0, "dwellings_1_units_detached": 133411.0, "dwellings_1_units_attached": 45089.0, "dwellings_2_units": 7642.0, "dwellings_3_to_4_units": 10668.0, "dwellings_5_to_9_units": 11205.0, "dwellings_10_to_19_units": 10208.0, "dwellings_20_to_49_units": 3305.0, "dwellings_50_or_more_units": 7853.0, "mobile_homes": 11654.0, "housing_built_2005_or_later": 10735.0, "housing_built_2000_to_2004": 15344.0, "housing_built_1939_or_earlier": 50022.0, "male_under_5": 16316.0, "male_5_to_9": 17318.0, "male_10_to_14": 17426.0, "male_15_to_17": 11549.0, "male_18_to_19": 7856.0, "male_20": 4678.0, "male_21": 3380.0, "male_22_to_24": 10053.0, "male_25_to_29": 18234.0, "male_30_to_34": 15847.0, "male_35_to_39": 16205.0, "male_40_to_44": 19112.0, "male_45_to_49": 19950.0, "male_50_to_54": 20754.0, "male_55_to_59": 18354.0, "male_60_61": 6665.0, "male_62_64": 10428.0, "male_65_to_66": 4518.0, "male_67_to_69": 6389.0, "male_70_to_74": 8130.0, "male_75_to_79": 5990.0, "male_80_to_84": 4924.0, "male_85_and_over": 3762.0, "female_under_5": 15369.0, "female_5_to_9": 16286.0, "female_10_to_14": 15970.0, "female_15_to_17": 11182.0, "female_18_to_19": 8398.0, "female_20": 6045.0, "female_21": 3016.0, "female_22_to_24": 10426.0, "female_25_to_29": 17292.0, "female_30_to_34": 16501.0, "female_35_to_39": 17891.0, "female_40_to_44": 17179.0, "female_45_to_49": 21384.0, "female_50_to_54": 21396.0, "female_55_to_59": 20615.0, "female_60_to_61": 6764.0, "female_62_to_64": 10031.0, "female_65_to_66": 5515.0, "female_67_to_69": 7258.0, "female_70_to_74": 9603.0, "female_75_to_79": 8726.0, "female_80_to_84": 6671.0, "female_85_and_over": 8720.0, "white_including_hispanic": 457326.0, "black_including_hispanic": 56348.0, "amerindian_including_hispanic": 610.0, "asian_including_hispanic": 14468.0, "commute_5_9_mins": 31999.0, "commute_35_39_mins": 8675.0, "commute_40_44_mins": 7261.0, "commute_60_89_mins": 6261.0, "commute_90_more_mins": 4878.0, "households_retirement_income": 54382.0, "asian_male_45_54": 847.0, "asian_male_55_64": 439.0, "black_male_45_54": 3650.0, "black_male_55_64": 2404.0, "hispanic_male_45_54": 930.0, "hispanic_male_55_64": 917.0, "white_male_45_54": 34869.0, "white_male_55_64": 31384.0, "bachelors_degree_2": 65818.0, "bachelors_degree_or_higher_25_64": 93565.0, "children": 121416.0, "children_in_single_female_hh": 33384.0, "commuters_by_bus": 4422.0, "commuters_by_car_truck_van": 242834.0, "commuters_by_carpool": 24011.0, "commuters_by_subway_or_elevated": 437.0, "commuters_drove_alone": 218823.0, "different_house_year_ago_different_city": 68621.0, "different_house_year_ago_same_city": 14162.0, "employed_agriculture_forestry_fishing_hunting_mining": 2881.0, "employed_arts_entertainment_recreation_accommodation_food": 21142.0, "employed_construction": 16608.0, "employed_education_health_social": 61787.0, "employed_finance_insurance_real_estate": 22266.0, "employed_information": 2961.0, "employed_manufacturing": 22475.0, "employed_other_services_not_public_admin": 10895.0, "employed_public_administration": 25738.0, "employed_retail_trade": 31340.0, "employed_science_management_admin_waste": 25759.0, "employed_transportation_warehousing_utilities": 16592.0, "employed_wholesale_trade": 9670.0, "female_female_households": 869.0, "four_more_cars": 13011.0, "gini_index": 0.417, "graduate_professional_degree": 41839.0, "group_quarters": 21542.0, "high_school_including_ged": 136692.0, "households_public_asst_or_food_stamps": 21075.0, "in_grades_1_to_4": 25691.0, "in_grades_5_to_8": 26497.0, "in_grades_9_to_12": 28645.0, "in_school": 137708.0, "in_undergrad_college": 33716.0, "less_than_high_school_graduate": 38648.0, "male_45_64_associates_degree": 5897.0, "male_45_64_bachelors_degree": 16274.0, "male_45_64_graduate_degree": 9169.0, "male_45_64_less_than_9_grade": 1774.0, "male_45_64_grade_9_12": 4761.0, "male_45_64_high_school": 27515.0, "male_45_64_some_college": 10761.0, "male_45_to_64": 76151.0, "male_male_households": 433.0, "management_business_sci_arts_employed": 99985.0, "no_car": 8797.0, "no_cars": 18654.0, "not_us_citizen_pop": 13861.0, "occupation_management_arts": 99985.0, "occupation_natural_resources_construction_maintenance": 22964.0, "occupation_production_transportation_material": 33445.0, "occupation_sales_office": 72675.0, "occupation_services": 41045.0, "one_car": 75654.0, "two_cars": 84216.0, "three_cars": 30132.0, "pop_25_64": 294602.0, "pop_determined_poverty_status": 529913.0, "population_1_year_and_over": 544480.0, "population_3_years_over": 531448.0, "poverty": 57718.0, "sales_office_employed": 72675.0, "some_college_and_associates_degree": 91811.0, "walked_to_work": 8848.0, "worked_at_home": 8524.0, "workers_16_and_over": 267548.0, "associates_degree": 28981.0, "bachelors_degree": 65818.0, "high_school_diploma": 122268.0, "less_one_year_college": 21944.0, "masters_degree": 30477.0, "one_year_more_college": 40886.0, "pop_25_years_over": 374808.0, "commute_35_44_mins": 15936.0, "commute_60_more_mins": 11139.0, "commute_less_10_mins": 42668.0, "commuters_16_over": 259024.0, "hispanic_any_race": NaN, "pop_5_years_over": NaN, "speak_only_english_at_home": NaN, "speak_spanish_at_home": NaN, "speak_spanish_at_home_low_english": NaN, "pop_15_and_over": 451391.0, "pop_never_married": 142685.0, "pop_now_married": 219309.0, "pop_separated": 11140.0, "pop_widowed": 30765.0, "pop_divorced": 44978.0, "do_date": "2010", "armed_forces": NaN, "civilian_labor_force": NaN, "employed_pop": NaN, "unemployed_pop": NaN, "not_in_labor_force": NaN, "pop_16_over": NaN, "pop_in_labor_force": NaN}, {"geo_id": "29540", "nonfamily_households": 57364.0, "family_households": 136664.0, "median_year_structure_built": 1973.0, "rent_burden_not_computed": 3198.0, "rent_over_50_percent": 15500.0, "rent_40_to_50_percent": 5118.0, "rent_35_to_40_percent": 3107.0, "rent_30_to_35_percent": 5039.0, "rent_25_to_30_percent": 7211.0, "rent_20_to_25_percent": 8357.0, "rent_15_to_20_percent": 6603.0, "rent_10_to_15_percent": 3434.0, "rent_under_10_percent": 1501.0, "total_pop": 520156.0, "male_pop": 254458.0, "female_pop": 265698.0, "median_age": 38.1, "white_pop": 440826.0, "black_pop": 16016.0, "asian_pop": 10340.0, "hispanic_pop": 45231.0, "amerindian_pop": 909.0, "other_race_pop": 820.0, "two_or_more_races_pop": 5916.0, "not_hispanic_pop": 474925.0, "commuters_by_public_transportation": 3358.0, "households": 194028.0, "median_income": 51740.0, "income_per_capita": 24871.0, "housing_units": 203175.0, "vacant_housing_units": 9147.0, "vacant_housing_units_for_rent": 2673.0, "vacant_housing_units_for_sale": 910.0, "median_rent": 680.0, "percent_income_spent_on_rent": 30.8, "owner_occupied_housing_units": 134960.0, "million_dollar_housing_units": 1622.0, "mortgaged_housing_units": 87394.0, "families_with_young_children": 41370.0, "two_parent_families_with_young_children": 30595.0, "two_parents_in_labor_force_families_with_young_children": 14760.0, "two_parents_father_in_labor_force_families_with_young_children": 14925.0, "two_parents_mother_in_labor_force_families_with_young_children": 736.0, "two_parents_not_in_labor_force_families_with_young_children": 174.0, "one_parent_families_with_young_children": 10775.0, "father_one_parent_families_with_young_children": 3028.0, "father_in_labor_force_one_parent_families_with_young_children": 2476.0, "commute_10_14_mins": 40341.0, "commute_15_19_mins": 39795.0, "commute_20_24_mins": 37624.0, "commute_25_29_mins": 12884.0, "commute_30_34_mins": 24448.0, "commute_45_59_mins": 10924.0, "aggregate_travel_time_to_work": 5154285.0, "income_less_10000": 9341.0, "income_10000_14999": 11213.0, "income_15000_19999": 11245.0, "income_20000_24999": 10699.0, "income_25000_29999": 9849.0, "income_30000_34999": 11240.0, "income_35000_39999": 9918.0, "income_40000_44999": 9610.0, "income_45000_49999": 9959.0, "income_50000_59999": 19184.0, "income_60000_74999": 21648.0, "income_75000_99999": 25878.0, "income_100000_124999": 14935.0, "income_125000_149999": 7772.0, "income_150000_199999": 6413.0, "income_200000_or_more": 5124.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 822.0, "owner_occupied_housing_units_lower_value_quartile": 133200.0, "owner_occupied_housing_units_median_value": 187400.0, "owner_occupied_housing_units_upper_value_quartile": 264200.0, "married_households": 114378.0, "occupied_housing_units": 194028.0, "housing_units_renter_occupied": 59068.0, "dwellings_1_units_detached": 111451.0, "dwellings_1_units_attached": 40287.0, "dwellings_2_units": 8966.0, "dwellings_3_to_4_units": 10919.0, "dwellings_5_to_9_units": 8594.0, "dwellings_10_to_19_units": 5168.0, "dwellings_20_to_49_units": 3485.0, "dwellings_50_or_more_units": 6370.0, "mobile_homes": 7935.0, "housing_built_2005_or_later": 9045.0, "housing_built_2000_to_2004": 12374.0, "housing_built_1939_or_earlier": 48748.0, "male_under_5": 17741.0, "male_5_to_9": 19245.0, "male_10_to_14": 17402.0, "male_15_to_17": 11101.0, "male_18_to_19": 7604.0, "male_20": 3675.0, "male_21": 3933.0, "male_22_to_24": 10439.0, "male_25_to_29": 15509.0, "male_30_to_34": 13717.0, "male_35_to_39": 16757.0, "male_40_to_44": 16412.0, "male_45_to_49": 17844.0, "male_50_to_54": 18709.0, "male_55_to_59": 17384.0, "male_60_61": 5752.0, "male_62_64": 7137.0, "male_65_to_66": 3954.0, "male_67_to_69": 6668.0, "male_70_to_74": 7714.0, "male_75_to_79": 6377.0, "male_80_to_84": 5009.0, "male_85_and_over": 4375.0, "female_under_5": 17073.0, "female_5_to_9": 17438.0, "female_10_to_14": 17429.0, "female_15_to_17": 11030.0, "female_18_to_19": 8162.0, "female_20": 4333.0, "female_21": 3992.0, "female_22_to_24": 9963.0, "female_25_to_29": 14349.0, "female_30_to_34": 15386.0, "female_35_to_39": 16149.0, "female_40_to_44": 15515.0, "female_45_to_49": 18917.0, "female_50_to_54": 19494.0, "female_55_to_59": 18205.0, "female_60_to_61": 4969.0, "female_62_to_64": 8373.0, "female_65_to_66": 4404.0, "female_67_to_69": 7888.0, "female_70_to_74": 8706.0, "female_75_to_79": 8140.0, "female_80_to_84": 7679.0, "female_85_and_over": 8104.0, "white_including_hispanic": 468862.0, "black_including_hispanic": 19638.0, "amerindian_including_hispanic": 1487.0, "asian_including_hispanic": 10517.0, "commute_5_9_mins": 30886.0, "commute_35_39_mins": 5461.0, "commute_40_44_mins": 5674.0, "commute_60_89_mins": 8267.0, "commute_90_more_mins": 5468.0, "households_retirement_income": 31220.0, "asian_male_45_54": 487.0, "asian_male_55_64": 468.0, "black_male_45_54": 1065.0, "black_male_55_64": 794.0, "hispanic_male_45_54": 2057.0, "hispanic_male_55_64": 1299.0, "white_male_45_54": 32754.0, "white_male_55_64": 27646.0, "bachelors_degree_2": 52772.0, "bachelors_degree_or_higher_25_64": 67385.0, "children": 128459.0, "children_in_single_female_hh": 21041.0, "commuters_by_bus": 2698.0, "commuters_by_car_truck_van": 215056.0, "commuters_by_carpool": 23090.0, "commuters_by_subway_or_elevated": 186.0, "commuters_drove_alone": 191966.0, "different_house_year_ago_different_city": 45337.0, "different_house_year_ago_same_city": 5201.0, "employed_agriculture_forestry_fishing_hunting_mining": 7495.0, "employed_arts_entertainment_recreation_accommodation_food": 17781.0, "employed_construction": 19926.0, "employed_education_health_social": 54987.0, "employed_finance_insurance_real_estate": 14267.0, "employed_information": 3604.0, "employed_manufacturing": 42392.0, "employed_other_services_not_public_admin": 12097.0, "employed_public_administration": 7127.0, "employed_retail_trade": 31478.0, "employed_science_management_admin_waste": 17670.0, "employed_transportation_warehousing_utilities": 10551.0, "employed_wholesale_trade": 10453.0, "female_female_households": 438.0, "four_more_cars": 11054.0, "gini_index": 0.424, "graduate_professional_degree": 29036.0, "group_quarters": 13393.0, "high_school_including_ged": 130661.0, "households_public_asst_or_food_stamps": 19828.0, "in_grades_1_to_4": 29448.0, "in_grades_5_to_8": 27206.0, "in_grades_9_to_12": 26144.0, "in_school": 129156.0, "in_undergrad_college": 27929.0, "less_than_high_school_graduate": 53983.0, "male_45_64_associates_degree": 3270.0, "male_45_64_bachelors_degree": 11096.0, "male_45_64_graduate_degree": 7368.0, "male_45_64_less_than_9_grade": 2897.0, "male_45_64_grade_9_12": 5572.0, "male_45_64_high_school": 26117.0, "male_45_64_some_college": 10506.0, "male_45_to_64": 66826.0, "male_male_households": 426.0, "management_business_sci_arts_employed": 79791.0, "no_car": 18629.0, "no_cars": 19956.0, "not_us_citizen_pop": 11906.0, "occupation_management_arts": 79791.0, "occupation_natural_resources_construction_maintenance": 26702.0, "occupation_production_transportation_material": 45078.0, "occupation_sales_office": 60527.0, "occupation_services": 37730.0, "one_car": 57563.0, "two_cars": 79229.0, "three_cars": 26226.0, "pop_25_64": 260578.0, "pop_determined_poverty_status": 506274.0, "population_1_year_and_over": 513773.0, "population_3_years_over": 499501.0, "poverty": 52918.0, "sales_office_employed": 60527.0, "some_college_and_associates_degree": 73144.0, "walked_to_work": 8872.0, "worked_at_home": 11843.0, "workers_16_and_over": 244202.0, "associates_degree": 19205.0, "bachelors_degree": 52772.0, "high_school_diploma": 115904.0, "less_one_year_college": 19927.0, "masters_degree": 20907.0, "one_year_more_college": 34012.0, "pop_25_years_over": 339596.0, "commute_35_44_mins": 11135.0, "commute_60_more_mins": 13735.0, "commute_less_10_mins": 41473.0, "commuters_16_over": 232359.0, "hispanic_any_race": NaN, "pop_5_years_over": NaN, "speak_only_english_at_home": NaN, "speak_spanish_at_home": NaN, "speak_spanish_at_home_low_english": NaN, "pop_15_and_over": 413828.0, "pop_never_married": 113893.0, "pop_now_married": 237154.0, "pop_separated": 6330.0, "pop_widowed": 24400.0, "pop_divorced": 29186.0, "do_date": "2010", "armed_forces": NaN, "civilian_labor_force": NaN, "employed_pop": NaN, "unemployed_pop": NaN, "not_in_labor_force": NaN, "pop_16_over": NaN, "pop_in_labor_force": NaN}, {"geo_id": "10740", "nonfamily_households": 122147.0, "family_households": 222653.0, "median_year_structure_built": 1982.0, "rent_burden_not_computed": 11106.0, "rent_over_50_percent": 24417.0, "rent_40_to_50_percent": 8630.0, "rent_35_to_40_percent": 7562.0, "rent_30_to_35_percent": 10121.0, "rent_25_to_30_percent": 13088.0, "rent_20_to_25_percent": 13023.0, "rent_15_to_20_percent": 13458.0, "rent_10_to_15_percent": 9550.0, "rent_under_10_percent": 3496.0, "total_pop": 892014.0, "male_pop": 437375.0, "female_pop": 454639.0, "median_age": 36.5, "white_pop": 374860.0, "black_pop": 20774.0, "asian_pop": 16197.0, "hispanic_pop": 418483.0, "amerindian_pop": 45900.0, "other_race_pop": 1642.0, "two_or_more_races_pop": 13921.0, "not_hispanic_pop": 473531.0, "commuters_by_public_transportation": 8257.0, "households": 344800.0, "median_income": 47383.0, "income_per_capita": 25044.0, "housing_units": 374674.0, "vacant_housing_units": 29874.0, "vacant_housing_units_for_rent": 7603.0, "vacant_housing_units_for_sale": 3399.0, "median_rent": 643.0, "percent_income_spent_on_rent": 29.6, "owner_occupied_housing_units": 230349.0, "million_dollar_housing_units": 2159.0, "mortgaged_housing_units": 162426.0, "families_with_young_children": 69664.0, "two_parent_families_with_young_children": 40591.0, "two_parents_in_labor_force_families_with_young_children": 20254.0, "two_parents_father_in_labor_force_families_with_young_children": 18790.0, "two_parents_mother_in_labor_force_families_with_young_children": 882.0, "two_parents_not_in_labor_force_families_with_young_children": 665.0, "one_parent_families_with_young_children": 29073.0, "father_one_parent_families_with_young_children": 5900.0, "father_in_labor_force_one_parent_families_with_young_children": 4936.0, "commute_10_14_mins": 58033.0, "commute_15_19_mins": 70345.0, "commute_20_24_mins": 69418.0, "commute_25_29_mins": 24280.0, "commute_30_34_mins": 57382.0, "commute_45_59_mins": 24153.0, "aggregate_travel_time_to_work": 9040190.0, "income_less_10000": 29066.0, "income_10000_14999": 18610.0, "income_15000_19999": 20051.0, "income_20000_24999": 20854.0, "income_25000_29999": 17631.0, "income_30000_34999": 20675.0, "income_35000_39999": 17654.0, "income_40000_44999": 20009.0, "income_45000_49999": 14532.0, "income_50000_59999": 26644.0, "income_60000_74999": 36487.0, "income_75000_99999": 41191.0, "income_100000_124999": 23337.0, "income_125000_149999": 15040.0, "income_150000_199999": 13178.0, "income_200000_or_more": 9841.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 748.0, "owner_occupied_housing_units_lower_value_quartile": 127200.0, "owner_occupied_housing_units_median_value": 183300.0, "owner_occupied_housing_units_upper_value_quartile": 267500.0, "married_households": 153687.0, "occupied_housing_units": 344800.0, "housing_units_renter_occupied": 114451.0, "dwellings_1_units_detached": 251654.0, "dwellings_1_units_attached": 15597.0, "dwellings_2_units": 4393.0, "dwellings_3_to_4_units": 19212.0, "dwellings_5_to_9_units": 12253.0, "dwellings_10_to_19_units": 15094.0, "dwellings_20_to_49_units": 9557.0, "dwellings_50_or_more_units": 12910.0, "mobile_homes": 33513.0, "housing_built_2005_or_later": 28721.0, "housing_built_2000_to_2004": 43471.0, "housing_built_1939_or_earlier": 14593.0, "male_under_5": 30808.0, "male_5_to_9": 31240.0, "male_10_to_14": 30351.0, "male_15_to_17": 17617.0, "male_18_to_19": 12780.0, "male_20": 6222.0, "male_21": 7095.0, "male_22_to_24": 18145.0, "male_25_to_29": 32417.0, "male_30_to_34": 29940.0, "male_35_to_39": 30014.0, "male_40_to_44": 27477.0, "male_45_to_49": 31292.0, "male_50_to_54": 30615.0, "male_55_to_59": 26680.0, "male_60_61": 10457.0, "male_62_64": 15562.0, "male_65_to_66": 6260.0, "male_67_to_69": 10908.0, "male_70_to_74": 11940.0, "male_75_to_79": 7859.0, "male_80_to_84": 7249.0, "male_85_and_over": 4447.0, "female_under_5": 30075.0, "female_5_to_9": 29553.0, "female_10_to_14": 29944.0, "female_15_to_17": 18493.0, "female_18_to_19": 12294.0, "female_20": 6043.0, "female_21": 5845.0, "female_22_to_24": 18799.0, "female_25_to_29": 31421.0, "female_30_to_34": 29273.0, "female_35_to_39": 30071.0, "female_40_to_44": 27194.0, "female_45_to_49": 32333.0, "female_50_to_54": 33445.0, "female_55_to_59": 29346.0, "female_60_to_61": 11780.0, "female_62_to_64": 16758.0, "female_65_to_66": 7483.0, "female_67_to_69": 10667.0, "female_70_to_74": 14685.0, "female_75_to_79": 10261.0, "female_80_to_84": 9275.0, "female_85_and_over": 9601.0, "white_including_hispanic": 624193.0, "black_including_hispanic": 23454.0, "amerindian_including_hispanic": 50703.0, "asian_including_hispanic": 17262.0, "commute_5_9_mins": 30891.0, "commute_35_39_mins": 7197.0, "commute_40_44_mins": 12511.0, "commute_60_89_mins": 10302.0, "commute_90_more_mins": 7780.0, "households_retirement_income": 66008.0, "asian_male_45_54": 1312.0, "asian_male_55_64": 820.0, "black_male_45_54": 1475.0, "black_male_55_64": 1552.0, "hispanic_male_45_54": 25240.0, "hispanic_male_55_64": 17581.0, "white_male_45_54": 31242.0, "white_male_55_64": 30386.0, "bachelors_degree_2": 97544.0, "bachelors_degree_or_higher_25_64": 141022.0, "children": 218081.0, "children_in_single_female_hh": 60890.0, "commuters_by_bus": 6326.0, "commuters_by_car_truck_van": 357453.0, "commuters_by_carpool": 37436.0, "commuters_by_subway_or_elevated": 133.0, "commuters_drove_alone": 320017.0, "different_house_year_ago_different_city": 65032.0, "different_house_year_ago_same_city": 61600.0, "employed_agriculture_forestry_fishing_hunting_mining": 4042.0, "employed_arts_entertainment_recreation_accommodation_food": 42650.0, "employed_construction": 30147.0, "employed_education_health_social": 98547.0, "employed_finance_insurance_real_estate": 18709.0, "employed_information": 8278.0, "employed_manufacturing": 21013.0, "employed_other_services_not_public_admin": 16513.0, "employed_public_administration": 35006.0, "employed_retail_trade": 50746.0, "employed_science_management_admin_waste": 51480.0, "employed_transportation_warehousing_utilities": 15876.0, "employed_wholesale_trade": 9629.0, "female_female_households": 2054.0, "four_more_cars": 23462.0, "gini_index": 0.456, "graduate_professional_degree": 74209.0, "group_quarters": 11136.0, "high_school_including_ged": 144541.0, "households_public_asst_or_food_stamps": 43291.0, "in_grades_1_to_4": 48853.0, "in_grades_5_to_8": 47932.0, "in_grades_9_to_12": 49243.0, "in_school": 243728.0, "in_undergrad_college": 62342.0, "less_than_high_school_graduate": 77274.0, "male_45_64_associates_degree": 7187.0, "male_45_64_bachelors_degree": 19741.0, "male_45_64_graduate_degree": 17606.0, "male_45_64_less_than_9_grade": 6863.0, "male_45_64_grade_9_12": 7587.0, "male_45_64_high_school": 27623.0, "male_45_64_some_college": 27999.0, "male_45_to_64": 114606.0, "male_male_households": 919.0, "management_business_sci_arts_employed": 160094.0, "no_car": 7627.0, "no_cars": 16484.0, "not_us_citizen_pop": 59398.0, "occupation_management_arts": 160094.0, "occupation_natural_resources_construction_maintenance": 36881.0, "occupation_production_transportation_material": 32124.0, "occupation_sales_office": 99092.0, "occupation_services": 74445.0, "one_car": 120481.0, "two_cars": 136269.0, "three_cars": 48104.0, "pop_25_64": 476075.0, "pop_determined_poverty_status": 881703.0, "population_1_year_and_over": 881171.0, "population_3_years_over": 856263.0, "poverty": 152091.0, "sales_office_employed": 99092.0, "some_college_and_associates_degree": 193142.0, "walked_to_work": 6239.0, "worked_at_home": 17677.0, "workers_16_and_over": 397604.0, "associates_degree": 40185.0, "bachelors_degree": 97544.0, "high_school_diploma": 120196.0, "less_one_year_college": 37494.0, "masters_degree": 48957.0, "one_year_more_college": 115463.0, "pop_25_years_over": 586710.0, "commute_35_44_mins": 19708.0, "commute_60_more_mins": 18082.0, "commute_less_10_mins": 38526.0, "commuters_16_over": 379927.0, "hispanic_any_race": 418483.0, "pop_5_years_over": NaN, "speak_only_english_at_home": NaN, "speak_spanish_at_home": NaN, "speak_spanish_at_home_low_english": NaN, "pop_15_and_over": 710043.0, "pop_never_married": 235759.0, "pop_now_married": 333072.0, "pop_separated": 13229.0, "pop_widowed": 31662.0, "pop_divorced": 84762.0, "do_date": "2010", "armed_forces": NaN, "civilian_labor_force": NaN, "employed_pop": NaN, "unemployed_pop": NaN, "not_in_labor_force": NaN, "pop_16_over": NaN, "pop_in_labor_force": NaN}, {"geo_id": "46700", "nonfamily_households": 41076.0, "family_households": 99126.0, "median_year_structure_built": 1979.0, "rent_burden_not_computed": 2416.0, "rent_over_50_percent": 14707.0, "rent_40_to_50_percent": 4816.0, "rent_35_to_40_percent": 3641.0, "rent_30_to_35_percent": 4715.0, "rent_25_to_30_percent": 5850.0, "rent_20_to_25_percent": 7886.0, "rent_15_to_20_percent": 5037.0, "rent_10_to_15_percent": 2699.0, "rent_under_10_percent": 820.0, "total_pop": 414305.0, "male_pop": 206773.0, "female_pop": 207532.0, "median_age": 36.6, "white_pop": 167985.0, "black_pop": 61181.0, "asian_pop": 59395.0, "hispanic_pop": 100019.0, "amerindian_pop": 1777.0, "other_race_pop": 1190.0, "two_or_more_races_pop": 18955.0, "not_hispanic_pop": 314286.0, "commuters_by_public_transportation": 4395.0, "households": 140202.0, "median_income": 63384.0, "income_per_capita": 26842.0, "housing_units": 152784.0, "vacant_housing_units": 12582.0, "vacant_housing_units_for_rent": 4719.0, "vacant_housing_units_for_sale": 1549.0, "median_rent": 1096.0, "percent_income_spent_on_rent": 33.0, "owner_occupied_housing_units": 87615.0, "million_dollar_housing_units": 1305.0, "mortgaged_housing_units": 67552.0, "families_with_young_children": 32053.0, "two_parent_families_with_young_children": 19294.0, "two_parents_in_labor_force_families_with_young_children": 11989.0, "two_parents_father_in_labor_force_families_with_young_children": 6461.0, "two_parents_mother_in_labor_force_families_with_young_children": 776.0, "two_parents_not_in_labor_force_families_with_young_children": 68.0, "one_parent_families_with_young_children": 12759.0, "father_one_parent_families_with_young_children": 3091.0, "father_in_labor_force_one_parent_families_with_young_children": 2545.0, "commute_10_14_mins": 27376.0, "commute_15_19_mins": 23631.0, "commute_20_24_mins": 21220.0, "commute_25_29_mins": 7353.0, "commute_30_34_mins": 19998.0, "commute_45_59_mins": 15658.0, "aggregate_travel_time_to_work": 4926990.0, "income_less_10000": 7039.0, "income_10000_14999": 6142.0, "income_15000_19999": 4521.0, "income_20000_24999": 5999.0, "income_25000_29999": 6350.0, "income_30000_34999": 5601.0, "income_35000_39999": 4719.0, "income_40000_44999": 5928.0, "income_45000_49999": 5603.0, "income_50000_59999": 12991.0, "income_60000_74999": 16884.0, "income_75000_99999": 19938.0, "income_100000_124999": 14840.0, "income_125000_149999": 9078.0, "income_150000_199999": 9700.0, "income_200000_or_more": 4869.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 1201.0, "owner_occupied_housing_units_lower_value_quartile": 176500.0, "owner_occupied_housing_units_median_value": 262600.0, "owner_occupied_housing_units_upper_value_quartile": 381000.0, "married_households": 69249.0, "occupied_housing_units": 140202.0, "housing_units_renter_occupied": 52587.0, "dwellings_1_units_detached": 107977.0, "dwellings_1_units_attached": 6264.0, "dwellings_2_units": 4075.0, "dwellings_3_to_4_units": 8048.0, "dwellings_5_to_9_units": 7586.0, "dwellings_10_to_19_units": 5316.0, "dwellings_20_to_49_units": 2590.0, "dwellings_50_or_more_units": 6706.0, "mobile_homes": 4124.0, "housing_built_2005_or_later": 8124.0, "housing_built_2000_to_2004": 13482.0, "housing_built_1939_or_earlier": 10028.0, "male_under_5": 13713.0, "male_5_to_9": 14273.0, "male_10_to_14": 14494.0, "male_15_to_17": 9605.0, "male_18_to_19": 6033.0, "male_20": 2722.0, "male_21": 2666.0, "male_22_to_24": 10006.0, "male_25_to_29": 14542.0, "male_30_to_34": 13787.0, "male_35_to_39": 13276.0, "male_40_to_44": 14210.0, "male_45_to_49": 15987.0, "male_50_to_54": 15671.0, "male_55_to_59": 12443.0, "male_60_61": 6312.0, "male_62_64": 6434.0, "male_65_to_66": 2901.0, "male_67_to_69": 3369.0, "male_70_to_74": 6103.0, "male_75_to_79": 4245.0, "male_80_to_84": 2249.0, "male_85_and_over": 1732.0, "female_under_5": 13019.0, "female_5_to_9": 14583.0, "female_10_to_14": 12630.0, "female_15_to_17": 9162.0, "female_18_to_19": 4969.0, "female_20": 2763.0, "female_21": 2247.0, "female_22_to_24": 8999.0, "female_25_to_29": 13843.0, "female_30_to_34": 13145.0, "female_35_to_39": 13431.0, "female_40_to_44": 13544.0, "female_45_to_49": 15854.0, "female_50_to_54": 16219.0, "female_55_to_59": 14200.0, "female_60_to_61": 4712.0, "female_62_to_64": 7293.0, "female_65_to_66": 3500.0, "female_67_to_69": 5134.0, "female_70_to_74": 5722.0, "female_75_to_79": 5439.0, "female_80_to_84": 3739.0, "female_85_and_over": 3385.0, "white_including_hispanic": 218365.0, "black_including_hispanic": 62724.0, "amerindian_including_hispanic": 2332.0, "asian_including_hispanic": 60726.0, "commute_5_9_mins": 19302.0, "commute_35_39_mins": 4768.0, "commute_40_44_mins": 6883.0, "commute_60_89_mins": 14188.0, "commute_90_more_mins": 8359.0, "households_retirement_income": 32811.0, "asian_male_45_54": 3804.0, "asian_male_55_64": 3775.0, "black_male_45_54": 5113.0, "black_male_55_64": 3801.0, "hispanic_male_45_54": 5612.0, "hispanic_male_55_64": 3108.0, "white_male_45_54": 15455.0, "white_male_55_64": 13550.0, "bachelors_degree_2": 41585.0, "bachelors_degree_or_higher_25_64": 51014.0, "children": 101479.0, "children_in_single_female_hh": 28549.0, "commuters_by_bus": 2616.0, "commuters_by_car_truck_van": 162460.0, "commuters_by_carpool": 25732.0, "commuters_by_subway_or_elevated": 1064.0, "commuters_drove_alone": 136728.0, "different_house_year_ago_different_city": 35533.0, "different_house_year_ago_same_city": 36875.0, "employed_agriculture_forestry_fishing_hunting_mining": 3036.0, "employed_arts_entertainment_recreation_accommodation_food": 20652.0, "employed_construction": 10186.0, "employed_education_health_social": 39419.0, "employed_finance_insurance_real_estate": 13646.0, "employed_information": 3880.0, "employed_manufacturing": 17511.0, "employed_other_services_not_public_admin": 7121.0, "employed_public_administration": 15288.0, "employed_retail_trade": 18728.0, "employed_science_management_admin_waste": 14521.0, "employed_transportation_warehousing_utilities": 11855.0, "employed_wholesale_trade": 5070.0, "female_female_households": 670.0, "four_more_cars": 13357.0, "gini_index": 0.405, "graduate_professional_degree": 20540.0, "group_quarters": 12622.0, "high_school_including_ged": 68128.0, "households_public_asst_or_food_stamps": 12369.0, "in_grades_1_to_4": 23015.0, "in_grades_5_to_8": 22683.0, "in_grades_9_to_12": 24830.0, "in_school": 114116.0, "in_undergrad_college": 27236.0, "less_than_high_school_graduate": 37202.0, "male_45_64_associates_degree": 6340.0, "male_45_64_bachelors_degree": 7587.0, "male_45_64_graduate_degree": 5345.0, "male_45_64_less_than_9_grade": 3596.0, "male_45_64_grade_9_12": 3037.0, "male_45_64_high_school": 13096.0, "male_45_64_some_college": 17846.0, "male_45_to_64": 56847.0, "male_male_households": 306.0, "management_business_sci_arts_employed": 61646.0, "no_car": 3390.0, "no_cars": 7743.0, "not_us_citizen_pop": 41853.0, "occupation_management_arts": 61646.0, "occupation_natural_resources_construction_maintenance": 17075.0, "occupation_production_transportation_material": 22710.0, "occupation_sales_office": 45142.0, "occupation_services": 34340.0, "one_car": 38086.0, "two_cars": 53709.0, "three_cars": 27307.0, "pop_25_64": 224903.0, "pop_determined_poverty_status": 401202.0, "population_1_year_and_over": 408243.0, "population_3_years_over": 398438.0, "poverty": 49701.0, "sales_office_employed": 45142.0, "some_college_and_associates_degree": 104966.0, "walked_to_work": 3657.0, "worked_at_home": 6479.0, "workers_16_and_over": 178631.0, "associates_degree": 28101.0, "bachelors_degree": 41585.0, "high_school_diploma": 59465.0, "less_one_year_college": 20662.0, "masters_degree": 12506.0, "one_year_more_college": 56203.0, "pop_25_years_over": 272421.0, "commute_35_44_mins": 11651.0, "commute_60_more_mins": 22547.0, "commute_less_10_mins": 22718.0, "commuters_16_over": 172152.0, "hispanic_any_race": NaN, "pop_5_years_over": NaN, "speak_only_english_at_home": NaN, "speak_spanish_at_home": NaN, "speak_spanish_at_home_low_english": NaN, "pop_15_and_over": 331593.0, "pop_never_married": 108502.0, "pop_now_married": 161926.0, "pop_separated": 7879.0, "pop_widowed": 13442.0, "pop_divorced": 28291.0, "do_date": "2010", "armed_forces": NaN, "civilian_labor_force": NaN, "employed_pop": NaN, "unemployed_pop": NaN, "not_in_labor_force": NaN, "pop_16_over": NaN, "pop_in_labor_force": NaN}]}