{"table_name": "cbsa_2010_3yr", "table_fullname": "bigquery-public-data.census_bureau_acs.cbsa_2010_3yr", "column_names": ["geo_id", "nonfamily_households", "family_households", "median_year_structure_built", "rent_burden_not_computed", "rent_over_50_percent", "rent_40_to_50_percent", "rent_35_to_40_percent", "rent_30_to_35_percent", "rent_25_to_30_percent", "rent_20_to_25_percent", "rent_15_to_20_percent", "rent_10_to_15_percent", "rent_under_10_percent", "total_pop", "male_pop", "female_pop", "median_age", "white_pop", "black_pop", "asian_pop", "hispanic_pop", "amerindian_pop", "other_race_pop", "two_or_more_races_pop", "not_hispanic_pop", "commuters_by_public_transportation", "households", "median_income", "income_per_capita", "housing_units", "vacant_housing_units", "vacant_housing_units_for_rent", "vacant_housing_units_for_sale", "median_rent", "percent_income_spent_on_rent", "owner_occupied_housing_units", "million_dollar_housing_units", "mortgaged_housing_units", "families_with_young_children", "two_parent_families_with_young_children", "two_parents_in_labor_force_families_with_young_children", "two_parents_father_in_labor_force_families_with_young_children", "two_parents_mother_in_labor_force_families_with_young_children", "two_parents_not_in_labor_force_families_with_young_children", "one_parent_families_with_young_children", "father_one_parent_families_with_young_children", "father_in_labor_force_one_parent_families_with_young_children", "commute_10_14_mins", "commute_15_19_mins", "commute_20_24_mins", "commute_25_29_mins", "commute_30_34_mins", "commute_45_59_mins", "aggregate_travel_time_to_work", "income_less_10000", "income_10000_14999", "income_15000_19999", "income_20000_24999", "income_25000_29999", "income_30000_34999", "income_35000_39999", "income_40000_44999", "income_45000_49999", "income_50000_59999", "income_60000_74999", "income_75000_99999", "income_100000_124999", "income_125000_149999", "income_150000_199999", "income_200000_or_more", "renter_occupied_housing_units_paying_cash_median_gross_rent", "owner_occupied_housing_units_lower_value_quartile", "owner_occupied_housing_units_median_value", "owner_occupied_housing_units_upper_value_quartile", "married_households", "occupied_housing_units", "housing_units_renter_occupied", "dwellings_1_units_detached", "dwellings_1_units_attached", "dwellings_2_units", "dwellings_3_to_4_units", "dwellings_5_to_9_units", "dwellings_10_to_19_units", "dwellings_20_to_49_units", "dwellings_50_or_more_units", "mobile_homes", "housing_built_2005_or_later", "housing_built_2000_to_2004", "housing_built_1939_or_earlier", "male_under_5", "male_5_to_9", "male_10_to_14", "male_15_to_17", "male_18_to_19", "male_20", "male_21", "male_22_to_24", "male_25_to_29", "male_30_to_34", "male_35_to_39", "male_40_to_44", "male_45_to_49", "male_50_to_54", "male_55_to_59", "male_60_61", "male_62_64", "male_65_to_66", "male_67_to_69", "male_70_to_74", "male_75_to_79", "male_80_to_84", "male_85_and_over", "female_under_5", "female_5_to_9", "female_10_to_14", "female_15_to_17", "female_18_to_19", "female_20", "female_21", "female_22_to_24", "female_25_to_29", "female_30_to_34", "female_35_to_39", "female_40_to_44", "female_45_to_49", "female_50_to_54", "female_55_to_59", "female_60_to_61", "female_62_to_64", "female_65_to_66", "female_67_to_69", "female_70_to_74", "female_75_to_79", "female_80_to_84", "female_85_and_over", "white_including_hispanic", "black_including_hispanic", "amerindian_including_hispanic", "asian_including_hispanic", "commute_5_9_mins", "commute_35_39_mins", "commute_40_44_mins", "commute_60_89_mins", "commute_90_more_mins", "households_retirement_income", "asian_male_45_54", "asian_male_55_64", "black_male_45_54", "black_male_55_64", "hispanic_male_45_54", "hispanic_male_55_64", "white_male_45_54", "white_male_55_64", "bachelors_degree_2", "bachelors_degree_or_higher_25_64", "children", "children_in_single_female_hh", "commuters_by_bus", "commuters_by_car_truck_van", "commuters_by_carpool", "commuters_by_subway_or_elevated", "commuters_drove_alone", "different_house_year_ago_different_city", "different_house_year_ago_same_city", "employed_agriculture_forestry_fishing_hunting_mining", "employed_arts_entertainment_recreation_accommodation_food", "employed_construction", "employed_education_health_social", "employed_finance_insurance_real_estate", "employed_information", "employed_manufacturing", "employed_other_services_not_public_admin", "employed_public_administration", "employed_retail_trade", "employed_science_management_admin_waste", "employed_transportation_warehousing_utilities", "employed_wholesale_trade", "female_female_households", "four_more_cars", "gini_index", "graduate_professional_degree", "group_quarters", "high_school_including_ged", "households_public_asst_or_food_stamps", "in_grades_1_to_4", "in_grades_5_to_8", "in_grades_9_to_12", "in_school", "in_undergrad_college", "less_than_high_school_graduate", "male_45_64_associates_degree", "male_45_64_bachelors_degree", "male_45_64_graduate_degree", "male_45_64_less_than_9_grade", "male_45_64_grade_9_12", "male_45_64_high_school", "male_45_64_some_college", "male_45_to_64", "male_male_households", "management_business_sci_arts_employed", "no_car", "no_cars", "not_us_citizen_pop", "occupation_management_arts", "occupation_natural_resources_construction_maintenance", "occupation_production_transportation_material", "occupation_sales_office", "occupation_services", "one_car", "two_cars", "three_cars", "pop_25_64", "pop_determined_poverty_status", "population_1_year_and_over", "population_3_years_over", "poverty", "sales_office_employed", "some_college_and_associates_degree", "walked_to_work", "worked_at_home", "workers_16_and_over", "associates_degree", "bachelors_degree", "high_school_diploma", "less_one_year_college", "masters_degree", "one_year_more_college", "pop_25_years_over", "commute_35_44_mins", "commute_60_more_mins", "commute_less_10_mins", "commuters_16_over", "hispanic_any_race", "pop_5_years_over", "speak_only_english_at_home", "speak_spanish_at_home", "speak_spanish_at_home_low_english", "pop_15_and_over", "pop_never_married", "pop_now_married", "pop_separated", "pop_widowed", "pop_divorced", "do_date", "armed_forces", "civilian_labor_force", "employed_pop", "unemployed_pop", "not_in_labor_force", "pop_16_over", "pop_in_labor_force"], "column_types": ["STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64"], "nested_column_names": ["geo_id", "nonfamily_households", "family_households", "median_year_structure_built", "rent_burden_not_computed", "rent_over_50_percent", "rent_40_to_50_percent", "rent_35_to_40_percent", "rent_30_to_35_percent", "rent_25_to_30_percent", "rent_20_to_25_percent", "rent_15_to_20_percent", "rent_10_to_15_percent", "rent_under_10_percent", "total_pop", "male_pop", "female_pop", "median_age", "white_pop", "black_pop", "asian_pop", "hispanic_pop", "amerindian_pop", "other_race_pop", "two_or_more_races_pop", "not_hispanic_pop", "commuters_by_public_transportation", "households", "median_income", "income_per_capita", "housing_units", "vacant_housing_units", "vacant_housing_units_for_rent", "vacant_housing_units_for_sale", "median_rent", "percent_income_spent_on_rent", "owner_occupied_housing_units", "million_dollar_housing_units", "mortgaged_housing_units", "families_with_young_children", "two_parent_families_with_young_children", "two_parents_in_labor_force_families_with_young_children", "two_parents_father_in_labor_force_families_with_young_children", "two_parents_mother_in_labor_force_families_with_young_children", "two_parents_not_in_labor_force_families_with_young_children", "one_parent_families_with_young_children", "father_one_parent_families_with_young_children", "father_in_labor_force_one_parent_families_with_young_children", "commute_10_14_mins", "commute_15_19_mins", "commute_20_24_mins", "commute_25_29_mins", "commute_30_34_mins", "commute_45_59_mins", "aggregate_travel_time_to_work", "income_less_10000", "income_10000_14999", "income_15000_19999", "income_20000_24999", "income_25000_29999", "income_30000_34999", "income_35000_39999", "income_40000_44999", "income_45000_49999", "income_50000_59999", "income_60000_74999", "income_75000_99999", "income_100000_124999", "income_125000_149999", "income_150000_199999", "income_200000_or_more", "renter_occupied_housing_units_paying_cash_median_gross_rent", "owner_occupied_housing_units_lower_value_quartile", "owner_occupied_housing_units_median_value", "owner_occupied_housing_units_upper_value_quartile", "married_households", "occupied_housing_units", "housing_units_renter_occupied", "dwellings_1_units_detached", "dwellings_1_units_attached", "dwellings_2_units", "dwellings_3_to_4_units", "dwellings_5_to_9_units", "dwellings_10_to_19_units", "dwellings_20_to_49_units", "dwellings_50_or_more_units", "mobile_homes", "housing_built_2005_or_later", "housing_built_2000_to_2004", "housing_built_1939_or_earlier", "male_under_5", "male_5_to_9", "male_10_to_14", "male_15_to_17", "male_18_to_19", "male_20", "male_21", "male_22_to_24", "male_25_to_29", "male_30_to_34", "male_35_to_39", "male_40_to_44", "male_45_to_49", "male_50_to_54", "male_55_to_59", "male_60_61", "male_62_64", "male_65_to_66", "male_67_to_69", "male_70_to_74", "male_75_to_79", "male_80_to_84", "male_85_and_over", "female_under_5", "female_5_to_9", "female_10_to_14", "female_15_to_17", "female_18_to_19", "female_20", "female_21", "female_22_to_24", "female_25_to_29", "female_30_to_34", "female_35_to_39", "female_40_to_44", "female_45_to_49", "female_50_to_54", "female_55_to_59", "female_60_to_61", "female_62_to_64", "female_65_to_66", "female_67_to_69", "female_70_to_74", "female_75_to_79", "female_80_to_84", "female_85_and_over", "white_including_hispanic", "black_including_hispanic", "amerindian_including_hispanic", "asian_including_hispanic", "commute_5_9_mins", "commute_35_39_mins", "commute_40_44_mins", "commute_60_89_mins", "commute_90_more_mins", "households_retirement_income", "asian_male_45_54", "asian_male_55_64", "black_male_45_54", "black_male_55_64", "hispanic_male_45_54", "hispanic_male_55_64", "white_male_45_54", "white_male_55_64", "bachelors_degree_2", "bachelors_degree_or_higher_25_64", "children", "children_in_single_female_hh", "commuters_by_bus", "commuters_by_car_truck_van", "commuters_by_carpool", "commuters_by_subway_or_elevated", "commuters_drove_alone", "different_house_year_ago_different_city", "different_house_year_ago_same_city", "employed_agriculture_forestry_fishing_hunting_mining", "employed_arts_entertainment_recreation_accommodation_food", "employed_construction", "employed_education_health_social", "employed_finance_insurance_real_estate", "employed_information", "employed_manufacturing", "employed_other_services_not_public_admin", "employed_public_administration", "employed_retail_trade", "employed_science_management_admin_waste", "employed_transportation_warehousing_utilities", "employed_wholesale_trade", "female_female_households", "four_more_cars", "gini_index", "graduate_professional_degree", "group_quarters", "high_school_including_ged", "households_public_asst_or_food_stamps", "in_grades_1_to_4", "in_grades_5_to_8", "in_grades_9_to_12", "in_school", "in_undergrad_college", "less_than_high_school_graduate", "male_45_64_associates_degree", "male_45_64_bachelors_degree", "male_45_64_graduate_degree", "male_45_64_less_than_9_grade", "male_45_64_grade_9_12", "male_45_64_high_school", "male_45_64_some_college", "male_45_to_64", "male_male_households", "management_business_sci_arts_employed", "no_car", "no_cars", "not_us_citizen_pop", "occupation_management_arts", "occupation_natural_resources_construction_maintenance", "occupation_production_transportation_material", "occupation_sales_office", "occupation_services", "one_car", "two_cars", "three_cars", "pop_25_64", "pop_determined_poverty_status", "population_1_year_and_over", "population_3_years_over", "poverty", "sales_office_employed", "some_college_and_associates_degree", "walked_to_work", "worked_at_home", "workers_16_and_over", "associates_degree", "bachelors_degree", "high_school_diploma", "less_one_year_college", "masters_degree", "one_year_more_college", "pop_25_years_over", "commute_35_44_mins", "commute_60_more_mins", "commute_less_10_mins", "commuters_16_over", "hispanic_any_race", "pop_5_years_over", "speak_only_english_at_home", "speak_spanish_at_home", "speak_spanish_at_home_low_english", "pop_15_and_over", "pop_never_married", "pop_now_married", "pop_separated", "pop_widowed", "pop_divorced", "do_date", "armed_forces", "civilian_labor_force", "employed_pop", "unemployed_pop", "not_in_labor_force", "pop_16_over", "pop_in_labor_force"], "nested_column_types": ["STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64"], "description": ["Core Based Statistical Area (CBSA)_2015 Geoids", "Nonfamily Households. A householder living alone or with nonrelatives only. Unmarried couples households, whether opposite-sex or same-sex, with no relatives of the householder present are tabulated in nonfamily households.", "Family Households. A family consists of a householder and one or more other people living in the same household who are related to the householder by birth, marriage, or adoption. All people in a household who are related to the householder are regarded as members of his or her family. A family household may contain people not related to the householder, but those people are not included as part of the family of the householder in tabulations. Thus, the number of family households is equal to the number of families, but family households may include more members than do families. A household can contain only one family for purposes of tabulations. Not all households contain families since a household may be comprised of a group of unrelated people or of one person living alone -- these are called nonfamily households. Families are classified by type as either a \"married couple family\" or \"other family\" according to the sex of the householder and the presence of relatives. The data on family type are based ...", "Median Year Structure Built. Median Year Structure Built", "Housing units without rent burden computed. Units for which no rent is paid and units occupied by households that reported no income or a net loss comprise this category", "Housing units spending over 50% income on rent. Gross rent over 50 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 40 to 49.9% income on rent. Gross rent from 40.0 to 49.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 35 to 39.9% income on rent. Gross rent from 35.0 to 39.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 30 to 34.9% income on rent. Gross rent from 30.0 to 34.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 25 to 29.9% income on rent. Gross rent from 25.0 to 29.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 20 to 24.9% income on rent. Gross rent from 20.0 to 24.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 15 to 19.9% income on rent. Gross rent from 15.0 to 19.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 10 to 14.9% income on rent. Gross rent from 10.0 to 14.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending less than 10% on rent. Gross rent less than 10 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Total Population. The total number of all people living in a given geographic area.  This is a very useful catch-all denominator when calculating rates.", "Male Population. The number of people within each geography who are male.", "Female Population. The number of people within each geography who are female.", "Median Age. The median age of all people in a given geographic area.", "White Population. The number of people identifying as white, non-Hispanic in each geography.", "Black or African American Population. The number of people identifying as black or African American, non-Hispanic in each geography.", "Asian Population. The number of people identifying as Asian, non-Hispanic in each geography.", "Hispanic Population. The number of people identifying as Hispanic or Latino in each geography.", "American Indian and Alaska Native Population. The number of people identifying as American Indian or Alaska native in each geography.", "Other Race population. The number of people identifying as another race in each geography", "Two or more races population. The number of people identifying as two or more races in each geography", "Population not Hispanic. The number of people not identifying as Hispanic or Latino in each geography.", "Commuters by Public Transportation. The number of workers age 16 years and over within a geographic area who primarily traveled to work by public transportation.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Households. A count of the number of households in each geography. A household consists of one or more people who live in the same dwelling and also share at meals or living accommodation, and may consist of a single family or some other grouping of people. ", "Median Household Income in the past 12 Months. Within a geographic area, the median income received by every household on a regular basis before payments for personal income taxes, social security, union dues, medicare deductions, etc.  It includes income received from wages, salary, commissions, bonuses, and tips; self-employment income from own nonfarm or farm businesses, including proprietorships and partnerships; interest, dividends, net rental income, royalty income, or income from estates and trusts; Social Security or Railroad Retirement income; Supplemental Security Income (SSI); any cash public assistance or welfare payments from the state or local welfare office; retirement, survivor, or disability benefits; and any other sources of income received regularly such as Veterans' (VA) payments, unemployment and/or worker's compensation, child support, and alimony.", "Per Capita Income in the past 12 Months. Per capita income is the mean income computed for every man, woman, and child in a particular group. It is derived by dividing the total income of a particular group by the total population.", "Housing Units. A count of housing units in each geography.  A housing unit is a house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters.", "Vacant Housing Units. The count of vacant housing units in a geographic area. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Vacant Housing Units for Rent. The count of vacant housing units in a geographic area that are for rent. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Vacant Housing Units for Sale. The count of vacant housing units in a geographic area that are for sale. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Median Rent. The median contract rent within a geographic area. The contract rent is the monthly rent agreed to or contracted for, regardless of any furnishings, utilities, fees, meals, or services that may be included. For vacant units, it is the monthly rent asked for the rental unit at the time of interview.", "Percent of Household Income Spent on Rent. Within a geographic area, the median percentage of household income which was spent on gross rent.  Gross rent is the amount of the contract rent plus the estimated average monthly cost of utilities (electricity, gas, water, sewer etc.) and fuels (oil, coal, wood, etc.) if these are paid by the renter.  Household income is the sum of the income of all people 15 years and older living in the household.", "Owner-occupied Housing Units", "Owner-occupied Housing Units valued at $1,000,000 or more.. The count of owner occupied housing units in a geographic area that are valued at $1,000,000 or more.  Value is the respondent's estimate of how much the property (house and lot, mobile home and lot, or condominium unit) would sell for if it were for sale.", "Owner-occupied Housing Units with a Mortgage. The count of housing units within a geographic area that are mortagaged. \"Mortgage\" refers to all forms of debt where the property is pledged as security for repayment of the debt, including deeds of trust, trust deed, contracts to purchase, land contracts, junior mortgages, and home equity loans.", "Families with young children (under 6 years of age)", "Two-parent families with young children (under 6 years of age)", "Two-parent families, both parents in labor force with young children (under 6 years of age)", "Two-parent families, father only in labor force with young children (under 6 years of age)", "Two-parent families, mother only in labor force with young children (under 6 years of age)", "Two-parent families, neither parent in labor force with young children (under 6 years of age)", "One-parent families with young children (under 6 years of age)", "One-parent families, father, with young children (under 6 years of age)", "One-parent families, father in labor force, with young children (under 6 years of age)", "Number of workers with a commute between 10 and 14 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 10 and 14 minutes. ", "Number of workers with a commute between 15 and 19 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 15 and 19 minutes. ", "Number of workers with a commute between 20 and 24 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 20 and 24 minutes.", "Number of workers with a commute between 25 and 29 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 25 and 29 minutes. ", "Number of workers with a commute between 30 and 34 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 30 and 34 minutes. ", "Number of workers with a commute between 45 and 59 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 45 and 59 minutes. ", "Aggregate travel time to work. The total number of minutes every worker in a geographic area over the age of 16 who did not work from home spent spent commuting to work in one day", "Households with income less than $10,000. The number of households in a geographic area whose annual income was less than $10,000.", "Households with income of $10,000 to $14,999. The number of households in a geographic area whose annual income was between $10,000 and $14,999.", "Households with income of $15,000 to $19,999. The number of households in a geographic area whose annual income was between $15,000 and $19,999.", "Households with income of $20,000 to $24,999. The number of households in a geographic area whose annual income was between $20,000 and $24,999.", "Households with income of $25,000 to $29,999. The number of households in a geographic area whose annual income was between $20,000 and $24,999.", "Households with income of $30,000 to $34,999. The number of households in a geographic area whose annual income was between $30,000 and $34,999.", "Households with income of $35,000 to $39,999. The number of households in a geographic area whose annual income was between $35,000 and $39,999.", "Households with income of $40,000 to $44,999. The number of households in a geographic area whose annual income was between $40,000 and $44,999.", "Households with income of $45,000 to $49,999. The number of households in a geographic area whose annual income was between $45,000 and $49,999.", "Households with income of $50,000 to $59,999. The number of households in a geographic area whose annual income was between $50,000 and $59,999.", "Households with income of $60,000 to $74,999. The number of households in a geographic area whose annual income was between $60,000 and $74,999.", "Households with income of $75,000 to $99,999. The number of households in a geographic area whose annual income was between $75,000 and $99,999.", "Households with income of $100,000 to $124,999. The number of households in a geographic area whose annual income was between $100,000 and $124,999.", "Households with income of $125,000 to $149,999. The number of households in a geographic area whose annual income was between $125,000 and $149,999.", "Households with income of $150,000 to $199,999. The number of households in a geographic area whose annual income was between $150,000 and $1999,999.", "Households with income of $200,000 Or More. The number of households in a geographic area whose annual income was more than $200,000.", "Renter-Occupied Housing Units Paying Cash Rent Median Gross Rent", "Owner-Occupied Housing Units Lower Value Quartile", "Owner-Occupied Housing Units Median Value. The middle value (median) in a geographic area owner occupied housing units.", "Owner-Occupied Housing Units Upper Value Quartile", "Married households. People in formal marriages, as well as people in common-law marriages, are included. Does not include same-sex marriages.", "Occupied housing units. A housing unit is classified as occupied if it is the usual place of residence of the person or group of people living in it at the time of enumeration.", "Renter occupied housing units. All occupied units which are not owner occupied, whether they are rented for cash rent or occupied without payment of cash rent, are classified as renter-occupied.", "Single-family (one unit) detached dwellings. This is a 1-unit structure detached from any other house, that is, with open space on all four sides. Such structures are considered detached even if they have an adjoining shed or garage. A one-family house that contains a business is considered detached as long as the building has open space on all four sides. Mobile homes to which one or more permanent rooms have been added or built also are included.", "Single-family (one unit) attached dwellings. This is a 1-unit structure that has one or more walls extending from ground to roof separating it from adjoining structures. In row houses (sometimes called townhouses), double houses, or houses attached to nonresidential structures, each house is a separate, attached structure if the dividing or common wall goes from ground to roof.", "Two-family (two unit) dwellings", "Multifamily dwellings with three to 4 units", "Apartment buildings with 5 to 9 units", "Apartment buildings with 10 to 19 units", "Apartment buildings with 20 to 49 units", "Apartment buildings with 50 or more units", "Mobile homes. A manufactured home is defined as a movable dwelling, 8 feet or more wide and 40 feet or more long, designed to be towed on its own chassis, with transportation gear integral to the unit when it leaves the factory, and without need of a permanent foundation. These homes are built in accordance with the U.S. Department of Housing and Urban Development (HUD) building code.", "Housing units built in 2005 or later. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built in 2005 or later.", "Housing units built between 2000 and 2004. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built from 2000 to 2004.", "Housing units built before 1939. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built in 1939 or earlier.", "Male under 5 years. The male population over the age of five years within the specified area.", "Male age 5 to 9. The male population between the age of five years to nine years within the specified area.", "Male age 10 to 14. The male population between the age of ten years to fourteen years within the specified area.", "Male age 15 to 17. The male population between the age of fifteeen years to seventeen years within the specified area.", "Male age 18 and 19. The male population between the age of eighteen years to nineteen years within the specified area.", "Male age 20. The male population with an age of twenty years within the specified area.", "Male age 21. The male population with an age of twenty-one years within the specified area.", "Male age 22 to 24. The male population between the age of twenty-two years to twenty-four years within the specified area.", "Male age 25 to 29. The male population between the age of twenty-five years to twenty-nine years within the specified area.", "Male age 30 to 34. The male population between the age of thirty years to thirty-four years within the specified area.", "Male age 35 to 39. The male population between the age of thirty-five years to thirty-nine years within the specified area.", "Male age 40 to 44. The male population between the age of fourty years to fourty-four years within the specified area.", "Men age 45 to 49. The male population between the age of fourty-five years to fourty-nine years within the specified area.", "Men age 50 to 54. The male population between the age of fifty years to fifty-four years within the specified area.", "Men age 55 to 59. The male population between the age of fifty-five years to fifty-nine years within the specified area.", "Men age 60 to 61. The male population between the age of sixty years to sixty-one years within the specified area.", "Men age 62 to 64. The male population between the age of sixty-two years to sixty-four years within the specified area.", "Male age 65 to 66. The male population between the age of sixty-five years to sixty-six years within the specified area.", "Male age 67 to 69. The male population between the age of sixty-seven years to sixty-nine years within the specified area.", "Male age 70 to 74. The male population between the age of seventy years to seventy-four years within the specified area.", "Male age 75 to 79. The male population between the age of seventy-five years to seventy-nine years within the specified area.", "Male age 80 to 84. The male population between the age of eighty years to eighty-four years within the specified area.", "Male age 85 and over. The male population of the age of eighty-five years and over within the specified area.", "Female under 5 years. The female population over the age of five years within the specified area.", "Female age 5 to 9. The female population between the age of five years to nine years within the specified area.", "Female age 10 to 14. The female population between the age of ten years to fourteen years within the specified area.", "Female age 15 to 17. The female population between the age of fifteeen years to seventeen years within the specified area.", "Female age 18 and 19. The female population between the age of eighteen years to nineteen years within the specified area.", "Female age 20. The female population with an age of twenty years within the specified area.", "Female age 21. The female population with an age of twenty-one years within the specified area.", "Female age 22 to 24. The female population between the age of twenty-two years to twenty-four years within the specified area.", "Female age 25 to 29. The female population between the age of twenty-five years to twenty-nine years within the specified area.", "Female age 30 to 34. The female population between the age of thirty years to thirty-four years within the specified area.", "Female age 35 to 39. The female population between the age of thirty-five years to thirty-nine years within the specified area.", "Female age 40 to 44. The female population between the age of fourty years to fourty-four years within the specified area.", "Female age 45 to 49. The female population between the age of fourty-five years to fourty-nine years within the specified area.", "Female age 50 to 54. The female population between the age of fifty years to fifty-four years within the specified area.", "Female age 55 to 59. The female population between the age of fifty-five years to fifty-nine years within the specified area.", "Female age 60 and 61. The female population between the age of sixty years to sixty-one years within the specified area.", "Female age 62 to 64. The female population between the age of sixty-two years to sixty-four years within the specified area.", "Female age 65 to 66. The female population between the age of sixty-five years to sixty-six years within the specified area.", "Female age 67 to 69. The female population between the age of sixty-seven years to sixty-nine years within the specified area.", "Female age 70 to 74. The female population between the age of seventy years to seventy-four years within the specified area.", "Female age 75 to 79. The female population between the age of seventy-five years to seventy-nine years within the specified area.", "Female age 80 to 84. The female population between the age of eighty years to eighty-four years within the specified area.", "Female age 85 and over. The female population of the age of eighty-five years and over within the specified area.", "White including Hispanic", "Black including Hispanic", "American Indian including Hispanic", "Asian including Hispanic", "Number of workers with a commute between 5 and 9 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 5 and 9 minutes.", "Number of workers with a commute between 35 and 39 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 35 and 39 minutes. ", "Number of workers with a commute between 40 and 44 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 40 and 44 minutes. ", "Number of workers with a commute between 60 and 89 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 60 and 89 minutes .", "Number of workers with a commute of more than 90 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute more than 90 minutes.", "Households receiving retirement income", "Asian Men age 45 to 54", "Asian Men age 55 to 64", "Black Men age 45 to 54", "Black Men age 55 to 64", "Hispanic Men age 45 to 54", "Hispanic Men age 55 to 64", "White Men age 45 to 54", "White Men age 55 to 64", "Population who completed a bachelor's degree. From mobility table.", "Population with Bachelors Degree or Higher, Ages 25 to 64. The number of people in each geography who are between the ages of 25 and 64 who have attained a bachelors degree or higher. ", "Children under 18 Years of Age. The number of people within each geography who are under 18 years of age.", "Children under 18 years of age in single female-led household", "Commuters by Bus. The number of workers age 16 years and over within a geographic area who primarily traveled to work by bus.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.  This is a subset of workers who commuted by public transport.", "Commuters by Car, Truck, or Van. The number of workers age 16 years and over within  a geographic area who primarily traveled to work by car, truck or  van.  This is the principal mode of travel or type of conveyance,  by distance rather than time, that the worker usually used to get  from home to work.", "Commuters by Carpool. The number of workers age 16 years and over within a geographic area who primarily traveled to work by carpool.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Commuters by Subway or Elevated. The number of workers age 16 years and over within a geographic area who primarily traveled to work by subway or elevated train.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.  This is a subset of workers who commuted by public transport.", "Commuters who drove alone. The number of workers age 16 years and over within a geographic area who primarily traveled by car driving alone. This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Lived in a different house one year ago in a different city. All people in a geographic area who lived in a different city within the year prior to the survey.", "Lived in a different house one year ago in the same city. All people in a geographic area who lived in the same city but moved to a different unit within the year prior to the survey.", "Workers employed in firms in agriculture, forestry, fishing, hunting, or mining. The Agriculture, Forestry, Fishing and Hunting sector comprises establishments primarily engaged in growing crops, raising animals, harvesting timber, and harvesting fish and other animals from a farm, ranch, or their natural habitats.", "Workers employed in firms in arts, entertainment, recreation, accommodation and food services. The Arts, Entertainment, and Recreation sector includes a wide range of establishments that operate facilities or provide services to meet varied cultural, entertainment, and recreational interests of their patrons. This sector comprises (1) establishments that are involved in producing, promoting, or participating in live performances, events, or exhibits intended for public viewing; (2) establishments that preserve and exhibit objects and sites of historical, cultural, or educational interest; and (3) establishments that operate facilities or provide services that enable patrons to participate in recreational activities or pursue amusement, hobby, and leisure-time interests.", "Workers employed in firms in construction. The Construction sector comprises establishments primarily engaged in the construction of buildings or engineering projects (e.g., highways and utility systems). Construction work done may include new work, additions, alterations, or maintenance and repairs.", "Workers employed in firms in educational services, health care, and social assistance. Outpatient health services, other than hospital care, including: public health administration; research and education; categorical health programs; treatment and immunization clinics; nursing; environmental health activities such as air and water pollution control; ambulance service if provided separately from fire protection services, and other general public health activities such as mosquito abatement. School health services provided by health agencies (rather than school agencies) are included here. Sewage treatment operations are classified under Sewerage.", "Workers employed in firms in finance, insurance, real estate and rental and leasing. The Real Estate and Rental and Leasing sector comprises establishments primarily engaged in renting, leasing, or otherwise allowing the use of tangible or intangible assets, and establishments providing related services. The major portion of this sector comprises establishments that rent, lease, or otherwise allow the use of their own assets by others. The assets may be tangible, as is the case of real estate and equipment, or intangible, as is the case with patents and trademarks.", "Workers employed in firms in information. The Information sector comprises establishments engaged in the following processes: (a) producing and distributing information and cultural products, (b) providing the means to transmit or distribute these products as well as data or communications, and (c) processing data. Included are the publishing industries, the motion picture and sound recording industries; the broadcasting industries, the telecommunications industries; Web search portals, data processing industries, and the information services industries.", "Workers employed in firms in manufacturing. The Manufacturing sector comprises establishments engaged in the mechanical, physical, or chemical transformation of materials, substances, or components into new products.", "Workers employed in firms in other services except public administration. The Other Services (Except Public Administration) sector comprises establishments engaged in providing services not specifically provided for elsewhere in the classification system. Establishments in this sector are primarily engaged in activities such as equipment and machinery repairing, promoting or administering religious activities, grantmaking, advocacy, and providing drycleaning and laundry services, personal care services, death care services, pet care services, photofinishing services, temporary parking services, and dating services. Private households that engage in employing workers on or about the premises in activities primarily concerned with the operation of the household are included in this sector.", "Workers employed in firms in public administration. The Public Administration sector consists of establishments of federal, state, and local government agencies that administer, oversee, and manage public programs and have executive, legislative, or judicial authority over other institutions within a given area. These agencies also set policy, create laws, adjudicate civil and criminal legal cases, provide for public safety and for national defense. In general, government establishments in the public administration sector oversee governmental programs and activities that are not performed by private establishments.", "Workers employed in firms in retail trade. The Retail Trade sector comprises establishments engaged in retailing merchandise, generally without transformation, and rendering services incidental to the sale of merchandise. The retailing process is the final step in the distribution of merchandise; retailers are, therefore, organized to sell merchandise in small quantities to the general public.", "Workers employed in firms in professional scientific, management, administrative and waste management services. The Administrative and Support and Waste Management and Remediation Services sector comprises establishments performing routine support activities for the day-to-day operations of other organizations. The establishments in this sector specialize in one or more of these support activities and provide these services to clients in a variety of industries and, in some cases, to households. Activities performed include office administration, hiring and placing of personnel, document preparation and similar clerical services, solicitation, collection, security and surveillance services, cleaning, and waste disposal services.", "Workers employed in firms in transportation, warehousing, and utilities. The Transportation and Warehousing sector includes industries providing transportation of passengers and cargo, warehousing and storage for goods, scenic and sightseeing transportation, and support activities related to modes of transportation. The modes of transportation are air, rail, water, road, and pipeline.", "Workers employed in firms in wholesale trade. The Wholesale Trade sector comprises establishments engaged in wholesaling merchandise, generally without transformation, and rendering services incidental to the sale of merchandise. The wholesaling process is an intermediate step in the distribution of merchandise. Wholesalers are organized to sell or arrange the purchase or sale of (a) goods for resale (i.e., goods sold to other wholesalers or retailers), (b) capital or durable nonconsumer goods, and (c) raw and intermediate materials and supplies used in production.", "Households with two female partners. An unmarried partner is a person age 15 years and over, who is not related to the householder, who shares living quarters, and who has an intimate relationship with the householder.", "Four car households. The number of households with four or more passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Gini Index. The Gini index, or index of income concentration, is a statistical measure of income inequality ranging from 0 to 1. A measure of 1 indicates perfect inequality, i.e., one household having all the income and rest having none. A measure of 0 indicates perfect equality, i.e., all households having an equal share of income.", "Population who completed a graduate or professional degree", "Population living in group quarters", "Population with high school degree, including GED. The number of people in a geographic area over the age of 25 who attained a high school degree or GED.", "Households on cash public assistance or receiving food stamps (SNAP)", "Students Enrolled in Grades 1 to 4. The total number of people in each geography currently enrolled in grades 1 through 4 inclusive.  This corresponds roughly to elementary school.", "Students Enrolled in Grades 5 to 8. The total number of people in each geography currently enrolled in grades 5 through 8 inclusive.  This corresponds roughly to middle school.", "Students Enrolled in Grades 9 to 12. The total number of people in each geography currently enrolled in grades 9 through 12 inclusive.  This corresponds roughly to high school.", "Students Enrolled in School. The total number of people in each geography currently enrolled at any level of school, from nursery or pre-school to advanced post-graduate education.  Only includes those over the age of 3.", "Students Enrolled as Undergraduate in College. The number of people in a geographic area who are enrolled in college at the undergraduate level. Enrollment refers to being registered or listed as a student in an educational program leading to a college degree. This may be a public school or college, a private school or college.", "Less than high school graduate. The number of people in a geographic area over the age of 25 who have not completed high school or any other advanced degree.", "Men age 45 to 64 who obtained an associate's degree", "Men age 45 to 64 who obtained a bachelor's degree", "Men age 45 to 64 who obtained a graduate or professional degree", "Men age 45 to 64 who attained less than a 9th grade education", "Men age 45 to 64 who attained between 9th and 12th grade, no diploma", "Men age 45 to 64 who completed high school or obtained GED", "Men age 45 to 64 who completed some college, no degree", "Men age 45 to 64 (\"middle aged\"). The male population between the age of fourty-five years to sixty-four years within the specified area.", "Households with two male partners. An unmarried partner is a person age 15 years and over, who is not related to the householder, who shares living quarters, and who has an intimate relationship with the householder.", "Civilian Employed Population in Management, Business, Science, and Arts Occupations. The number of employed civilians 16 years old and over in each geography in the Management, Business, Science, and Arts occupations. Occupation codes are based on the Standard Occupational Classification (SOC), published by the Executive Office of the President, Office of Management and Budget.", "Workers age 16 and over with no vehicle. All people in a geographic area over the age of 16 who do not own a car.", "Car-free households. The number of households without passenger cars, vans, and pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Not a U.S. Citizen Population. The number of people within each geography who indicated that they are not U.S. citizens.", "Workers employed in management business science and arts occupations", "Workers employed in natural resources, construction, and maintenance occupations", "Workers employed in production, transportation, and material moving", "Workers employed in sales and office occupations", "Workers employed in service occupations", "One car households. The number of households with one passenger car, van, , pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Two car households. The number of households with two passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Three car households. The number of households with one passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Population age 25 to 64. The number of people in each geography who are between the ages of 25 and 64. ", "Population for Whom Poverty Status Determined. The number of people in each geography who could be identified as either living in poverty or not.  This should be used as the denominator when calculating poverty rates, as it excludes people for whom it was not possible to determine poverty.", "Population 1 year and over. All people, male and female, child and adult, living in a given geographic area that are 1 year and older.", "Population 3 Years and Over. The total number of people in each geography age 3 years and over.  This denominator is mostly used to calculate rates of school enrollment.", "Income In The Past 12 Months Below Poverty Level. The number of people in a geographic area who are part of a family (which could be just them as an individual) determined to be \"in poverty\" following the Office of Management and Budget's Directive 14. (https://www.census.gov/hhes/povmeas/methodology/ombdir14.html)", "Civilian Employed Population in Sales and Office Occupations. The number of employed civilians 16 years old and over in each geography in the Sales and Office occupations. Occupation codes are based on the Standard Occupational Classification (SOC), published by the Executive Office of the President, Office of Management and Budget.", "Population who completed some college or obtained associate's degree. The number of people in a geographic area over the age of 25 who obtained an associate's degree, and did not complete a more advanced degree.", "Walked to Work. The number of workers age 16 years and over within a geographic area who primarily walked to work.  This would mean that of any way of getting to work, they travelled the most distance walking.", "Worked at Home. The count within a geographical area of workers over the age of 16 who worked at home.", "Workers over the Age of 16. The number of people in each geography who work. Workers include those employed at private for-profit companies, the self-employed, government workers and non-profit employees.", "Population Completed Associate's Degree. The number of people in a geographic area over the age of 25 who obtained a associate's degree, and did not complete a more advanced degree.", "Population Completed Bachelor's Degree. The number of people in a geographic area over the age of 25 who obtained a bachelor's degree, and did not complete a more advanced degree.", "Population Completed High School. The number of people in a geographic area over the age of 25 who completed high school, and did not complete a more advanced degree.", "Population completed less than one year of college, no degree. The number of people in a geographic area over the age of 25 who attended college for less than one year and no further.", "Population Completed Master's Degree. The number of people in a geographic area over the age of 25 who obtained a master's degree, but did not complete a more advanced degree.", "Population completed more than one year of college, no degree. The number of people in a geographic area over the age of 25 who attended college for more than one year but did not obtain a degree", "Population 25 Years and Over. The number of people in a geographic area who are over the age of 25.  This is used mostly as a denominator of educational attainment.", "Number of workers with a commute between 35 and 44 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 35 and 44 minutes. ", "Number of workers with a commute of over 60 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in over 60 minutes.", "Number of workers with less than 10 minute commute. The number of workers in a geographic area over the age of 16 who do not work from home and commute in less than 10 minutes.", "Workers age 16 and over who do not work from home. The number of workers in a geographic area over the age of 16 who do not work from home", "Hispanic of any race", "Population 5 Years and Over. The number of people in a geographic area who are over the age of 5.  This is primarily used as a denominator of measures of language spoken at home.", "Speaks only English at Home. The number of people in a geographic area over age 5 who speak only English at home.", "Speaks Spanish at Home. The number of people in a geographic area over age 5 who speak Spanish at home, possibly in addition to other languages.", "Speaks Spanish at Home, speaks English less than \"very well\"", "Population 15 Years and Over. The number of people in a geographic area who are over the age of 15.  This is used mostly as a denominator of marital status.", "Never Married. The number of people in a geographic area who have never been married.", "Currently married. The number of people in a geographic area who are currently married", "Married but separated. The number of people in a geographic area who are married but separated", "Widowed. The number of people in a geographic area who are widowed", "Divorced. The number of people in a geographic area who are divorced", null, "Population in Armed Forces. The number of people in each geography who are members of the U.S. Armed Forces (people on active duty with the United States Army, Air Force, Navy, Marine Corps, or Coast Guard).", "Population in Civilian Labor Force. The number of civilians 16 years and over in each geography who can be classified as either \"employed\" or \"unemployed\" below.", "Employed Population. The number of civilians 16 years old and over in each geography who either (1) were \"at work,\" that is, those who did any work at all during the reference week as paid employees, worked in their own business or profession, worked on their own farm, or worked 15 hours or more as unpaid workers on a family farm or in a family business; or (2) were \"with a job but not at work,\" that is, those who did not work during the reference week but had jobs or businesses from which they were temporarily absent due to illness, bad weather, industrial dispute, vacation, or other personal reasons. Excluded from the employed are people whose only activity consisted of work around the house or unpaid volunteer work for religious, charitable, and similar organizations; also excluded are all institutionalized people and people on active duty in the United States Armed Forces.", "", "Population Not in Labor Force. The number of people in each geography who are 16 years old and over who are not classified as members of the labor force. This category consists mainly of students, homemakers, retired workers, seasonal workers interviewed in an off season who were not looking for work, institutionalized people, and people doing only incidental unpaid family work (less than 15 hours during the reference week).", "Population age 16 and over. The number of people in each geography who are age 16 or over.", "Population in Labor Force. The number of people in each geography who are either in the civilian labor force or are members of the U.S. Armed Forces (people on active duty with the United States Army, Air Force, Navy, Marine Corps, or Coast Guard)."], "sample_rows": [{"geo_id": "14260", "nonfamily_households": 68289.0, "family_households": 154662.0, "median_year_structure_built": 1990.0, "rent_burden_not_computed": 4388.0, "rent_over_50_percent": 14293.0, "rent_40_to_50_percent": 6143.0, "rent_35_to_40_percent": 5184.0, "rent_30_to_35_percent": 6512.0, "rent_25_to_30_percent": 7031.0, "rent_20_to_25_percent": 9305.0, "rent_15_to_20_percent": 8621.0, "rent_10_to_15_percent": 4408.0, "rent_under_10_percent": 1895.0, "total_pop": 610912.0, "male_pop": 305376.0, "female_pop": 305536.0, "median_age": 33.9, "white_pop": 502598.0, "black_pop": 4846.0, "asian_pop": 11781.0, "hispanic_pop": 75582.0, "amerindian_pop": 3015.0, "other_race_pop": 432.0, "two_or_more_races_pop": 11721.0, "not_hispanic_pop": 535330.0, "commuters_by_public_transportation": 1591.0, "households": 222951.0, "median_income": 50026.0, "income_per_capita": 23502.0, "housing_units": 244521.0, "vacant_housing_units": 21570.0, "vacant_housing_units_for_rent": 6314.0, "vacant_housing_units_for_sale": 5587.0, "median_rent": 659.0, "percent_income_spent_on_rent": 30.3, "owner_occupied_housing_units": 155171.0, "million_dollar_housing_units": 1656.0, "mortgaged_housing_units": 118616.0, "families_with_young_children": 58256.0, "two_parent_families_with_young_children": 43787.0, "two_parents_in_labor_force_families_with_young_children": 23281.0, "two_parents_father_in_labor_force_families_with_young_children": 19226.0, "two_parents_mother_in_labor_force_families_with_young_children": 871.0, "two_parents_not_in_labor_force_families_with_young_children": 409.0, "one_parent_families_with_young_children": 14469.0, "father_one_parent_families_with_young_children": 3543.0, "father_in_labor_force_one_parent_families_with_young_children": 3325.0, "commute_10_14_mins": 43676.0, "commute_15_19_mins": 48402.0, "commute_20_24_mins": 42995.0, "commute_25_29_mins": 17849.0, "commute_30_34_mins": 33343.0, "commute_45_59_mins": 12307.0, "aggregate_travel_time_to_work": 5493635.0, "income_less_10000": 13541.0, "income_10000_14999": 12411.0, "income_15000_19999": 11447.0, "income_20000_24999": 13387.0, "income_25000_29999": 12719.0, "income_30000_34999": 13676.0, "income_35000_39999": 11721.0, "income_40000_44999": 12341.0, "income_45000_49999": 10156.0, "income_50000_59999": 22385.0, "income_60000_74999": 25815.0, "income_75000_99999": 27562.0, "income_100000_124999": 14661.0, "income_125000_149999": 8096.0, "income_150000_199999": 7270.0, "income_200000_or_more": 5763.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 767.0, "owner_occupied_housing_units_lower_value_quartile": 134800.0, "owner_occupied_housing_units_median_value": 185900.0, "owner_occupied_housing_units_upper_value_quartile": 274500.0, "married_households": 122753.0, "occupied_housing_units": 222951.0, "housing_units_renter_occupied": 67780.0, "dwellings_1_units_detached": 182082.0, "dwellings_1_units_attached": 9639.0, "dwellings_2_units": 5513.0, "dwellings_3_to_4_units": 10731.0, "dwellings_5_to_9_units": 6232.0, "dwellings_10_to_19_units": 4848.0, "dwellings_20_to_49_units": 4245.0, "dwellings_50_or_more_units": 6165.0, "mobile_homes": 14723.0, "housing_built_2005_or_later": 26587.0, "housing_built_2000_to_2004": 41137.0, "housing_built_1939_or_earlier": 14427.0, "male_under_5": 24923.0, "male_5_to_9": 24246.0, "male_10_to_14": 24604.0, "male_15_to_17": 14250.0, "male_18_to_19": 8052.0, "male_20": 4011.0, "male_21": 3528.0, "male_22_to_24": 12849.0, "male_25_to_29": 22332.0, "male_30_to_34": 21807.0, "male_35_to_39": 21463.0, "male_40_to_44": 21188.0, "male_45_to_49": 21477.0, "male_50_to_54": 19977.0, "male_55_to_59": 17267.0, "male_60_61": 6498.0, "male_62_64": 8025.0, "male_65_to_66": 4412.0, "male_67_to_69": 5584.0, "male_70_to_74": 7078.0, "male_75_to_79": 4928.0, "male_80_to_84": 3886.0, "male_85_and_over": 2991.0, "female_under_5": 23841.0, "female_5_to_9": 23965.0, "female_10_to_14": 22679.0, "female_15_to_17": 12820.0, "female_18_to_19": 8210.0, "female_20": 3116.0, "female_21": 4136.0, "female_22_to_24": 11984.0, "female_25_to_29": 22068.0, "female_30_to_34": 21255.0, "female_35_to_39": 20921.0, "female_40_to_44": 20124.0, "female_45_to_49": 21446.0, "female_50_to_54": 20207.0, "female_55_to_59": 18028.0, "female_60_to_61": 6990.0, "female_62_to_64": 7811.0, "female_65_to_66": 4676.0, "female_67_to_69": 6291.0, "female_70_to_74": 7752.0, "female_75_to_79": 6260.0, "female_80_to_84": 4995.0, "female_85_and_over": 5961.0, "white_including_hispanic": 562726.0, "black_including_hispanic": 4928.0, "amerindian_including_hispanic": 3840.0, "asian_including_hispanic": 11966.0, "commute_5_9_mins": 29537.0, "commute_35_39_mins": 4880.0, "commute_40_44_mins": 7193.0, "commute_60_89_mins": 6601.0, "commute_90_more_mins": 2838.0, "households_retirement_income": 34974.0, "asian_male_45_54": 775.0, "asian_male_55_64": 361.0, "black_male_45_54": 583.0, "black_male_55_64": 231.0, "hispanic_male_45_54": 3423.0, "hispanic_male_55_64": 1750.0, "white_male_45_54": 35844.0, "white_male_55_64": 28961.0, "bachelors_degree_2": 73229.0, "bachelors_degree_or_higher_25_64": 94432.0, "children": 171328.0, "children_in_single_female_hh": 29547.0, "commuters_by_bus": 1436.0, "commuters_by_car_truck_van": 240693.0, "commuters_by_carpool": 26074.0, "commuters_by_subway_or_elevated": 86.0, "commuters_drove_alone": 214619.0, "different_house_year_ago_different_city": 71117.0, "different_house_year_ago_same_city": 42708.0, "employed_agriculture_forestry_fishing_hunting_mining": 7596.0, "employed_arts_entertainment_recreation_accommodation_food": 22678.0, "employed_construction": 20430.0, "employed_education_health_social": 58097.0, "employed_finance_insurance_real_estate": 17792.0, "employed_information": 6850.0, "employed_manufacturing": 28720.0, "employed_other_services_not_public_admin": 12365.0, "employed_public_administration": 16067.0, "employed_retail_trade": 33533.0, "employed_science_management_admin_waste": 30830.0, "employed_transportation_warehousing_utilities": 13302.0, "employed_wholesale_trade": 9070.0, "female_female_households": 646.0, "four_more_cars": 17196.0, "gini_index": 0.431, "graduate_professional_degree": 34094.0, "group_quarters": 13815.0, "high_school_including_ged": 99356.0, "households_public_asst_or_food_stamps": 23716.0, "in_grades_1_to_4": 37899.0, "in_grades_5_to_8": 37054.0, "in_grades_9_to_12": 35765.0, "in_school": 172388.0, "in_undergrad_college": 34535.0, "less_than_high_school_graduate": 42143.0, "male_45_64_associates_degree": 5764.0, "male_45_64_bachelors_degree": 15705.0, "male_45_64_graduate_degree": 8252.0, "male_45_64_less_than_9_grade": 3058.0, "male_45_64_grade_9_12": 4536.0, "male_45_64_high_school": 16969.0, "male_45_64_some_college": 18960.0, "male_45_to_64": 73244.0, "male_male_households": 233.0, "management_business_sci_arts_employed": 102556.0, "no_car": 4816.0, "no_cars": 9781.0, "not_us_citizen_pop": 29271.0, "occupation_management_arts": 102556.0, "occupation_natural_resources_construction_maintenance": 27180.0, "occupation_production_transportation_material": 29182.0, "occupation_sales_office": 72164.0, "occupation_services": 46248.0, "one_car": 63288.0, "two_cars": 92946.0, "three_cars": 39740.0, "pop_25_64": 318884.0, "pop_determined_poverty_status": 597220.0, "population_1_year_and_over": 601318.0, "population_3_years_over": 581175.0, "poverty": 84814.0, "sales_office_employed": 72164.0, "some_college_and_associates_degree": 134876.0, "walked_to_work": 5420.0, "worked_at_home": 15433.0, "workers_16_and_over": 273851.0, "associates_degree": 29481.0, "bachelors_degree": 73229.0, "high_school_diploma": 79958.0, "less_one_year_college": 32628.0, "masters_degree": 23485.0, "one_year_more_college": 72767.0, "pop_25_years_over": 383698.0, "commute_35_44_mins": 12073.0, "commute_60_more_mins": 9439.0, "commute_less_10_mins": 38334.0, "commuters_16_over": 258418.0, "hispanic_any_race": 75582.0, "pop_5_years_over": 562148.0, "speak_only_english_at_home": 493231.0, "speak_spanish_at_home": 48313.0, "speak_spanish_at_home_low_english": 17617.0, "pop_15_and_over": 466654.0, "pop_never_married": 122306.0, "pop_now_married": 259534.0, "pop_separated": 7728.0, "pop_widowed": 19041.0, "pop_divorced": 54542.0, "do_date": "20082010", "armed_forces": NaN, "civilian_labor_force": NaN, "employed_pop": NaN, "unemployed_pop": NaN, "not_in_labor_force": NaN, "pop_16_over": NaN, "pop_in_labor_force": NaN}, {"geo_id": "19660", "nonfamily_households": 71676.0, "family_households": 125537.0, "median_year_structure_built": 1984.0, "rent_burden_not_computed": 4685.0, "rent_over_50_percent": 15049.0, "rent_40_to_50_percent": 4154.0, "rent_35_to_40_percent": 2679.0, "rent_30_to_35_percent": 3778.0, "rent_25_to_30_percent": 5275.0, "rent_20_to_25_percent": 5179.0, "rent_15_to_20_percent": 4519.0, "rent_10_to_15_percent": 2394.0, "rent_under_10_percent": 1081.0, "total_pop": 495527.0, "male_pop": 242093.0, "female_pop": 253434.0, "median_age": 44.9, "white_pop": 373800.0, "black_pop": 49564.0, "asian_pop": 7832.0, "hispanic_pop": 54591.0, "amerindian_pop": 952.0, "other_race_pop": 2688.0, "two_or_more_races_pop": 5924.0, "not_hispanic_pop": 440936.0, "commuters_by_public_transportation": 1878.0, "households": 197213.0, "median_income": 44133.0, "income_per_capita": 23845.0, "housing_units": 253541.0, "vacant_housing_units": 56328.0, "vacant_housing_units_for_rent": 5791.0, "vacant_housing_units_for_sale": 6405.0, "median_rent": 725.0, "percent_income_spent_on_rent": 34.8, "owner_occupied_housing_units": 148420.0, "million_dollar_housing_units": 1753.0, "mortgaged_housing_units": 92636.0, "families_with_young_children": 27898.0, "two_parent_families_with_young_children": 15629.0, "two_parents_in_labor_force_families_with_young_children": 8953.0, "two_parents_father_in_labor_force_families_with_young_children": 5851.0, "two_parents_mother_in_labor_force_families_with_young_children": 454.0, "two_parents_not_in_labor_force_families_with_young_children": 371.0, "one_parent_families_with_young_children": 12269.0, "father_one_parent_families_with_young_children": 3169.0, "father_in_labor_force_one_parent_families_with_young_children": 2697.0, "commute_10_14_mins": 30624.0, "commute_15_19_mins": 32673.0, "commute_20_24_mins": 32187.0, "commute_25_29_mins": 10853.0, "commute_30_34_mins": 25529.0, "commute_45_59_mins": 13382.0, "aggregate_travel_time_to_work": 4966215.0, "income_less_10000": 15899.0, "income_10000_14999": 12946.0, "income_15000_19999": 12736.0, "income_20000_24999": 13180.0, "income_25000_29999": 11844.0, "income_30000_34999": 11613.0, "income_35000_39999": 11557.0, "income_40000_44999": 10379.0, "income_45000_49999": 10059.0, "income_50000_59999": 18162.0, "income_60000_74999": 21421.0, "income_75000_99999": 21330.0, "income_100000_124999": 11908.0, "income_125000_149999": 5399.0, "income_150000_199999": 4352.0, "income_200000_or_more": 4428.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 867.0, "owner_occupied_housing_units_lower_value_quartile": 112800.0, "owner_occupied_housing_units_median_value": 172800.0, "owner_occupied_housing_units_upper_value_quartile": 259900.0, "married_households": 96417.0, "occupied_housing_units": 197213.0, "housing_units_renter_occupied": 48793.0, "dwellings_1_units_detached": 165210.0, "dwellings_1_units_attached": 11088.0, "dwellings_2_units": 3710.0, "dwellings_3_to_4_units": 6068.0, "dwellings_5_to_9_units": 8817.0, "dwellings_10_to_19_units": 9435.0, "dwellings_20_to_49_units": 8836.0, "dwellings_50_or_more_units": 16129.0, "mobile_homes": 23921.0, "housing_built_2005_or_later": 13564.0, "housing_built_2000_to_2004": 33087.0, "housing_built_1939_or_earlier": 6755.0, "male_under_5": 12278.0, "male_5_to_9": 12344.0, "male_10_to_14": 14435.0, "male_15_to_17": 9203.0, "male_18_to_19": 8418.0, "male_20": 4203.0, "male_21": 3253.0, "male_22_to_24": 7909.0, "male_25_to_29": 13397.0, "male_30_to_34": 12247.0, "male_35_to_39": 13931.0, "male_40_to_44": 15281.0, "male_45_to_49": 18150.0, "male_50_to_54": 18067.0, "male_55_to_59": 17108.0, "male_60_61": 6586.0, "male_62_64": 9320.0, "male_65_to_66": 5895.0, "male_67_to_69": 7256.0, "male_70_to_74": 11695.0, "male_75_to_79": 9348.0, "male_80_to_84": 6484.0, "male_85_and_over": 5285.0, "female_under_5": 12338.0, "female_5_to_9": 12186.0, "female_10_to_14": 13386.0, "female_15_to_17": 8421.0, "female_18_to_19": 6651.0, "female_20": 3406.0, "female_21": 3052.0, "female_22_to_24": 7764.0, "female_25_to_29": 12949.0, "female_30_to_34": 12387.0, "female_35_to_39": 13489.0, "female_40_to_44": 15422.0, "female_45_to_49": 18858.0, "female_50_to_54": 19399.0, "female_55_to_59": 18941.0, "female_60_to_61": 7310.0, "female_62_to_64": 10119.0, "female_65_to_66": 6101.0, "female_67_to_69": 9787.0, "female_70_to_74": 12281.0, "female_75_to_79": 10852.0, "female_80_to_84": 10022.0, "female_85_and_over": 8313.0, "white_including_hispanic": 407852.0, "black_including_hispanic": 50590.0, "amerindian_including_hispanic": 1243.0, "asian_including_hispanic": 7924.0, "commute_5_9_mins": 18478.0, "commute_35_39_mins": 4729.0, "commute_40_44_mins": 6951.0, "commute_60_89_mins": 9886.0, "commute_90_more_mins": 5221.0, "households_retirement_income": 45329.0, "asian_male_45_54": 465.0, "asian_male_55_64": 357.0, "black_male_45_54": 3026.0, "black_male_55_64": 2243.0, "hispanic_male_45_54": 3130.0, "hispanic_male_55_64": 1994.0, "white_male_45_54": 29057.0, "white_male_55_64": 28170.0, "bachelors_degree_2": 48034.0, "bachelors_degree_or_higher_25_64": 55891.0, "children": 94591.0, "children_in_single_female_hh": 25612.0, "commuters_by_bus": 1785.0, "commuters_by_car_truck_van": 185061.0, "commuters_by_carpool": 16431.0, "commuters_by_subway_or_elevated": 93.0, "commuters_drove_alone": 168630.0, "different_house_year_ago_different_city": 54332.0, "different_house_year_ago_same_city": 11951.0, "employed_agriculture_forestry_fishing_hunting_mining": 1852.0, "employed_arts_entertainment_recreation_accommodation_food": 23660.0, "employed_construction": 16972.0, "employed_education_health_social": 45966.0, "employed_finance_insurance_real_estate": 13756.0, "employed_information": 4339.0, "employed_manufacturing": 13448.0, "employed_other_services_not_public_admin": 10952.0, "employed_public_administration": 10036.0, "employed_retail_trade": 29641.0, "employed_science_management_admin_waste": 22722.0, "employed_transportation_warehousing_utilities": 8996.0, "employed_wholesale_trade": 5268.0, "female_female_households": 453.0, "four_more_cars": 7709.0, "gini_index": 0.442, "graduate_professional_degree": 27100.0, "group_quarters": 15733.0, "high_school_including_ged": 114135.0, "households_public_asst_or_food_stamps": 18414.0, "in_grades_1_to_4": 20385.0, "in_grades_5_to_8": 23282.0, "in_grades_9_to_12": 21553.0, "in_school": 113659.0, "in_undergrad_college": 33553.0, "less_than_high_school_graduate": 45058.0, "male_45_64_associates_degree": 6235.0, "male_45_64_bachelors_degree": 10288.0, "male_45_64_graduate_degree": 5967.0, "male_45_64_less_than_9_grade": 1874.0, "male_45_64_grade_9_12": 5839.0, "male_45_64_high_school": 20948.0, "male_45_64_some_college": 18080.0, "male_45_to_64": 69231.0, "male_male_households": 568.0, "management_business_sci_arts_employed": 66084.0, "no_car": 4986.0, "no_cars": 12661.0, "not_us_citizen_pop": 17131.0, "occupation_management_arts": 66084.0, "occupation_natural_resources_construction_maintenance": 22459.0, "occupation_production_transportation_material": 19059.0, "occupation_sales_office": 56299.0, "occupation_services": 43707.0, "one_car": 79403.0, "two_cars": 75335.0, "three_cars": 22105.0, "pop_25_64": 252961.0, "pop_determined_poverty_status": 479694.0, "population_1_year_and_over": 491115.0, "population_3_years_over": 480866.0, "poverty": 69228.0, "sales_office_employed": 56299.0, "some_college_and_associates_degree": 121953.0, "walked_to_work": 3390.0, "worked_at_home": 9030.0, "workers_16_and_over": 204740.0, "associates_degree": 33655.0, "bachelors_degree": 48034.0, "high_school_diploma": 96303.0, "less_one_year_college": 25709.0, "masters_degree": 19089.0, "one_year_more_college": 62589.0, "pop_25_years_over": 356280.0, "commute_35_44_mins": 11680.0, "commute_60_more_mins": 15107.0, "commute_less_10_mins": 23675.0, "commuters_16_over": 195710.0, "hispanic_any_race": 54591.0, "pop_5_years_over": 470911.0, "speak_only_english_at_home": 410712.0, "speak_spanish_at_home": 40183.0, "speak_spanish_at_home_low_english": 13644.0, "pop_15_and_over": 418560.0, "pop_never_married": 112697.0, "pop_now_married": 207146.0, "pop_separated": 7734.0, "pop_widowed": 31927.0, "pop_divorced": 53146.0, "do_date": "20082010", "armed_forces": NaN, "civilian_labor_force": NaN, "employed_pop": NaN, "unemployed_pop": NaN, "not_in_labor_force": NaN, "pop_16_over": NaN, "pop_in_labor_force": NaN}, {"geo_id": "38300", "nonfamily_households": 370379.0, "family_households": 615102.0, "median_year_structure_built": 1957.0, "rent_burden_not_computed": 23741.0, "rent_over_50_percent": 63931.0, "rent_40_to_50_percent": 21630.0, "rent_35_to_40_percent": 15826.0, "rent_30_to_35_percent": 22208.0, "rent_25_to_30_percent": 30860.0, "rent_20_to_25_percent": 34519.0, "rent_15_to_20_percent": 35160.0, "rent_10_to_15_percent": 27065.0, "rent_under_10_percent": 13035.0, "total_pop": 2356205.0, "male_pop": 1137105.0, "female_pop": 1219100.0, "median_age": 42.4, "white_pop": 2054810.0, "black_pop": 190377.0, "asian_pop": 40138.0, "hispanic_pop": 29544.0, "amerindian_pop": 2096.0, "other_race_pop": 3457.0, "two_or_more_races_pop": 35352.0, "not_hispanic_pop": 2326661.0, "commuters_by_public_transportation": 64082.0, "households": 985481.0, "median_income": 47549.0, "income_per_capita": 27453.0, "housing_units": 1102399.0, "vacant_housing_units": 116918.0, "vacant_housing_units_for_rent": 18515.0, "vacant_housing_units_for_sale": 15140.0, "median_rent": 516.0, "percent_income_spent_on_rent": 28.6, "owner_occupied_housing_units": 697506.0, "million_dollar_housing_units": 3282.0, "mortgaged_housing_units": 420791.0, "families_with_young_children": 141994.0, "two_parent_families_with_young_children": 93376.0, "two_parents_in_labor_force_families_with_young_children": 59924.0, "two_parents_father_in_labor_force_families_with_young_children": 29855.0, "two_parents_mother_in_labor_force_families_with_young_children": 2441.0, "two_parents_not_in_labor_force_families_with_young_children": 1156.0, "one_parent_families_with_young_children": 48618.0, "father_one_parent_families_with_young_children": 9028.0, "father_in_labor_force_one_parent_families_with_young_children": 8106.0, "commute_10_14_mins": 143790.0, "commute_15_19_mins": 153191.0, "commute_20_24_mins": 155542.0, "commute_25_29_mins": 68995.0, "commute_30_34_mins": 141792.0, "commute_45_59_mins": 93226.0, "aggregate_travel_time_to_work": 27259200.0, "income_less_10000": 73797.0, "income_10000_14999": 62140.0, "income_15000_19999": 62534.0, "income_20000_24999": 62963.0, "income_25000_29999": 55238.0, "income_30000_34999": 54795.0, "income_35000_39999": 48929.0, "income_40000_44999": 48922.0, "income_45000_49999": 43987.0, "income_50000_59999": 82252.0, "income_60000_74999": 103450.0, "income_75000_99999": 116276.0, "income_100000_124999": 69235.0, "income_125000_149999": 35854.0, "income_150000_199999": 33687.0, "income_200000_or_more": 31422.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 654.0, "owner_occupied_housing_units_lower_value_quartile": 76800.0, "owner_occupied_housing_units_median_value": 121200.0, "owner_occupied_housing_units_upper_value_quartile": 190200.0, "married_households": 467571.0, "occupied_housing_units": 985481.0, "housing_units_renter_occupied": 287975.0, "dwellings_1_units_detached": 739174.0, "dwellings_1_units_attached": 87189.0, "dwellings_2_units": 47250.0, "dwellings_3_to_4_units": 44336.0, "dwellings_5_to_9_units": 42348.0, "dwellings_10_to_19_units": 32681.0, "dwellings_20_to_49_units": 25457.0, "dwellings_50_or_more_units": 38842.0, "mobile_homes": 44955.0, "housing_built_2005_or_later": 22784.0, "housing_built_2000_to_2004": 45405.0, "housing_built_1939_or_earlier": 314974.0, "male_under_5": 61745.0, "male_5_to_9": 67164.0, "male_10_to_14": 69064.0, "male_15_to_17": 47322.0, "male_18_to_19": 33013.0, "male_20": 17529.0, "male_21": 14809.0, "male_22_to_24": 42887.0, "male_25_to_29": 71320.0, "male_30_to_34": 64422.0, "male_35_to_39": 70719.0, "male_40_to_44": 75494.0, "male_45_to_49": 88222.0, "male_50_to_54": 94802.0, "male_55_to_59": 83241.0, "male_60_61": 31791.0, "male_62_64": 36867.0, "male_65_to_66": 21645.0, "male_67_to_69": 27681.0, "male_70_to_74": 37666.0, "male_75_to_79": 33283.0, "male_80_to_84": 25634.0, "male_85_and_over": 20785.0, "female_under_5": 59161.0, "female_5_to_9": 63184.0, "female_10_to_14": 67495.0, "female_15_to_17": 44017.0, "female_18_to_19": 32037.0, "female_20": 16276.0, "female_21": 14973.0, "female_22_to_24": 42314.0, "female_25_to_29": 70035.0, "female_30_to_34": 64124.0, "female_35_to_39": 71223.0, "female_40_to_44": 79585.0, "female_45_to_49": 92683.0, "female_50_to_54": 98730.0, "female_55_to_59": 88239.0, "female_60_to_61": 33517.0, "female_62_to_64": 40560.0, "female_65_to_66": 25582.0, "female_67_to_69": 32570.0, "female_70_to_74": 48470.0, "female_75_to_79": 46899.0, "female_80_to_84": 43018.0, "female_85_and_over": 44408.0, "white_including_hispanic": 2074512.0, "black_including_hispanic": 192528.0, "amerindian_including_hispanic": 2368.0, "asian_including_hispanic": 40398.0, "commute_5_9_mins": 106049.0, "commute_35_39_mins": 35917.0, "commute_40_44_mins": 47138.0, "commute_60_89_mins": 56746.0, "commute_90_more_mins": 20355.0, "households_retirement_income": 211515.0, "asian_male_45_54": 1936.0, "asian_male_55_64": 1260.0, "black_male_45_54": 12330.0, "black_male_55_64": 9311.0, "hispanic_male_45_54": 1487.0, "hispanic_male_55_64": 904.0, "white_male_45_54": 166070.0, "white_male_55_64": 139468.0, "bachelors_degree_2": 299046.0, "bachelors_degree_or_higher_25_64": 412818.0, "children": 479152.0, "children_in_single_female_hh": 117784.0, "commuters_by_bus": 58943.0, "commuters_by_car_truck_van": 944809.0, "commuters_by_carpool": 103143.0, "commuters_by_subway_or_elevated": 1681.0, "commuters_drove_alone": 841666.0, "different_house_year_ago_different_city": 211746.0, "different_house_year_ago_same_city": 57966.0, "employed_agriculture_forestry_fishing_hunting_mining": 9252.0, "employed_arts_entertainment_recreation_accommodation_food": 98820.0, "employed_construction": 65997.0, "employed_education_health_social": 294596.0, "employed_finance_insurance_real_estate": 78672.0, "employed_information": 22572.0, "employed_manufacturing": 117080.0, "employed_other_services_not_public_admin": 53999.0, "employed_public_administration": 36841.0, "employed_retail_trade": 132780.0, "employed_science_management_admin_waste": 111520.0, "employed_transportation_warehousing_utilities": 64713.0, "employed_wholesale_trade": 33966.0, "female_female_households": 2133.0, "four_more_cars": 39778.0, "gini_index": 0.462, "graduate_professional_degree": 178597.0, "group_quarters": 64937.0, "high_school_including_ged": 612309.0, "households_public_asst_or_food_stamps": 111346.0, "in_grades_1_to_4": 104797.0, "in_grades_5_to_8": 106027.0, "in_grades_9_to_12": 121745.0, "in_school": 556743.0, "in_undergrad_college": 127504.0, "less_than_high_school_graduate": 147966.0, "male_45_64_associates_degree": 27124.0, "male_45_64_bachelors_degree": 61607.0, "male_45_64_graduate_degree": 40498.0, "male_45_64_less_than_9_grade": 4829.0, "male_45_64_grade_9_12": 15467.0, "male_45_64_high_school": 128809.0, "male_45_64_some_college": 56589.0, "male_45_to_64": 334923.0, "male_male_households": 1737.0, "management_business_sci_arts_employed": 414722.0, "no_car": 45827.0, "no_cars": 111622.0, "not_us_citizen_pop": 37046.0, "occupation_management_arts": 414722.0, "occupation_natural_resources_construction_maintenance": 92579.0, "occupation_production_transportation_material": 129857.0, "occupation_sales_office": 285057.0, "occupation_services": 198593.0, "one_car": 355334.0, "two_cars": 364350.0, "three_cars": 114397.0, "pop_25_64": 1255574.0, "pop_determined_poverty_status": 2296888.0, "population_1_year_and_over": 2332700.0, "population_3_years_over": 2284305.0, "poverty": 277327.0, "sales_office_employed": 285057.0, "some_college_and_associates_degree": 425297.0, "walked_to_work": 40833.0, "worked_at_home": 36008.0, "workers_16_and_over": 1097231.0, "associates_degree": 147644.0, "bachelors_degree": 299046.0, "high_school_diploma": 549305.0, "less_one_year_college": 94129.0, "masters_degree": 125880.0, "one_year_more_college": 183524.0, "pop_25_years_over": 1663215.0, "commute_35_44_mins": 83055.0, "commute_60_more_mins": 77101.0, "commute_less_10_mins": 144531.0, "commuters_16_over": 1061223.0, "hispanic_any_race": 29544.0, "pop_5_years_over": 2235299.0, "speak_only_english_at_home": 2121486.0, "speak_spanish_at_home": 25264.0, "speak_spanish_at_home_low_english": 6507.0, "pop_15_and_over": 1968392.0, "pop_never_married": 605824.0, "pop_now_married": 972517.0, "pop_separated": 41482.0, "pop_widowed": 157583.0, "pop_divorced": 181025.0, "do_date": "20082010", "armed_forces": NaN, "civilian_labor_force": NaN, "employed_pop": NaN, "unemployed_pop": NaN, "not_in_labor_force": NaN, "pop_16_over": NaN, "pop_in_labor_force": NaN}, {"geo_id": "16980", "nonfamily_households": 1149913.0, "family_households": 2272467.0, "median_year_structure_built": 1966.0, "rent_burden_not_computed": 65197.0, "rent_over_50_percent": 294853.0, "rent_40_to_50_percent": 96014.0, "rent_35_to_40_percent": 70337.0, "rent_30_to_35_percent": 94350.0, "rent_25_to_30_percent": 121321.0, "rent_20_to_25_percent": 131721.0, "rent_15_to_20_percent": 130393.0, "rent_10_to_15_percent": 85951.0, "rent_under_10_percent": 35488.0, "total_pop": 9429473.0, "male_pop": 4609726.0, "female_pop": 4819747.0, "median_age": 35.6, "white_pop": 5213409.0, "black_pop": 1617066.0, "asian_pop": 526527.0, "hispanic_pop": 1921084.0, "amerindian_pop": 9922.0, "other_race_pop": 20516.0, "two_or_more_races_pop": 118632.0, "not_hispanic_pop": 7508389.0, "commuters_by_public_transportation": 499481.0, "households": 3422380.0, "median_income": 59707.0, "income_per_capita": 29963.0, "housing_units": 3794332.0, "vacant_housing_units": 371952.0, "vacant_housing_units_for_rent": 99671.0, "vacant_housing_units_for_sale": 65058.0, "median_rent": 794.0, "percent_income_spent_on_rent": 31.3, "owner_occupied_housing_units": 2296755.0, "million_dollar_housing_units": 52131.0, "mortgaged_housing_units": 1704298.0, "families_with_young_children": 745624.0, "two_parent_families_with_young_children": 504592.0, "two_parents_in_labor_force_families_with_young_children": 288232.0, "two_parents_father_in_labor_force_families_with_young_children": 199744.0, "two_parents_mother_in_labor_force_families_with_young_children": 10962.0, "two_parents_not_in_labor_force_families_with_young_children": 5654.0, "one_parent_families_with_young_children": 241032.0, "father_one_parent_families_with_young_children": 50866.0, "father_in_labor_force_one_parent_families_with_young_children": 46595.0, "commute_10_14_mins": 460537.0, "commute_15_19_mins": 497539.0, "commute_20_24_mins": 528395.0, "commute_25_29_mins": 232773.0, "commute_30_34_mins": 662733.0, "commute_45_59_mins": 501595.0, "aggregate_travel_time_to_work": *********.0, "income_less_10000": 226966.0, "income_10000_14999": 148070.0, "income_15000_19999": 158356.0, "income_20000_24999": 165697.0, "income_25000_29999": 153031.0, "income_30000_34999": 159042.0, "income_35000_39999": 147318.0, "income_40000_44999": 151742.0, "income_45000_49999": 137891.0, "income_50000_59999": 269391.0, "income_60000_74999": 357736.0, "income_75000_99999": 458560.0, "income_100000_124999": 313853.0, "income_125000_149999": 189471.0, "income_150000_199999": 191371.0, "income_200000_or_more": 193885.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 908.0, "owner_occupied_housing_units_lower_value_quartile": 168900.0, "owner_occupied_housing_units_median_value": 250500.0, "owner_occupied_housing_units_upper_value_quartile": 372300.0, "married_households": 1657892.0, "occupied_housing_units": 3422380.0, "housing_units_renter_occupied": 1125625.0, "dwellings_1_units_detached": 1965701.0, "dwellings_1_units_attached": 279286.0, "dwellings_2_units": 267889.0, "dwellings_3_to_4_units": 303904.0, "dwellings_5_to_9_units": 287939.0, "dwellings_10_to_19_units": 176822.0, "dwellings_20_to_49_units": 166118.0, "dwellings_50_or_more_units": 307012.0, "mobile_homes": 38599.0, "housing_built_2005_or_later": 153790.0, "housing_built_2000_to_2004": 279792.0, "housing_built_1939_or_earlier": 861097.0, "male_under_5": 325359.0, "male_5_to_9": 332942.0, "male_10_to_14": 343805.0, "male_15_to_17": 216243.0, "male_18_to_19": 135702.0, "male_20": 64305.0, "male_21": 60647.0, "male_22_to_24": 192922.0, "male_25_to_29": 347704.0, "male_30_to_34": 328796.0, "male_35_to_39": 333244.0, "male_40_to_44": 327173.0, "male_45_to_49": 343588.0, "male_50_to_54": 326186.0, "male_55_to_59": 274269.0, "male_60_61": 98613.0, "male_62_64": 115233.0, "male_65_to_66": 67941.0, "male_67_to_69": 81797.0, "male_70_to_74": 106852.0, "male_75_to_79": 81682.0, "male_80_to_84": 60361.0, "male_85_and_over": 44362.0, "female_under_5": 312315.0, "female_5_to_9": 320860.0, "female_10_to_14": 327439.0, "female_15_to_17": 205429.0, "female_18_to_19": 125910.0, "female_20": 61211.0, "female_21": 59716.0, "female_22_to_24": 187630.0, "female_25_to_29": 351860.0, "female_30_to_34": 333126.0, "female_35_to_39": 336000.0, "female_40_to_44": 339716.0, "female_45_to_49": 358320.0, "female_50_to_54": 345006.0, "female_55_to_59": 296212.0, "female_60_to_61": 108123.0, "female_62_to_64": 127368.0, "female_65_to_66": 79091.0, "female_67_to_69": 96945.0, "female_70_to_74": 135315.0, "female_75_to_79": 111661.0, "female_80_to_84": 94897.0, "female_85_and_over": 105597.0, "white_including_hispanic": 6225797.0, "black_including_hispanic": 1640721.0, "amerindian_including_hispanic": 20236.0, "asian_including_hispanic": 530947.0, "commute_5_9_mins": 305548.0, "commute_35_39_mins": 143813.0, "commute_40_44_mins": 235969.0, "commute_60_89_mins": 426683.0, "commute_90_more_mins": 142019.0, "households_retirement_income": 499726.0, "asian_male_45_54": 32427.0, "asian_male_55_64": 26792.0, "black_male_45_54": 103591.0, "black_male_55_64": 72725.0, "hispanic_male_45_54": 99489.0, "hispanic_male_55_64": 54168.0, "white_male_45_54": 429463.0, "white_male_55_64": 331744.0, "bachelors_degree_2": 1287076.0, "bachelors_degree_or_higher_25_64": 1848563.0, "children": 2384392.0, "children_in_single_female_hh": 582883.0, "commuters_by_bus": 207487.0, "commuters_by_car_truck_van": 3510183.0, "commuters_by_carpool": 387756.0, "commuters_by_subway_or_elevated": 146882.0, "commuters_drove_alone": 3122427.0, "different_house_year_ago_different_city": 610204.0, "different_house_year_ago_same_city": 513015.0, "employed_agriculture_forestry_fishing_hunting_mining": 14398.0, "employed_arts_entertainment_recreation_accommodation_food": 407224.0, "employed_construction": 247695.0, "employed_education_health_social": 950905.0, "employed_finance_insurance_real_estate": 366687.0, "employed_information": 105809.0, "employed_manufacturing": 567788.0, "employed_other_services_not_public_admin": 214982.0, "employed_public_administration": 155023.0, "employed_retail_trade": 479746.0, "employed_science_management_admin_waste": 558805.0, "employed_transportation_warehousing_utilities": 266916.0, "employed_wholesale_trade": 152442.0, "female_female_households": 7145.0, "four_more_cars": 155777.0, "gini_index": 0.468, "graduate_professional_degree": 792433.0, "group_quarters": 153206.0, "high_school_including_ged": 1547669.0, "households_public_asst_or_food_stamps": 360154.0, "in_grades_1_to_4": 520844.0, "in_grades_5_to_8": 535987.0, "in_grades_9_to_12": 566852.0, "in_school": 2617657.0, "in_undergrad_college": 519402.0, "less_than_high_school_graduate": 856415.0, "male_45_64_associates_degree": 73766.0, "male_45_64_bachelors_degree": 229403.0, "male_45_64_graduate_degree": 172338.0, "male_45_64_less_than_9_grade": 69044.0, "male_45_64_grade_9_12": 78594.0, "male_45_64_high_school": 293118.0, "male_45_64_some_college": 241626.0, "male_45_to_64": 1157889.0, "male_male_households": 9349.0, "management_business_sci_arts_employed": 1675998.0, "no_car": 247176.0, "no_cars": 405723.0, "not_us_citizen_pop": 913939.0, "occupation_management_arts": 1675998.0, "occupation_natural_resources_construction_maintenance": 330207.0, "occupation_production_transportation_material": 592063.0, "occupation_sales_office": 1159579.0, "occupation_services": 730573.0, "one_car": 1212308.0, "two_cars": 1243484.0, "three_cars": 405088.0, "pop_25_64": 5090537.0, "pop_determined_poverty_status": 9282547.0, "population_1_year_and_over": 9307187.0, "population_3_years_over": 9050741.0, "poverty": 1174457.0, "sales_office_employed": 1159579.0, "some_college_and_associates_degree": 1673445.0, "walked_to_work": 136485.0, "worked_at_home": 184447.0, "workers_16_and_over": 4403255.0, "associates_degree": 408914.0, "bachelors_degree": 1287076.0, "high_school_diploma": 1379742.0, "less_one_year_college": 361255.0, "masters_degree": 572380.0, "one_year_more_college": 903276.0, "pop_25_years_over": 6157038.0, "commute_35_44_mins": 379782.0, "commute_60_more_mins": 568702.0, "commute_less_10_mins": 386752.0, "commuters_16_over": 4218808.0, "hispanic_any_race": 1921084.0, "pop_5_years_over": 8791799.0, "speak_only_english_at_home": 6300032.0, "speak_spanish_at_home": 1475740.0, "speak_spanish_at_home_low_english": 699689.0, "pop_15_and_over": 7466753.0, "pop_never_married": 2637455.0, "pop_now_married": 3593560.0, "pop_separated": 133967.0, "pop_widowed": 348901.0, "pop_divorced": 579108.0, "do_date": "20082010", "armed_forces": NaN, "civilian_labor_force": NaN, "employed_pop": NaN, "unemployed_pop": NaN, "not_in_labor_force": NaN, "pop_16_over": NaN, "pop_in_labor_force": NaN}, {"geo_id": "37100", "nonfamily_households": 69056.0, "family_households": 196173.0, "median_year_structure_built": 1975.0, "rent_burden_not_computed": 3976.0, "rent_over_50_percent": 23590.0, "rent_40_to_50_percent": 10381.0, "rent_35_to_40_percent": 7107.0, "rent_30_to_35_percent": 8576.0, "rent_25_to_30_percent": 11673.0, "rent_20_to_25_percent": 11130.0, "rent_15_to_20_percent": 9848.0, "rent_10_to_15_percent": 4662.0, "rent_under_10_percent": 2054.0, "total_pop": 815730.0, "male_pop": 405308.0, "female_pop": 410422.0, "median_age": 36.2, "white_pop": 402057.0, "black_pop": 12669.0, "asian_pop": 54966.0, "hispanic_pop": 324148.0, "amerindian_pop": 1566.0, "other_race_pop": 1219.0, "two_or_more_races_pop": 17751.0, "not_hispanic_pop": 491582.0, "commuters_by_public_transportation": 4711.0, "households": 265229.0, "median_income": 73907.0, "income_per_capita": 31679.0, "housing_units": 280950.0, "vacant_housing_units": 15721.0, "vacant_housing_units_for_rent": 4399.0, "vacant_housing_units_for_sale": 2642.0, "median_rent": 1302.0, "percent_income_spent_on_rent": 33.0, "owner_occupied_housing_units": 172232.0, "million_dollar_housing_units": 13069.0, "mortgaged_housing_units": 134544.0, "families_with_young_children": 65175.0, "two_parent_families_with_young_children": 45293.0, "two_parents_in_labor_force_families_with_young_children": 24628.0, "two_parents_father_in_labor_force_families_with_young_children": 18812.0, "two_parents_mother_in_labor_force_families_with_young_children": 1289.0, "two_parents_not_in_labor_force_families_with_young_children": 564.0, "one_parent_families_with_young_children": 19882.0, "father_one_parent_families_with_young_children": 5745.0, "father_in_labor_force_one_parent_families_with_young_children": 4936.0, "commute_10_14_mins": 58924.0, "commute_15_19_mins": 59684.0, "commute_20_24_mins": 49942.0, "commute_25_29_mins": 19418.0, "commute_30_34_mins": 43679.0, "commute_45_59_mins": 24354.0, "aggregate_travel_time_to_work": 8987825.0, "income_less_10000": 9826.0, "income_10000_14999": 9622.0, "income_15000_19999": 9403.0, "income_20000_24999": 10463.0, "income_25000_29999": 10011.0, "income_30000_34999": 10036.0, "income_35000_39999": 8581.0, "income_40000_44999": 11723.0, "income_45000_49999": 8661.0, "income_50000_59999": 19446.0, "income_60000_74999": 26467.0, "income_75000_99999": 36210.0, "income_100000_124999": 31712.0, "income_125000_149999": 18996.0, "income_150000_199999": 22169.0, "income_200000_or_more": 21903.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 1404.0, "owner_occupied_housing_units_lower_value_quartile": 348300.0, "owner_occupied_housing_units_median_value": 497000.0, "owner_occupied_housing_units_upper_value_quartile": 693700.0, "married_households": 148058.0, "occupied_housing_units": 265229.0, "housing_units_renter_occupied": 92997.0, "dwellings_1_units_detached": 179204.0, "dwellings_1_units_attached": 32333.0, "dwellings_2_units": 3889.0, "dwellings_3_to_4_units": 11613.0, "dwellings_5_to_9_units": 12957.0, "dwellings_10_to_19_units": 10497.0, "dwellings_20_to_49_units": 8555.0, "dwellings_50_or_more_units": 10387.0, "mobile_homes": 11191.0, "housing_built_2005_or_later": 9468.0, "housing_built_2000_to_2004": 20670.0, "housing_built_1939_or_earlier": 10727.0, "male_under_5": 28283.0, "male_5_to_9": 29335.0, "male_10_to_14": 30443.0, "male_15_to_17": 20307.0, "male_18_to_19": 12979.0, "male_20": 7217.0, "male_21": 6483.0, "male_22_to_24": 15973.0, "male_25_to_29": 27849.0, "male_30_to_34": 25365.0, "male_35_to_39": 27503.0, "male_40_to_44": 28455.0, "male_45_to_49": 30934.0, "male_50_to_54": 29334.0, "male_55_to_59": 23994.0, "male_60_61": 9418.0, "male_62_64": 10483.0, "male_65_to_66": 5849.0, "male_67_to_69": 8347.0, "male_70_to_74": 8968.0, "male_75_to_79": 7332.0, "male_80_to_84": 5913.0, "male_85_and_over": 4544.0, "female_under_5": 27381.0, "female_5_to_9": 29546.0, "female_10_to_14": 27610.0, "female_15_to_17": 19377.0, "female_18_to_19": 11522.0, "female_20": 6069.0, "female_21": 5439.0, "female_22_to_24": 14693.0, "female_25_to_29": 25639.0, "female_30_to_34": 24652.0, "female_35_to_39": 26454.0, "female_40_to_44": 30280.0, "female_45_to_49": 31903.0, "female_50_to_54": 30333.0, "female_55_to_59": 24165.0, "female_60_to_61": 10005.0, "female_62_to_64": 12218.0, "female_65_to_66": 7732.0, "female_67_to_69": 7975.0, "female_70_to_74": 10861.0, "female_75_to_79": 9800.0, "female_80_to_84": 8596.0, "female_85_and_over": 8172.0, "white_including_hispanic": 570912.0, "black_including_hispanic": 14132.0, "amerindian_including_hispanic": 11732.0, "asian_including_hispanic": 56417.0, "commute_5_9_mins": 41389.0, "commute_35_39_mins": 10400.0, "commute_40_44_mins": 11622.0, "commute_60_89_mins": 20183.0, "commute_90_more_mins": 10505.0, "households_retirement_income": 47352.0, "asian_male_45_54": 4253.0, "asian_male_55_64": 2960.0, "black_male_45_54": 1216.0, "black_male_55_64": 884.0, "hispanic_male_45_54": 17409.0, "hispanic_male_55_64": 9488.0, "white_male_45_54": 36430.0, "white_male_55_64": 29903.0, "bachelors_degree_2": 101547.0, "bachelors_degree_or_higher_25_64": 135702.0, "children": 212282.0, "children_in_single_female_hh": 38289.0, "commuters_by_bus": 3626.0, "commuters_by_car_truck_van": 340152.0, "commuters_by_carpool": 47964.0, "commuters_by_subway_or_elevated": 139.0, "commuters_drove_alone": 292188.0, "different_house_year_ago_different_city": 63897.0, "different_house_year_ago_same_city": 48016.0, "employed_agriculture_forestry_fishing_hunting_mining": 17011.0, "employed_arts_entertainment_recreation_accommodation_food": 33919.0, "employed_construction": 22371.0, "employed_education_health_social": 71794.0, "employed_finance_insurance_real_estate": 32181.0, "employed_information": 10925.0, "employed_manufacturing": 41640.0, "employed_other_services_not_public_admin": 18537.0, "employed_public_administration": 20263.0, "employed_retail_trade": 42492.0, "employed_science_management_admin_waste": 47981.0, "employed_transportation_warehousing_utilities": 13074.0, "employed_wholesale_trade": 13385.0, "female_female_households": 650.0, "four_more_cars": 24426.0, "gini_index": 0.436, "graduate_professional_degree": 58596.0, "group_quarters": 10649.0, "high_school_including_ged": 99399.0, "households_public_asst_or_food_stamps": 15785.0, "in_grades_1_to_4": 47837.0, "in_grades_5_to_8": 45508.0, "in_grades_9_to_12": 52315.0, "in_school": 230955.0, "in_undergrad_college": 50385.0, "less_than_high_school_graduate": 92516.0, "male_45_64_associates_degree": 8160.0, "male_45_64_bachelors_degree": 22330.0, "male_45_64_graduate_degree": 16136.0, "male_45_64_less_than_9_grade": 9400.0, "male_45_64_grade_9_12": 5671.0, "male_45_64_high_school": 17063.0, "male_45_64_some_college": 25403.0, "male_45_to_64": 104163.0, "male_male_households": 720.0, "management_business_sci_arts_employed": 144816.0, "no_car": 5724.0, "no_cars": 11092.0, "not_us_citizen_pop": 108268.0, "occupation_management_arts": 144816.0, "occupation_natural_resources_construction_maintenance": 42931.0, "occupation_production_transportation_material": 36669.0, "occupation_sales_office": 98170.0, "occupation_services": 62987.0, "one_car": 71766.0, "two_cars": 107846.0, "three_cars": 50099.0, "pop_25_64": 428984.0, "pop_determined_poverty_status": 804989.0, "population_1_year_and_over": 805929.0, "population_3_years_over": 782706.0, "poverty": 79199.0, "sales_office_employed": 98170.0, "some_college_and_associates_degree": 171015.0, "walked_to_work": 7797.0, "worked_at_home": 20293.0, "workers_16_and_over": 379484.0, "associates_degree": 42735.0, "bachelors_degree": 101547.0, "high_school_diploma": 90026.0, "less_one_year_college": 35240.0, "masters_degree": 39636.0, "one_year_more_college": 93040.0, "pop_25_years_over": 523073.0, "commute_35_44_mins": 22022.0, "commute_60_more_mins": 30688.0, "commute_less_10_mins": 50480.0, "commuters_16_over": 359191.0, "hispanic_any_race": 324148.0, "pop_5_years_over": 760066.0, "speak_only_english_at_home": 473944.0, "speak_spanish_at_home": 225553.0, "speak_spanish_at_home_low_english": 111515.0, "pop_15_and_over": 643132.0, "pop_never_married": 200815.0, "pop_now_married": 333428.0, "pop_separated": 10665.0, "pop_widowed": 23363.0, "pop_divorced": 53475.0, "do_date": "20082010", "armed_forces": NaN, "civilian_labor_force": NaN, "employed_pop": NaN, "unemployed_pop": NaN, "not_in_labor_force": NaN, "pop_16_over": NaN, "pop_in_labor_force": NaN}]}