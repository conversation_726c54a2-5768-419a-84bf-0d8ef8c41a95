{"table_name": "place_2015_1yr", "table_fullname": "bigquery-public-data.census_bureau_acs.place_2015_1yr", "column_names": ["geo_id", "nonfamily_households", "family_households", "median_year_structure_built", "rent_burden_not_computed", "rent_over_50_percent", "rent_40_to_50_percent", "rent_35_to_40_percent", "rent_30_to_35_percent", "rent_25_to_30_percent", "rent_20_to_25_percent", "rent_15_to_20_percent", "rent_10_to_15_percent", "rent_under_10_percent", "total_pop", "male_pop", "female_pop", "median_age", "white_pop", "black_pop", "asian_pop", "hispanic_pop", "amerindian_pop", "other_race_pop", "two_or_more_races_pop", "not_hispanic_pop", "commuters_by_public_transportation", "households", "median_income", "income_per_capita", "housing_units", "vacant_housing_units", "vacant_housing_units_for_rent", "vacant_housing_units_for_sale", "median_rent", "percent_income_spent_on_rent", "owner_occupied_housing_units", "million_dollar_housing_units", "mortgaged_housing_units", "families_with_young_children", "two_parent_families_with_young_children", "two_parents_in_labor_force_families_with_young_children", "two_parents_father_in_labor_force_families_with_young_children", "two_parents_mother_in_labor_force_families_with_young_children", "two_parents_not_in_labor_force_families_with_young_children", "one_parent_families_with_young_children", "father_one_parent_families_with_young_children", "father_in_labor_force_one_parent_families_with_young_children", "commute_10_14_mins", "commute_15_19_mins", "commute_20_24_mins", "commute_25_29_mins", "commute_30_34_mins", "commute_45_59_mins", "aggregate_travel_time_to_work", "income_less_10000", "income_10000_14999", "income_15000_19999", "income_20000_24999", "income_25000_29999", "income_30000_34999", "income_35000_39999", "income_40000_44999", "income_45000_49999", "income_50000_59999", "income_60000_74999", "income_75000_99999", "income_100000_124999", "income_125000_149999", "income_150000_199999", "income_200000_or_more", "renter_occupied_housing_units_paying_cash_median_gross_rent", "owner_occupied_housing_units_lower_value_quartile", "owner_occupied_housing_units_median_value", "owner_occupied_housing_units_upper_value_quartile", "married_households", "occupied_housing_units", "housing_units_renter_occupied", "dwellings_1_units_detached", "dwellings_1_units_attached", "dwellings_2_units", "dwellings_3_to_4_units", "dwellings_5_to_9_units", "dwellings_10_to_19_units", "dwellings_20_to_49_units", "dwellings_50_or_more_units", "mobile_homes", "housing_built_2005_or_later", "housing_built_2000_to_2004", "housing_built_1939_or_earlier", "male_under_5", "male_5_to_9", "male_10_to_14", "male_15_to_17", "male_18_to_19", "male_20", "male_21", "male_22_to_24", "male_25_to_29", "male_30_to_34", "male_35_to_39", "male_40_to_44", "male_45_to_49", "male_50_to_54", "male_55_to_59", "male_60_61", "male_62_64", "male_65_to_66", "male_67_to_69", "male_70_to_74", "male_75_to_79", "male_80_to_84", "male_85_and_over", "female_under_5", "female_5_to_9", "female_10_to_14", "female_15_to_17", "female_18_to_19", "female_20", "female_21", "female_22_to_24", "female_25_to_29", "female_30_to_34", "female_35_to_39", "female_40_to_44", "female_45_to_49", "female_50_to_54", "female_55_to_59", "female_60_to_61", "female_62_to_64", "female_65_to_66", "female_67_to_69", "female_70_to_74", "female_75_to_79", "female_80_to_84", "female_85_and_over", "white_including_hispanic", "black_including_hispanic", "amerindian_including_hispanic", "asian_including_hispanic", "commute_5_9_mins", "commute_35_39_mins", "commute_40_44_mins", "commute_60_89_mins", "commute_90_more_mins", "households_retirement_income", "armed_forces", "civilian_labor_force", "employed_pop", "unemployed_pop", "not_in_labor_force", "pop_16_over", "pop_in_labor_force", "asian_male_45_54", "asian_male_55_64", "black_male_45_54", "black_male_55_64", "hispanic_male_45_54", "hispanic_male_55_64", "white_male_45_54", "white_male_55_64", "bachelors_degree_2", "bachelors_degree_or_higher_25_64", "children", "children_in_single_female_hh", "commuters_by_bus", "commuters_by_car_truck_van", "commuters_by_carpool", "commuters_by_subway_or_elevated", "commuters_drove_alone", "different_house_year_ago_different_city", "different_house_year_ago_same_city", "employed_agriculture_forestry_fishing_hunting_mining", "employed_arts_entertainment_recreation_accommodation_food", "employed_construction", "employed_education_health_social", "employed_finance_insurance_real_estate", "employed_information", "employed_manufacturing", "employed_other_services_not_public_admin", "employed_public_administration", "employed_retail_trade", "employed_science_management_admin_waste", "employed_transportation_warehousing_utilities", "employed_wholesale_trade", "female_female_households", "four_more_cars", "gini_index", "graduate_professional_degree", "group_quarters", "high_school_including_ged", "households_public_asst_or_food_stamps", "in_grades_1_to_4", "in_grades_5_to_8", "in_grades_9_to_12", "in_school", "in_undergrad_college", "less_than_high_school_graduate", "male_45_64_associates_degree", "male_45_64_bachelors_degree", "male_45_64_graduate_degree", "male_45_64_less_than_9_grade", "male_45_64_grade_9_12", "male_45_64_high_school", "male_45_64_some_college", "male_45_to_64", "male_male_households", "management_business_sci_arts_employed", "no_car", "no_cars", "not_us_citizen_pop", "occupation_management_arts", "occupation_natural_resources_construction_maintenance", "occupation_production_transportation_material", "occupation_sales_office", "occupation_services", "one_car", "two_cars", "three_cars", "pop_25_64", "pop_determined_poverty_status", "population_1_year_and_over", "population_3_years_over", "poverty", "sales_office_employed", "some_college_and_associates_degree", "walked_to_work", "worked_at_home", "workers_16_and_over", "associates_degree", "bachelors_degree", "high_school_diploma", "less_one_year_college", "masters_degree", "one_year_more_college", "pop_25_years_over", "commute_35_44_mins", "commute_60_more_mins", "commute_less_10_mins", "commuters_16_over", "hispanic_any_race", "pop_5_years_over", "speak_only_english_at_home", "speak_spanish_at_home", "speak_spanish_at_home_low_english", "do_date", "pop_15_and_over", "pop_never_married", "pop_now_married", "pop_separated", "pop_widowed", "pop_divorced"], "column_types": ["STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64"], "nested_column_names": ["geo_id", "nonfamily_households", "family_households", "median_year_structure_built", "rent_burden_not_computed", "rent_over_50_percent", "rent_40_to_50_percent", "rent_35_to_40_percent", "rent_30_to_35_percent", "rent_25_to_30_percent", "rent_20_to_25_percent", "rent_15_to_20_percent", "rent_10_to_15_percent", "rent_under_10_percent", "total_pop", "male_pop", "female_pop", "median_age", "white_pop", "black_pop", "asian_pop", "hispanic_pop", "amerindian_pop", "other_race_pop", "two_or_more_races_pop", "not_hispanic_pop", "commuters_by_public_transportation", "households", "median_income", "income_per_capita", "housing_units", "vacant_housing_units", "vacant_housing_units_for_rent", "vacant_housing_units_for_sale", "median_rent", "percent_income_spent_on_rent", "owner_occupied_housing_units", "million_dollar_housing_units", "mortgaged_housing_units", "families_with_young_children", "two_parent_families_with_young_children", "two_parents_in_labor_force_families_with_young_children", "two_parents_father_in_labor_force_families_with_young_children", "two_parents_mother_in_labor_force_families_with_young_children", "two_parents_not_in_labor_force_families_with_young_children", "one_parent_families_with_young_children", "father_one_parent_families_with_young_children", "father_in_labor_force_one_parent_families_with_young_children", "commute_10_14_mins", "commute_15_19_mins", "commute_20_24_mins", "commute_25_29_mins", "commute_30_34_mins", "commute_45_59_mins", "aggregate_travel_time_to_work", "income_less_10000", "income_10000_14999", "income_15000_19999", "income_20000_24999", "income_25000_29999", "income_30000_34999", "income_35000_39999", "income_40000_44999", "income_45000_49999", "income_50000_59999", "income_60000_74999", "income_75000_99999", "income_100000_124999", "income_125000_149999", "income_150000_199999", "income_200000_or_more", "renter_occupied_housing_units_paying_cash_median_gross_rent", "owner_occupied_housing_units_lower_value_quartile", "owner_occupied_housing_units_median_value", "owner_occupied_housing_units_upper_value_quartile", "married_households", "occupied_housing_units", "housing_units_renter_occupied", "dwellings_1_units_detached", "dwellings_1_units_attached", "dwellings_2_units", "dwellings_3_to_4_units", "dwellings_5_to_9_units", "dwellings_10_to_19_units", "dwellings_20_to_49_units", "dwellings_50_or_more_units", "mobile_homes", "housing_built_2005_or_later", "housing_built_2000_to_2004", "housing_built_1939_or_earlier", "male_under_5", "male_5_to_9", "male_10_to_14", "male_15_to_17", "male_18_to_19", "male_20", "male_21", "male_22_to_24", "male_25_to_29", "male_30_to_34", "male_35_to_39", "male_40_to_44", "male_45_to_49", "male_50_to_54", "male_55_to_59", "male_60_61", "male_62_64", "male_65_to_66", "male_67_to_69", "male_70_to_74", "male_75_to_79", "male_80_to_84", "male_85_and_over", "female_under_5", "female_5_to_9", "female_10_to_14", "female_15_to_17", "female_18_to_19", "female_20", "female_21", "female_22_to_24", "female_25_to_29", "female_30_to_34", "female_35_to_39", "female_40_to_44", "female_45_to_49", "female_50_to_54", "female_55_to_59", "female_60_to_61", "female_62_to_64", "female_65_to_66", "female_67_to_69", "female_70_to_74", "female_75_to_79", "female_80_to_84", "female_85_and_over", "white_including_hispanic", "black_including_hispanic", "amerindian_including_hispanic", "asian_including_hispanic", "commute_5_9_mins", "commute_35_39_mins", "commute_40_44_mins", "commute_60_89_mins", "commute_90_more_mins", "households_retirement_income", "armed_forces", "civilian_labor_force", "employed_pop", "unemployed_pop", "not_in_labor_force", "pop_16_over", "pop_in_labor_force", "asian_male_45_54", "asian_male_55_64", "black_male_45_54", "black_male_55_64", "hispanic_male_45_54", "hispanic_male_55_64", "white_male_45_54", "white_male_55_64", "bachelors_degree_2", "bachelors_degree_or_higher_25_64", "children", "children_in_single_female_hh", "commuters_by_bus", "commuters_by_car_truck_van", "commuters_by_carpool", "commuters_by_subway_or_elevated", "commuters_drove_alone", "different_house_year_ago_different_city", "different_house_year_ago_same_city", "employed_agriculture_forestry_fishing_hunting_mining", "employed_arts_entertainment_recreation_accommodation_food", "employed_construction", "employed_education_health_social", "employed_finance_insurance_real_estate", "employed_information", "employed_manufacturing", "employed_other_services_not_public_admin", "employed_public_administration", "employed_retail_trade", "employed_science_management_admin_waste", "employed_transportation_warehousing_utilities", "employed_wholesale_trade", "female_female_households", "four_more_cars", "gini_index", "graduate_professional_degree", "group_quarters", "high_school_including_ged", "households_public_asst_or_food_stamps", "in_grades_1_to_4", "in_grades_5_to_8", "in_grades_9_to_12", "in_school", "in_undergrad_college", "less_than_high_school_graduate", "male_45_64_associates_degree", "male_45_64_bachelors_degree", "male_45_64_graduate_degree", "male_45_64_less_than_9_grade", "male_45_64_grade_9_12", "male_45_64_high_school", "male_45_64_some_college", "male_45_to_64", "male_male_households", "management_business_sci_arts_employed", "no_car", "no_cars", "not_us_citizen_pop", "occupation_management_arts", "occupation_natural_resources_construction_maintenance", "occupation_production_transportation_material", "occupation_sales_office", "occupation_services", "one_car", "two_cars", "three_cars", "pop_25_64", "pop_determined_poverty_status", "population_1_year_and_over", "population_3_years_over", "poverty", "sales_office_employed", "some_college_and_associates_degree", "walked_to_work", "worked_at_home", "workers_16_and_over", "associates_degree", "bachelors_degree", "high_school_diploma", "less_one_year_college", "masters_degree", "one_year_more_college", "pop_25_years_over", "commute_35_44_mins", "commute_60_more_mins", "commute_less_10_mins", "commuters_16_over", "hispanic_any_race", "pop_5_years_over", "speak_only_english_at_home", "speak_spanish_at_home", "speak_spanish_at_home_low_english", "do_date", "pop_15_and_over", "pop_never_married", "pop_now_married", "pop_separated", "pop_widowed", "pop_divorced"], "nested_column_types": ["STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "STRING", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64", "FLOAT64"], "description": ["Incorporated Places Geoids", "Nonfamily Households. A householder living alone or with nonrelatives only. Unmarried couples households, whether opposite-sex or same-sex, with no relatives of the householder present are tabulated in nonfamily households.", "Family Households. A family consists of a householder and one or more other people living in the same household who are related to the householder by birth, marriage, or adoption. All people in a household who are related to the householder are regarded as members of his or her family. A family household may contain people not related to the householder, but those people are not included as part of the family of the householder in tabulations. Thus, the number of family households is equal to the number of families, but family households may include more members than do families. A household can contain only one family for purposes of tabulations. Not all households contain families since a household may be comprised of a group of unrelated people or of one person living alone -- these are called nonfamily households. Families are classified by type as either a \"married couple family\" or \"other family\" according to the sex of the householder and the presence of relatives. The data on family type are based ...", "Median Year Structure Built. Median Year Structure Built", "Housing units without rent burden computed. Units for which no rent is paid and units occupied by households that reported no income or a net loss comprise this category", "Housing units spending over 50% income on rent. Gross rent over 50 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 40 to 49.9% income on rent. Gross rent from 40.0 to 49.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 35 to 39.9% income on rent. Gross rent from 35.0 to 39.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 30 to 34.9% income on rent. Gross rent from 30.0 to 34.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 25 to 29.9% income on rent. Gross rent from 25.0 to 29.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 20 to 24.9% income on rent. Gross rent from 20.0 to 24.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 15 to 19.9% income on rent. Gross rent from 15.0 to 19.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending 10 to 14.9% income on rent. Gross rent from 10.0 to 14.9 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Housing units spending less than 10% on rent. Gross rent less than 10 percent of household income. Computed ratio of monthly gross rent to monthly household income (total household income divided by 12). The ratio is computed separately for each unit and is rounded to the nearest tenth. Units for which no rent is paid and units occupied by households that report no income or a net loss comprise the category, \"Not computed\". Gross rent as a percentage of household income provides information on the monthly housing cost expenses for renters. ", "Total Population. The total number of all people living in a given geographic area.  This is a very useful catch-all denominator when calculating rates.", "Male Population. The number of people within each geography who are male.", "Female Population. The number of people within each geography who are female.", "Median Age. The median age of all people in a given geographic area.", "White Population. The number of people identifying as white, non-Hispanic in each geography.", "Black or African American Population. The number of people identifying as black or African American, non-Hispanic in each geography.", "Asian Population. The number of people identifying as Asian, non-Hispanic in each geography.", "Hispanic Population. The number of people identifying as Hispanic or Latino in each geography.", "American Indian and Alaska Native Population. The number of people identifying as American Indian or Alaska native in each geography.", "Other Race population. The number of people identifying as another race in each geography", "Two or more races population. The number of people identifying as two or more races in each geography", "Population not Hispanic. The number of people not identifying as Hispanic or Latino in each geography.", "Commuters by Public Transportation. The number of workers age 16 years and over within a geographic area who primarily traveled to work by public transportation.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Households. A count of the number of households in each geography. A household consists of one or more people who live in the same dwelling and also share at meals or living accommodation, and may consist of a single family or some other grouping of people. ", "Median Household Income in the past 12 Months. Within a geographic area, the median income received by every household on a regular basis before payments for personal income taxes, social security, union dues, medicare deductions, etc.  It includes income received from wages, salary, commissions, bonuses, and tips; self-employment income from own nonfarm or farm businesses, including proprietorships and partnerships; interest, dividends, net rental income, royalty income, or income from estates and trusts; Social Security or Railroad Retirement income; Supplemental Security Income (SSI); any cash public assistance or welfare payments from the state or local welfare office; retirement, survivor, or disability benefits; and any other sources of income received regularly such as Veterans' (VA) payments, unemployment and/or worker's compensation, child support, and alimony.", "Per Capita Income in the past 12 Months. Per capita income is the mean income computed for every man, woman, and child in a particular group. It is derived by dividing the total income of a particular group by the total population.", "Housing Units. A count of housing units in each geography.  A housing unit is a house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters.", "Vacant Housing Units. The count of vacant housing units in a geographic area. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Vacant Housing Units for Rent. The count of vacant housing units in a geographic area that are for rent. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Vacant Housing Units for Sale. The count of vacant housing units in a geographic area that are for sale. A housing unit is vacant if no one is living in it at the time of enumeration, unless its occupants are only temporarily absent. Units temporarily occupied at the time of enumeration entirely by people who have a usual residence elsewhere are also classified as vacant.", "Median Rent. The median contract rent within a geographic area. The contract rent is the monthly rent agreed to or contracted for, regardless of any furnishings, utilities, fees, meals, or services that may be included. For vacant units, it is the monthly rent asked for the rental unit at the time of interview.", "Percent of Household Income Spent on Rent. Within a geographic area, the median percentage of household income which was spent on gross rent.  Gross rent is the amount of the contract rent plus the estimated average monthly cost of utilities (electricity, gas, water, sewer etc.) and fuels (oil, coal, wood, etc.) if these are paid by the renter.  Household income is the sum of the income of all people 15 years and older living in the household.", "Owner-occupied Housing Units", "Owner-occupied Housing Units valued at $1,000,000 or more.. The count of owner occupied housing units in a geographic area that are valued at $1,000,000 or more.  Value is the respondent's estimate of how much the property (house and lot, mobile home and lot, or condominium unit) would sell for if it were for sale.", "Owner-occupied Housing Units with a Mortgage. The count of housing units within a geographic area that are mortagaged. \"Mortgage\" refers to all forms of debt where the property is pledged as security for repayment of the debt, including deeds of trust, trust deed, contracts to purchase, land contracts, junior mortgages, and home equity loans.", "Families with young children (under 6 years of age)", "Two-parent families with young children (under 6 years of age)", "Two-parent families, both parents in labor force with young children (under 6 years of age)", "Two-parent families, father only in labor force with young children (under 6 years of age)", "Two-parent families, mother only in labor force with young children (under 6 years of age)", "Two-parent families, neither parent in labor force with young children (under 6 years of age)", "One-parent families with young children (under 6 years of age)", "One-parent families, father, with young children (under 6 years of age)", "One-parent families, father in labor force, with young children (under 6 years of age)", "Number of workers with a commute between 10 and 14 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 10 and 14 minutes. ", "Number of workers with a commute between 15 and 19 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 15 and 19 minutes. ", "Number of workers with a commute between 20 and 24 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 20 and 24 minutes.", "Number of workers with a commute between 25 and 29 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 25 and 29 minutes. ", "Number of workers with a commute between 30 and 34 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 30 and 34 minutes. ", "Number of workers with a commute between 45 and 59 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 45 and 59 minutes. ", "Aggregate travel time to work. The total number of minutes every worker in a geographic area over the age of 16 who did not work from home spent spent commuting to work in one day", "Households with income less than $10,000. The number of households in a geographic area whose annual income was less than $10,000.", "Households with income of $10,000 to $14,999. The number of households in a geographic area whose annual income was between $10,000 and $14,999.", "Households with income of $15,000 to $19,999. The number of households in a geographic area whose annual income was between $15,000 and $19,999.", "Households with income of $20,000 to $24,999. The number of households in a geographic area whose annual income was between $20,000 and $24,999.", "Households with income of $25,000 to $29,999. The number of households in a geographic area whose annual income was between $20,000 and $24,999.", "Households with income of $30,000 to $34,999. The number of households in a geographic area whose annual income was between $30,000 and $34,999.", "Households with income of $35,000 to $39,999. The number of households in a geographic area whose annual income was between $35,000 and $39,999.", "Households with income of $40,000 to $44,999. The number of households in a geographic area whose annual income was between $40,000 and $44,999.", "Households with income of $45,000 to $49,999. The number of households in a geographic area whose annual income was between $45,000 and $49,999.", "Households with income of $50,000 to $59,999. The number of households in a geographic area whose annual income was between $50,000 and $59,999.", "Households with income of $60,000 to $74,999. The number of households in a geographic area whose annual income was between $60,000 and $74,999.", "Households with income of $75,000 to $99,999. The number of households in a geographic area whose annual income was between $75,000 and $99,999.", "Households with income of $100,000 to $124,999. The number of households in a geographic area whose annual income was between $100,000 and $124,999.", "Households with income of $125,000 to $149,999. The number of households in a geographic area whose annual income was between $125,000 and $149,999.", "Households with income of $150,000 to $199,999. The number of households in a geographic area whose annual income was between $150,000 and $1999,999.", "Households with income of $200,000 Or More. The number of households in a geographic area whose annual income was more than $200,000.", "Renter-Occupied Housing Units Paying Cash Rent Median Gross Rent", "Owner-Occupied Housing Units Lower Value Quartile", "Owner-Occupied Housing Units Median Value. The middle value (median) in a geographic area owner occupied housing units.", "Owner-Occupied Housing Units Upper Value Quartile", "Married households. People in formal marriages, as well as people in common-law marriages, are included. Does not include same-sex marriages.", "Occupied housing units. A housing unit is classified as occupied if it is the usual place of residence of the person or group of people living in it at the time of enumeration.", "Renter occupied housing units. All occupied units which are not owner occupied, whether they are rented for cash rent or occupied without payment of cash rent, are classified as renter-occupied.", "Single-family (one unit) detached dwellings. This is a 1-unit structure detached from any other house, that is, with open space on all four sides. Such structures are considered detached even if they have an adjoining shed or garage. A one-family house that contains a business is considered detached as long as the building has open space on all four sides. Mobile homes to which one or more permanent rooms have been added or built also are included.", "Single-family (one unit) attached dwellings. This is a 1-unit structure that has one or more walls extending from ground to roof separating it from adjoining structures. In row houses (sometimes called townhouses), double houses, or houses attached to nonresidential structures, each house is a separate, attached structure if the dividing or common wall goes from ground to roof.", "Two-family (two unit) dwellings", "Multifamily dwellings with three to 4 units", "Apartment buildings with 5 to 9 units", "Apartment buildings with 10 to 19 units", "Apartment buildings with 20 to 49 units", "Apartment buildings with 50 or more units", "Mobile homes. A manufactured home is defined as a movable dwelling, 8 feet or more wide and 40 feet or more long, designed to be towed on its own chassis, with transportation gear integral to the unit when it leaves the factory, and without need of a permanent foundation. These homes are built in accordance with the U.S. Department of Housing and Urban Development (HUD) building code.", "Housing units built in 2005 or later. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built in 2005 or later.", "Housing units built between 2000 and 2004. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built from 2000 to 2004.", "Housing units built before 1939. A house, an apartment, a mobile home or trailer, a group of rooms, or a single room occupied as separate living quarters, or if vacant, intended for occupancy as separate living quarters built in 1939 or earlier.", "Male under 5 years. The male population over the age of five years within the specified area.", "Male age 5 to 9. The male population between the age of five years to nine years within the specified area.", "Male age 10 to 14. The male population between the age of ten years to fourteen years within the specified area.", "Male age 15 to 17. The male population between the age of fifteeen years to seventeen years within the specified area.", "Male age 18 and 19. The male population between the age of eighteen years to nineteen years within the specified area.", "Male age 20. The male population with an age of twenty years within the specified area.", "Male age 21. The male population with an age of twenty-one years within the specified area.", "Male age 22 to 24. The male population between the age of twenty-two years to twenty-four years within the specified area.", "Male age 25 to 29. The male population between the age of twenty-five years to twenty-nine years within the specified area.", "Male age 30 to 34. The male population between the age of thirty years to thirty-four years within the specified area.", "Male age 35 to 39. The male population between the age of thirty-five years to thirty-nine years within the specified area.", "Male age 40 to 44. The male population between the age of fourty years to fourty-four years within the specified area.", "Men age 45 to 49. The male population between the age of fourty-five years to fourty-nine years within the specified area.", "Men age 50 to 54. The male population between the age of fifty years to fifty-four years within the specified area.", "Men age 55 to 59. The male population between the age of fifty-five years to fifty-nine years within the specified area.", "Men age 60 to 61. The male population between the age of sixty years to sixty-one years within the specified area.", "Men age 62 to 64. The male population between the age of sixty-two years to sixty-four years within the specified area.", "Male age 65 to 66. The male population between the age of sixty-five years to sixty-six years within the specified area.", "Male age 67 to 69. The male population between the age of sixty-seven years to sixty-nine years within the specified area.", "Male age 70 to 74. The male population between the age of seventy years to seventy-four years within the specified area.", "Male age 75 to 79. The male population between the age of seventy-five years to seventy-nine years within the specified area.", "Male age 80 to 84. The male population between the age of eighty years to eighty-four years within the specified area.", "Male age 85 and over. The male population of the age of eighty-five years and over within the specified area.", "Female under 5 years. The female population over the age of five years within the specified area.", "Female age 5 to 9. The female population between the age of five years to nine years within the specified area.", "Female age 10 to 14. The female population between the age of ten years to fourteen years within the specified area.", "Female age 15 to 17. The female population between the age of fifteeen years to seventeen years within the specified area.", "Female age 18 and 19. The female population between the age of eighteen years to nineteen years within the specified area.", "Female age 20. The female population with an age of twenty years within the specified area.", "Female age 21. The female population with an age of twenty-one years within the specified area.", "Female age 22 to 24. The female population between the age of twenty-two years to twenty-four years within the specified area.", "Female age 25 to 29. The female population between the age of twenty-five years to twenty-nine years within the specified area.", "Female age 30 to 34. The female population between the age of thirty years to thirty-four years within the specified area.", "Female age 35 to 39. The female population between the age of thirty-five years to thirty-nine years within the specified area.", "Female age 40 to 44. The female population between the age of fourty years to fourty-four years within the specified area.", "Female age 45 to 49. The female population between the age of fourty-five years to fourty-nine years within the specified area.", "Female age 50 to 54. The female population between the age of fifty years to fifty-four years within the specified area.", "Female age 55 to 59. The female population between the age of fifty-five years to fifty-nine years within the specified area.", "Female age 60 and 61. The female population between the age of sixty years to sixty-one years within the specified area.", "Female age 62 to 64. The female population between the age of sixty-two years to sixty-four years within the specified area.", "Female age 65 to 66. The female population between the age of sixty-five years to sixty-six years within the specified area.", "Female age 67 to 69. The female population between the age of sixty-seven years to sixty-nine years within the specified area.", "Female age 70 to 74. The female population between the age of seventy years to seventy-four years within the specified area.", "Female age 75 to 79. The female population between the age of seventy-five years to seventy-nine years within the specified area.", "Female age 80 to 84. The female population between the age of eighty years to eighty-four years within the specified area.", "Female age 85 and over. The female population of the age of eighty-five years and over within the specified area.", "White including Hispanic", "Black including Hispanic", "American Indian including Hispanic", "Asian including Hispanic", "Number of workers with a commute between 5 and 9 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 5 and 9 minutes.", "Number of workers with a commute between 35 and 39 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 35 and 39 minutes. ", "Number of workers with a commute between 40 and 44 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 40 and 44 minutes. ", "Number of workers with a commute between 60 and 89 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 60 and 89 minutes .", "Number of workers with a commute of more than 90 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute more than 90 minutes.", "Households receiving retirement income", "Population in Armed Forces. The number of people in each geography who are members of the U.S. Armed Forces (people on active duty with the United States Army, Air Force, Navy, Marine Corps, or Coast Guard).", "Population in Civilian Labor Force. The number of civilians 16 years and over in each geography who can be classified as either \"employed\" or \"unemployed\" below.", "Employed Population. The number of civilians 16 years old and over in each geography who either (1) were \"at work,\" that is, those who did any work at all during the reference week as paid employees, worked in their own business or profession, worked on their own farm, or worked 15 hours or more as unpaid workers on a family farm or in a family business; or (2) were \"with a job but not at work,\" that is, those who did not work during the reference week but had jobs or businesses from which they were temporarily absent due to illness, bad weather, industrial dispute, vacation, or other personal reasons. Excluded from the employed are people whose only activity consisted of work around the house or unpaid volunteer work for religious, charitable, and similar organizations; also excluded are all institutionalized people and people on active duty in the United States Armed Forces.", "", "Population Not in Labor Force. The number of people in each geography who are 16 years old and over who are not classified as members of the labor force. This category consists mainly of students, homemakers, retired workers, seasonal workers interviewed in an off season who were not looking for work, institutionalized people, and people doing only incidental unpaid family work (less than 15 hours during the reference week).", "Population age 16 and over. The number of people in each geography who are age 16 or over.", "Population in Labor Force. The number of people in each geography who are either in the civilian labor force or are members of the U.S. Armed Forces (people on active duty with the United States Army, Air Force, Navy, Marine Corps, or Coast Guard).", "Asian Men age 45 to 54", "Asian Men age 55 to 64", "Black Men age 45 to 54", "Black Men age 55 to 64", "Hispanic Men age 45 to 54", "Hispanic Men age 55 to 64", "White Men age 45 to 54", "White Men age 55 to 64", "Population who completed a bachelor's degree. From mobility table.", "Population with Bachelors Degree or Higher, Ages 25 to 64. The number of people in each geography who are between the ages of 25 and 64 who have attained a bachelors degree or higher. ", "Children under 18 Years of Age. The number of people within each geography who are under 18 years of age.", "Children under 18 years of age in single female-led household", "Commuters by Bus. The number of workers age 16 years and over within a geographic area who primarily traveled to work by bus.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.  This is a subset of workers who commuted by public transport.", "Commuters by Car, Truck, or Van. The number of workers age 16 years and over within  a geographic area who primarily traveled to work by car, truck or  van.  This is the principal mode of travel or type of conveyance,  by distance rather than time, that the worker usually used to get  from home to work.", "Commuters by Carpool. The number of workers age 16 years and over within a geographic area who primarily traveled to work by carpool.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Commuters by Subway or Elevated. The number of workers age 16 years and over within a geographic area who primarily traveled to work by subway or elevated train.  This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.  This is a subset of workers who commuted by public transport.", "Commuters who drove alone. The number of workers age 16 years and over within a geographic area who primarily traveled by car driving alone. This is the principal mode of travel or type of conveyance, by distance rather than time, that the worker usually used to get from home to work.", "Lived in a different house one year ago in a different city. All people in a geographic area who lived in a different city within the year prior to the survey.", "Lived in a different house one year ago in the same city. All people in a geographic area who lived in the same city but moved to a different unit within the year prior to the survey.", "Workers employed in firms in agriculture, forestry, fishing, hunting, or mining. The Agriculture, Forestry, Fishing and Hunting sector comprises establishments primarily engaged in growing crops, raising animals, harvesting timber, and harvesting fish and other animals from a farm, ranch, or their natural habitats.", "Workers employed in firms in arts, entertainment, recreation, accommodation and food services. The Arts, Entertainment, and Recreation sector includes a wide range of establishments that operate facilities or provide services to meet varied cultural, entertainment, and recreational interests of their patrons. This sector comprises (1) establishments that are involved in producing, promoting, or participating in live performances, events, or exhibits intended for public viewing; (2) establishments that preserve and exhibit objects and sites of historical, cultural, or educational interest; and (3) establishments that operate facilities or provide services that enable patrons to participate in recreational activities or pursue amusement, hobby, and leisure-time interests.", "Workers employed in firms in construction. The Construction sector comprises establishments primarily engaged in the construction of buildings or engineering projects (e.g., highways and utility systems). Construction work done may include new work, additions, alterations, or maintenance and repairs.", "Workers employed in firms in educational services, health care, and social assistance. Outpatient health services, other than hospital care, including: public health administration; research and education; categorical health programs; treatment and immunization clinics; nursing; environmental health activities such as air and water pollution control; ambulance service if provided separately from fire protection services, and other general public health activities such as mosquito abatement. School health services provided by health agencies (rather than school agencies) are included here. Sewage treatment operations are classified under Sewerage.", "Workers employed in firms in finance, insurance, real estate and rental and leasing. The Real Estate and Rental and Leasing sector comprises establishments primarily engaged in renting, leasing, or otherwise allowing the use of tangible or intangible assets, and establishments providing related services. The major portion of this sector comprises establishments that rent, lease, or otherwise allow the use of their own assets by others. The assets may be tangible, as is the case of real estate and equipment, or intangible, as is the case with patents and trademarks.", "Workers employed in firms in information. The Information sector comprises establishments engaged in the following processes: (a) producing and distributing information and cultural products, (b) providing the means to transmit or distribute these products as well as data or communications, and (c) processing data. Included are the publishing industries, the motion picture and sound recording industries; the broadcasting industries, the telecommunications industries; Web search portals, data processing industries, and the information services industries.", "Workers employed in firms in manufacturing. The Manufacturing sector comprises establishments engaged in the mechanical, physical, or chemical transformation of materials, substances, or components into new products.", "Workers employed in firms in other services except public administration. The Other Services (Except Public Administration) sector comprises establishments engaged in providing services not specifically provided for elsewhere in the classification system. Establishments in this sector are primarily engaged in activities such as equipment and machinery repairing, promoting or administering religious activities, grantmaking, advocacy, and providing drycleaning and laundry services, personal care services, death care services, pet care services, photofinishing services, temporary parking services, and dating services. Private households that engage in employing workers on or about the premises in activities primarily concerned with the operation of the household are included in this sector.", "Workers employed in firms in public administration. The Public Administration sector consists of establishments of federal, state, and local government agencies that administer, oversee, and manage public programs and have executive, legislative, or judicial authority over other institutions within a given area. These agencies also set policy, create laws, adjudicate civil and criminal legal cases, provide for public safety and for national defense. In general, government establishments in the public administration sector oversee governmental programs and activities that are not performed by private establishments.", "Workers employed in firms in retail trade. The Retail Trade sector comprises establishments engaged in retailing merchandise, generally without transformation, and rendering services incidental to the sale of merchandise. The retailing process is the final step in the distribution of merchandise; retailers are, therefore, organized to sell merchandise in small quantities to the general public.", "Workers employed in firms in professional scientific, management, administrative and waste management services. The Administrative and Support and Waste Management and Remediation Services sector comprises establishments performing routine support activities for the day-to-day operations of other organizations. The establishments in this sector specialize in one or more of these support activities and provide these services to clients in a variety of industries and, in some cases, to households. Activities performed include office administration, hiring and placing of personnel, document preparation and similar clerical services, solicitation, collection, security and surveillance services, cleaning, and waste disposal services.", "Workers employed in firms in transportation, warehousing, and utilities. The Transportation and Warehousing sector includes industries providing transportation of passengers and cargo, warehousing and storage for goods, scenic and sightseeing transportation, and support activities related to modes of transportation. The modes of transportation are air, rail, water, road, and pipeline.", "Workers employed in firms in wholesale trade. The Wholesale Trade sector comprises establishments engaged in wholesaling merchandise, generally without transformation, and rendering services incidental to the sale of merchandise. The wholesaling process is an intermediate step in the distribution of merchandise. Wholesalers are organized to sell or arrange the purchase or sale of (a) goods for resale (i.e., goods sold to other wholesalers or retailers), (b) capital or durable nonconsumer goods, and (c) raw and intermediate materials and supplies used in production.", "Households with two female partners. An unmarried partner is a person age 15 years and over, who is not related to the householder, who shares living quarters, and who has an intimate relationship with the householder.", "Four car households. The number of households with four or more passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Gini Index. The Gini index, or index of income concentration, is a statistical measure of income inequality ranging from 0 to 1. A measure of 1 indicates perfect inequality, i.e., one household having all the income and rest having none. A measure of 0 indicates perfect equality, i.e., all households having an equal share of income.", "Population who completed a graduate or professional degree", "Population living in group quarters", "Population with high school degree, including GED. The number of people in a geographic area over the age of 25 who attained a high school degree or GED.", "Households on cash public assistance or receiving food stamps (SNAP)", "Students Enrolled in Grades 1 to 4. The total number of people in each geography currently enrolled in grades 1 through 4 inclusive.  This corresponds roughly to elementary school.", "Students Enrolled in Grades 5 to 8. The total number of people in each geography currently enrolled in grades 5 through 8 inclusive.  This corresponds roughly to middle school.", "Students Enrolled in Grades 9 to 12. The total number of people in each geography currently enrolled in grades 9 through 12 inclusive.  This corresponds roughly to high school.", "Students Enrolled in School. The total number of people in each geography currently enrolled at any level of school, from nursery or pre-school to advanced post-graduate education.  Only includes those over the age of 3.", "Students Enrolled as Undergraduate in College. The number of people in a geographic area who are enrolled in college at the undergraduate level. Enrollment refers to being registered or listed as a student in an educational program leading to a college degree. This may be a public school or college, a private school or college.", "Less than high school graduate. The number of people in a geographic area over the age of 25 who have not completed high school or any other advanced degree.", "Men age 45 to 64 who obtained an associate's degree", "Men age 45 to 64 who obtained a bachelor's degree", "Men age 45 to 64 who obtained a graduate or professional degree", "Men age 45 to 64 who attained less than a 9th grade education", "Men age 45 to 64 who attained between 9th and 12th grade, no diploma", "Men age 45 to 64 who completed high school or obtained GED", "Men age 45 to 64 who completed some college, no degree", "Men age 45 to 64 (\"middle aged\"). The male population between the age of fourty-five years to sixty-four years within the specified area.", "Households with two male partners. An unmarried partner is a person age 15 years and over, who is not related to the householder, who shares living quarters, and who has an intimate relationship with the householder.", "Civilian Employed Population in Management, Business, Science, and Arts Occupations. The number of employed civilians 16 years old and over in each geography in the Management, Business, Science, and Arts occupations. Occupation codes are based on the Standard Occupational Classification (SOC), published by the Executive Office of the President, Office of Management and Budget.", "Workers age 16 and over with no vehicle. All people in a geographic area over the age of 16 who do not own a car.", "Car-free households. The number of households without passenger cars, vans, and pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Not a U.S. Citizen Population. The number of people within each geography who indicated that they are not U.S. citizens.", "Workers employed in management business science and arts occupations", "Workers employed in natural resources, construction, and maintenance occupations", "Workers employed in production, transportation, and material moving", "Workers employed in sales and office occupations", "Workers employed in service occupations", "One car households. The number of households with one passenger car, van, , pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Two car households. The number of households with two passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Three car households. The number of households with one passenger cars, vans, pickup or panel trucks of one-ton capacity or less kept at home and available for the use of household members. Vehicles rented or leased for one month or more, company vehicles, and police and government vehicles are included if kept at home and used for non-business purposes. Dismantled or immobile vehicles ware excluded. Vehicles kept at home but used only for business purposes also are excluded.", "Population age 25 to 64. The number of people in each geography who are between the ages of 25 and 64. ", "Population for Whom Poverty Status Determined. The number of people in each geography who could be identified as either living in poverty or not.  This should be used as the denominator when calculating poverty rates, as it excludes people for whom it was not possible to determine poverty.", "Population 1 year and over. All people, male and female, child and adult, living in a given geographic area that are 1 year and older.", "Population 3 Years and Over. The total number of people in each geography age 3 years and over.  This denominator is mostly used to calculate rates of school enrollment.", "Income In The Past 12 Months Below Poverty Level. The number of people in a geographic area who are part of a family (which could be just them as an individual) determined to be \"in poverty\" following the Office of Management and Budget's Directive 14. (https://www.census.gov/hhes/povmeas/methodology/ombdir14.html)", "Civilian Employed Population in Sales and Office Occupations. The number of employed civilians 16 years old and over in each geography in the Sales and Office occupations. Occupation codes are based on the Standard Occupational Classification (SOC), published by the Executive Office of the President, Office of Management and Budget.", "Population who completed some college or obtained associate's degree. The number of people in a geographic area over the age of 25 who obtained an associate's degree, and did not complete a more advanced degree.", "Walked to Work. The number of workers age 16 years and over within a geographic area who primarily walked to work.  This would mean that of any way of getting to work, they travelled the most distance walking.", "Worked at Home. The count within a geographical area of workers over the age of 16 who worked at home.", "Workers over the Age of 16. The number of people in each geography who work. Workers include those employed at private for-profit companies, the self-employed, government workers and non-profit employees.", "Population Completed Associate's Degree. The number of people in a geographic area over the age of 25 who obtained a associate's degree, and did not complete a more advanced degree.", "Population Completed Bachelor's Degree. The number of people in a geographic area over the age of 25 who obtained a bachelor's degree, and did not complete a more advanced degree.", "Population Completed High School. The number of people in a geographic area over the age of 25 who completed high school, and did not complete a more advanced degree.", "Population completed less than one year of college, no degree. The number of people in a geographic area over the age of 25 who attended college for less than one year and no further.", "Population Completed Master's Degree. The number of people in a geographic area over the age of 25 who obtained a master's degree, but did not complete a more advanced degree.", "Population completed more than one year of college, no degree. The number of people in a geographic area over the age of 25 who attended college for more than one year but did not obtain a degree", "Population 25 Years and Over. The number of people in a geographic area who are over the age of 25.  This is used mostly as a denominator of educational attainment.", "Number of workers with a commute between 35 and 44 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in between 35 and 44 minutes. ", "Number of workers with a commute of over 60 minutes. The number of workers in a geographic area over the age of 16 who do not work from home and commute in over 60 minutes.", "Number of workers with less than 10 minute commute. The number of workers in a geographic area over the age of 16 who do not work from home and commute in less than 10 minutes.", "Workers age 16 and over who do not work from home. The number of workers in a geographic area over the age of 16 who do not work from home", "Hispanic of any race", "Population 5 Years and Over. The number of people in a geographic area who are over the age of 5.  This is primarily used as a denominator of measures of language spoken at home.", "Speaks only English at Home. The number of people in a geographic area over age 5 who speak only English at home.", "Speaks Spanish at Home. The number of people in a geographic area over age 5 who speak Spanish at home, possibly in addition to other languages.", "Speaks Spanish at Home, speaks English less than \"very well\"", null, "Population 15 Years and Over. The number of people in a geographic area who are over the age of 15.  This is used mostly as a denominator of marital status.", "Never Married. The number of people in a geographic area who have never been married.", "Currently married. The number of people in a geographic area who are currently married", "Married but separated. The number of people in a geographic area who are married but separated", "Widowed. The number of people in a geographic area who are widowed", "Divorced. The number of people in a geographic area who are divorced"], "sample_rows": [{"geo_id": "1150000", "nonfamily_households": 156609.0, "family_households": 125178.0, "median_year_structure_built": 1952.0, "rent_burden_not_computed": 9593.0, "rent_over_50_percent": 38220.0, "rent_40_to_50_percent": 12641.0, "rent_35_to_40_percent": 11002.0, "rent_30_to_35_percent": 15346.0, "rent_25_to_30_percent": 19734.0, "rent_20_to_25_percent": 22373.0, "rent_15_to_20_percent": 19595.0, "rent_10_to_15_percent": 13721.0, "rent_under_10_percent": 7007.0, "total_pop": 672228.0, "male_pop": 319705.0, "female_pop": 352523.0, "median_age": 33.8, "white_pop": 241855.0, "black_pop": 312914.0, "asian_pop": 25633.0, "hispanic_pop": 71129.0, "amerindian_pop": 959.0, "other_race_pop": 2976.0, "two_or_more_races_pop": 16395.0, "not_hispanic_pop": 601099.0, "commuters_by_public_transportation": 128135.0, "households": 281787.0, "median_income": 75628.0, "income_per_capita": 50187.0, "housing_units": 309596.0, "vacant_housing_units": 27809.0, "vacant_housing_units_for_rent": 9251.0, "vacant_housing_units_for_sale": 1661.0, "median_rent": 1326.0, "percent_income_spent_on_rent": 29.3, "owner_occupied_housing_units": 112555.0, "million_dollar_housing_units": 11389.0, "mortgaged_housing_units": 85687.0, "families_with_young_children": 49043.0, "two_parent_families_with_young_children": 24976.0, "two_parents_in_labor_force_families_with_young_children": 20058.0, "two_parents_father_in_labor_force_families_with_young_children": 4395.0, "two_parents_mother_in_labor_force_families_with_young_children": 239.0, "two_parents_not_in_labor_force_families_with_young_children": 284.0, "one_parent_families_with_young_children": 24067.0, "father_one_parent_families_with_young_children": 2839.0, "father_in_labor_force_one_parent_families_with_young_children": 2428.0, "commute_10_14_mins": 25834.0, "commute_15_19_mins": 39346.0, "commute_20_24_mins": 53919.0, "commute_25_29_mins": 24702.0, "commute_30_34_mins": 67939.0, "commute_45_59_mins": 40750.0, "aggregate_travel_time_to_work": 10040065.0, "income_less_10000": 24290.0, "income_10000_14999": 12308.0, "income_15000_19999": 10691.0, "income_20000_24999": 9907.0, "income_25000_29999": 10814.0, "income_30000_34999": 9265.0, "income_35000_39999": 7393.0, "income_40000_44999": 8972.0, "income_45000_49999": 9720.0, "income_50000_59999": 15656.0, "income_60000_74999": 20760.0, "income_75000_99999": 31240.0, "income_100000_124999": 25997.0, "income_125000_149999": 18986.0, "income_150000_199999": 24445.0, "income_200000_or_more": 41343.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 1417.0, "owner_occupied_housing_units_lower_value_quartile": 334200.0, "owner_occupied_housing_units_median_value": 551300.0, "owner_occupied_housing_units_upper_value_quartile": 852700.0, "married_households": 71857.0, "occupied_housing_units": 281787.0, "housing_units_renter_occupied": 169232.0, "dwellings_1_units_detached": 37325.0, "dwellings_1_units_attached": 77280.0, "dwellings_2_units": 11340.0, "dwellings_3_to_4_units": 21329.0, "dwellings_5_to_9_units": 18922.0, "dwellings_10_to_19_units": 35933.0, "dwellings_20_to_49_units": 24754.0, "dwellings_50_or_more_units": 82337.0, "mobile_homes": 108.0, "housing_built_2005_or_later": 1745.0, "housing_built_2000_to_2004": 9924.0, "housing_built_1939_or_earlier": 36411.0, "male_under_5": 22126.0, "male_5_to_9": 16204.0, "male_10_to_14": 13429.0, "male_15_to_17": 8317.0, "male_18_to_19": 10034.0, "male_20": 6270.0, "male_21": 7050.0, "male_22_to_24": 13855.0, "male_25_to_29": 36112.0, "male_30_to_34": 35515.0, "male_35_to_39": 25775.0, "male_40_to_44": 22250.0, "male_45_to_49": 19418.0, "male_50_to_54": 19345.0, "male_55_to_59": 18900.0, "male_60_61": 6166.0, "male_62_64": 8168.0, "male_65_to_66": 3500.0, "male_67_to_69": 6188.0, "male_70_to_74": 9231.0, "male_75_to_79": 4795.0, "male_80_to_84": 3038.0, "male_85_and_over": 4019.0, "female_under_5": 21104.0, "female_5_to_9": 16358.0, "female_10_to_14": 13061.0, "female_15_to_17": 7239.0, "female_18_to_19": 12935.0, "female_20": 5474.0, "female_21": 6194.0, "female_22_to_24": 19694.0, "female_25_to_29": 41909.0, "female_30_to_34": 38951.0, "female_35_to_39": 27301.0, "female_40_to_44": 21093.0, "female_45_to_49": 19534.0, "female_50_to_54": 19056.0, "female_55_to_59": 18864.0, "female_60_to_61": 7817.0, "female_62_to_64": 10119.0, "female_65_to_66": 6265.0, "female_67_to_69": 9103.0, "female_70_to_74": 9805.0, "female_75_to_79": 7516.0, "female_80_to_84": 5470.0, "female_85_and_over": 7661.0, "white_including_hispanic": 269143.0, "black_including_hispanic": 318831.0, "amerindian_including_hispanic": 1661.0, "asian_including_hispanic": 25944.0, "commute_5_9_mins": 14107.0, "commute_35_39_mins": 17632.0, "commute_40_44_mins": 20134.0, "commute_60_89_mins": 21720.0, "commute_90_more_mins": 5893.0, "households_retirement_income": 38982.0, "armed_forces": 4164.0, "civilian_labor_force": 389414.0, "employed_pop": 361179.0, "unemployed_pop": 28235.0, "not_in_labor_force": 170628.0, "pop_16_over": 564206.0, "pop_in_labor_force": 393578.0, "asian_male_45_54": 1093.0, "asian_male_55_64": 613.0, "black_male_45_54": 19792.0, "black_male_55_64": 18714.0, "hispanic_male_45_54": 4211.0, "hispanic_male_55_64": 2858.0, "white_male_45_54": 14031.0, "white_male_55_64": 10430.0, "bachelors_degree_2": 112629.0, "bachelors_degree_or_higher_25_64": 237387.0, "children": 117838.0, "children_in_single_female_hh": 52663.0, "commuters_by_bus": 48547.0, "commuters_by_car_truck_van": 138789.0, "commuters_by_carpool": 18456.0, "commuters_by_subway_or_elevated": 77461.0, "commuters_drove_alone": 120333.0, "different_house_year_ago_different_city": 59905.0, "different_house_year_ago_same_city": 67188.0, "employed_agriculture_forestry_fishing_hunting_mining": 195.0, "employed_arts_entertainment_recreation_accommodation_food": 35208.0, "employed_construction": 9342.0, "employed_education_health_social": 70276.0, "employed_finance_insurance_real_estate": 22532.0, "employed_information": 14995.0, "employed_manufacturing": 5025.0, "employed_other_services_not_public_admin": 31896.0, "employed_public_administration": 58849.0, "employed_retail_trade": 18086.0, "employed_science_management_admin_waste": 83089.0, "employed_transportation_warehousing_utilities": 9754.0, "employed_wholesale_trade": 1932.0, "female_female_households": 1010.0, "four_more_cars": 2905.0, "gini_index": 0.5354, "graduate_professional_degree": 155716.0, "group_quarters": 40209.0, "high_school_including_ged": 82181.0, "households_public_asst_or_food_stamps": 44868.0, "in_grades_1_to_4": 24241.0, "in_grades_5_to_8": 20313.0, "in_grades_9_to_12": 24605.0, "in_school": 166940.0, "in_undergrad_college": 51175.0, "less_than_high_school_graduate": 48249.0, "male_45_64_associates_degree": 1842.0, "male_45_64_bachelors_degree": 14291.0, "male_45_64_graduate_degree": 19901.0, "male_45_64_less_than_9_grade": 3865.0, "male_45_64_grade_9_12": 5266.0, "male_45_64_high_school": 14691.0, "male_45_64_some_college": 12141.0, "male_45_to_64": 71997.0, "male_male_households": 1850.0, "management_business_sci_arts_employed": 223262.0, "no_car": 91122.0, "no_cars": 101954.0, "not_us_citizen_pop": 53472.0, "occupation_management_arts": 223262.0, "occupation_natural_resources_construction_maintenance": 7533.0, "occupation_production_transportation_material": 12443.0, "occupation_sales_office": 61396.0, "occupation_services": 56545.0, "one_car": 123053.0, "two_cars": 46234.0, "three_cars": 7641.0, "pop_25_64": 396293.0, "pop_determined_poverty_status": 638027.0, "population_1_year_and_over": 663006.0, "population_3_years_over": 645020.0, "poverty": 110500.0, "sales_office_employed": 61396.0, "some_college_and_associates_degree": 74109.0, "walked_to_work": 50165.0, "worked_at_home": 21529.0, "workers_16_and_over": 358150.0, "associates_degree": 14002.0, "bachelors_degree": 112629.0, "high_school_diploma": 69891.0, "less_one_year_college": 15015.0, "masters_degree": 97445.0, "one_year_more_college": 45092.0, "pop_25_years_over": 472884.0, "commute_35_44_mins": 37766.0, "commute_60_more_mins": 27613.0, "commute_less_10_mins": 18752.0, "commuters_16_over": 336621.0, "hispanic_any_race": 71129.0, "pop_5_years_over": 628998.0, "speak_only_english_at_home": 519448.0, "speak_spanish_at_home": 56171.0, "speak_spanish_at_home_low_english": 22974.0, "do_date": "2015", "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN}, {"geo_id": "0653000", "nonfamily_households": 70300.0, "family_households": 90804.0, "median_year_structure_built": 1953.0, "rent_burden_not_computed": 3824.0, "rent_over_50_percent": 25900.0, "rent_40_to_50_percent": 10075.0, "rent_35_to_40_percent": 6497.0, "rent_30_to_35_percent": 8795.0, "rent_25_to_30_percent": 11024.0, "rent_20_to_25_percent": 11843.0, "rent_15_to_20_percent": 9599.0, "rent_10_to_15_percent": 7505.0, "rent_under_10_percent": 3307.0, "total_pop": 419278.0, "male_pop": 203827.0, "female_pop": 215451.0, "median_age": 35.7, "white_pop": 117575.0, "black_pop": 101116.0, "asian_pop": 63735.0, "hispanic_pop": 114054.0, "amerindian_pop": 1505.0, "other_race_pop": 2079.0, "two_or_more_races_pop": 17535.0, "not_hispanic_pop": 305224.0, "commuters_by_public_transportation": 47981.0, "households": 161104.0, "median_income": 58807.0, "income_per_capita": 34587.0, "housing_units": 169213.0, "vacant_housing_units": 8109.0, "vacant_housing_units_for_rent": 2081.0, "vacant_housing_units_for_sale": 201.0, "median_rent": 1157.0, "percent_income_spent_on_rent": 32.3, "owner_occupied_housing_units": 62735.0, "million_dollar_housing_units": 5940.0, "mortgaged_housing_units": 48118.0, "families_with_young_children": 29505.0, "two_parent_families_with_young_children": 16808.0, "two_parents_in_labor_force_families_with_young_children": 9415.0, "two_parents_father_in_labor_force_families_with_young_children": 6920.0, "two_parents_mother_in_labor_force_families_with_young_children": 473.0, "two_parents_not_in_labor_force_families_with_young_children": 0.0, "one_parent_families_with_young_children": 12697.0, "father_one_parent_families_with_young_children": 2931.0, "father_in_labor_force_one_parent_families_with_young_children": 2707.0, "commute_10_14_mins": 25084.0, "commute_15_19_mins": 27933.0, "commute_20_24_mins": 25581.0, "commute_25_29_mins": 8372.0, "commute_30_34_mins": 28776.0, "commute_45_59_mins": 22537.0, "aggregate_travel_time_to_work": 6372700.0, "income_less_10000": 9647.0, "income_10000_14999": 11686.0, "income_15000_19999": 8046.0, "income_20000_24999": 8824.0, "income_25000_29999": 7601.0, "income_30000_34999": 7144.0, "income_35000_39999": 6092.0, "income_40000_44999": 6514.0, "income_45000_49999": 5604.0, "income_50000_59999": 10148.0, "income_60000_74999": 15783.0, "income_75000_99999": 16709.0, "income_100000_124999": 12189.0, "income_125000_149999": 8946.0, "income_150000_199999": 10862.0, "income_200000_or_more": 15309.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 1240.0, "owner_occupied_housing_units_lower_value_quartile": 346800.0, "owner_occupied_housing_units_median_value": 557000.0, "owner_occupied_housing_units_upper_value_quartile": 864300.0, "married_households": 57835.0, "occupied_housing_units": 161104.0, "housing_units_renter_occupied": 98369.0, "dwellings_1_units_detached": 71090.0, "dwellings_1_units_attached": 8264.0, "dwellings_2_units": 9510.0, "dwellings_3_to_4_units": 19663.0, "dwellings_5_to_9_units": 12535.0, "dwellings_10_to_19_units": 13598.0, "dwellings_20_to_49_units": 16525.0, "dwellings_50_or_more_units": 17294.0, "mobile_homes": 683.0, "housing_built_2005_or_later": 26.0, "housing_built_2000_to_2004": 1812.0, "housing_built_1939_or_earlier": 18948.0, "male_under_5": 12954.0, "male_5_to_9": 11700.0, "male_10_to_14": 11430.0, "male_15_to_17": 6223.0, "male_18_to_19": 4612.0, "male_20": 2081.0, "male_21": 1563.0, "male_22_to_24": 9116.0, "male_25_to_29": 21222.0, "male_30_to_34": 19122.0, "male_35_to_39": 17029.0, "male_40_to_44": 15872.0, "male_45_to_49": 14412.0, "male_50_to_54": 12904.0, "male_55_to_59": 10964.0, "male_60_61": 4699.0, "male_62_64": 5241.0, "male_65_to_66": 3634.0, "male_67_to_69": 5345.0, "male_70_to_74": 5571.0, "male_75_to_79": 3165.0, "male_80_to_84": 2413.0, "male_85_and_over": 2555.0, "female_under_5": 12709.0, "female_5_to_9": 12344.0, "female_10_to_14": 11122.0, "female_15_to_17": 6552.0, "female_18_to_19": 4643.0, "female_20": 2688.0, "female_21": 1961.0, "female_22_to_24": 9206.0, "female_25_to_29": 21805.0, "female_30_to_34": 20232.0, "female_35_to_39": 17549.0, "female_40_to_44": 15228.0, "female_45_to_49": 13746.0, "female_50_to_54": 13770.0, "female_55_to_59": 13629.0, "female_60_to_61": 5380.0, "female_62_to_64": 6440.0, "female_65_to_66": 4699.0, "female_67_to_69": 5037.0, "female_70_to_74": 5988.0, "female_75_to_79": 3809.0, "female_80_to_84": 2560.0, "female_85_and_over": 4354.0, "white_including_hispanic": 154160.0, "black_including_hispanic": 104481.0, "amerindian_including_hispanic": 3540.0, "asian_including_hispanic": 64168.0, "commute_5_9_mins": 8119.0, "commute_35_39_mins": 7046.0, "commute_40_44_mins": 10474.0, "commute_60_89_mins": 22238.0, "commute_90_more_mins": 9438.0, "households_retirement_income": 20598.0, "armed_forces": 0.0, "civilian_labor_force": 234016.0, "employed_pop": 217581.0, "unemployed_pop": 16435.0, "not_in_labor_force": 108379.0, "pop_16_over": 342395.0, "pop_in_labor_force": 234016.0, "asian_male_45_54": 3856.0, "asian_male_55_64": 4059.0, "black_male_45_54": 7184.0, "black_male_55_64": 6074.0, "hispanic_male_45_54": 6746.0, "hispanic_male_55_64": 3276.0, "white_male_45_54": 9224.0, "white_male_55_64": 6857.0, "bachelors_degree_2": 66787.0, "bachelors_degree_or_higher_25_64": 101759.0, "children": 85034.0, "children_in_single_female_hh": 25677.0, "commuters_by_bus": 17690.0, "commuters_by_car_truck_van": 131756.0, "commuters_by_carpool": 21852.0, "commuters_by_subway_or_elevated": 29098.0, "commuters_drove_alone": 109904.0, "different_house_year_ago_different_city": 35402.0, "different_house_year_ago_same_city": 19004.0, "employed_agriculture_forestry_fishing_hunting_mining": 1571.0, "employed_arts_entertainment_recreation_accommodation_food": 25824.0, "employed_construction": 13355.0, "employed_education_health_social": 49099.0, "employed_finance_insurance_real_estate": 11712.0, "employed_information": 7582.0, "employed_manufacturing": 14612.0, "employed_other_services_not_public_admin": 14804.0, "employed_public_administration": 8569.0, "employed_retail_trade": 16311.0, "employed_science_management_admin_waste": 38559.0, "employed_transportation_warehousing_utilities": 10660.0, "employed_wholesale_trade": 4923.0, "female_female_households": 923.0, "four_more_cars": 5805.0, "gini_index": 0.5069, "graduate_professional_degree": 52656.0, "group_quarters": 6817.0, "high_school_including_ged": 46425.0, "households_public_asst_or_food_stamps": 20849.0, "in_grades_1_to_4": 17576.0, "in_grades_5_to_8": 18031.0, "in_grades_9_to_12": 19417.0, "in_school": 99945.0, "in_undergrad_college": 25790.0, "less_than_high_school_graduate": 61024.0, "male_45_64_associates_degree": 1689.0, "male_45_64_bachelors_degree": 9477.0, "male_45_64_graduate_degree": 9262.0, "male_45_64_less_than_9_grade": 7697.0, "male_45_64_grade_9_12": 4790.0, "male_45_64_high_school": 6981.0, "male_45_64_some_college": 8324.0, "male_45_to_64": 48220.0, "male_male_households": 676.0, "management_business_sci_arts_employed": 95837.0, "no_car": 18871.0, "no_cars": 27881.0, "not_us_citizen_pop": 62669.0, "occupation_management_arts": 95837.0, "occupation_natural_resources_construction_maintenance": 15351.0, "occupation_production_transportation_material": 21734.0, "occupation_sales_office": 39738.0, "occupation_services": 44921.0, "one_car": 64433.0, "two_cars": 46280.0, "three_cars": 16705.0, "pop_25_64": 249244.0, "pop_determined_poverty_status": 413953.0, "population_1_year_and_over": 413520.0, "population_3_years_over": 402936.0, "poverty": 81919.0, "sales_office_employed": 39738.0, "some_college_and_associates_degree": 71482.0, "walked_to_work": 7252.0, "worked_at_home": 13532.0, "workers_16_and_over": 210931.0, "associates_degree": 15841.0, "bachelors_degree": 66787.0, "high_school_diploma": 41297.0, "less_one_year_college": 12034.0, "masters_degree": 34593.0, "one_year_more_college": 43607.0, "pop_25_years_over": 298374.0, "commute_35_44_mins": 17520.0, "commute_60_more_mins": 31676.0, "commute_less_10_mins": 9920.0, "commuters_16_over": 197399.0, "hispanic_any_race": 114054.0, "pop_5_years_over": 393615.0, "speak_only_english_at_home": 234857.0, "speak_spanish_at_home": 87798.0, "speak_spanish_at_home_low_english": 46605.0, "do_date": "2015", "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN}, {"geo_id": "3436000", "nonfamily_households": 37770.0, "family_households": 63985.0, "median_year_structure_built": 1950.0, "rent_burden_not_computed": 3817.0, "rent_over_50_percent": 18668.0, "rent_40_to_50_percent": 5438.0, "rent_35_to_40_percent": 2996.0, "rent_30_to_35_percent": 6284.0, "rent_25_to_30_percent": 7917.0, "rent_20_to_25_percent": 9724.0, "rent_15_to_20_percent": 10162.0, "rent_10_to_15_percent": 7610.0, "rent_under_10_percent": 2765.0, "total_pop": 264277.0, "male_pop": 131765.0, "female_pop": 132512.0, "median_age": 34.3, "white_pop": 57533.0, "black_pop": 53962.0, "asian_pop": 63974.0, "hispanic_pop": 79718.0, "amerindian_pop": 630.0, "other_race_pop": 2761.0, "two_or_more_races_pop": 5595.0, "not_hispanic_pop": 184559.0, "commuters_by_public_transportation": 62063.0, "households": 101755.0, "median_income": 59485.0, "income_per_capita": 33901.0, "housing_units": 113410.0, "vacant_housing_units": 11655.0, "vacant_housing_units_for_rent": 3933.0, "vacant_housing_units_for_sale": 707.0, "median_rent": 1090.0, "percent_income_spent_on_rent": 28.5, "owner_occupied_housing_units": 26374.0, "million_dollar_housing_units": 679.0, "mortgaged_housing_units": 18416.0, "families_with_young_children": 23409.0, "two_parent_families_with_young_children": 13703.0, "two_parents_in_labor_force_families_with_young_children": 7133.0, "two_parents_father_in_labor_force_families_with_young_children": 5690.0, "two_parents_mother_in_labor_force_families_with_young_children": 534.0, "two_parents_not_in_labor_force_families_with_young_children": 346.0, "one_parent_families_with_young_children": 9706.0, "father_one_parent_families_with_young_children": 1526.0, "father_in_labor_force_one_parent_families_with_young_children": 1395.0, "commute_10_14_mins": 8559.0, "commute_15_19_mins": 11570.0, "commute_20_24_mins": 12289.0, "commute_25_29_mins": 6238.0, "commute_30_34_mins": 23503.0, "commute_45_59_mins": 23621.0, "aggregate_travel_time_to_work": 4651805.0, "income_less_10000": 11895.0, "income_10000_14999": 5330.0, "income_15000_19999": 5136.0, "income_20000_24999": 4731.0, "income_25000_29999": 4367.0, "income_30000_34999": 3785.0, "income_35000_39999": 3889.0, "income_40000_44999": 3446.0, "income_45000_49999": 2460.0, "income_50000_59999": 6022.0, "income_60000_74999": 8783.0, "income_75000_99999": 10457.0, "income_100000_124999": 9685.0, "income_125000_149999": 6107.0, "income_150000_199999": 7182.0, "income_200000_or_more": 8480.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 1241.0, "owner_occupied_housing_units_lower_value_quartile": 239300.0, "owner_occupied_housing_units_median_value": 331100.0, "owner_occupied_housing_units_upper_value_quartile": 468400.0, "married_households": 40091.0, "occupied_housing_units": 101755.0, "housing_units_renter_occupied": 75381.0, "dwellings_1_units_detached": 8042.0, "dwellings_1_units_attached": 8525.0, "dwellings_2_units": 26271.0, "dwellings_3_to_4_units": 15170.0, "dwellings_5_to_9_units": 12217.0, "dwellings_10_to_19_units": 7908.0, "dwellings_20_to_49_units": 11445.0, "dwellings_50_or_more_units": 23464.0, "mobile_homes": 142.0, "housing_built_2005_or_later": 0.0, "housing_built_2000_to_2004": 2813.0, "housing_built_1939_or_earlier": 10157.0, "male_under_5": 9897.0, "male_5_to_9": 8330.0, "male_10_to_14": 5512.0, "male_15_to_17": 3656.0, "male_18_to_19": 2360.0, "male_20": 1310.0, "male_21": 1386.0, "male_22_to_24": 6682.0, "male_25_to_29": 13720.0, "male_30_to_34": 15675.0, "male_35_to_39": 12854.0, "male_40_to_44": 11446.0, "male_45_to_49": 8184.0, "male_50_to_54": 8355.0, "male_55_to_59": 6343.0, "male_60_61": 2655.0, "male_62_64": 2707.0, "male_65_to_66": 2492.0, "male_67_to_69": 2022.0, "male_70_to_74": 2718.0, "male_75_to_79": 1644.0, "male_80_to_84": 1005.0, "male_85_and_over": 812.0, "female_under_5": 11118.0, "female_5_to_9": 6856.0, "female_10_to_14": 5631.0, "female_15_to_17": 4354.0, "female_18_to_19": 2679.0, "female_20": 972.0, "female_21": 675.0, "female_22_to_24": 7186.0, "female_25_to_29": 13430.0, "female_30_to_34": 14463.0, "female_35_to_39": 11701.0, "female_40_to_44": 8467.0, "female_45_to_49": 9067.0, "female_50_to_54": 7995.0, "female_55_to_59": 6602.0, "female_60_to_61": 2782.0, "female_62_to_64": 3097.0, "female_65_to_66": 2613.0, "female_67_to_69": 2991.0, "female_70_to_74": 3234.0, "female_75_to_79": 2450.0, "female_80_to_84": 2117.0, "female_85_and_over": 2032.0, "white_including_hispanic": 93626.0, "black_including_hispanic": 60304.0, "amerindian_including_hispanic": 1317.0, "asian_including_hispanic": 64310.0, "commute_5_9_mins": 4089.0, "commute_35_39_mins": 5159.0, "commute_40_44_mins": 7903.0, "commute_60_89_mins": 17368.0, "commute_90_more_mins": 5707.0, "households_retirement_income": 7624.0, "armed_forces": 48.0, "civilian_labor_force": 145166.0, "employed_pop": 134339.0, "unemployed_pop": 10827.0, "not_in_labor_force": 69125.0, "pop_16_over": 214339.0, "pop_in_labor_force": 145214.0, "asian_male_45_54": 2929.0, "asian_male_55_64": 2465.0, "black_male_45_54": 3670.0, "black_male_55_64": 2770.0, "hispanic_male_45_54": 5066.0, "hispanic_male_55_64": 3572.0, "white_male_45_54": 4984.0, "white_male_55_64": 2767.0, "bachelors_degree_2": 49215.0, "bachelors_degree_or_higher_25_64": 74276.0, "children": 55354.0, "children_in_single_female_hh": 19364.0, "commuters_by_bus": 22112.0, "commuters_by_car_truck_van": 52672.0, "commuters_by_carpool": 10323.0, "commuters_by_subway_or_elevated": 34421.0, "commuters_drove_alone": 42349.0, "different_house_year_ago_different_city": 16184.0, "different_house_year_ago_same_city": 21798.0, "employed_agriculture_forestry_fishing_hunting_mining": 0.0, "employed_arts_entertainment_recreation_accommodation_food": 10912.0, "employed_construction": 5206.0, "employed_education_health_social": 26442.0, "employed_finance_insurance_real_estate": 18044.0, "employed_information": 4683.0, "employed_manufacturing": 8214.0, "employed_other_services_not_public_admin": 6268.0, "employed_public_administration": 3419.0, "employed_retail_trade": 13210.0, "employed_science_management_admin_waste": 22735.0, "employed_transportation_warehousing_utilities": 11112.0, "employed_wholesale_trade": 4094.0, "female_female_households": 240.0, "four_more_cars": 389.0, "gini_index": 0.5204, "graduate_professional_degree": 30547.0, "group_quarters": 2672.0, "high_school_including_ged": 47155.0, "households_public_asst_or_food_stamps": 20907.0, "in_grades_1_to_4": 12068.0, "in_grades_5_to_8": 8999.0, "in_grades_9_to_12": 10822.0, "in_school": 61618.0, "in_undergrad_college": 13087.0, "less_than_high_school_graduate": 25785.0, "male_45_64_associates_degree": 1185.0, "male_45_64_bachelors_degree": 6132.0, "male_45_64_graduate_degree": 2642.0, "male_45_64_less_than_9_grade": 2343.0, "male_45_64_grade_9_12": 2516.0, "male_45_64_high_school": 9036.0, "male_45_64_some_college": 4390.0, "male_45_to_64": 28244.0, "male_male_households": 621.0, "management_business_sci_arts_employed": 57889.0, "no_car": 41033.0, "no_cars": 40799.0, "not_us_citizen_pop": 61324.0, "occupation_management_arts": 57889.0, "occupation_natural_resources_construction_maintenance": 7606.0, "occupation_production_transportation_material": 16716.0, "occupation_sales_office": 30358.0, "occupation_services": 21770.0, "one_car": 42949.0, "two_cars": 15213.0, "three_cars": 2405.0, "pop_25_64": 159543.0, "pop_determined_poverty_status": 261583.0, "population_1_year_and_over": 259679.0, "population_3_years_over": 250451.0, "poverty": 54826.0, "sales_office_employed": 30358.0, "some_college_and_associates_degree": 32971.0, "walked_to_work": 9477.0, "worked_at_home": 5089.0, "workers_16_and_over": 132551.0, "associates_degree": 8229.0, "bachelors_degree": 49215.0, "high_school_diploma": 42370.0, "less_one_year_college": 6803.0, "masters_degree": 23250.0, "one_year_more_college": 17939.0, "pop_25_years_over": 185673.0, "commute_35_44_mins": 13062.0, "commute_60_more_mins": 23075.0, "commute_less_10_mins": 5545.0, "commuters_16_over": 127462.0, "hispanic_any_race": 79718.0, "pop_5_years_over": 243262.0, "speak_only_english_at_home": 110680.0, "speak_spanish_at_home": 59223.0, "speak_spanish_at_home_low_english": 23764.0, "do_date": "2015", "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN}, {"geo_id": "1714000", "nonfamily_households": 485659.0, "family_households": 567570.0, "median_year_structure_built": 1947.0, "rent_burden_not_computed": 37577.0, "rent_over_50_percent": 147943.0, "rent_40_to_50_percent": 51145.0, "rent_35_to_40_percent": 38256.0, "rent_30_to_35_percent": 45362.0, "rent_25_to_30_percent": 61100.0, "rent_20_to_25_percent": 71277.0, "rent_15_to_20_percent": 72214.0, "rent_10_to_15_percent": 45190.0, "rent_under_10_percent": 21678.0, "total_pop": 2720556.0, "male_pop": 1320015.0, "female_pop": 1400541.0, "median_age": 34.2, "white_pop": 879318.0, "black_pop": 834048.0, "asian_pop": 166771.0, "hispanic_pop": 785725.0, "amerindian_pop": 3938.0, "other_race_pop": 4236.0, "two_or_more_races_pop": 46124.0, "not_hispanic_pop": 1934831.0, "commuters_by_public_transportation": 363322.0, "households": 1053229.0, "median_income": 50702.0, "income_per_capita": 31641.0, "housing_units": 1202265.0, "vacant_housing_units": 149036.0, "vacant_housing_units_for_rent": 35741.0, "vacant_housing_units_for_sale": 14886.0, "median_rent": 864.0, "percent_income_spent_on_rent": 30.6, "owner_occupied_housing_units": 461487.0, "million_dollar_housing_units": 8910.0, "mortgaged_housing_units": 313199.0, "families_with_young_children": 196768.0, "two_parent_families_with_young_children": 103985.0, "two_parents_in_labor_force_families_with_young_children": 63300.0, "two_parents_father_in_labor_force_families_with_young_children": 35823.0, "two_parents_mother_in_labor_force_families_with_young_children": 2945.0, "two_parents_not_in_labor_force_families_with_young_children": 1917.0, "one_parent_families_with_young_children": 92783.0, "father_one_parent_families_with_young_children": 17735.0, "father_in_labor_force_one_parent_families_with_young_children": 15626.0, "commute_10_14_mins": 85930.0, "commute_15_19_mins": 119062.0, "commute_20_24_mins": 151361.0, "commute_25_29_mins": 69228.0, "commute_30_34_mins": 230362.0, "commute_45_59_mins": 177983.0, "aggregate_travel_time_to_work": 42931360.0, "income_less_10000": 109844.0, "income_10000_14999": 57888.0, "income_15000_19999": 62080.0, "income_20000_24999": 64060.0, "income_25000_29999": 49219.0, "income_30000_34999": 52100.0, "income_35000_39999": 43462.0, "income_40000_44999": 44498.0, "income_45000_49999": 34834.0, "income_50000_59999": 73596.0, "income_60000_74999": 87435.0, "income_75000_99999": 115900.0, "income_100000_124999": 80400.0, "income_125000_149999": 50460.0, "income_150000_199999": 56611.0, "income_200000_or_more": 70842.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 985.0, "owner_occupied_housing_units_lower_value_quartile": 152600.0, "owner_occupied_housing_units_median_value": 238500.0, "owner_occupied_housing_units_upper_value_quartile": 377100.0, "married_households": 343134.0, "occupied_housing_units": 1053229.0, "housing_units_renter_occupied": 591742.0, "dwellings_1_units_detached": 308558.0, "dwellings_1_units_attached": 43217.0, "dwellings_2_units": 178483.0, "dwellings_3_to_4_units": 185057.0, "dwellings_5_to_9_units": 141097.0, "dwellings_10_to_19_units": 51768.0, "dwellings_20_to_49_units": 83175.0, "dwellings_50_or_more_units": 207951.0, "mobile_homes": 2863.0, "housing_built_2005_or_later": 3644.0, "housing_built_2000_to_2004": 11920.0, "housing_built_1939_or_earlier": 119058.0, "male_under_5": 87627.0, "male_5_to_9": 81184.0, "male_10_to_14": 75762.0, "male_15_to_17": 46789.0, "male_18_to_19": 33008.0, "male_20": 18957.0, "male_21": 22829.0, "male_22_to_24": 66432.0, "male_25_to_29": 133112.0, "male_30_to_34": 127817.0, "male_35_to_39": 100473.0, "male_40_to_44": 91074.0, "male_45_to_49": 84630.0, "male_50_to_54": 78447.0, "male_55_to_59": 77409.0, "male_60_61": 26420.0, "male_62_64": 36269.0, "male_65_to_66": 22325.0, "male_67_to_69": 26025.0, "male_70_to_74": 33763.0, "male_75_to_79": 22313.0, "male_80_to_84": 16171.0, "male_85_and_over": 11179.0, "female_under_5": 87970.0, "female_5_to_9": 78461.0, "female_10_to_14": 78934.0, "female_15_to_17": 46360.0, "female_18_to_19": 37151.0, "female_20": 20485.0, "female_21": 19286.0, "female_22_to_24": 67469.0, "female_25_to_29": 141022.0, "female_30_to_34": 128793.0, "female_35_to_39": 101402.0, "female_40_to_44": 88282.0, "female_45_to_49": 82535.0, "female_50_to_54": 81274.0, "female_55_to_59": 84557.0, "female_60_to_61": 29639.0, "female_62_to_64": 42535.0, "female_65_to_66": 24833.0, "female_67_to_69": 31849.0, "female_70_to_74": 44696.0, "female_75_to_79": 29842.0, "female_80_to_84": 25938.0, "female_85_and_over": 27228.0, "white_including_hispanic": 1316927.0, "black_including_hispanic": 844768.0, "amerindian_including_hispanic": 8982.0, "asian_including_hispanic": 168744.0, "commute_5_9_mins": 42288.0, "commute_35_39_mins": 46865.0, "commute_40_44_mins": 83118.0, "commute_60_89_mins": 160548.0, "commute_90_more_mins": 48186.0, "households_retirement_income": 127410.0, "armed_forces": 883.0, "civilian_labor_force": 1450688.0, "employed_pop": 1312930.0, "unemployed_pop": 137758.0, "not_in_labor_force": 749022.0, "pop_16_over": 2200593.0, "pop_in_labor_force": 1451571.0, "asian_male_45_54": 9346.0, "asian_male_55_64": 6402.0, "black_male_45_54": 48313.0, "black_male_55_64": 46466.0, "hispanic_male_45_54": 47290.0, "hispanic_male_55_64": 32793.0, "white_male_45_54": 56667.0, "white_male_55_64": 53048.0, "bachelors_degree_2": 404544.0, "bachelors_degree_or_higher_25_64": 607043.0, "children": 583087.0, "children_in_single_female_hh": 227444.0, "commuters_by_bus": 178932.0, "commuters_by_car_truck_van": 733308.0, "commuters_by_carpool": 97198.0, "commuters_by_subway_or_elevated": 158841.0, "commuters_drove_alone": 636110.0, "different_house_year_ago_different_city": 109568.0, "different_house_year_ago_same_city": 276026.0, "employed_agriculture_forestry_fishing_hunting_mining": 3441.0, "employed_arts_entertainment_recreation_accommodation_food": 144384.0, "employed_construction": 52114.0, "employed_education_health_social": 298537.0, "employed_finance_insurance_real_estate": 109820.0, "employed_information": 30384.0, "employed_manufacturing": 113497.0, "employed_other_services_not_public_admin": 61958.0, "employed_public_administration": 54251.0, "employed_retail_trade": 121654.0, "employed_science_management_admin_waste": 207275.0, "employed_transportation_warehousing_utilities": 82584.0, "employed_wholesale_trade": 33031.0, "female_female_households": 2317.0, "four_more_cars": 17696.0, "gini_index": 0.5314, "graduate_professional_degree": 274035.0, "group_quarters": 58318.0, "high_school_including_ged": 436156.0, "households_public_asst_or_food_stamps": 223616.0, "in_grades_1_to_4": 126079.0, "in_grades_5_to_8": 124134.0, "in_grades_9_to_12": 125570.0, "in_school": 680596.0, "in_undergrad_college": 161419.0, "less_than_high_school_graduate": 294813.0, "male_45_64_associates_degree": 18625.0, "male_45_64_bachelors_degree": 46953.0, "male_45_64_graduate_degree": 35434.0, "male_45_64_less_than_9_grade": 30245.0, "male_45_64_grade_9_12": 27513.0, "male_45_64_high_school": 86473.0, "male_45_64_some_college": 57932.0, "male_45_to_64": 303175.0, "male_male_households": 4883.0, "management_business_sci_arts_employed": 522780.0, "no_car": 200159.0, "no_cars": 278691.0, "not_us_citizen_pop": 333325.0, "occupation_management_arts": 522780.0, "occupation_natural_resources_construction_maintenance": 69088.0, "occupation_production_transportation_material": 162884.0, "occupation_sales_office": 297788.0, "occupation_services": 260390.0, "one_car": 473495.0, "two_cars": 230283.0, "three_cars": 53064.0, "pop_25_64": 1535690.0, "pop_determined_poverty_status": 2666026.0, "population_1_year_and_over": 2685744.0, "population_3_years_over": 2619021.0, "poverty": 556134.0, "sales_office_employed": 297788.0, "some_college_and_associates_degree": 442304.0, "walked_to_work": 86276.0, "worked_at_home": 58114.0, "workers_16_and_over": 1284520.0, "associates_degree": 112943.0, "bachelors_degree": 404544.0, "high_school_diploma": 385849.0, "less_one_year_college": 78562.0, "masters_degree": 190167.0, "one_year_more_college": 250799.0, "pop_25_years_over": 1851852.0, "commute_35_44_mins": 129983.0, "commute_60_more_mins": 208734.0, "commute_less_10_mins": 53763.0, "commuters_16_over": 1226406.0, "hispanic_any_race": 785725.0, "pop_5_years_over": 2544959.0, "speak_only_english_at_home": 1619603.0, "speak_spanish_at_home": 629641.0, "speak_spanish_at_home_low_english": 256244.0, "do_date": "2015", "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN}, {"geo_id": "4835000", "nonfamily_households": 324605.0, "family_households": 525369.0, "median_year_structure_built": 1977.0, "rent_burden_not_computed": 24749.0, "rent_over_50_percent": 120204.0, "rent_40_to_50_percent": 42712.0, "rent_35_to_40_percent": 32454.0, "rent_30_to_35_percent": 43567.0, "rent_25_to_30_percent": 53931.0, "rent_20_to_25_percent": 58482.0, "rent_15_to_20_percent": 62142.0, "rent_10_to_15_percent": 42856.0, "rent_under_10_percent": 17293.0, "total_pop": 2298628.0, "male_pop": 1149686.0, "female_pop": 1148942.0, "median_age": 32.6, "white_pop": 571986.0, "black_pop": 505472.0, "asian_pop": 157897.0, "hispanic_pop": 1028148.0, "amerindian_pop": 1463.0, "other_race_pop": 6208.0, "two_or_more_races_pop": 26708.0, "not_hispanic_pop": 1270480.0, "commuters_by_public_transportation": 44996.0, "households": 849974.0, "median_income": 48064.0, "income_per_capita": 29310.0, "housing_units": 946571.0, "vacant_housing_units": 96597.0, "vacant_housing_units_for_rent": 42376.0, "vacant_housing_units_for_sale": 6481.0, "median_rent": 783.0, "percent_income_spent_on_rent": 30.2, "owner_occupied_housing_units": 351584.0, "million_dollar_housing_units": 6135.0, "mortgaged_housing_units": 194283.0, "families_with_young_children": 204659.0, "two_parent_families_with_young_children": 112098.0, "two_parents_in_labor_force_families_with_young_children": 51802.0, "two_parents_father_in_labor_force_families_with_young_children": 54634.0, "two_parents_mother_in_labor_force_families_with_young_children": 2862.0, "two_parents_not_in_labor_force_families_with_young_children": 2800.0, "one_parent_families_with_young_children": 92561.0, "father_one_parent_families_with_young_children": 21290.0, "father_in_labor_force_one_parent_families_with_young_children": 19937.0, "commute_10_14_mins": 126310.0, "commute_15_19_mins": 149655.0, "commute_20_24_mins": 165385.0, "commute_25_29_mins": 63055.0, "commute_30_34_mins": 222847.0, "commute_45_59_mins": 97343.0, "aggregate_travel_time_to_work": 29301455.0, "income_less_10000": 67800.0, "income_10000_14999": 50165.0, "income_15000_19999": 52220.0, "income_20000_24999": 52404.0, "income_25000_29999": 47279.0, "income_30000_34999": 47841.0, "income_35000_39999": 41470.0, "income_40000_44999": 44716.0, "income_45000_49999": 33681.0, "income_50000_59999": 67515.0, "income_60000_74999": 74590.0, "income_75000_99999": 83288.0, "income_100000_124999": 56135.0, "income_125000_149999": 31641.0, "income_150000_199999": 40580.0, "income_200000_or_more": 58649.0, "renter_occupied_housing_units_paying_cash_median_gross_rent": 923.0, "owner_occupied_housing_units_lower_value_quartile": 90400.0, "owner_occupied_housing_units_median_value": 152200.0, "owner_occupied_housing_units_upper_value_quartile": 315800.0, "married_households": 321658.0, "occupied_housing_units": 849974.0, "housing_units_renter_occupied": 498390.0, "dwellings_1_units_detached": 414983.0, "dwellings_1_units_attached": 52289.0, "dwellings_2_units": 15764.0, "dwellings_3_to_4_units": 35945.0, "dwellings_5_to_9_units": 73820.0, "dwellings_10_to_19_units": 138916.0, "dwellings_20_to_49_units": 70353.0, "dwellings_50_or_more_units": 135384.0, "mobile_homes": 8572.0, "housing_built_2005_or_later": 7284.0, "housing_built_2000_to_2004": 35799.0, "housing_built_1939_or_earlier": 40006.0, "male_under_5": 87889.0, "male_5_to_9": 84381.0, "male_10_to_14": 78589.0, "male_15_to_17": 45662.0, "male_18_to_19": 28367.0, "male_20": 17987.0, "male_21": 14120.0, "male_22_to_24": 60687.0, "male_25_to_29": 109635.0, "male_30_to_34": 103713.0, "male_35_to_39": 90282.0, "male_40_to_44": 77651.0, "male_45_to_49": 71002.0, "male_50_to_54": 67446.0, "male_55_to_59": 59818.0, "male_60_61": 25033.0, "male_62_64": 31322.0, "male_65_to_66": 14659.0, "male_67_to_69": 20506.0, "male_70_to_74": 24766.0, "male_75_to_79": 16722.0, "male_80_to_84": 11530.0, "male_85_and_over": 7919.0, "female_under_5": 88475.0, "female_5_to_9": 85832.0, "female_10_to_14": 73295.0, "female_15_to_17": 43458.0, "female_18_to_19": 28151.0, "female_20": 16138.0, "female_21": 17850.0, "female_22_to_24": 55839.0, "female_25_to_29": 103487.0, "female_30_to_34": 100233.0, "female_35_to_39": 81589.0, "female_40_to_44": 76934.0, "female_45_to_49": 69261.0, "female_50_to_54": 65891.0, "female_55_to_59": 61957.0, "female_60_to_61": 21701.0, "female_62_to_64": 33160.0, "female_65_to_66": 19483.0, "female_67_to_69": 23942.0, "female_70_to_74": 29624.0, "female_75_to_79": 21035.0, "female_80_to_84": 15309.0, "female_85_and_over": 16298.0, "white_including_hispanic": 1351048.0, "black_including_hispanic": 512039.0, "amerindian_including_hispanic": 6484.0, "asian_including_hispanic": 159292.0, "commute_5_9_mins": 68268.0, "commute_35_39_mins": 32695.0, "commute_40_44_mins": 45427.0, "commute_60_89_mins": 66959.0, "commute_90_more_mins": 17858.0, "households_retirement_income": 78799.0, "armed_forces": 1170.0, "civilian_labor_force": 1205150.0, "employed_pop": 1134035.0, "unemployed_pop": 71115.0, "not_in_labor_force": 565014.0, "pop_16_over": 1771334.0, "pop_in_labor_force": 1206320.0, "asian_male_45_54": 9075.0, "asian_male_55_64": 6772.0, "black_male_45_54": 30010.0, "black_male_55_64": 28235.0, "hispanic_male_45_54": 60188.0, "hispanic_male_55_64": 36867.0, "white_male_45_54": 38477.0, "white_male_55_64": 43827.0, "bachelors_degree_2": 277222.0, "bachelors_degree_or_higher_25_64": 388085.0, "children": 587581.0, "children_in_single_female_hh": 212880.0, "commuters_by_bus": 43961.0, "commuters_by_car_truck_van": 975750.0, "commuters_by_carpool": 116415.0, "commuters_by_subway_or_elevated": 301.0, "commuters_drove_alone": 859335.0, "different_house_year_ago_different_city": 143253.0, "different_house_year_ago_same_city": 245290.0, "employed_agriculture_forestry_fishing_hunting_mining": 36097.0, "employed_arts_entertainment_recreation_accommodation_food": 117760.0, "employed_construction": 124556.0, "employed_education_health_social": 207099.0, "employed_finance_insurance_real_estate": 71672.0, "employed_information": 14017.0, "employed_manufacturing": 91035.0, "employed_other_services_not_public_admin": 63695.0, "employed_public_administration": 24772.0, "employed_retail_trade": 126125.0, "employed_science_management_admin_waste": 154065.0, "employed_transportation_warehousing_utilities": 67670.0, "employed_wholesale_trade": 35472.0, "female_female_households": 2542.0, "four_more_cars": 31187.0, "gini_index": 0.5286, "graduate_professional_degree": 177019.0, "group_quarters": 34366.0, "high_school_including_ged": 341559.0, "households_public_asst_or_food_stamps": 129747.0, "in_grades_1_to_4": 133849.0, "in_grades_5_to_8": 123938.0, "in_grades_9_to_12": 117578.0, "in_school": 609644.0, "in_undergrad_college": 128928.0, "less_than_high_school_graduate": 327930.0, "male_45_64_associates_degree": 12776.0, "male_45_64_bachelors_degree": 41436.0, "male_45_64_graduate_degree": 30718.0, "male_45_64_less_than_9_grade": 41793.0, "male_45_64_grade_9_12": 23490.0, "male_45_64_high_school": 63109.0, "male_45_64_some_college": 41299.0, "male_45_to_64": 254621.0, "male_male_households": 2477.0, "management_business_sci_arts_employed": 381445.0, "no_car": 45024.0, "no_cars": 70163.0, "not_us_citizen_pop": 502665.0, "occupation_management_arts": 381445.0, "occupation_natural_resources_construction_maintenance": 136861.0, "occupation_production_transportation_material": 144977.0, "occupation_sales_office": 251011.0, "occupation_services": 219741.0, "one_car": 371803.0, "two_cars": 293507.0, "three_cars": 83314.0, "pop_25_64": 1250115.0, "pop_determined_poverty_status": 2267844.0, "population_1_year_and_over": 2261829.0, "population_3_years_over": 2193401.0, "poverty": 480534.0, "sales_office_employed": 251011.0, "some_college_and_associates_degree": 348178.0, "walked_to_work": 23090.0, "worked_at_home": 40944.0, "workers_16_and_over": 1113447.0, "associates_degree": 76656.0, "bachelors_degree": 277222.0, "high_school_diploma": 291762.0, "less_one_year_college": 58901.0, "masters_degree": 117969.0, "one_year_more_college": 212621.0, "pop_25_years_over": 1471908.0, "commute_35_44_mins": 78122.0, "commute_60_more_mins": 84817.0, "commute_less_10_mins": 84969.0, "commuters_16_over": 1072503.0, "hispanic_any_race": 1028148.0, "pop_5_years_over": 2122264.0, "speak_only_english_at_home": 1075407.0, "speak_spanish_at_home": 825216.0, "speak_spanish_at_home_low_english": 428975.0, "do_date": "2015", "pop_15_and_over": NaN, "pop_never_married": NaN, "pop_now_married": NaN, "pop_separated": NaN, "pop_widowed": NaN, "pop_divorced": NaN}]}