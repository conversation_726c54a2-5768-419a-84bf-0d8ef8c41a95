version: 2

models:
  - name: finishes_by_constructor
    description: "Summarizes constructor performance across all Formula 1 races, including their finishes, podiums, pole positions, fastest laps, and race outcomes such as disqualifications, failures to qualify, and retirements."

    refs:
      - name: stg_f1_dataset__constructors
      - name: stg_f1_dataset__results

    columns:
      - name: constructor_id
        description: "Unique identifier for each constructor."
        tests:
          - not_null
          - unique
      
      - name: constructor_name
        description: "The full name of the Formula 1 constructor."
      
      - name: races
        description: "The total number of races the constructor has participated in."
      
      - name: podiums
        description: "The number of podium finishes (top 3 positions) achieved by the constructor."
      
      - name: pole_positions
        description: "The number of pole positions achieved by the constructor (started from the first grid position)."
      
      - name: fastest_laps
        description: "The number of fastest laps recorded by the constructor."
      
      - name: p1
        description: "The number of times the constructor finished in 1st place."

      - name: p2
        description: "The number of times the constructor finished in 2nd place."

      - name: p3
        description: "The number of times the constructor finished in 3rd place."

      - name: p4
        description: "The number of times the constructor finished in 4th place."

      - name: p5
        description: "The number of times the constructor finished in 5th place."

      - name: p6
        description: "The number of times the constructor finished in 6th place."

      - name: p7
        description: "The number of times the constructor finished in 7th place."

      - name: p8
        description: "The number of times the constructor finished in 8th place."

      - name: p9
        description: "The number of times the constructor finished in 9th place."

      - name: p10
        description: "The number of times the constructor finished in 10th place."

      - name: p11
        description: "The number of times the constructor finished in 11th place."

      - name: p12
        description: "The number of times the constructor finished in 12th place."

      - name: p13
        description: "The number of times the constructor finished in 13th place."

      - name: p14
        description: "The number of times the constructor finished in 14th place."

      - name: p15
        description: "The number of times the constructor finished in 15th place."

      - name: p16
        description: "The number of times the constructor finished in 16th place."

      - name: p17
        description: "The number of times the constructor finished in 17th place."

      - name: p18
        description: "The number of times the constructor finished in 18th place."

      - name: p19
        description: "The number of times the constructor finished in 19th place."

      - name: p20
        description: "The number of times the constructor finished in 20th place."

      - name: p21plus
        description: "The number of times the constructor finished beyond 20th place."

      - name: disqualified
        description: "The number of times the constructor was disqualified from a race."

      - name: excluded
        description: "The number of times the constructor was excluded from race results."

      - name: failed_to_qualify
        description: "The number of times the constructor failed to qualify for a race."

      - name: not_classified
        description: "The number of times the constructor was not classified in race results."

      - name: retired
        description: "The number of times the constructor's car retired from the race."

      - name: withdrew
        description: "The number of times the constructor's car withdrew from the race."

  - name: construtor_drivers_championships
    description: "Summarizes the number of driver championships won by each constructor based on their driver with the highest points in each season."

    refs:
      - name: stg_f1_dataset__constructors
      - name: stg_f1_dataset__results
      - name: stg_f1_dataset__races
      - name: stg_f1_dataset__driver_standings

    columns:
      - name: constructor_name
        description: "The full name of the Formula 1 constructor."
        tests:
          - not_null
          - unique

      - name: total_driver_championships
        description: "The total number of driver championships won by the constructor."

  - name: driver_championships
    description: "Summarizes the number of championships won by drivers based on the highest points achieved in each season."

    refs:
      - name: stg_f1_dataset__drivers
      - name: stg_f1_dataset__results
      - name: stg_f1_dataset__races
      - name: stg_f1_dataset__driver_standings

    columns:
      - name: driver_full_name
        description: "The full name of the Formula 1 driver."
        tests:
          - not_null
          - unique

      - name: total_championships
        description: "The total number of championships won by the driver."
