
# HEY! This file is used in the dbt-utils integrations tests with CircleCI.
# You should __NEVER__ check credentials into version control. Thanks for reading :)

config:
    send_anonymous_usage_stats: False
    use_colors: True

integration_tests:
  target: postgres
  outputs:
    postgres:
      type: postgres
      host: "{{ env_var('POSTGRES_TEST_HOST') }}"
      user: "{{ env_var('POSTGRES_TEST_USER') }}"
      pass: "{{ env_var('POSTGRES_TEST_PASS') }}"
      port: "{{ env_var('POSTGRES_TEST_PORT') | as_number }}"
      dbname: "{{ env_var('POSTGRES_TEST_DBNAME') }}"
      schema: dbt_utils_integration_tests_postgres
      threads: 5

    redshift:
      type: redshift
      host: "{{ env_var('REDSHIFT_TEST_HOST') }}"
      user: "{{ env_var('REDSHIFT_TEST_USER') }}"
      pass: "{{ env_var('REDSHIFT_TEST_PASS') }}"
      dbname: "{{ env_var('REDSHIFT_TEST_DBNAME') }}"
      port: "{{ env_var('REDSHIFT_TEST_PORT') | as_number }}"
      schema: dbt_utils_integration_tests_redshift
      threads: 5

    bigquery:
      type: bigquery
      method: service-account
      keyfile: "{{ env_var('BIGQUERY_SERVICE_KEY_PATH') }}"
      project: "{{ env_var('BIGQUERY_TEST_DATABASE') }}"
      schema: dbt_utils_integration_tests_bigquery
      threads: 10

    snowflake:
      type: snowflake
      account: "{{ env_var('SNOWFLAKE_TEST_ACCOUNT') }}"
      user: "{{ env_var('SNOWFLAKE_TEST_USER') }}"
      password: "{{ env_var('SNOWFLAKE_TEST_PASSWORD') }}"
      role: "{{ env_var('SNOWFLAKE_TEST_ROLE') }}"
      database: "{{ env_var('SNOWFLAKE_TEST_DATABASE') }}"
      warehouse: "{{ env_var('SNOWFLAKE_TEST_WAREHOUSE') }}"
      schema: dbt_utils_integration_tests_snowflake
      threads: 10
