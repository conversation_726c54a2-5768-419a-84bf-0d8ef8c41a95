name: "dbt_date_integration_tests"
version: "1.0"

profile: "integration_tests"

config-version: 2

model-paths: ["models"]
test-paths: ["tests"]
seed-paths: ["data"]
macro-paths: ["macros"]

target-path: "target"
clean-targets: ["target", "dbt_modules", "dbt_packages"]

dispatch:
  - macro_namespace: dbt_date
    search_order: ['dbt_date_integration_tests', 'dbt_date']  # enable override

vars:
    dbt_date_dispatch_list: ['dbt_date_integration_tests']
    "dbt_date:time_zone": "America/Los_Angeles"

quoting:
    database: false
    identifier: false
    schema: false

models:
  dbt_date_integration_tests:
    schema: dbt_date_integration_tests
    materialized: table
