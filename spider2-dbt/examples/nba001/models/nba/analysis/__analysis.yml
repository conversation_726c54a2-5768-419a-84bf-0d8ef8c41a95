version: 2

models:
  - name: reg_season_summary
    description: "A joined view that combines data from regular season results, Vegas predictions, and actual team performances. The view provides insights into the difference between Elo and Vegas win predictions."

    refs:
      - name: reg_season_end
      - name: nba_vegas_wins
      - name: nba_reg_season_actuals
    
    columns:
      - name: team
        description: "The name of the team participating in the NBA season."
        tests:
          - not_null
          - unique
      
      - name: conf
        description: "The conference to which the team belongs (Eastern or Western)."
      
      - name: record
        description: "The team's win-loss record for the regular season (e.g., '50 - 32')."
      
      - name: avg_wins
        description: "The average number of wins for the team according to Elo predictions, rounded to one decimal place."
      
      - name: vegas_wins
        description: "The predicted win total for the team according to Vegas odds."
      
      - name: elo_vs_vegas
        description: "The difference between Elo predictions and Vegas win totals, indicating whether the Elo system over- or under-predicted wins compared to Vegas."


  - name: season_summary
    description: "A summary view combining regular season performance, playoff achievements, and Elo ratings for NBA teams. This view highlights the team's performance and their progress in the playoffs."
    
    refs:
      - name: reg_season_summary
      - name: playoff_summary
      - name: nba_ratings
    
    columns:
      - name: elo_rating
        description: "The current Elo rating for the team, formatted with a change indicator compared to the original rating."
      
      - name: record
        description: "The team's win-loss record for the regular season, formatted as 'wins - losses'."
      
      - name: avg_wins
        description: "The average number of wins during the regular season, calculated from Elo predictions."
      
      - name: vegas_wins
        description: "The predicted number of wins according to Vegas odds."
      
      - name: elo_vs_vegas
        description: "The difference between Elo predicted wins and Vegas win totals."
      
      - name: made_playoffs
        description: "Indicates whether the team made it to the playoffs (1 if yes, null if no)."
      
      - name: made_conf_semis
        description: "Indicates whether the team made it to the conference semifinals (1 if yes, null if no)."
      
      - name: made_conf_finals
        description: "Indicates whether the team made it to the conference finals (1 if yes, null if no)."
      
      - name: made_finals
        description: "Indicates whether the team made it to the NBA finals (1 if yes, null if no)."
      
      - name: won_finals
        description: "Indicates whether the team won the NBA finals (1 if yes, null if no)."


  - name: playoff_summary
    description: "A summary view of playoff achievements for NBA teams, showing their progress through different rounds of the playoffs."
    
    refs:
      - name: initialize_seeding
      - name: playoff_sim_r1
      - name: playoff_sim_r2
      - name: playoff_sim_r3
      - name: playoff_sim_r4
      - name: nba_teams
    
    columns:
      - name: team
        description: "The name of the NBA team."
        tests:
          - not_null
          - unique
      
      - name: made_playoffs
        description: "Indicates if the team made it to the playoffs (1 if yes, null if no)."
      
      - name: made_conf_semis
        description: "Indicates if the team made it to the conference semifinals (1 if yes, null if no)."
      
      - name: made_conf_finals
        description: "Indicates if the team made it to the conference finals (1 if yes, null if no)."
      
      - name: made_finals
        description: "Indicates if the team made it to the NBA finals (1 if yes, null if no)."
      
      - name: won_finals
        description: "Indicates if the team won the NBA finals (1 if yes, null if no)."