version: 2

models:
  - name: qualtrics__survey
    description: Detailed view of all surveys created, enhanced with distribution and response metrics.
    tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - source_relation
            - survey_id
    columns:
      - name: is_deleted
        description: "{{ doc('_fivetran_deleted') }}"
      - name: _fivetran_synced
        description: "{{ doc('_fivetran_synced') }}"
      - name: auto_scoring_category
        description: "{{ doc('auto_scoring_category') }}"
      - name: brand_base_url
        description: "{{ doc('brand_base_url') }}"
      - name: brand_id
        description: "{{ doc('brand_id') }}"
      - name: bundle_short_name
        description: "{{ doc('bundle_short_name') }}"
      - name: composition_type
        description: "{{ doc('composition_type') }}"
      - name: creator_user_id
        description: "{{ doc('creator_id') }}"
      - name: default_scoring_category
        description: "{{ doc('default_scoring_category') }}"
      - name: division_id
        description: "{{ doc('division_id') }}"
      - name: survey_id
        description: "{{ doc('survey_id') }}"
      - name: last_accessed_at
        description: "{{ doc('last_accessed') }}"
      - name: last_activated_at
        description: "{{ doc('last_activated') }}"
      - name: last_modified_at
        description: "{{ doc('last_modified_date') }}"
      - name: owner_user_id
        description: "{{ doc('owner_id') }}"
      - name: project_category
        description: "{{ doc('project_category') }}"
      - name: project_type
        description: "{{ doc('project_type') }}"
      - name: registry_sha
        description: "{{ doc('registry_sha') }}"
      - name: registry_version
        description: "{{ doc('registry_version') }}"
      - name: schema_version
        description: "{{ doc('schema_version') }}"
      - name: scoring_summary_after_questions
        description: "{{ doc('scoring_summary_after_questions') }}"
      - name: scoring_summary_after_survey
        description: "{{ doc('scoring_summary_after_survey') }}"
      - name: scoring_summary_category
        description: "{{ doc('scoring_summary_category') }}"
      - name: survey_name
        description: "{{ doc('survey_name') }}"
      - name: survey_status
        description: "{{ doc('survey_status') }}"
      - name: version_id
        description: "{{ doc('version_id') }}"
      - name: version_number
        description: "{{ doc('version_number') }}"
      - name: version_description
        description: "{{ doc('survey_version_description') }}"
      - name: survey_version_created_at
        description: "{{ doc('creation_date') }}"
      - name: count_published_versions
        description: "{{ doc('count_published_versions') }}"
      - name: publisher_user_id
        description: "{{ doc('publisher_user_id') }}"
      - name: publisher_email
        description: "{{ doc('publisher_email') }}"
      - name: creator_email
        description: "{{ doc('creator_email') }}"
      - name: owner_email
        description: "{{ doc('owner_email') }}"
      - name: count_questions
        description: "{{ doc('count_questions') }}"
      - name: avg_response_duration_in_seconds
        description: "{{ doc('avg_response_duration_in_seconds') }}"
      - name: avg_survey_progress_pct
        description: "{{ doc('avg_survey_progress_pct') }}"
      - name: median_response_duration_in_seconds
        description: "{{ doc('median_response_duration_in_seconds') }}"
      - name: median_survey_progress_pct
        description: "{{ doc('median_survey_progress_pct') }}"
      - name: count_survey_responses
        description: "{{ doc('count_survey_responses') }}"
      - name: count_completed_survey_responses
        description: "{{ doc('count_completed_survey_responses') }}"
      - name: count_survey_responses_30d
        description: "{{ doc('count_survey_responses_30d') }}"
      - name: count_completed_survey_responses_30d
        description: "{{ doc('count_completed_survey_responses_30d') }}"
      - name: count_anonymous_survey_responses
        description: "{{ doc('count_anonymous_survey_responses') }}"
      - name: count_anonymous_completed_survey_responses
        description: "{{ doc('count_anonymous_completed_survey_responses') }}"
      - name: count_social_media_survey_responses
        description: "{{ doc('count_social_media_survey_responses') }}"
      - name: count_social_media_completed_survey_responses
        description: "{{ doc('count_social_media_completed_survey_responses') }}"
      - name: count_personal_link_survey_responses
        description: "{{ doc('count_personal_link_survey_responses') }}"
      - name: count_personal_link_completed_survey_responses
        description: "{{ doc('count_personal_link_completed_survey_responses') }}"
      - name: count_qr_code_survey_responses
        description: "{{ doc('count_qr_code_survey_responses') }}"
      - name: count_qr_code_completed_survey_responses
        description: "{{ doc('count_qr_code_completed_survey_responses') }}"
      - name: count_email_survey_responses
        description: "{{ doc('count_email_survey_responses') }}"
      - name: count_email_completed_survey_responses
        description: "{{ doc('count_email_completed_survey_responses') }}"
      - name: count_sms_survey_responses
        description: "{{ doc('count_sms_survey_responses') }}"
      - name: count_sms_completed_survey_responses
        description: "{{ doc('count_sms_completed_survey_responses') }}"
      - name: count_uncategorized_survey_responses
        description: "{{ doc('count_uncategorized_survey_responses') }}"
      - name: count_uncategorized_completed_survey_responses
        description: "{{ doc('count_uncategorized_completed_survey_responses') }}"
      - name: source_relation
        description: "{{ doc('source_relation') }}"

  - name: qualtrics__contact
    description: Detailed view of all contacts (from both the XM Directory and Research Core contact endpoints), ehanced with response and mailing list metrics.
    tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - source_relation
            - directory_id
            - contact_id
    columns:
      - name: contact_id
        description: "{{ doc('contact_id') }}"
      - name: is_xm_directory_contact
        description: "{{ doc('is_xm_directory_contact') }}"
      - name: is_research_core_contact
        description: "{{ doc('is_research_core_contact') }}"
      - name: directory_id
        description: "{{ doc('directory_id') }}"
      - name: email
        description: "{{ doc('email') }}"
      - name: email_domain
        description: "{{ doc('email_domain') }}"
      - name: first_name
        description: "{{ doc('contact_first_name') }}"
      - name: last_name
        description: "{{ doc('contact_last_name') }}"
      - name: external_data_reference
        description: "{{ doc('ext_ref') }}"
      - name: language
        description: "{{ doc('language') }}"
      - name: is_unsubscribed_from_directory
        description: "{{ doc('directory_unsubscribed') }}"
      - name: unsubscribed_from_directory_at
        description: "{{ doc('directory_unsubscribe_date') }}"
      - name: last_modified_at
        description: "{{ doc('last_modified_date') }}"
      - name: mailing_list_ids
        description: "{{ doc('mailing_list_ids') }}"
      - name: count_mailing_lists_subscribed_to
        description: "{{ doc('count_mailing_lists_subscribed_to') }}"
      - name: count_mailing_lists_unsubscribed_from
        description: "{{ doc('count_mailing_lists_unsubscribed_from') }}"
      - name: source_relation
        description: "{{ doc('source_relation') }}"
      - name: count_surveys_sent_email
        description: "{{ doc('count_surveys_sent_email') }}"
      - name: count_surveys_sent_sms
        description: "{{ doc('count_surveys_sent_sms') }}"
      - name: count_surveys_opened_email
        description: "{{ doc('count_surveys_opened_email') }}"
      - name: count_surveys_opened_sms
        description: "{{ doc('count_surveys_opened_sms') }}"
      - name: count_surveys_started_email
        description: "{{ doc('count_surveys_started_email') }}"
      - name: count_surveys_started_sms
        description: "{{ doc('count_surveys_started_sms') }}"
      - name: count_surveys_completed_email
        description: "{{ doc('count_surveys_completed_email') }}"
      - name: count_surveys_completed_sms
        description: "{{ doc('count_surveys_completed_sms') }}"
      - name: total_count_surveys
        description: "{{ doc('total_count_surveys') }}"
      - name: total_count_completed_surveys
        description: "{{ doc('total_count_completed_surveys') }}"
      - name: avg_survey_progress_pct
        description: "{{ doc('avg_survey_progress_pct') }}"
      - name: avg_survey_duration_in_seconds
        description: "{{ doc('avg_response_duration_in_seconds') }}"
      - name: median_survey_progress_pct
        description: "{{ doc('median_survey_progress_pct') }}"
      - name: median_survey_duration_in_seconds
        description: "{{ doc('median_response_duration_in_seconds') }}"
      - name: last_survey_response_recorded_at
        description: "{{ doc('last_survey_response_recorded_at') }}"
      - name: first_survey_response_recorded_at
        description: "{{ doc('first_survey_response_recorded_at') }}"
      - name: directory_name
        description: "{{ doc('directory_name') }}"
      - name: is_in_default_directory
        description: "{{ doc('is_default') }}"
      - name: is_directory_deleted
        description: "{{ doc('_fivetran_deleted') }}"
      - name: created_at
        description: "{{ doc('creation_date') }}"

  - name: qualtrics__directory
    description: >
      A directory is an address book for the entire brand and contains all of the contacts that have been added by your users.
      This model provides a detailed view of each directory, enhanced with metrics regarding contacts, survey distribution, and engagement. 
    tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - source_relation
            - directory_id
    columns:
      - name: is_deleted
        description: "{{ doc('_fivetran_deleted') }}"
      - name: _fivetran_synced
        description: "{{ doc('_fivetran_synced') }}"
      - name: is_deduped_on_email
        description: "{{ doc('deduplication_criteria_email') }}"
      - name: is_deduped_on_ext_ref
        description: "{{ doc('deduplication_criteria_external_data_reference') }}"
      - name: is_deduped_on_first_name
        description: "{{ doc('deduplication_criteria_first_name') }}"
      - name: is_deduped_on_last_name
        description: "{{ doc('deduplication_criteria_last_name') }}"
      - name: is_deduped_on_phone
        description: "{{ doc('deduplication_criteria_phone') }}"
      - name: directory_id
        description: "{{ doc('directory_id') }}"
      - name: is_default
        description: "{{ doc('is_default') }}"
      - name: name
        description: "{{ doc('directory_name') }}"
      - name: count_distinct_emails
        description: "{{ doc('count_distinct_emails') }}"
      - name: count_distinct_phones
        description: "{{ doc('count_distinct_phones') }}"
      - name: total_count_contacts
        description: "{{ doc('total_count_contacts') }}"
      - name: total_count_unsubscribed_contacts
        description: "{{ doc('total_count_unsubscribed_contacts') }}"
      - name: count_contacts_created_30d
        description: "{{ doc('count_contacts_created_30d') }}"
      - name: count_contacts_unsubscribed_30d
        description: "{{ doc('count_contacts_unsubscribed_30d') }}"
      - name: count_contacts_sent_survey_30d
        description: "{{ doc('count_contacts_sent_survey_30d') }}"
      - name: count_contacts_opened_survey_30d
        description: "{{ doc('count_contacts_opened_survey_30d') }}"
      - name: count_contacts_started_survey_30d
        description: "{{ doc('count_contacts_started_survey_30d') }}"
      - name: count_contacts_completed_survey_30d
        description: "{{ doc('count_contacts_completed_survey_30d') }}"
      - name: count_surveys_sent_30d
        description: "{{ doc('count_surveys_sent_30d') }}"
      - name: count_mailing_lists
        description: "{{ doc('count_mailing_lists') }}"
      - name: source_relation
        description: "{{ doc('source_relation') }}"

  - name: qualtrics__distribution
    description: >
      Table of each survey's distribution (method of reaching out to XM directory contacts) enhanced with 
      survey response and status metrics.
    tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - source_relation
            - distribution_id
    columns:
      - name: is_deleted
        description: "{{ doc('_fivetran_deleted') }}"
      - name: _fivetran_synced
        description: "{{ doc('_fivetran_synced') }}"
      - name: created_at
        description: "{{ doc('creation_date') }}"
      - name: header_from_email
        description: "{{ doc('header_from_email') }}"
      - name: header_from_name
        description: "{{ doc('header_from_name') }}"
      - name: header_reply_to_email
        description: "{{ doc('header_reply_to_email') }}"
      - name: header_subject
        description: "{{ doc('header_subject') }}"
      - name: distribution_id
        description: "{{ doc('distribution_id') }}"
      - name: message_library_id
        description: "{{ doc('message_library_id') }}"
      - name: message_id
        description: "{{ doc('message_message_id') }}"
      - name: message_text
        description: "{{ doc('message_message_text') }}"
      - name: last_modified_at
        description: "{{ doc('last_modified_date') }}"
      - name: organization_id
        description: "{{ doc('organization_id') }}"
      - name: owner_user_id
        description: "{{ doc('owner_id') }}"
      - name: parent_distribution_id
        description: "{{ doc('parent_distribution_id') }}"
      - name: recipient_contact_id
        description: "{{ doc('recipient_contact_id') }}"
      - name: recipient_library_id
        description: "{{ doc('recipient_library_id') }}"
      - name: recipient_mailing_list_id
        description: "{{ doc('recipient_mailing_list_id') }}"
      - name: recipient_sample_id
        description: "{{ doc('recipient_sample_id') }}"
      - name: request_status
        description: "{{ doc('request_status') }}"
      - name: request_type
        description: "{{ doc('request_type') }}"
      - name: send_at
        description: "{{ doc('send_date') }}"
      - name: survey_link_expires_at
        description: "{{ doc('survey_link_expiration_date') }}"
      - name: survey_link_type
        description: "{{ doc('survey_link_link_type') }}"
      - name: survey_id
        description: "{{ doc('survey_link_survey_id') }}"
      - name: source_relation
        description: "{{ doc('source_relation') }}"
      - name: parent_distribution_header_subject
        description: "{{ doc('parent_distribution_header_subject') }}"
      - name: recipient_mailing_list_name
        description: "{{ doc('recipient_mailing_list_name') }}"
      - name: owner_email
        description: "{{ doc('owner_email') }}"
      - name: owner_first_name
        description: "{{ doc('owner_first_name') }}"
      - name: owner_last_name
        description: "{{ doc('owner_last_name') }}"
      - name: total_count_contacts
        description: "{{ doc('total_count_contacts') }}"
      - name: count_contacts_sent_surveys
        description: "{{ doc('count_contacts_sent_surveys') }}"
      - name: count_contacts_opened_surveys
        description: "{{ doc('count_contacts_opened_surveys') }}"
      - name: count_contacts_started_surveys
        description: "{{ doc('count_contacts_started_surveys') }}"
      - name: count_contacts_completed_surveys
        description: "{{ doc('count_contacts_completed_surveys') }}"
      - name: first_survey_sent_at
        description: "{{ doc('first_survey_sent_at') }}"
      - name: last_survey_sent_at
        description: "{{ doc('last_survey_sent_at') }}"
      - name: first_survey_opened_at
        description: "{{ doc('first_survey_opened_at') }}"
      - name: last_survey_opened_at
        description: "{{ doc('last_survey_opened_at') }}"
      - name: first_response_completed_at
        description: "{{ doc('first_response_completed_at') }}"
      - name: last_response_completed_at
        description: "{{ doc('last_response_completed_at') }}"
      - name: avg_time_to_open_in_seconds
        description: "{{ doc('avg_time_to_open_in_seconds') }}"
      - name: avg_time_to_start_in_seconds
        description: "{{ doc('avg_time_to_start_in_seconds') }}"
      - name: avg_time_to_complete_in_seconds
        description: "{{ doc('avg_time_to_complete_in_seconds') }}"
      - name: median_time_to_open_in_seconds
        description: "{{ doc('median_time_to_open_in_seconds') }}"
      - name: median_time_to_start_in_seconds
        description: "{{ doc('median_time_to_start_in_seconds') }}"
      - name: median_time_to_complete_in_seconds
        description: "{{ doc('median_time_to_complete_in_seconds') }}"
      - name: current_count_surveys_pending
        description: "{{ doc('current_count_surveys_pending') }}"
      - name: current_count_surveys_success
        description: "{{ doc('current_count_surveys_success') }}"
      - name: current_count_surveys_error
        description: "{{ doc('current_count_surveys_error') }}"
      - name: current_count_surveys_opened
        description: "{{ doc('current_count_surveys_opened') }}"
      - name: current_count_surveys_complaint
        description: "{{ doc('current_count_surveys_complaint') }}"
      - name: current_count_surveys_skipped
        description: "{{ doc('current_count_surveys_skipped') }}"
      - name: current_count_surveys_blocked
        description: "{{ doc('current_count_surveys_blocked') }}"
      - name: current_count_surveys_failure
        description: "{{ doc('current_count_surveys_failure') }}"
      - name: current_count_surveys_unknown
        description: "{{ doc('current_count_surveys_unknown') }}"
      - name: current_count_surveys_softbounce
        description: "{{ doc('current_count_surveys_softbounce') }}"
      - name: current_count_surveys_hardbounce
        description: "{{ doc('current_count_surveys_hardbounce') }}"
      - name: current_count_surveys_surveystarted
        description: "{{ doc('current_count_surveys_surveystarted') }}"
      - name: current_count_surveys_surveyfinished
        description: "{{ doc('current_count_surveys_surveyfinished') }}"
      - name: current_count_surveys_surveyscreenedout
        description: "{{ doc('current_count_surveys_surveyscreenedout') }}"
      - name: current_count_surveys_sessionexpired
        description: "{{ doc('current_count_surveys_sessionexpired') }}"
      - name: current_count_surveys_surveypartiallyfinished
        description: "{{ doc('current_count_surveys_surveypartiallyfinished') }}"

  - name: qualtrics__daily_breakdown
    description: Daily breakdown of activities related to surveys and distribution in your Qualtrics instance.
    tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - date_day
            - source_relation
    columns:
      - name: source_relation
        description: "{{ doc('source_relation') }}"
      - name: date_day
        description: "{{ doc('date_day') }}"
      - name: count_contacts_created
        description: "{{ doc('count_contacts_created') }}"
      - name: count_contacts_unsubscribed_from_directory
        description: "{{ doc('count_contacts_unsubscribed_from_directory') }}"
      - name: count_contacts_unsubscribed_from_mailing_list
        description: "{{ doc('count_contacts_unsubscribed_from_mailing_list') }}"
      - name: count_contacts_sent_surveys
        description: "{{ doc('count_contacts_sent_surveys') }}"
      - name: count_contacts_opened_sent_surveys
        description: "{{ doc('count_contacts_opened_sent_surveys') }}"
      - name: count_contacts_started_sent_surveys
        description: "{{ doc('count_contacts_started_sent_surveys') }}"
      - name: count_contacts_completed_sent_surveys
        description: "{{ doc('count_contacts_completed_sent_surveys') }}"
      - name: count_distinct_surveys_responded_to
        description: "{{ doc('count_distinct_surveys_responded_to') }}"
      - name: total_count_survey_responses
        description: "{{ doc('total_count_survey_responses') }}"
      - name: total_count_completed_survey_responses
        description: "{{ doc('total_count_completed_survey_responses') }}"
      - name: count_anonymous_survey_responses
        description: "{{ doc('count_anonymous_survey_responses') }}"
      - name: count_anonymous_completed_survey_responses
        description: "{{ doc('count_anonymous_completed_survey_responses') }}"
      - name: count_social_media_survey_responses
        description: "{{ doc('count_social_media_survey_responses') }}"
      - name: count_social_media_completed_survey_responses
        description: "{{ doc('count_social_media_completed_survey_responses') }}"
      - name: count_personal_link_survey_responses
        description: "{{ doc('count_personal_link_survey_responses') }}"
      - name: count_personal_link_completed_survey_responses
        description: "{{ doc('count_personal_link_completed_survey_responses') }}"
      - name: count_qr_code_survey_responses
        description: "{{ doc('count_qr_code_survey_responses') }}"
      - name: count_qr_code_completed_survey_responses
        description: "{{ doc('count_qr_code_completed_survey_responses') }}"
      - name: count_email_survey_responses
        description: "{{ doc('count_email_survey_responses') }}"
      - name: count_email_completed_survey_responses
        description: "{{ doc('count_email_completed_survey_responses') }}"
      - name: count_sms_survey_responses
        description: "{{ doc('count_sms_survey_responses') }}"
      - name: count_sms_completed_survey_responses
        description: "{{ doc('count_sms_completed_survey_responses') }}"
      - name: count_uncategorized_survey_responses
        description: "{{ doc('count_uncategorized_survey_responses') }}"
      - name: count_uncategorized_completed_survey_responses
        description: "{{ doc('count_uncategorized_completed_survey_responses') }}"

  - name: qualtrics__response
    description: >
      Breakdown of responses to individual questions (and their sub-questions). Enhanced with information regarding the survey-level 
      response and the survey.
    tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - source_relation
            - survey_response_id
            - question_response_id
            - sub_question_key
    columns:
      - name: question_response_id
        description: "{{ doc('question_response_fivetran_id') }}"
      - name: survey_response_id
        description: "{{ doc('response_id') }}"
      - name: survey_id
        description: "{{ doc('survey_id') }}"
      - name: question_id
        description: "{{ doc('question_id') }}"
      - name: question_text
        description: "{{ doc('question_text') }}"
      - name: sub_question_key
        description: "{{ doc('sub_question_key') }}"
      - name: sub_question_text
        description: "{{ doc('sub_question_text') }}"
      - name: question_option_key
        description: "{{ doc('question_option_key') }}"
      - name: value
        description: "{{ doc('response_value') }}"
      - name: loop_id
        description: "{{ doc('loop_id') }}"
      - name: distribution_channel
        description: "{{ doc('distribution_channel') }}"
      - name: survey_response_status
        description: "{{ doc('status') }}"
      - name: survey_progress
        description: "{{ doc('progress') }}"
      - name: duration_in_seconds
        description: "{{ doc('duration_in_seconds') }}"
      - name: is_finished_with_survey
        description: "{{ doc('finished') }}"
      - name: survey_finished_at
        description: "{{ doc('end_date') }}"
      - name: survey_response_last_modified_at
        description: "{{ doc('last_modified_date') }}"
      - name: survey_response_recorded_at
        description: "{{ doc('recorded_date') }}"
      - name: survey_response_started_at
        description: "{{ doc('start_date') }}"
      - name: recipient_email
        description: "{{ doc('recipient_email') }}"
      - name: recipient_first_name
        description: "{{ doc('recipient_first_name') }}"
      - name: recipient_last_name
        description: "{{ doc('recipient_last_name') }}"
      - name: user_language
        description: "{{ doc('user_language') }}"
      - name: ip_address
        description: "{{ doc('ip_address') }}"
      - name: location_latitude
        description: "{{ doc('location_latitude') }}"
      - name: location_longitude
        description: "{{ doc('location_longitude') }}"
      - name: source_relation
        description: "{{ doc('source_relation') }}"
      - name: recode_value
        description: "{{ doc('recode_value') }}"
      - name: question_option_text
        description: "{{ doc('question_option_text') }}"
      - name: first_name
        description: "{{ doc('contact_first_name') }}"
      - name: last_name
        description: "{{ doc('contact_last_name') }}"
      - name: email_domain
        description: "{{ doc('email_domain') }}"
      - name: contact_language
        description: "{{ doc('language') }}"
      - name: contact_external_data_reference
        description: "{{ doc('ext_ref') }}"
      - name: embedded_data
        description: "{{ doc('embedded_data') }}"
      - name: survey_name
        description: "{{ doc('survey_name') }}"
      - name: survey_status
        description: "{{ doc('survey_status') }}"
      - name: project_category
        description: "{{ doc('project_category') }}"
      - name: project_type
        description: "{{ doc('project_type') }}"
      - name: brand_base_url
        description: "{{ doc('brand_base_url') }}"
      - name: question_description
        description: "{{ doc('question_description') }}"
      - name: question_type
        description: "{{ doc('question_type') }}"
      - name: block_id
        description: "{{ doc('block_id') }}"
      - name: block_description
        description: "{{ doc('block_description') }}"
      - name: block_question_randomization
        description: "{{ doc('randomize_questions') }}"
      - name: block_type
        description: "{{ doc('block_type') }}"
      - name: block_visibility
        description: "{{ doc('block_visibility') }}"
      - name: validation_setting_force_response
        description: "{{ doc('validation_setting_force_response') }}"
      - name: validation_setting_force_response_type
        description: "{{ doc('validation_setting_force_response_type') }}"
      - name: validation_setting_type
        description: "{{ doc('validation_setting_type') }}"
      - name: is_question_deleted
        description: "{{ doc('_fivetran_deleted') }}"
      - name: is_block_deleted
        description: "{{ doc('_fivetran_deleted') }}"
      - name: is_xm_directory_contact
        description: "{{ doc('is_xm_directory_contact') }}"
      - name: is_research_core_contact
        description: "{{ doc('is_research_core_contact') }}"