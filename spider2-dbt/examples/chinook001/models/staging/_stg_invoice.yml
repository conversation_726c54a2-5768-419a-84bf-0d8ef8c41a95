version: 2

models:
  - name: stg_invoice
    description: "Contains list of invoice lines"
    columns:
      - name: invoice_id
        description: "Invoice identifier key"
        tests:
          - not_null
          - unique
      - name: customer_id
        description: "Customer identifier key"
        tests:
          - not_null
      - name: invoice_date
        description: "Date of the invoice"
        tests:
          - not_null
      - name: invoice_billing_address
        description: "Address of the billing"
        tests:
          - not_null
      - name: invoice_billing_city
        description: "City of the billing address"
        tests:
          - not_null
      - name: invoice_billing_state
        description: "State of the billing address"
      - name: invoice_billing_country
        description: "Country of the billing address"
        tests:
          - not_null
      - name: invoice_billing_postal_code
        description: "Postal code of the billing address"
      - name: invoice_total
        description: "Total amount of the billing"
        tests:
          - not_null