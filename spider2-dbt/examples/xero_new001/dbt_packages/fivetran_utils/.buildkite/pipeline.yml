steps:
## Ads Packages 1
  - label: ":snowflake: Run Tests - Ads Packages :bookmark: :one:"
    key: "run_dbt_snowflake_ads_1"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_SNOWFLAKE_DBT_ACCOUNT"
            - "CI_SNOWFLAKE_DATABASE_FT_UTILS"
            - "CI_SNOWFLAKE_DBT_PASS"
            - "CI_SNOWFLAKE_DBT_ROLE_FT_UTILS_TEST"
            - "CI_SNOWFLAKE_DBT_USER"
            - "CI_SNOWFLAKE_DBT_WAREHOUSE"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh snowflake linkedin apple_search_ads snapchat_ads facebook_ads

  - label: ":postgres: Run Tests - Ads Packages :bookmark: :one:"
    key: "run-dbt-postgres-ads_1"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_POSTGRES_DBT_DBNAME"
            - "CI_POSTGRES_DBT_HOST"
            - "CI_POSTGRES_DBT_PASS"
            - "CI_POSTGRES_DBT_USER"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh postgres linkedin apple_search_ads snapchat_ads facebook_ads

  - label: ":gcloud: Run Tests - Ads Packages :bookmark: :one:"
    key: "run_dbt_bigquery_ads_1"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "GCLOUD_SERVICE_KEY"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh bigquery linkedin apple_search_ads snapchat_ads facebook_ads

  - label: ":amazon-redshift: Run Tests - Ads Packages :bookmark: :one:"
    key: "run_dbt_redshift_ads_1"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_REDSHIFT_DBT_DBNAME"
            - "CI_REDSHIFT_DBT_HOST"
            - "CI_REDSHIFT_DBT_PASS"
            - "CI_REDSHIFT_DBT_USER"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh redshift linkedin apple_search_ads snapchat_ads facebook_ads

  - label: ":databricks: Run Tests - Ads Packages :bookmark: :one:"
    key: "run_dbt_databricks_ads_1"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_DATABRICKS_DBT_HOST"
            - "CI_DATABRICKS_DBT_HTTP_PATH"
            - "CI_DATABRICKS_DBT_TOKEN"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh databricks linkedin apple_search_ads snapchat_ads facebook_ads 

## Ads Packages 2
  - label: ":snowflake: Run Tests - Ads Packages :bookmark: :two:"
    key: "run_dbt_snowflake_ads_2"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_SNOWFLAKE_DBT_ACCOUNT"
            - "CI_SNOWFLAKE_DATABASE_FT_UTILS"
            - "CI_SNOWFLAKE_DBT_PASS"
            - "CI_SNOWFLAKE_DBT_ROLE_FT_UTILS_TEST"
            - "CI_SNOWFLAKE_DBT_USER"
            - "CI_SNOWFLAKE_DBT_WAREHOUSE"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh snowflake tiktok_ads twitter_ads amazon_ads reddit_ads

  - label: ":postgres: Run Tests - Ads Packages :bookmark: :two:"
    key: "run-dbt-postgres-ads_2"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_POSTGRES_DBT_DBNAME"
            - "CI_POSTGRES_DBT_HOST"
            - "CI_POSTGRES_DBT_PASS"
            - "CI_POSTGRES_DBT_USER"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh postgres tiktok_ads twitter_ads amazon_ads reddit_ads

  - label: ":gcloud: Run Tests - Ads Packages :bookmark: :two:"
    key: "run_dbt_bigquery_ads_2"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "GCLOUD_SERVICE_KEY"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh bigquery tiktok_ads twitter_ads amazon_ads reddit_ads

  - label: ":amazon-redshift: Run Tests - Ads Packages :bookmark: :two:"
    key: "run_dbt_redshift_ads_2"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_REDSHIFT_DBT_DBNAME"
            - "CI_REDSHIFT_DBT_HOST"
            - "CI_REDSHIFT_DBT_PASS"
            - "CI_REDSHIFT_DBT_USER"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh redshift tiktok_ads twitter_ads amazon_ads reddit_ads

  - label: ":databricks: Run Tests - Ads Packages :bookmark: :two:"
    key: "run_dbt_databricks_ads_2"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_DATABRICKS_DBT_HOST"
            - "CI_DATABRICKS_DBT_HTTP_PATH"
            - "CI_DATABRICKS_DBT_TOKEN"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh databricks tiktok_ads twitter_ads amazon_ads reddit_ads
    

## Ads Packages 3
  - label: ":snowflake: Run Tests - Ads Packages :bookmark: :three:"
    key: "run_dbt_snowflake_ads_3"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_SNOWFLAKE_DBT_ACCOUNT"
            - "CI_SNOWFLAKE_DATABASE_FT_UTILS"
            - "CI_SNOWFLAKE_DBT_PASS"
            - "CI_SNOWFLAKE_DBT_ROLE_FT_UTILS_TEST"
            - "CI_SNOWFLAKE_DBT_USER"
            - "CI_SNOWFLAKE_DBT_WAREHOUSE"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh snowflake google_ads microsoft_ads pinterest

  - label: ":postgres: Run Tests - Ads Packages :bookmark: :three:"
    key: "run-dbt-postgres-ads_3"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_POSTGRES_DBT_DBNAME"
            - "CI_POSTGRES_DBT_HOST"
            - "CI_POSTGRES_DBT_PASS"
            - "CI_POSTGRES_DBT_USER"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh postgres google_ads microsoft_ads pinterest

  - label: ":gcloud: Run Tests - Ads Packages :bookmark: :three:"
    key: "run_dbt_bigquery_ads_3"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "GCLOUD_SERVICE_KEY"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh bigquery google_ads microsoft_ads pinterest

  - label: ":amazon-redshift: Run Tests - Ads Packages :bookmark: :three:"
    key: "run_dbt_redshift_ads_3"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_REDSHIFT_DBT_DBNAME"
            - "CI_REDSHIFT_DBT_HOST"
            - "CI_REDSHIFT_DBT_PASS"
            - "CI_REDSHIFT_DBT_USER"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh redshift google_ads microsoft_ads pinterest

  - label: ":databricks: Run Tests - Ads Packages :bookmark: :three:"
    key: "run_dbt_databricks_ads_3"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_DATABRICKS_DBT_HOST"
            - "CI_DATABRICKS_DBT_HTTP_PATH"
            - "CI_DATABRICKS_DBT_TOKEN"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh databricks google_ads microsoft_ads pinterest
    

## Social Packages
  - label: ":snowflake: Run Tests - Social Packages :iphone:"
    key: "run_dbt_snowflake_social"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_SNOWFLAKE_DBT_ACCOUNT"
            - "CI_SNOWFLAKE_DATABASE_FT_UTILS"
            - "CI_SNOWFLAKE_DBT_PASS"
            - "CI_SNOWFLAKE_DBT_ROLE_FT_UTILS_TEST"
            - "CI_SNOWFLAKE_DBT_USER"
            - "CI_SNOWFLAKE_DBT_WAREHOUSE"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh snowflake linkedin_pages facebook_pages instagram_business twitter_organic youtube_analytics

  - label: ":postgres: Run Tests - Social Packages :iphone:"
    key: "run-dbt-postgres-social"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_POSTGRES_DBT_DBNAME"
            - "CI_POSTGRES_DBT_HOST"
            - "CI_POSTGRES_DBT_PASS"
            - "CI_POSTGRES_DBT_USER"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh postgres linkedin_pages facebook_pages instagram_business twitter_organic youtube_analytics

  - label: ":gcloud: Run Tests - Social Packages :iphone:"
    key: "run_dbt_bigquery_social"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "GCLOUD_SERVICE_KEY"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh bigquery linkedin_pages facebook_pages instagram_business twitter_organic youtube_analytics

  - label: ":amazon-redshift: Run Tests - Social Packages :iphone:"
    key: "run_dbt_redshift_social"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_REDSHIFT_DBT_DBNAME"
            - "CI_REDSHIFT_DBT_HOST"
            - "CI_REDSHIFT_DBT_PASS"
            - "CI_REDSHIFT_DBT_USER"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh redshift linkedin_pages facebook_pages instagram_business twitter_organic youtube_analytics

  - label: ":databricks: Run Tests - Social Packages :iphone:"
    key: "run_dbt_databricks_social"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_DATABRICKS_DBT_HOST"
            - "CI_DATABRICKS_DBT_HTTP_PATH"
            - "CI_DATABRICKS_DBT_TOKEN"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh databricks linkedin_pages facebook_pages instagram_business twitter_organic youtube_analytics
    

## App Packages
  - label: ":snowflake: Run Tests - App Packages :video_game:"
    key: "run_dbt_snowflake_app"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_SNOWFLAKE_DBT_ACCOUNT"
            - "CI_SNOWFLAKE_DATABASE_FT_UTILS"
            - "CI_SNOWFLAKE_DBT_PASS"
            - "CI_SNOWFLAKE_DBT_ROLE_FT_UTILS_TEST"
            - "CI_SNOWFLAKE_DBT_USER"
            - "CI_SNOWFLAKE_DBT_WAREHOUSE"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh snowflake google_play apple_store

  - label: ":postgres: Run Tests - App Packages :video_game:"
    key: "run-dbt-postgres-app"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_POSTGRES_DBT_DBNAME"
            - "CI_POSTGRES_DBT_HOST"
            - "CI_POSTGRES_DBT_PASS"
            - "CI_POSTGRES_DBT_USER"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh postgres google_play apple_store

  - label: ":gcloud: Run Tests - App Packages :video_game:"
    key: "run_dbt_bigquery_app"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "GCLOUD_SERVICE_KEY"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh bigquery google_play apple_store

  - label: ":amazon-redshift: Run Tests - App Packages :video_game:"
    key: "run_dbt_redshift_app"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_REDSHIFT_DBT_DBNAME"
            - "CI_REDSHIFT_DBT_HOST"
            - "CI_REDSHIFT_DBT_PASS"
            - "CI_REDSHIFT_DBT_USER"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh redshift google_play apple_store

  - label: ":databricks: Run Tests - App Packages :video_game:"
    key: "run_dbt_databricks_app"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_DATABRICKS_DBT_HOST"
            - "CI_DATABRICKS_DBT_HTTP_PATH"
            - "CI_DATABRICKS_DBT_TOKEN"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh databricks google_play apple_store
    

## CRM Packages
  - label: ":snowflake: Run Tests - CRM Packages :salesforce::hubspot:"
    key: "run_dbt_snowflake_crm"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_SNOWFLAKE_DBT_ACCOUNT"
            - "CI_SNOWFLAKE_DATABASE_FT_UTILS"
            - "CI_SNOWFLAKE_DBT_PASS"
            - "CI_SNOWFLAKE_DBT_ROLE_FT_UTILS_TEST"
            - "CI_SNOWFLAKE_DBT_USER"
            - "CI_SNOWFLAKE_DBT_WAREHOUSE"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh snowflake salesforce hubspot

  - label: ":postgres: Run Tests - CRM Packages :salesforce::hubspot:"
    key: "run-dbt-postgres-crm"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_POSTGRES_DBT_DBNAME"
            - "CI_POSTGRES_DBT_HOST"
            - "CI_POSTGRES_DBT_PASS"
            - "CI_POSTGRES_DBT_USER"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh postgres salesforce hubspot

  - label: ":gcloud: Run Tests - CRM Packages :salesforce::hubspot:"
    key: "run_dbt_bigquery_crm"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "GCLOUD_SERVICE_KEY"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh bigquery salesforce hubspot

  - label: ":amazon-redshift: Run Tests - CRM Packages :salesforce::hubspot:"
    key: "run_dbt_redshift_crm"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_REDSHIFT_DBT_DBNAME"
            - "CI_REDSHIFT_DBT_HOST"
            - "CI_REDSHIFT_DBT_PASS"
            - "CI_REDSHIFT_DBT_USER"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh redshift salesforce hubspot

  - label: ":databricks: Run Tests - CRM Packages :salesforce::hubspot:"
    key: "run_dbt_databricks_crm"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_DATABRICKS_DBT_HOST"
            - "CI_DATABRICKS_DBT_HTTP_PATH"
            - "CI_DATABRICKS_DBT_TOKEN"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh databricks salesforce hubspot
    

## Ecommerce and Subscription Packages
  - label: ":snowflake: Run Tests - Ecommerce and Subscription Packages :shopping_bags:"
    key: "run_dbt_snowflake_esub"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_SNOWFLAKE_DBT_ACCOUNT"
            - "CI_SNOWFLAKE_DATABASE_FT_UTILS"
            - "CI_SNOWFLAKE_DBT_PASS"
            - "CI_SNOWFLAKE_DBT_ROLE_FT_UTILS_TEST"
            - "CI_SNOWFLAKE_DBT_USER"
            - "CI_SNOWFLAKE_DBT_WAREHOUSE"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh snowflake shopify recurly stripe zuora

  - label: ":postgres: Run Tests - Ecommerce and Subscription Packages :shopping_bags:"
    key: "run-dbt-postgres-esub"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_POSTGRES_DBT_DBNAME"
            - "CI_POSTGRES_DBT_HOST"
            - "CI_POSTGRES_DBT_PASS"
            - "CI_POSTGRES_DBT_USER"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh postgres shopify recurly stripe zuora

  - label: ":gcloud: Run Tests - Ecommerce and Subscription Packages :shopping_bags:"
    key: "run_dbt_bigquery_esub"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "GCLOUD_SERVICE_KEY"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh bigquery shopify recurly stripe zuora

  - label: ":amazon-redshift: Run Tests - Ecommerce and Subscription Packages :shopping_bags:"
    key: "run_dbt_redshift_esub"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_REDSHIFT_DBT_DBNAME"
            - "CI_REDSHIFT_DBT_HOST"
            - "CI_REDSHIFT_DBT_PASS"
            - "CI_REDSHIFT_DBT_USER"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh redshift shopify recurly stripe zuora

  - label: ":databricks: Run Tests - Ecommerce and Subscription Packages :shopping_bags:"
    key: "run_dbt_databricks_esub"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_DATABRICKS_DBT_HOST"
            - "CI_DATABRICKS_DBT_HTTP_PATH"
            - "CI_DATABRICKS_DBT_TOKEN"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh databricks shopify recurly stripe zuora
    

## Email Marketing Packages
  - label: ":snowflake: Run Tests - Email Marketing Packages :email:"
    key: "run_dbt_snowflake_email"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_SNOWFLAKE_DBT_ACCOUNT"
            - "CI_SNOWFLAKE_DATABASE_FT_UTILS"
            - "CI_SNOWFLAKE_DBT_PASS"
            - "CI_SNOWFLAKE_DBT_ROLE_FT_UTILS_TEST"
            - "CI_SNOWFLAKE_DBT_USER"
            - "CI_SNOWFLAKE_DBT_WAREHOUSE"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh snowflake marketo iterable klaviyo mailchimp pardot

  - label: ":postgres: Run Tests - Email Marketing Packages :email:"
    key: "run-dbt-postgres-email"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_POSTGRES_DBT_DBNAME"
            - "CI_POSTGRES_DBT_HOST"
            - "CI_POSTGRES_DBT_PASS"
            - "CI_POSTGRES_DBT_USER"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh postgres marketo iterable klaviyo mailchimp pardot

  - label: ":gcloud: Run Tests - Email Marketing Packages :email:"
    key: "run_dbt_bigquery_email"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "GCLOUD_SERVICE_KEY"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh bigquery marketo iterable klaviyo mailchimp pardot

  - label: ":amazon-redshift: Run Tests - Email Marketing Packages :email:"
    key: "run_dbt_redshift_email"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_REDSHIFT_DBT_DBNAME"
            - "CI_REDSHIFT_DBT_HOST"
            - "CI_REDSHIFT_DBT_PASS"
            - "CI_REDSHIFT_DBT_USER"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh redshift marketo iterable klaviyo mailchimp pardot

  - label: ":databricks: Run Tests - Email Marketing Packages :email:"
    key: "run_dbt_databricks_email"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_DATABRICKS_DBT_HOST"
            - "CI_DATABRICKS_DBT_HTTP_PATH"
            - "CI_DATABRICKS_DBT_TOKEN"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh databricks marketo klaviyo mailchimp pardot
    # Iterable removed as it is not databricks compatible for now.
    

## Finance Packages
  - label: ":snowflake: Run Tests - Finance Packages :money_with_wings:"
    key: "run_dbt_snowflake_finance"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_SNOWFLAKE_DBT_ACCOUNT"
            - "CI_SNOWFLAKE_DATABASE_FT_UTILS"
            - "CI_SNOWFLAKE_DBT_PASS"
            - "CI_SNOWFLAKE_DBT_ROLE_FT_UTILS_TEST"
            - "CI_SNOWFLAKE_DBT_USER"
            - "CI_SNOWFLAKE_DBT_WAREHOUSE"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh snowflake quickbooks xero sage_intacct netsuite

  - label: ":postgres: Run Tests - Finance Packages :money_with_wings:"
    key: "run-dbt-postgres-finance"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_POSTGRES_DBT_DBNAME"
            - "CI_POSTGRES_DBT_HOST"
            - "CI_POSTGRES_DBT_PASS"
            - "CI_POSTGRES_DBT_USER"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh postgres quickbooks xero sage_intacct netsuite

  - label: ":gcloud: Run Tests - Finance Packages :money_with_wings:"
    key: "run_dbt_bigquery_finance"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "GCLOUD_SERVICE_KEY"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh bigquery quickbooks xero sage_intacct netsuite

  - label: ":amazon-redshift: Run Tests - Finance Packages :money_with_wings:"
    key: "run_dbt_redshift_finance"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_REDSHIFT_DBT_DBNAME"
            - "CI_REDSHIFT_DBT_HOST"
            - "CI_REDSHIFT_DBT_PASS"
            - "CI_REDSHIFT_DBT_USER"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh redshift quickbooks xero sage_intacct netsuite

  - label: ":databricks: Run Tests - Finance Packages :money_with_wings:"
    key: "run_dbt_databricks_finance"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_DATABRICKS_DBT_HOST"
            - "CI_DATABRICKS_DBT_HTTP_PATH"
            - "CI_DATABRICKS_DBT_TOKEN"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh databricks xero sage_intacct netsuite
    # Quickbooks removed as it is not databricks compatible for now
    

## Log and People Packages
  - label: ":snowflake: Run Tests - Log and People Packages :people_hugging:"
    key: "run_dbt_snowflake_log_people"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_SNOWFLAKE_DBT_ACCOUNT"
            - "CI_SNOWFLAKE_DATABASE_FT_UTILS"
            - "CI_SNOWFLAKE_DBT_PASS"
            - "CI_SNOWFLAKE_DBT_ROLE_FT_UTILS_TEST"
            - "CI_SNOWFLAKE_DBT_USER"
            - "CI_SNOWFLAKE_DBT_WAREHOUSE"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh snowflake fivetran_log greenhouse lever

  - label: ":postgres: Run Tests - Log and People Packages :people_hugging:"
    key: "run-dbt-postgres-log_people"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_POSTGRES_DBT_DBNAME"
            - "CI_POSTGRES_DBT_HOST"
            - "CI_POSTGRES_DBT_PASS"
            - "CI_POSTGRES_DBT_USER"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh postgres fivetran_log greenhouse lever

  - label: ":gcloud: Run Tests - Log and People Packages :people_hugging:"
    key: "run_dbt_bigquery_log_people"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "GCLOUD_SERVICE_KEY"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh bigquery fivetran_log greenhouse lever

  - label: ":amazon-redshift: Run Tests - Log and People Packages :people_hugging:"
    key: "run_dbt_redshift_log_people"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_REDSHIFT_DBT_DBNAME"
            - "CI_REDSHIFT_DBT_HOST"
            - "CI_REDSHIFT_DBT_PASS"
            - "CI_REDSHIFT_DBT_USER"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh redshift fivetran_log greenhouse lever

  - label: ":databricks: Run Tests - Log and People Packages :people_hugging:"
    key: "run_dbt_databricks_log_people"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_DATABRICKS_DBT_HOST"
            - "CI_DATABRICKS_DBT_HTTP_PATH"
            - "CI_DATABRICKS_DBT_TOKEN"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh databricks fivetran_log
    ## Removed greenhouse and lever as it is not databricks compatible at the moment.
    

## Product Packages
  - label: ":snowflake: Run Tests - Product Packages :unicorn_face:"
    key: "run_dbt_snowflake_product"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_SNOWFLAKE_DBT_ACCOUNT"
            - "CI_SNOWFLAKE_DATABASE_FT_UTILS"
            - "CI_SNOWFLAKE_DBT_PASS"
            - "CI_SNOWFLAKE_DBT_ROLE_FT_UTILS_TEST"
            - "CI_SNOWFLAKE_DBT_USER"
            - "CI_SNOWFLAKE_DBT_WAREHOUSE"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh snowflake amplitude mixpanel pendo

  - label: ":postgres: Run Tests - Product Packages :unicorn_face:"
    key: "run-dbt-postgres-product"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_POSTGRES_DBT_DBNAME"
            - "CI_POSTGRES_DBT_HOST"
            - "CI_POSTGRES_DBT_PASS"
            - "CI_POSTGRES_DBT_USER"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh postgres amplitude mixpanel pendo

  - label: ":gcloud: Run Tests - Product Packages :unicorn_face:"
    key: "run_dbt_bigquery_product"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "GCLOUD_SERVICE_KEY"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh bigquery amplitude mixpanel pendo

  - label: ":amazon-redshift: Run Tests - Product Packages :unicorn_face:"
    key: "run_dbt_redshift_product"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_REDSHIFT_DBT_DBNAME"
            - "CI_REDSHIFT_DBT_HOST"
            - "CI_REDSHIFT_DBT_PASS"
            - "CI_REDSHIFT_DBT_USER"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh redshift amplitude mixpanel pendo

  - label: ":databricks: Run Tests - Product Packages :unicorn_face:"
    key: "run_dbt_databricks_product"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_DATABRICKS_DBT_HOST"
            - "CI_DATABRICKS_DBT_HTTP_PATH"
            - "CI_DATABRICKS_DBT_TOKEN"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh databricks amplitude mixpanel pendo
    

## Velocity Packages
  - label: ":snowflake: Run Tests - Velocity Packages :roller_coaster:"
    key: "run_dbt_snowflake_velocity"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_SNOWFLAKE_DBT_ACCOUNT"
            - "CI_SNOWFLAKE_DATABASE_FT_UTILS"
            - "CI_SNOWFLAKE_DBT_PASS"
            - "CI_SNOWFLAKE_DBT_ROLE_FT_UTILS_TEST"
            - "CI_SNOWFLAKE_DBT_USER"
            - "CI_SNOWFLAKE_DBT_WAREHOUSE"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh snowflake intercom github zendesk asana jira

  - label: ":postgres: Run Tests - Velocity Packages :roller_coaster:"
    key: "run-dbt-postgres-velocity"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_POSTGRES_DBT_DBNAME"
            - "CI_POSTGRES_DBT_HOST"
            - "CI_POSTGRES_DBT_PASS"
            - "CI_POSTGRES_DBT_USER"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh postgres intercom github zendesk asana jira

  - label: ":gcloud: Run Tests - Velocity Packages :roller_coaster:"
    key: "run_dbt_bigquery_velocity"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "GCLOUD_SERVICE_KEY"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh bigquery intercom github zendesk asana jira

  - label: ":amazon-redshift: Run Tests - Velocity Packages :roller_coaster:"
    key: "run_dbt_redshift_velocity"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_REDSHIFT_DBT_DBNAME"
            - "CI_REDSHIFT_DBT_HOST"
            - "CI_REDSHIFT_DBT_PASS"
            - "CI_REDSHIFT_DBT_USER"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh redshift intercom github zendesk asana jira

  - label: ":databricks: Run Tests - Velocity Packages :roller_coaster:"
    key: "run_dbt_databricks_velocity"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_DATABRICKS_DBT_HOST"
            - "CI_DATABRICKS_DBT_HTTP_PATH"
            - "CI_DATABRICKS_DBT_TOKEN"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh databricks github zendesk jira
    ## Intercome and Asana are removed since they are not compatible with databricks at the moment.
    

## Ad Reporting Roll Up Packages
  - label: ":snowflake: Run Tests - Ad Reporting Roll Up Package :roller_skate:"
    key: "run_dbt_snowflake_ad_roll"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_SNOWFLAKE_DBT_ACCOUNT"
            - "CI_SNOWFLAKE_DATABASE_FT_UTILS"
            - "CI_SNOWFLAKE_DBT_PASS"
            - "CI_SNOWFLAKE_DBT_ROLE_FT_UTILS_TEST"
            - "CI_SNOWFLAKE_DBT_USER"
            - "CI_SNOWFLAKE_DBT_WAREHOUSE"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh snowflake ad_reporting

  - label: ":postgres: Run Tests - Ad Reporting Roll Up Package :roller_skate:"
    key: "run-dbt-postgres-ad_roll"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_POSTGRES_DBT_DBNAME"
            - "CI_POSTGRES_DBT_HOST"
            - "CI_POSTGRES_DBT_PASS"
            - "CI_POSTGRES_DBT_USER"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh postgres ad_reporting

  - label: ":gcloud: Run Tests - Ad Reporting Roll Up Package :roller_skate:"
    key: "run_dbt_bigquery_ad_roll"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "GCLOUD_SERVICE_KEY"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh bigquery ad_reporting

  - label: ":amazon-redshift: Run Tests - Ad Reporting Roll Up Package :roller_skate:"
    key: "run_dbt_redshift_ad_roll"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_REDSHIFT_DBT_DBNAME"
            - "CI_REDSHIFT_DBT_HOST"
            - "CI_REDSHIFT_DBT_PASS"
            - "CI_REDSHIFT_DBT_USER"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh redshift ad_reporting

  - label: ":databricks: Run Tests - Ad Reporting Roll Up Package :roller_skate:"
    key: "run_dbt_databricks_ad_roll"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_DATABRICKS_DBT_HOST"
            - "CI_DATABRICKS_DBT_HTTP_PATH"
            - "CI_DATABRICKS_DBT_TOKEN"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh databricks ad_reporting
    

## App Reporting Roll Up Packages
  - label: ":snowflake: Run Tests - App Reporting Roll Up Package :roller_skate:"
    key: "run_dbt_snowflake_app_roll"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_SNOWFLAKE_DBT_ACCOUNT"
            - "CI_SNOWFLAKE_DATABASE_FT_UTILS"
            - "CI_SNOWFLAKE_DBT_PASS"
            - "CI_SNOWFLAKE_DBT_ROLE_FT_UTILS_TEST"
            - "CI_SNOWFLAKE_DBT_USER"
            - "CI_SNOWFLAKE_DBT_WAREHOUSE"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh snowflake app_reporting

  - label: ":postgres: Run Tests - App Reporting Roll Up Package :roller_skate:"
    key: "run-dbt-postgres-app_roll"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_POSTGRES_DBT_DBNAME"
            - "CI_POSTGRES_DBT_HOST"
            - "CI_POSTGRES_DBT_PASS"
            - "CI_POSTGRES_DBT_USER"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh postgres app_reporting

  - label: ":gcloud: Run Tests - App Reporting Roll Up Package :roller_skate:"
    key: "run_dbt_bigquery_app_roll"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "GCLOUD_SERVICE_KEY"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh bigquery app_reporting

  - label: ":amazon-redshift: Run Tests - App Reporting Roll Up Package :roller_skate:"
    key: "run_dbt_redshift_app_roll"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_REDSHIFT_DBT_DBNAME"
            - "CI_REDSHIFT_DBT_HOST"
            - "CI_REDSHIFT_DBT_PASS"
            - "CI_REDSHIFT_DBT_USER"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh redshift app_reporting

  - label: ":databricks: Run Tests - App Reporting Roll Up Package :roller_skate:"
    key: "run_dbt_databricks_app_roll"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_DATABRICKS_DBT_HOST"
            - "CI_DATABRICKS_DBT_HTTP_PATH"
            - "CI_DATABRICKS_DBT_TOKEN"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh databricks app_reporting
    

## Social Media Reporting Roll Up Packages
  - label: ":snowflake: Run Tests - Social Reporting Roll Up Package :roller_skate:"
    key: "run_dbt_snowflake_social_roll"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_SNOWFLAKE_DBT_ACCOUNT"
            - "CI_SNOWFLAKE_DATABASE_FT_UTILS"
            - "CI_SNOWFLAKE_DBT_PASS"
            - "CI_SNOWFLAKE_DBT_ROLE_FT_UTILS_TEST"
            - "CI_SNOWFLAKE_DBT_USER"
            - "CI_SNOWFLAKE_DBT_WAREHOUSE"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh snowflake social_media_reporting

  - label: ":postgres: Run Tests - Social Reporting Roll Up Package :roller_skate:"
    key: "run-dbt-postgres-social_roll"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_POSTGRES_DBT_DBNAME"
            - "CI_POSTGRES_DBT_HOST"
            - "CI_POSTGRES_DBT_PASS"
            - "CI_POSTGRES_DBT_USER"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh postgres social_media_reporting

  - label: ":gcloud: Run Tests - Social Reporting Roll Up Package :roller_skate:"
    key: "run_dbt_bigquery_social_roll"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "GCLOUD_SERVICE_KEY"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh bigquery social_media_reporting

  - label: ":amazon-redshift: Run Tests - Social Reporting Roll Up Package :roller_skate:"
    key: "run_dbt_redshift_social_roll"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_REDSHIFT_DBT_DBNAME"
            - "CI_REDSHIFT_DBT_HOST"
            - "CI_REDSHIFT_DBT_PASS"
            - "CI_REDSHIFT_DBT_USER"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh redshift social_media_reporting

  - label: ":databricks: Run Tests - Social Reporting Roll Up Package :roller_skate:"
    key: "run_dbt_databricks_social_roll"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_DATABRICKS_DBT_HOST"
            - "CI_DATABRICKS_DBT_HTTP_PATH"
            - "CI_DATABRICKS_DBT_TOKEN"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh databricks social_media_reporting
    

## Shopify Holistic Reporting Roll Up Packages
  - label: ":snowflake: Run Tests - Shopify Holistic Reporting Roll Up Package :roller_skate:"
    key: "run_dbt_snowflake_shopify_holistic_roll"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_SNOWFLAKE_DBT_ACCOUNT"
            - "CI_SNOWFLAKE_DATABASE_FT_UTILS"
            - "CI_SNOWFLAKE_DBT_PASS"
            - "CI_SNOWFLAKE_DBT_ROLE_FT_UTILS_TEST"
            - "CI_SNOWFLAKE_DBT_USER"
            - "CI_SNOWFLAKE_DBT_WAREHOUSE"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh snowflake shopify_holistic_reporting

  - label: ":postgres: Run Tests - Shopify Holistic Reporting Roll Up Package :roller_skate:"
    key: "run-dbt-postgres-shopify_holistic_roll"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_POSTGRES_DBT_DBNAME"
            - "CI_POSTGRES_DBT_HOST"
            - "CI_POSTGRES_DBT_PASS"
            - "CI_POSTGRES_DBT_USER"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh postgres shopify_holistic_reporting

  - label: ":gcloud: Run Tests - Shopify Holistic Reporting Roll Up Package :roller_skate:"
    key: "run_dbt_bigquery_shopify_holistic_roll"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "GCLOUD_SERVICE_KEY"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh bigquery shopify_holistic_reporting

  - label: ":amazon-redshift: Run Tests - Shopify Holistic Reporting Roll Up Package :roller_skate:"
    key: "run_dbt_redshift_shopify_holistic_roll"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_REDSHIFT_DBT_DBNAME"
            - "CI_REDSHIFT_DBT_HOST"
            - "CI_REDSHIFT_DBT_PASS"
            - "CI_REDSHIFT_DBT_USER"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh redshift shopify_holistic_reporting

  - label: ":databricks: Run Tests - Shopify Holistic Reporting Roll Up Package :roller_skate:"
    key: "run_dbt_databricks_shopify_holistic_roll"
    plugins:
      - docker#v3.13.0:
          image: "python:3.8"
          shell: [ "/bin/bash", "-e", "-c" ]
          environment:
            - "BASH_ENV=/tmp/.bashrc"
            - "CI_DATABRICKS_DBT_HOST"
            - "CI_DATABRICKS_DBT_HTTP_PATH"
            - "CI_DATABRICKS_DBT_TOKEN"
    commands: |
      bash .buildkite/scripts/run_standard_models.sh databricks shopify_holistic_reporting
    