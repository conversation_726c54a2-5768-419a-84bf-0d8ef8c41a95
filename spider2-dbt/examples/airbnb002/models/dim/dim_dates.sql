{{
	config(
		materialized="table" ,
		alias="dim_dates" ,
		schema="main" ,
		unique_key="DATE_ID"
	)
}}

SELECT 
    STRFTIME('%Y%m%d', DATUM)::INT AS DATE_ID,
    DATUM AS DATE_ACTUAL,
    EXTRACT(EPOCH FROM DATUM) AS EPOCH,
    STRFTIME('%d', DATUM) || CASE
        WHEN STRFTIME('%d', DATUM) = '01' THEN 'st'
        WHEN STRFTIME('%d', DATUM) = '02' THEN 'nd'
        WHEN STRFTIME('%d', DATUM) = '03' THEN 'rd'
        ELSE 'th'
    END AS DAY_SUFFIX,
    STRFTIME('%A', DATUM) AS DAY_NAME,
    EXTRACT(ISODOW FROM DATUM) AS DAY_OF_WEEK,
    EXTRACT(DAY FROM DATUM) AS DAY_OF_MONTH,
    (DATUM - DATE_TRUNC('quarter', DATUM)::DATE + INTERVAL '1 day') AS DAY_OF_QUARTER,
    EXTRACT(DOY FROM DATUM) AS DAY_OF_YEAR,
    STRFTIME('%W', DATUM)::INT AS WEEK_OF_MONTH,
    EXTRACT(WEEK FROM DATUM) AS WEEK_OF_YEAR,
    EXTRACT(ISOYEAR FROM DATUM) || '-W' || STRFTIME('%V', DATUM) || '-' || EXTRACT(ISODOW FROM DATUM) AS WEEK_OF_YEAR_ISO,
    EXTRACT(MONTH FROM DATUM) AS MONTH_ACTUAL,
    STRFTIME('%B', DATUM) AS MONTH_NAME,
    STRFTIME('%b', DATUM) AS MONTH_NAME_ABBREVIATED,
    EXTRACT(QUARTER FROM DATUM) AS QUARTER_ACTUAL,
    CASE
        WHEN EXTRACT(QUARTER FROM DATUM) = 1 THEN 'First'
        WHEN EXTRACT(QUARTER FROM DATUM) = 2 THEN 'Second'
        WHEN EXTRACT(QUARTER FROM DATUM) = 3 THEN 'Third'
        WHEN EXTRACT(QUARTER FROM DATUM) = 4 THEN 'Fourth'
    END AS QUARTER_NAME,
    EXTRACT(YEAR FROM DATUM) AS year_actual,
    DATUM + INTERVAL '1 day' * (1 - EXTRACT(ISODOW FROM DATUM)) AS FIRST_DAY_OF_WEEK,
    DATUM + INTERVAL '1 day' * (7 - EXTRACT(ISODOW FROM DATUM)) AS LAST_DAY_OF_WEEK,
    DATUM + INTERVAL '1 day' * (1 - EXTRACT(DAY FROM DATUM)) AS FIRST_DAY_OF_MONTH,
    (DATE_TRUNC('MONTH', DATUM) + INTERVAL '1 MONTH' - INTERVAL '1 day')::DATE AS LAST_DAY_OF_MONTH,
    STRFTIME('%Y-01-01', DATUM)::DATE AS FIRST_DAY_OF_YEAR,
    STRFTIME('%Y-12-31', DATUM)::DATE AS LAST_DAY_OF_YEAR,
    STRFTIME('%m%Y', DATUM) AS MMYYYY,
    STRFTIME('%m%d%Y', DATUM) AS MMDDYYYY,
    CASE
        WHEN EXTRACT(ISODOW FROM DATUM) IN (6, 7) THEN TRUE
        ELSE FALSE
    END AS WEEKEND_INDR
FROM (
    SELECT '1970-01-01'::DATE + SEQUENCE.DAY * INTERVAL '1 day' AS DATUM
    FROM GENERATE_SERIES(0, 29219) AS SEQUENCE (DAY)
    GROUP BY SEQUENCE.DAY
) DQ
ORDER BY 1
