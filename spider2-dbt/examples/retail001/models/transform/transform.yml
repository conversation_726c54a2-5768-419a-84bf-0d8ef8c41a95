version: 2

models:
  - name: fct_invoices
    description: "A fact table containing invoice data for retail transactions, enriched with dimension tables for datetime, products, and customers."
    columns:
      - name: invoice_id
        description: "The unique identifier for the invoice."
        tests:
          - not_null
          - unique

      - name: datetime_id
        description: "The identifier linking to the dim_datetime table, representing the date and time of the invoice."
        tests:
          - not_null
          - relationships:
              to: ref('dim_datetime')
              field: datetime_id

      - name: product_id
        description: "A surrogate key generated using the StockCode, Description, and UnitPrice of the product."
        tests:
          - not_null
          - unique
          - relationships:
              to: ref('dim_product')
              field: product_id

      - name: customer_id
        description: "A surrogate key generated using the CustomerID and Country to identify the customer."
        tests:
          - not_null
          - unique
          - relationships:
              to: ref('dim_customer')
              field: customer_id

      - name: quantity
        description: "The quantity of the product ordered in the invoice."
        tests:
          - not_null
          - accepted_range:
              min_value: 0

      - name: total
        description: "The total amount for the product, calculated as Quantity * UnitPrice."
        tests:
          - not_null
          - accepted_range:
              min_value: 0

    refs:
      - name: dim_datetime
      - name: dim_product
      - name: dim_customer
