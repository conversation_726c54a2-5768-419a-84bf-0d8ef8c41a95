version: 2

models:
  - name: xero__general_ledger
    description: 
      This table represents each individual journal line item recorded in the Xero accounting system. It is essential for constructing both the balance sheet and profit and loss statement. Each line item corresponds to an accounting transaction that impacts one or more accounts, and contains detailed information on the transaction, including the related account, amounts, and tax details.
    tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - journal_line_id
            - source_relation
            - journal_id
    columns:
      - name: journal_line_id
        description: 
          The unique identifier for each journal line item in Xero. Each journal entry consists of one or more journal lines, and this ID allows tracking of the individual transaction lines within a journal entry.
      - name: journal_id
        description: 
          The unique identifier for the journal entry. This links multiple journal lines together under a single accounting event. For example, a sales invoice might generate multiple journal lines representing different parts of the transaction (revenue, tax, etc.).
        tests:
          - not_null
      - name: created_date_utc
        description: 
          The UTC date and time when the journal entry was created in Xero. This can be used to track when the transaction was initially recorded.
      - name: journal_date
        description: 
          The date on which the journal entry was posted. This is the effective date of the accounting transaction and may differ from the creation date.
      - name: journal_number
        description: 
          The unique journal number assigned by Xero. This number can be used for internal reference or auditing purposes.
      - name: reference
        description: 
          Any associated reference for the journal entry, such as a purchase order number or invoice number, which may be used to trace the origin of the transaction.
      - name: source_id
        description: 
          The unique identifier for the source transaction that generated this journal line. This field is used to link the journal entry to its origin, such as an invoice or a payment, allowing for more detailed investigation or reconciliation.
      - name: source_type
        description: 
          The type of source transaction that generated the journal entry. This determines which table the journal can be linked to for additional details. Common values include invoices, bank transactions, payments, etc.
      - name: account_code
        description: 
          The alphanumeric code representing the account impacted by this journal line. This code is defined by the customer and is often used in reporting or categorization, such as "200" or "SALES".
      - name: account_id
        description: 
          The Xero identifier for the account associated with this journal line. This is the internal ID used to join to other tables that contain account details.
      - name: account_name
        description: 
          The human-readable name of the account related to this journal line. This can provide additional clarity in reports or analysis, such as "Revenue" or "Accounts Receivable".
      - name: account_type
        description: 
          The classification of the account associated with this journal line, such as "Asset", "Liability", "Revenue", or "Expense". This field is critical for constructing financial statements.
      - name: description
        description: 
          A description or narrative explaining the journal line. This is often derived from the source transaction and may provide additional context for the entry.
      - name: gross_amount
        description: 
          The total gross amount for the journal line, calculated as the sum of the net amount and the tax amount. This represents the full value of the transaction, including taxes.
      - name: net_amount
        description: 
          The net amount for the journal line, excluding taxes. In accounting, this is typically the actual transaction value, which is positive for debits and negative for credits.
      - name: tax_amount
        description: 
          The total tax applied to the journal line. This can be used to calculate the tax liability or recoverable tax for the transaction.
      - name: tax_name
        description: 
          The name of the tax rate applied to the journal line, such as "GST" or "VAT". This can be used to differentiate between different tax rates in reports.
      - name: tax_type
        description: 
          The type or category of tax applied to this journal line. This might be specific to the local tax jurisdiction or the type of transaction.
      - name: account_class
        description: 
          A broader classification of the account, such as "Asset", "Equity", "Expense", "Liability", or "Revenue". This field is used to group accounts into higher-level categories in financial reporting.
      - name: invoice_id
        description: 
          The unique identifier for the invoice, where applicable. This allows joining the journal line to the invoice table to access details about the related invoice transaction.
      - name: bank_transaction_id
        description: 
          The unique identifier for the bank transaction, where applicable. This can be used to join the journal line to the bank transaction table for further details about the cash movement.
      - name: bank_transfer_id
        description: 
          The unique identifier for a bank transfer, if this journal line was generated from a transfer of funds between bank accounts.
      - name: manual_journal_id
        description: 
          The unique identifier for manual journal entries, used for adjusting entries made directly in the general ledger without an associated source transaction.
      - name: payment_id
        description: 
          The unique identifier for a payment transaction, if this journal line was generated from a payment event. This can be linked to the payment table for more details.
      - name: credit_note_id
        description: 
          The unique identifier for a credit note, where applicable. This field allows the journal line to be joined to the credit note table to trace any credit adjustments.
      - name: contact_id
        description: 
          The unique identifier for the associated contact, such as a customer or vendor, related to the journal entry. This field is populated when the journal line is linked to a contact.
      - name: contact_name
        description: 
          The name of the contact, such as the customer or vendor, associated with the journal entry. This field provides context for identifying the external party involved in the transaction.
      - name: source_relation
        description: >
          The source where this data was pulled from. If using the `union_schemas` variable, this field will contain the source schema. If using the `union_databases` variable, it will contain the source database. If there are no multiple sources combined, this will be an empty string.


  - name: xero__balance_sheet_report
    description: 
      This model represents the state of the balance sheet for each account at the end of each month. The data is sourced from `xero__general_ledger`, and net balances are calculated for accounts classified as assets, liabilities, and equity. The report provides a monthly snapshot of the organization's financial position.
    tests:
      - dbt_utils.unique_combination_of_columns:
          combination_of_columns:
            - source_relation
            - date_month
            - account_name
    columns:
      - name: date_month
        description: 
          The month being reported. This field captures the financial status of the balance sheet as of the last day of the given month.
      - name: account_name
        description: 
          The name of the related account in Xero. If the account is classified as an asset, equity, or liability, it reflects the specific account name (e.g., Cash, Accounts Receivable). For earnings accounts, it is categorized based on the fiscal year:Retained Earnings for previous years or Current Year Earnings for the current fiscal year, depending on the journal entry date relative to the organization s year-end date.
      - name: account_code
        description: 
          The alphanumeric code assigned to the account in Xero, which can be used for categorizing and reporting.
      - name: account_class
        description: 
          The classification of the account in Xero, such as "Asset", "Liability", or "Equity". This helps group accounts into major sections of the balance sheet.
      - name: account_id
        description: 
          The unique identifier for the account in Xero. This ID can be used to join to other tables or systems for further details about the account.
      - name: account_type
        description: 
          The type of account in Xero. This field provides more granular categorization within the broader "account_class" field (e.g., within "Asset", there may be types like "Current Asset" or "Fixed Asset").
      - name: net_amount
        description: 
          The net balance amount for the account at the end of the reported month. For balance sheet accounts (assets, liabilities, and equity), this represents the actual financial balance. For earnings accounts, it summarizes the earnings for the period.