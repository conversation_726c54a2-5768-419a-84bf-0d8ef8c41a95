name: 'xero_source'
version: '0.6.0'
config-version: 2
require-dbt-version: [">=1.3.0", "<2.0.0"]
vars:
  xero_source:
    account: "{{ source('xero','account') }}"
    journal_line: "{{ source('xero','journal_line') }}"
    journal: "{{ source('xero','journal') }}"
    invoice: "{{ source('xero','invoice') }}"
    invoice_line_item: "{{ source('xero','invoice_line_item') }}"
    contact: "{{ source('xero','contact') }}"
    organization: "{{ source('xero','organization') }}"
    bank_transaction: "{{ source('xero','bank_transaction') }}"
    credit_note: "{{ source('xero','credit_note') }}"
models:
  xero_source:
    +materialized: table
    +schema: stg_xero
    tmp:
      +materialized: view
