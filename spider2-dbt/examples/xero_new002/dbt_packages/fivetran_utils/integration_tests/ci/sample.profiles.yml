# HEY! This file is used in the dbt package integrations tests with Buildkite.
# You should __NEVER__ check credentials into version control. Thanks for reading :)

config:
    send_anonymous_usage_stats: False
    use_colors: True

integration_tests:
  target: postgres
  outputs:
    redshift:
      type: redshift
      host: "{{ env_var('CI_REDSHIFT_DBT_HOST') }}"
      user: "{{ env_var('CI_REDSHIFT_DBT_USER') }}"
      pass: "{{ env_var('CI_REDSHIFT_DBT_PASS') }}"
      dbname: "{{ env_var('CI_REDSHIFT_DBT_DBNAME') }}"
      port: 5439
      schema: fivetran_utils_integration_tests
      threads: 8
    bigquery:
      type: bigquery
      method: service-account-json
      project: 'dbt-package-testing'
      schema: fivetran_utils_integration_tests
      threads: 8
      keyfile_json: "{{ env_var('GCLOUD_SERVICE_KEY') | as_native }}"
    snowflake:
      type: snowflake
      account: "{{ env_var('CI_SNOWFLAKE_DBT_ACCOUNT') }}"
      user: "{{ env_var('CI_SNOWFLAKE_DBT_USER') }}"
      password: "{{ env_var('CI_SNOWFLAKE_DBT_PASS') }}"
      role: "{{ env_var('CI_SNOWFLAKE_DBT_ROLE_FT_UTILS_TEST') }}"
      database: "{{ env_var('CI_SNOWFLAKE_DATABASE_FT_UTILS') }}"
      warehouse: "{{ env_var('CI_SNOWFLAKE_DBT_WAREHOUSE') }}"
      schema: fivetran_utils_integration_tests
      threads: 8
    postgres:
      type: postgres
      host: "{{ env_var('CI_POSTGRES_DBT_HOST') }}"
      user: "{{ env_var('CI_POSTGRES_DBT_USER') }}"
      pass: "{{ env_var('CI_POSTGRES_DBT_PASS') }}"
      dbname: "{{ env_var('CI_POSTGRES_DBT_DBNAME') }}"
      port: 5432
      schema: fivetran_utils_integration_tests
      threads: 8
    databricks:
      catalog: hive_metastore
      host: "{{ env_var('CI_DATABRICKS_DBT_HOST') }}"
      http_path: "{{ env_var('CI_DATABRICKS_DBT_HTTP_PATH') }}"
      schema: fivetran_utils_integration_tests
      threads: 8
      token: "{{ env_var('CI_DATABRICKS_DBT_TOKEN') }}"
      type: databricks