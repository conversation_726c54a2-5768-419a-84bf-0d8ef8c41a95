{"metadata": {"dbt_schema_version": "https://schemas.getdbt.com/dbt/run-results/v4.json", "dbt_version": "1.0.4", "generated_at": "2022-04-04T13:59:53.906391Z", "invocation_id": "cd337e8c-7562-4ea1-a984-9fb3a454bc80", "env": {}}, "results": [{"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.255111Z", "completed_at": "2022-04-04T13:59:52.260229Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.260649Z", "completed_at": "2022-04-04T13:59:52.260664Z"}], "thread_id": "Thread-1", "execution_time": 0.006472349166870117, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.dbt_expectations_integration_tests.data_test"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.298477Z", "completed_at": "2022-04-04T13:59:52.303739Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.304285Z", "completed_at": "2022-04-04T13:59:52.304298Z"}], "thread_id": "Thread-1", "execution_time": 0.043018341064453125, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.dbt_expectations_integration_tests.data_test_factored"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.306335Z", "completed_at": "2022-04-04T13:59:52.309707Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.310220Z", "completed_at": "2022-04-04T13:59:52.310231Z"}], "thread_id": "Thread-1", "execution_time": 0.00503087043762207, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.dbt_expectations_integration_tests.emails"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.312392Z", "completed_at": "2022-04-04T13:59:52.316967Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.317462Z", "completed_at": "2022-04-04T13:59:52.317472Z"}], "thread_id": "Thread-1", "execution_time": 0.006440877914428711, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.dbt_expectations_integration_tests.series_10"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.319180Z", "completed_at": "2022-04-04T13:59:52.323652Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.324144Z", "completed_at": "2022-04-04T13:59:52.324153Z"}], "thread_id": "Thread-1", "execution_time": 0.0061168670654296875, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.dbt_expectations_integration_tests.series_4"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.325986Z", "completed_at": "2022-04-04T13:59:52.361946Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.362377Z", "completed_at": "2022-04-04T13:59:52.362387Z"}], "thread_id": "Thread-1", "execution_time": 0.03797769546508789, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.dbt_expectations_integration_tests.timeseries_base"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.364626Z", "completed_at": "2022-04-04T13:59:52.381426Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.381869Z", "completed_at": "2022-04-04T13:59:52.381880Z"}], "thread_id": "Thread-1", "execution_time": 0.018798112869262695, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.dbt_expectations_integration_tests.timeseries_hourly"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.384042Z", "completed_at": "2022-04-04T13:59:52.387805Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.388213Z", "completed_at": "2022-04-04T13:59:52.388221Z"}], "thread_id": "Thread-1", "execution_time": 0.005149126052856445, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.dbt_expectations_integration_tests.window_function_test"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.389724Z", "completed_at": "2022-04-04T13:59:52.396541Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.396950Z", "completed_at": "2022-04-04T13:59:52.396959Z"}], "thread_id": "Thread-1", "execution_time": 0.008091926574707031, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_equal_expression_data_test_ref_data_test___sum_col_numeric_a___idx.b487d295fb"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.398377Z", "completed_at": "2022-04-04T13:59:52.405086Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.405483Z", "completed_at": "2022-04-04T13:59:52.405491Z"}], "thread_id": "Thread-1", "execution_time": 0.007968902587890625, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_equal_expression_data_test_sum_col_numeric_a_5___ref_data_test___sum_col_numeric_a___idx__0_5.ce68b2893f"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.406867Z", "completed_at": "2022-04-04T13:59:52.414698Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.415096Z", "completed_at": "2022-04-04T13:59:52.415104Z"}], "thread_id": "Thread-1", "execution_time": 0.009083986282348633, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_equal_expression_data_test_sum_col_numeric_b___ref_data_test___sum_col_numeric_a_.674a29f4ac"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.416488Z", "completed_at": "2022-04-04T13:59:52.422461Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.422853Z", "completed_at": "2022-04-04T13:59:52.422861Z"}], "thread_id": "Thread-1", "execution_time": 0.007202863693237305, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_distinct_values_to_be_in_set_data_test_col_string_a__True__a__b__c__d.ba496b564d"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.424225Z", "completed_at": "2022-04-04T13:59:52.429883Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.430360Z", "completed_at": "2022-04-04T13:59:52.430370Z"}], "thread_id": "Thread-1", "execution_time": 0.007029056549072266, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_distinct_values_to_contain_set_data_test_col_string_a__True__a__b.cb3359e163"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.431784Z", "completed_at": "2022-04-04T13:59:52.438110Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.438514Z", "completed_at": "2022-04-04T13:59:52.438522Z"}], "thread_id": "Thread-1", "execution_time": 0.007564067840576172, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_distinct_values_to_equal_set_data_test_col_string_a__True__a__b__c__c.351d3e42e2"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.439895Z", "completed_at": "2022-04-04T13:59:52.445444Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.445850Z", "completed_at": "2022-04-04T13:59:52.445859Z"}], "thread_id": "Thread-1", "execution_time": 0.006822824478149414, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_max_to_be_between_data_test_col_numeric_a__1__1.da51d2d9cb"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.447283Z", "completed_at": "2022-04-04T13:59:52.452728Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.453135Z", "completed_at": "2022-04-04T13:59:52.453142Z"}], "thread_id": "Thread-1", "execution_time": 0.006708860397338867, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_mean_to_be_between_data_test_col_numeric_a__1_5__0.885aa2d309"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.454538Z", "completed_at": "2022-04-04T13:59:52.461212Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.461652Z", "completed_at": "2022-04-04T13:59:52.461662Z"}], "thread_id": "Thread-1", "execution_time": 0.007990837097167969, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_min_to_be_between_data_test_col_numeric_a__0__0.0b9263ab44"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.463101Z", "completed_at": "2022-04-04T13:59:52.467872Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.468268Z", "completed_at": "2022-04-04T13:59:52.468276Z"}], "thread_id": "Thread-1", "execution_time": 0.0060460567474365234, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_most_common_value_to_be_in_set_data_test_col_numeric_a__1__0_5.0c353bf758"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.469663Z", "completed_at": "2022-04-04T13:59:52.475306Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.475725Z", "completed_at": "2022-04-04T13:59:52.475735Z"}], "thread_id": "Thread-1", "execution_time": 0.006947994232177734, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_pair_values_A_to_be_greater_than_B_data_test_col_numeric_a_10__col_numeric_a.95a5cc1fac"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.477214Z", "completed_at": "2022-04-04T13:59:52.483172Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.483590Z", "completed_at": "2022-04-04T13:59:52.483600Z"}], "thread_id": "Thread-1", "execution_time": 0.007239818572998047, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_pair_values_A_to_be_greater_than_B_data_test_col_numeric_a__col_numeric_a__True.996ee156f4"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.485057Z", "completed_at": "2022-04-04T13:59:52.491140Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.491577Z", "completed_at": "2022-04-04T13:59:52.491588Z"}], "thread_id": "Thread-1", "execution_time": 0.00745081901550293, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_pair_values_to_be_equal_data_test_col_numeric_a__col_numeric_a.6237de7ec0"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.493092Z", "completed_at": "2022-04-04T13:59:52.500762Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.501175Z", "completed_at": "2022-04-04T13:59:52.501185Z"}], "thread_id": "Thread-1", "execution_time": 0.008983612060546875, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_pair_values_to_be_in_set_data_test_col_numeric_a__col_numeric_b___0_1____1_0____0_5_0_5____0_5_0_5_.fcd2f00790"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.502621Z", "completed_at": "2022-04-04T13:59:52.508038Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.508442Z", "completed_at": "2022-04-04T13:59:52.508451Z"}], "thread_id": "Thread-1", "execution_time": 0.0066950321197509766, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_proportion_of_unique_values_to_be_between_data_test_col_numeric_a__0_75__0.50b672f9f5"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.509906Z", "completed_at": "2022-04-04T13:59:52.515914Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.516431Z", "completed_at": "2022-04-04T13:59:52.516443Z"}], "thread_id": "Thread-1", "execution_time": 0.007455110549926758, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_stdev_to_be_between_data_test_col_numeric_a__0__True.0828889e83"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.517943Z", "completed_at": "2022-04-04T13:59:52.523695Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.524095Z", "completed_at": "2022-04-04T13:59:52.524104Z"}], "thread_id": "Thread-1", "execution_time": 0.007025003433227539, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_stdev_to_be_between_data_test_col_numeric_a__2__0.6aea0941f3"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.525514Z", "completed_at": "2022-04-04T13:59:52.531373Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.531782Z", "completed_at": "2022-04-04T13:59:52.531791Z"}], "thread_id": "Thread-1", "execution_time": 0.007143974304199219, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_sum_to_be_between_data_test_col_numeric_a__3__1.768d8a3df1"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.533216Z", "completed_at": "2022-04-04T13:59:52.584061Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.584492Z", "completed_at": "2022-04-04T13:59:52.584502Z"}], "thread_id": "Thread-1", "execution_time": 0.05270218849182129, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_to_exist_data_test_1__idx.fe00acd806"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.586531Z", "completed_at": "2022-04-04T13:59:52.617106Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.617524Z", "completed_at": "2022-04-04T13:59:52.617534Z"}], "thread_id": "Thread-1", "execution_time": 0.032476186752319336, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_to_exist_data_test_3__col_numeric_a.707ebd64f9"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.619555Z", "completed_at": "2022-04-04T13:59:52.650927Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.651335Z", "completed_at": "2022-04-04T13:59:52.651346Z"}], "thread_id": "Thread-1", "execution_time": 0.033164024353027344, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_to_exist_data_test_col_numeric_a.05ed4c08ef"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.653301Z", "completed_at": "2022-04-04T13:59:52.660969Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.661651Z", "completed_at": "2022-04-04T13:59:52.661665Z"}], "thread_id": "Thread-1", "execution_time": 0.009293794631958008, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_unique_value_count_to_be_between_data_test_col_numeric_a__3__3.29365f2765"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.663172Z", "completed_at": "2022-04-04T13:59:52.668934Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.669339Z", "completed_at": "2022-04-04T13:59:52.669348Z"}], "thread_id": "Thread-1", "execution_time": 0.0070400238037109375, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_value_lengths_to_be_between_data_test_col_string_b__1.11ec77adb3"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.670749Z", "completed_at": "2022-04-04T13:59:52.676255Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.676670Z", "completed_at": "2022-04-04T13:59:52.676679Z"}], "thread_id": "Thread-1", "execution_time": 0.0067899227142333984, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_value_lengths_to_be_between_data_test_col_string_b__4.c9921ddc5a"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.678295Z", "completed_at": "2022-04-04T13:59:52.683865Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.684263Z", "completed_at": "2022-04-04T13:59:52.684272Z"}], "thread_id": "Thread-1", "execution_time": 0.006963253021240234, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_value_lengths_to_be_between_data_test_col_string_b__4__1.7124d69fa8"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.685649Z", "completed_at": "2022-04-04T13:59:52.691144Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.691566Z", "completed_at": "2022-04-04T13:59:52.691575Z"}], "thread_id": "Thread-1", "execution_time": 0.006788730621337891, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_value_lengths_to_equal_data_test_col_string_a__1.914eeb47db"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.692995Z", "completed_at": "2022-04-04T13:59:52.698674Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.699075Z", "completed_at": "2022-04-04T13:59:52.699085Z"}], "thread_id": "Thread-1", "execution_time": 0.006958723068237305, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_values_to_be_between_data_test_col_numeric_a__0.38e10ae5de"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.700553Z", "completed_at": "2022-04-04T13:59:52.707589Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.707992Z", "completed_at": "2022-04-04T13:59:52.708001Z"}], "thread_id": "Thread-1", "execution_time": 0.008321046829223633, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_values_to_be_between_data_test_col_numeric_a__1__0.dd0c29c89d"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.710338Z", "completed_at": "2022-04-04T13:59:52.717152Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.717552Z", "completed_at": "2022-04-04T13:59:52.717560Z"}], "thread_id": "Thread-1", "execution_time": 0.008819341659545898, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_values_to_be_in_set_data_test_col_string_a__True__a__b__c.0d9c524932"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.719031Z", "completed_at": "2022-04-04T13:59:52.724213Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.724614Z", "completed_at": "2022-04-04T13:59:52.724624Z"}], "thread_id": "Thread-1", "execution_time": 0.0064661502838134766, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_values_to_be_increasing_data_test_col_numeric_a__col_numeric_a__False.275c036446"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.726061Z", "completed_at": "2022-04-04T13:59:52.731596Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.732023Z", "completed_at": "2022-04-04T13:59:52.732033Z"}], "thread_id": "Thread-1", "execution_time": 0.006970882415771484, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_values_to_be_increasing_data_test_idx__idx.f1baf74a63"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.733492Z", "completed_at": "2022-04-04T13:59:52.738714Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.739139Z", "completed_at": "2022-04-04T13:59:52.739148Z"}], "thread_id": "Thread-1", "execution_time": 0.006521701812744141, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_values_to_be_null_data_test_col_null.0be3aa326a"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.740558Z", "completed_at": "2022-04-04T13:59:52.745397Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.745859Z", "completed_at": "2022-04-04T13:59:52.745868Z"}], "thread_id": "Thread-1", "execution_time": 0.006170034408569336, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_values_to_be_unique_data_test_idx.1fedbd4c8c"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.748713Z", "completed_at": "2022-04-04T13:59:52.753267Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.753667Z", "completed_at": "2022-04-04T13:59:52.753676Z"}], "thread_id": "Thread-1", "execution_time": 0.0072479248046875, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_values_to_have_consistent_casing_data_test_col_string_a.97aaa47da3"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.755070Z", "completed_at": "2022-04-04T13:59:52.759680Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.760081Z", "completed_at": "2022-04-04T13:59:52.760089Z"}], "thread_id": "Thread-1", "execution_time": 0.005868673324584961, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_values_to_have_consistent_casing_data_test_col_string_a__True.dbf8671a4f"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.761639Z", "completed_at": "2022-04-04T13:59:52.767418Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.767820Z", "completed_at": "2022-04-04T13:59:52.767829Z"}], "thread_id": "Thread-1", "execution_time": 0.0070798397064208984, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_values_to_not_be_in_set_data_test_col_string_a__True__2__3.bba7663cef"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.769238Z", "completed_at": "2022-04-04T13:59:52.774965Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.775368Z", "completed_at": "2022-04-04T13:59:52.775377Z"}], "thread_id": "Thread-1", "execution_time": 0.0070002079010009766, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_values_to_not_be_in_set_data_test_col_string_b__True__a__c.430e7a2045"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.776780Z", "completed_at": "2022-04-04T13:59:52.781992Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.782404Z", "completed_at": "2022-04-04T13:59:52.782414Z"}], "thread_id": "Thread-1", "execution_time": 0.00651097297668457, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_values_to_not_be_null_data_test_col_numeric_a.1c5c036a67"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.783841Z", "completed_at": "2022-04-04T13:59:52.791522Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.792014Z", "completed_at": "2022-04-04T13:59:52.792025Z"}], "thread_id": "Thread-1", "execution_time": 0.009088993072509766, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_compound_columns_to_be_unique_data_test_date_col__col_string_b__any_value_is_missing.6809061372"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.793514Z", "completed_at": "2022-04-04T13:59:52.799736Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.800156Z", "completed_at": "2022-04-04T13:59:52.800166Z"}], "thread_id": "Thread-1", "execution_time": 0.007647275924682617, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_multicolumn_sum_to_equal_data_test_col_numeric_a__col_numeric_b__4.491d71e071"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.801607Z", "completed_at": "2022-04-04T13:59:52.807757Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.808164Z", "completed_at": "2022-04-04T13:59:52.808172Z"}], "thread_id": "Thread-1", "execution_time": 0.007433176040649414, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_select_column_values_to_be_unique_within_record_data_test_col_string_a__col_string_b__any_value_is_missing.4673d37284"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.809545Z", "completed_at": "2022-04-04T13:59:52.836294Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.836712Z", "completed_at": "2022-04-04T13:59:52.836723Z"}], "thread_id": "Thread-1", "execution_time": 0.028562068939208984, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_table_column_count_to_be_between_data_test_1.81327d9e76"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.838686Z", "completed_at": "2022-04-04T13:59:52.866461Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.866880Z", "completed_at": "2022-04-04T13:59:52.866891Z"}], "thread_id": "Thread-1", "execution_time": 0.029651165008544922, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_table_column_count_to_be_between_data_test_10.53e3f2be23"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.868891Z", "completed_at": "2022-04-04T13:59:52.894554Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.895150Z", "completed_at": "2022-04-04T13:59:52.895162Z"}], "thread_id": "Thread-1", "execution_time": 0.027773141860961914, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_table_column_count_to_be_between_data_test_10__1.31fd1ecbff"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.897263Z", "completed_at": "2022-04-04T13:59:52.922860Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.923308Z", "completed_at": "2022-04-04T13:59:52.923318Z"}], "thread_id": "Thread-1", "execution_time": 0.02747201919555664, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_table_column_count_to_equal_data_test_7.552fe2a555"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.925298Z", "completed_at": "2022-04-04T13:59:52.961006Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.961435Z", "completed_at": "2022-04-04T13:59:52.961447Z"}], "thread_id": "Thread-1", "execution_time": 0.03757905960083008, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_table_column_count_to_equal_other_table_data_test_ref_data_test_.47d89a52cb"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:52.963580Z", "completed_at": "2022-04-04T13:59:52.997912Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:52.998330Z", "completed_at": "2022-04-04T13:59:52.998341Z"}], "thread_id": "Thread-1", "execution_time": 0.03623485565185547, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_table_columns_to_contain_set_data_test_col_numeric_b__col_string_a.428ef19067"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.000410Z", "completed_at": "2022-04-04T13:59:53.035067Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.035476Z", "completed_at": "2022-04-04T13:59:53.035487Z"}], "thread_id": "Thread-1", "execution_time": 0.03648209571838379, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_table_columns_to_match_ordered_list_data_test_idx__date_col__col_numeric_a__col_numeric_b__col_string_a__col_string_b__col_null.847d680085"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.037402Z", "completed_at": "2022-04-04T13:59:53.070661Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.071067Z", "completed_at": "2022-04-04T13:59:53.071077Z"}], "thread_id": "Thread-1", "execution_time": 0.035143136978149414, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_table_columns_to_match_set_data_test_idx__date_col__col_numeric_a__col_numeric_b__col_string_a__col_string_b__col_null.43007c4dca"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.073022Z", "completed_at": "2022-04-04T13:59:53.103058Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.103480Z", "completed_at": "2022-04-04T13:59:53.103490Z"}], "thread_id": "Thread-1", "execution_time": 0.0318751335144043, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_table_columns_to_not_contain_set_data_test_col_numeric_c__col_string_d.d79c96d480"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.105456Z", "completed_at": "2022-04-04T13:59:53.112069Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.112470Z", "completed_at": "2022-04-04T13:59:53.112480Z"}], "thread_id": "Thread-1", "execution_time": 0.007885932922363281, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_table_row_count_to_be_between_data_test_1.2707fd03e7"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.113931Z", "completed_at": "2022-04-04T13:59:53.119679Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.120095Z", "completed_at": "2022-04-04T13:59:53.120106Z"}], "thread_id": "Thread-1", "execution_time": 0.007064104080200195, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_table_row_count_to_be_between_data_test_4.740d584f1d"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.121542Z", "completed_at": "2022-04-04T13:59:53.127418Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.127847Z", "completed_at": "2022-04-04T13:59:53.127858Z"}], "thread_id": "Thread-1", "execution_time": 0.007190704345703125, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_table_row_count_to_be_between_data_test_4__1.68b3ca52d7"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.129296Z", "completed_at": "2022-04-04T13:59:53.134272Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.134694Z", "completed_at": "2022-04-04T13:59:53.134704Z"}], "thread_id": "Thread-1", "execution_time": 0.0062868595123291016, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_table_row_count_to_equal_data_test_4.0893914b47"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.136169Z", "completed_at": "2022-04-04T13:59:53.143317Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.143744Z", "completed_at": "2022-04-04T13:59:53.143754Z"}], "thread_id": "Thread-1", "execution_time": 0.008478164672851562, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_table_row_count_to_equal_other_table_data_test_ref_data_test___1_1__1_1.bb1a037ebc"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.145219Z", "completed_at": "2022-04-04T13:59:53.150893Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.151318Z", "completed_at": "2022-04-04T13:59:53.151329Z"}], "thread_id": "Thread-1", "execution_time": 0.006994962692260742, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expression_is_true_data_test__col_numeric_a_col_numeric_b_1_.72e60a34af"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.152774Z", "completed_at": "2022-04-04T13:59:53.160110Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.160587Z", "completed_at": "2022-04-04T13:59:53.160599Z"}], "thread_id": "Thread-1", "execution_time": 0.00873708724975586, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_table_row_count_to_equal_other_table_times_factor_data_test_factored_ref_data_test___2.297ec8375e"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.162177Z", "completed_at": "2022-04-04T13:59:53.167870Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.168290Z", "completed_at": "2022-04-04T13:59:53.168301Z"}], "thread_id": "Thread-1", "execution_time": 0.007014036178588867, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_values_to_match_like_pattern_emails_email_address___.08c0c874a1"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.169801Z", "completed_at": "2022-04-04T13:59:53.175662Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.176054Z", "completed_at": "2022-04-04T13:59:53.176063Z"}], "thread_id": "Thread-1", "execution_time": 0.007116794586181641, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_values_to_match_like_pattern_list_emails_email_address______.004645a3ac"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.177448Z", "completed_at": "2022-04-04T13:59:53.183055Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.183524Z", "completed_at": "2022-04-04T13:59:53.183534Z"}], "thread_id": "Thread-1", "execution_time": 0.006960868835449219, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_values_to_match_regex_emails_email_address___.9de7a56999"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.184942Z", "completed_at": "2022-04-04T13:59:53.190997Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.191403Z", "completed_at": "2022-04-04T13:59:53.191412Z"}], "thread_id": "Thread-1", "execution_time": 0.0074138641357421875, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_values_to_match_regex_list_emails_email_address______.959ffbd98d"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.192788Z", "completed_at": "2022-04-04T13:59:53.200079Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.200515Z", "completed_at": "2022-04-04T13:59:53.200526Z"}], "thread_id": "Thread-1", "execution_time": 0.008599996566772461, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_values_to_not_match_like_pattern_emails_email_address___.969be3889d"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.201966Z", "completed_at": "2022-04-04T13:59:53.243059Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.243597Z", "completed_at": "2022-04-04T13:59:53.243609Z"}], "thread_id": "Thread-1", "execution_time": 0.042584896087646484, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_values_to_not_match_like_pattern_list_emails_email_address______.31da73e0cd"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.245339Z", "completed_at": "2022-04-04T13:59:53.251912Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.252466Z", "completed_at": "2022-04-04T13:59:53.252479Z"}], "thread_id": "Thread-1", "execution_time": 0.008186101913452148, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_values_to_not_match_regex_emails_email_address___.ff3a15060f"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.254218Z", "completed_at": "2022-04-04T13:59:53.261355Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.261850Z", "completed_at": "2022-04-04T13:59:53.261862Z"}], "thread_id": "Thread-1", "execution_time": 0.008671998977661133, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_values_to_not_match_regex_list_emails_email_address______.8bac11c2c5"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.263489Z", "completed_at": "2022-04-04T13:59:53.269476Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.269956Z", "completed_at": "2022-04-04T13:59:53.269965Z"}], "thread_id": "Thread-1", "execution_time": 0.0074160099029541016, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.dbt_expectations_integration_tests.timeseries_data"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.271770Z", "completed_at": "2022-04-04T13:59:53.277240Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.277653Z", "completed_at": "2022-04-04T13:59:53.277663Z"}], "thread_id": "Thread-1", "execution_time": 0.0071408748626708984, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.dbt_expectations_integration_tests.timeseries_data_extended"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.279692Z", "completed_at": "2022-04-04T13:59:53.286691Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.287118Z", "completed_at": "2022-04-04T13:59:53.287127Z"}], "thread_id": "Thread-1", "execution_time": 0.008796930313110352, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.dbt_expectations_integration_tests.timeseries_data_grouped"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.288764Z", "completed_at": "2022-04-04T13:59:53.294055Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.294466Z", "completed_at": "2022-04-04T13:59:53.294476Z"}], "thread_id": "Thread-1", "execution_time": 0.006582021713256836, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.dbt_expectations_integration_tests.timeseries_hourly_data_extended"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.296057Z", "completed_at": "2022-04-04T13:59:53.301474Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.301945Z", "completed_at": "2022-04-04T13:59:53.301953Z"}], "thread_id": "Thread-1", "execution_time": 0.007013082504272461, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_values_to_be_decreasing_window_function_test_rolling_sum_decreasing__idx__date_col__True.1c67e8ae47"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.303479Z", "completed_at": "2022-04-04T13:59:53.308874Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.309279Z", "completed_at": "2022-04-04T13:59:53.309288Z"}], "thread_id": "Thread-1", "execution_time": 0.006822824478149414, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_values_to_be_increasing_window_function_test_rolling_sum_increasing__idx__date_col__True.996e133dc9"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.310713Z", "completed_at": "2022-04-04T13:59:53.316437Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.316956Z", "completed_at": "2022-04-04T13:59:53.316968Z"}], "thread_id": "Thread-1", "execution_time": 0.007399797439575195, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_distinct_count_to_equal_other_table_timeseries_data_date_day.79cb16c1eb"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.318890Z", "completed_at": "2022-04-04T13:59:53.350507Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.350904Z", "completed_at": "2022-04-04T13:59:53.350914Z"}], "thread_id": "Thread-1", "execution_time": 0.03345298767089844, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_values_to_be_in_type_list_timeseries_data_date_datetime__date___dbt_expectations_type_datetime_.a431470019"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.352968Z", "completed_at": "2022-04-04T13:59:53.385295Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.385695Z", "completed_at": "2022-04-04T13:59:53.385705Z"}], "thread_id": "Thread-1", "execution_time": 0.03428387641906738, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_values_to_be_in_type_list_timeseries_data_date_day__date___dbt_expectations_type_timestamp_.79af0ea1f3"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.387794Z", "completed_at": "2022-04-04T13:59:53.418233Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.418632Z", "completed_at": "2022-04-04T13:59:53.418643Z"}], "thread_id": "Thread-1", "execution_time": 0.032346248626708984, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_values_to_be_in_type_list_timeseries_data_date_timestamp__date___dbt_expectations_type_timestamp_.4fc8b3eb42"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.420692Z", "completed_at": "2022-04-04T13:59:53.427095Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.427490Z", "completed_at": "2022-04-04T13:59:53.427500Z"}], "thread_id": "Thread-1", "execution_time": 0.007745027542114258, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_values_to_be_increasing_timeseries_data_date_day__date_day.b349b0a845"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.428864Z", "completed_at": "2022-04-04T13:59:53.436647Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.437051Z", "completed_at": "2022-04-04T13:59:53.437061Z"}], "thread_id": "Thread-1", "execution_time": 0.009056806564331055, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_values_to_be_within_n_moving_stdevs_timeseries_data_row_value__date_day__6__True.86019a24d0"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.438477Z", "completed_at": "2022-04-04T13:59:53.443184Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.443576Z", "completed_at": "2022-04-04T13:59:53.443585Z"}], "thread_id": "Thread-1", "execution_time": 0.005988121032714844, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_values_to_be_within_n_stdevs_timeseries_data_row_value__6.be239e407d"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.444946Z", "completed_at": "2022-04-04T13:59:53.467151Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.467578Z", "completed_at": "2022-04-04T13:59:53.467589Z"}], "thread_id": "Thread-1", "execution_time": 0.023998022079467773, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_row_values_to_have_data_for_every_n_datepart_timeseries_data_date_day__day.910ac3a171"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.469477Z", "completed_at": "2022-04-04T13:59:53.490248Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.490677Z", "completed_at": "2022-04-04T13:59:53.490688Z"}], "thread_id": "Thread-1", "execution_time": 0.022609233856201172, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_row_values_to_have_data_for_every_n_datepart_timeseries_data_date_day__day__not_date_day_2021_10_19_.39ae519ce7"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.492582Z", "completed_at": "2022-04-04T13:59:53.499490Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.499883Z", "completed_at": "2022-04-04T13:59:53.499891Z"}], "thread_id": "Thread-1", "execution_time": 0.008131027221679688, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_row_values_to_have_recent_data_timeseries_data_date_datetime__day__1.29c33143be"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.501252Z", "completed_at": "2022-04-04T13:59:53.508392Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.508800Z", "completed_at": "2022-04-04T13:59:53.508810Z"}], "thread_id": "Thread-1", "execution_time": 0.008419036865234375, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_row_values_to_have_recent_data_timeseries_data_date_day__day__1.9222375acc"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.510196Z", "completed_at": "2022-04-04T13:59:53.517123Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.517517Z", "completed_at": "2022-04-04T13:59:53.517526Z"}], "thread_id": "Thread-1", "execution_time": 0.008170843124389648, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_row_values_to_have_recent_data_timeseries_data_date_timestamp__day__1.300ccbdad1"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.518874Z", "completed_at": "2022-04-04T13:59:53.551070Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.551485Z", "completed_at": "2022-04-04T13:59:53.551496Z"}], "thread_id": "Thread-1", "execution_time": 0.0339970588684082, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_table_columns_to_match_ordered_list_timeseries_data_date_day__date_datetime__date_timestamp__row_value__row_value_log.fea981f1fa"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.553409Z", "completed_at": "2022-04-04T13:59:53.561400Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.561798Z", "completed_at": "2022-04-04T13:59:53.561807Z"}], "thread_id": "Thread-1", "execution_time": 0.009256839752197266, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_distinct_count_to_be_greater_than_timeseries_data_extended_row_value__date_day_dbt_date_yesterday___1.ab45a07e5c"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.563184Z", "completed_at": "2022-04-04T13:59:53.569521Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.569912Z", "completed_at": "2022-04-04T13:59:53.569921Z"}], "thread_id": "Thread-1", "execution_time": 0.0075778961181640625, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_distinct_count_to_equal_other_table_timeseries_data_date_day__date_day__ref_timeseries_data_extended_.668a389f21"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.571365Z", "completed_at": "2022-04-04T13:59:53.577246Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.577694Z", "completed_at": "2022-04-04T13:59:53.577704Z"}], "thread_id": "Thread-1", "execution_time": 0.007205963134765625, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_distinct_count_to_equal_other_table_timeseries_data_date_day__ref_timeseries_data_extended_.6fd6f4ea6b"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.579141Z", "completed_at": "2022-04-04T13:59:53.585779Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.586176Z", "completed_at": "2022-04-04T13:59:53.586186Z"}], "thread_id": "Thread-1", "execution_time": 0.007911205291748047, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_distinct_count_to_equal_timeseries_data_extended_row_value__date_day_dbt_date_yesterday___10.633f73d315"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.587556Z", "completed_at": "2022-04-04T13:59:53.619801Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.620256Z", "completed_at": "2022-04-04T13:59:53.620270Z"}], "thread_id": "Thread-1", "execution_time": 0.0343480110168457, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_values_to_be_in_type_list_timeseries_data_extended_date_day__date___dbt_expectations_type_datetime_.64544aba59"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.622396Z", "completed_at": "2022-04-04T13:59:53.651166Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.651571Z", "completed_at": "2022-04-04T13:59:53.651581Z"}], "thread_id": "Thread-1", "execution_time": 0.030546903610229492, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_values_to_be_of_type_timeseries_data_extended_date_day___dbt_expectations_type_datetime_.3377fc291f"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.653505Z", "completed_at": "2022-04-04T13:59:53.661544Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.661947Z", "completed_at": "2022-04-04T13:59:53.661956Z"}], "thread_id": "Thread-1", "execution_time": 0.00932002067565918, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_values_to_be_within_n_moving_stdevs_timeseries_data_extended_row_value_log__cast_date_day_as_dbt_expectations_type_datetime___6__False.91749cfd51"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.663340Z", "completed_at": "2022-04-04T13:59:53.683534Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.683934Z", "completed_at": "2022-04-04T13:59:53.683944Z"}], "thread_id": "Thread-1", "execution_time": 0.021947145462036133, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_row_values_to_have_data_for_every_n_datepart_timeseries_data_extended_date_day__day.fef2d1f342"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.685811Z", "completed_at": "2022-04-04T13:59:53.705798Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.706215Z", "completed_at": "2022-04-04T13:59:53.706226Z"}], "thread_id": "Thread-1", "execution_time": 0.021764039993286133, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_row_values_to_have_data_for_every_n_datepart_timeseries_data_extended_date_day__day__7.e436a78560"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.708085Z", "completed_at": "2022-04-04T13:59:53.715204Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.715616Z", "completed_at": "2022-04-04T13:59:53.715626Z"}], "thread_id": "Thread-1", "execution_time": 0.008385658264160156, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_row_values_to_have_recent_data_timeseries_data_extended_date_day__day__1.918ff17d7d"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.717047Z", "completed_at": "2022-04-04T13:59:53.746324Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.746854Z", "completed_at": "2022-04-04T13:59:53.746867Z"}], "thread_id": "Thread-1", "execution_time": 0.03133678436279297, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_table_columns_to_match_ordered_list_timeseries_data_extended_date_day__row_value__row_value_log.c4fbe0bb2c"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.748912Z", "completed_at": "2022-04-04T13:59:53.757103Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.757505Z", "completed_at": "2022-04-04T13:59:53.757515Z"}], "thread_id": "Thread-1", "execution_time": 0.00945901870727539, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_grouped_row_values_to_have_recent_data_timeseries_data_grouped_day__group_id__1__date_day.75413a8781"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.758871Z", "completed_at": "2022-04-04T13:59:53.766392Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.766804Z", "completed_at": "2022-04-04T13:59:53.766814Z"}], "thread_id": "Thread-1", "execution_time": 0.008800268173217773, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_grouped_row_values_to_have_recent_data_timeseries_data_grouped_day__group_id__1__group_id_4__date_day.eb518be9a8"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.768213Z", "completed_at": "2022-04-04T13:59:53.775562Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.775959Z", "completed_at": "2022-04-04T13:59:53.775968Z"}], "thread_id": "Thread-1", "execution_time": 0.008617162704467773, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_grouped_row_values_to_have_recent_data_timeseries_data_grouped_day__group_id__row_value__1__date_day.53695446a8"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.777342Z", "completed_at": "2022-04-04T13:59:53.782590Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.782981Z", "completed_at": "2022-04-04T13:59:53.782989Z"}], "thread_id": "Thread-1", "execution_time": 0.006498098373413086, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_table_row_count_to_be_between_timeseries_data_grouped_10.d82948f3e6"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.784338Z", "completed_at": "2022-04-04T13:59:53.790112Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.790519Z", "completed_at": "2022-04-04T13:59:53.790529Z"}], "thread_id": "Thread-1", "execution_time": 0.007050037384033203, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_table_row_count_to_be_between_timeseries_data_grouped_date_day__10.28ef2972b1"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.791905Z", "completed_at": "2022-04-04T13:59:53.798874Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.799270Z", "completed_at": "2022-04-04T13:59:53.799279Z"}], "thread_id": "Thread-1", "execution_time": 0.00822591781616211, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_table_row_count_to_be_between_timeseries_data_grouped_group_id__10000__True.1370954d9b"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.800684Z", "completed_at": "2022-04-04T13:59:53.829528Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.829955Z", "completed_at": "2022-04-04T13:59:53.829966Z"}], "thread_id": "Thread-1", "execution_time": 0.030699968338012695, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_values_to_be_in_type_list_timeseries_hourly_data_extended_date_hour___dbt_expectations_type_datetime_.e2af5fe244"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.831929Z", "completed_at": "2022-04-04T13:59:53.860549Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.860972Z", "completed_at": "2022-04-04T13:59:53.860983Z"}], "thread_id": "Thread-1", "execution_time": 0.030498027801513672, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_values_to_be_of_type_timeseries_hourly_data_extended_date_hour___dbt_expectations_type_datetime_.fa9ac6d2a4"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.862994Z", "completed_at": "2022-04-04T13:59:53.871859Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.872278Z", "completed_at": "2022-04-04T13:59:53.872289Z"}], "thread_id": "Thread-1", "execution_time": 0.010190010070800781, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_column_values_to_be_within_n_moving_stdevs_timeseries_hourly_data_extended_row_value_log__cast_date_hour_as_dbt_expectations_type_datetime___hour__6__False__12__48.c200505cc1"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.873709Z", "completed_at": "2022-04-04T13:59:53.894793Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.895197Z", "completed_at": "2022-04-04T13:59:53.895208Z"}], "thread_id": "Thread-1", "execution_time": 0.022849082946777344, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_row_values_to_have_data_for_every_n_datepart_timeseries_hourly_data_extended_date_hour__hour.5afac62404"}, {"status": "success", "timing": [{"name": "compile", "started_at": "2022-04-04T13:59:53.897078Z", "completed_at": "2022-04-04T13:59:53.904292Z"}, {"name": "execute", "started_at": "2022-04-04T13:59:53.904685Z", "completed_at": "2022-04-04T13:59:53.904693Z"}], "thread_id": "Thread-1", "execution_time": 0.008460044860839844, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.dbt_expectations_integration_tests.dbt_expectations_expect_row_values_to_have_recent_data_timeseries_hourly_data_extended_date_hour__hour__24.a182297cd8"}], "elapsed_time": 1.9208459854125977, "args": {"write_json": true, "use_colors": true, "printer_width": 80, "version_check": true, "partial_parse": true, "static_parser": true, "profiles_dir": "/Users/<USER>/.dbt", "send_anonymous_usage_stats": false, "event_buffer_size": 100000, "compile": true, "which": "generate", "rpc_method": "docs.generate", "indirect_selection": "eager"}}