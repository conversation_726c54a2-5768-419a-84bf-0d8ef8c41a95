with employee_surrogate_key as (
    
    select 
        {{ dbt_utils.generate_surrogate_key(['worker_id', 'source_relation', 'position_id', 'position_start_date']) }} as employee_id,
        worker_id,
        source_relation,
        position_id,
        position_start_date,
        worker_code,
        user_id,
        universal_id,
        is_user_active,
        is_employed,
        hire_date,
        departure_date,    
        days_as_worker,
        is_terminated,
        primary_termination_category,
        primary_termination_reason,
        is_regrettable_termination, 
        compensation_effective_date,
        employee_compensation_frequency,
        annual_currency_summary_currency,
        annual_currency_summary_total_base_pay,
        annual_currency_summary_primary_compensation_basis,
        annual_summary_currency,
        annual_summary_total_base_pay,
        annual_summary_primary_compensation_basis,
        compensation_grade_id,
        compensation_grade_profile_id
        first_name,
        last_name,
        date_of_birth,
        gender,
        is_hispanic_or_latino,
        email_address,
        ethnicity_codes,
        military_status,
        business_title,
        job_profile_id,
        employee_type,
        position_location,
        management_level_code,
        fte_percent,
        position_end_date,
        position_effective_date,
        days_employed,
        is_employed_one_year,
        is_employed_five_years,
        is_employed_ten_years,
        is_employed_twenty_years,
        is_employed_thirty_years,
        is_current_employee_one_year,
        is_current_employee_five_years,
        is_current_employee_ten_years,
        is_current_employee_twenty_years,
        is_current_employee_thirty_years
    from {{ ref('int_workday__worker_employee_enhanced') }} 
)

select * 
from employee_surrogate_key