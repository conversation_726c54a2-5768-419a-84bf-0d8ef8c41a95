Pull Request
**Are you a current Fivetran customer?** 
<!--- Please tell us your name, title and company -->

**What change(s) does this PR introduce?** 
<!--- Describe what changes your PR introduces to the package and how to leverage this new feature. -->

**Did you update the CHANGELOG?** 
<!--- Please update the new package version’s CHANGELOG entry detailing the changes included in this PR. -->
<!--- To select a checkbox you simply need to add an "x" with no spaces between the brackets (eg. [x] Yes). -->
- [ ] Yes

**Does this PR introduce a breaking change?**
<!--- Does this PR introduce changes that will cause current package users' jobs to fail or require a `--full-refresh`? -->
<!--- To select a checkbox you simply need to add an "x" with no spaces between the brackets (eg. [x] Yes). -->
- [ ] Yes (please provide breaking change details below.)
- [ ] No  (please provide an explanation as to how the change is non-breaking below.)

**Did you update the dbt_project.yml files with the version upgrade (please leverage standard semantic versioning)? (In both your main project and integration_tests)** 
<!--- The dbt_project.yml and the integration_tests/dbt_project.yml files contain the version number. Be sure to upgrade it accordingly -->
<!--- To select a checkbox you simply need to add an "x" with no spaces between the brackets (eg. [x] Yes). -->
- [ ] Yes

**Is this PR in response to a previously created Bug or Feature Request**
<!--- If an Issue was created it is helpful to track the progress by linking it in the PR. -->
<!--- To select a checkbox you simply need to add an "x" with no spaces between the brackets (eg. [x] Yes). -->
- [ ] Yes, Issue/Feature [link bug/feature number here]
- [ ] No 

**How did you test the PR changes?** 
<!--- Proof of testing is required in order for the PR to be approved. -->
<!--- To check a box, remove the space and insert an x in the box (eg. [x] Buildkite). --> 
<!--- To select a checkbox you simply need to add an "x" with no spaces between the brackets (eg. [x] Yes). -->
- [ ] Buildkite <!--- Buildkite testing is only applicable to Fivetran employees. --> 
- [ ] Local (please provide additional testing details below)

**Select which warehouse(s) were used to test the PR**
<!--- To check a warehouse remove the space and insert an x in the box (eg. [x] Bigquery). --> 
<!--- To select a checkbox you simply need to add an "x" with no spaces between the brackets (eg. [x] Yes). -->
- [ ] BigQuery
- [ ] Redshift
- [ ] Snowflake
- [ ] Postgres
- [ ] Databricks
- [ ] Other (provide details below)

**Provide an emoji that best describes your current mood**
<!--- For a complete list of markdown compatible emojis check our this git repo (https://gist.github.com/rxaviers/7360908)  --> 
:dancer:

**Feedback**

We are so excited you decided to contribute to the Fivetran community dbt package! We continue to work to improve the packages and would greatly appreciate your [feedback](https://www.surveymonkey.com/r/DQ7K7WW) on our existing dbt packages or what you'd like to see next.
