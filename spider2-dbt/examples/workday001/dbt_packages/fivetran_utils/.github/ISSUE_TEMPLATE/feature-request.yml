name: 🎉 Feature
description: Suggest a new feature for the Fivetran dbt package
title: "[Feature] <title>"
labels: ["enhancement"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for using the Fivetran dbt package and for taking the time to fill out this feature request. Your contributions help improve this package for the entire community of users!
  - type: checkboxes
    attributes:
      label: Is there an existing feature request for this?
      description: Please search to see if an issue already exists for the feature you would like.
      options:
        - label: I have searched the existing issues
          required: true
  - type: textarea
    attributes:
      label: Describe the Feature
      description: A clear and concise description of what you want to happen and why you want the new feature.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Describe alternatives you've considered
      description: |
        A clear and concise description of any alternative solutions or features you've considered.
    validations:
      required: false
  - type: checkboxes
    id: contributing
    attributes:
      label: Are you interested in contributing this feature?
      description: Our team will assess this feature and let you know if we will add it to a future sprint. However, if you would like to expedite the feature, we encourage you to contribute to the package via a PR. Our team will then work with you to approve and merge your contributions as soon as possible.
      options:
        - label: Yes.
        - label: Yes, but I will need assistance and will schedule time during your [office hours](https://calendly.com/fivetran-solutions-team/fivetran-solutions-team-office-hours) for guidance.
        - label: No.
          required: false
  - type: textarea
    attributes:
      label: Anything else?
      description: |
        Links? References? Anything that will give us more context about the feature you are suggesting!
    validations:
      required: false
