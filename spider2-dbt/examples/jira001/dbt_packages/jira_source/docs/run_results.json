{"metadata": {"dbt_schema_version": "https://schemas.getdbt.com/dbt/run-results/v5.json", "dbt_version": "1.7.9", "generated_at": "2024-04-18T23:37:06.179226Z", "invocation_id": "6b815115-594e-4922-9025-42cc41721923", "env": {}}, "results": [{"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.359888Z", "completed_at": "2024-04-18T23:37:04.380177Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.391192Z", "completed_at": "2024-04-18T23:37:04.391201Z"}], "thread_id": "Thread-1", "execution_time": 0.039359331130981445, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.jira_source.stg_jira__comment_tmp", "compiled": true, "compiled_code": "select * \nfrom \"postgres\".\"jira_source_integration_tests_302\".\"comment\"", "relation_name": "\"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__comment_tmp\""}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.367834Z", "completed_at": "2024-04-18T23:37:04.388596Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.392398Z", "completed_at": "2024-04-18T23:37:04.392401Z"}], "thread_id": "Thread-2", "execution_time": 0.039793968200683594, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.jira_source.stg_jira__component_tmp", "compiled": true, "compiled_code": "\n\nselect * \nfrom \"postgres\".\"jira_source_integration_tests_302\".\"component\"", "relation_name": "\"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__component_tmp\""}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.377725Z", "completed_at": "2024-04-18T23:37:04.389447Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.393665Z", "completed_at": "2024-04-18T23:37:04.393669Z"}], "thread_id": "Thread-5", "execution_time": 0.03762006759643555, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.jira_source.stg_jira__issue_field_history_tmp", "compiled": true, "compiled_code": "select * \nfrom \"postgres\".\"jira_source_integration_tests_302\".\"issue_field_history\"", "relation_name": "\"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__issue_field_history_tmp\""}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.372598Z", "completed_at": "2024-04-18T23:37:04.389760Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.394172Z", "completed_at": "2024-04-18T23:37:04.394175Z"}], "thread_id": "Thread-3", "execution_time": 0.039843082427978516, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.jira_source.stg_jira__field_option_tmp", "compiled": true, "compiled_code": "select * \nfrom \"postgres\".\"jira_source_integration_tests_302\".\"field_option\"", "relation_name": "\"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__field_option_tmp\""}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.375205Z", "completed_at": "2024-04-18T23:37:04.390032Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.394434Z", "completed_at": "2024-04-18T23:37:04.394437Z"}], "thread_id": "Thread-4", "execution_time": 0.039727210998535156, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.jira_source.stg_jira__field_tmp", "compiled": true, "compiled_code": "select * \nfrom \"postgres\".\"jira_source_integration_tests_302\".\"field\"", "relation_name": "\"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__field_tmp\""}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.380534Z", "completed_at": "2024-04-18T23:37:04.390614Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.395177Z", "completed_at": "2024-04-18T23:37:04.395180Z"}], "thread_id": "Thread-6", "execution_time": 0.03912091255187988, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.jira_source.stg_jira__issue_link_tmp", "compiled": true, "compiled_code": "select * \nfrom \"postgres\".\"jira_source_integration_tests_302\".\"issue_link\"", "relation_name": "\"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__issue_link_tmp\""}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.383135Z", "completed_at": "2024-04-18T23:37:04.390911Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.395440Z", "completed_at": "2024-04-18T23:37:04.395443Z"}], "thread_id": "Thread-7", "execution_time": 0.03240013122558594, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.jira_source.stg_jira__issue_multiselect_history_tmp", "compiled": true, "compiled_code": "select * \nfrom \"postgres\".\"jira_source_integration_tests_302\".\"issue_multiselect_history\"", "relation_name": "\"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__issue_multiselect_history_tmp\""}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.385611Z", "completed_at": "2024-04-18T23:37:04.391702Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.396533Z", "completed_at": "2024-04-18T23:37:04.396536Z"}], "thread_id": "Thread-8", "execution_time": 0.033084869384765625, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.jira_source.stg_jira__issue_tmp", "compiled": true, "compiled_code": "select * \nfrom \"postgres\".\"jira_source_integration_tests_302\".\"issue\"", "relation_name": "\"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__issue_tmp\""}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.404361Z", "completed_at": "2024-04-18T23:37:04.418602Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.428532Z", "completed_at": "2024-04-18T23:37:04.428536Z"}], "thread_id": "Thread-1", "execution_time": 0.031536102294921875, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.jira_source.stg_jira__issue_type_tmp", "compiled": true, "compiled_code": "select * \nfrom \"postgres\".\"jira_source_integration_tests_302\".\"issue_type\"", "relation_name": "\"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__issue_type_tmp\""}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.407932Z", "completed_at": "2024-04-18T23:37:04.423328Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.429294Z", "completed_at": "2024-04-18T23:37:04.429298Z"}], "thread_id": "Thread-2", "execution_time": 0.030769824981689453, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.jira_source.stg_jira__priority_tmp", "compiled": true, "compiled_code": "\n\nselect * from \"postgres\".\"jira_source_integration_tests_302\".\"priority\"", "relation_name": "\"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__priority_tmp\""}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.410916Z", "completed_at": "2024-04-18T23:37:04.426007Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.430002Z", "completed_at": "2024-04-18T23:37:04.430005Z"}], "thread_id": "Thread-5", "execution_time": 0.030587196350097656, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.jira_source.stg_jira__project_tmp", "compiled": true, "compiled_code": "select * \nfrom \"postgres\".\"jira_source_integration_tests_302\".\"project\"", "relation_name": "\"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__project_tmp\""}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.413392Z", "completed_at": "2024-04-18T23:37:04.426292Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.430250Z", "completed_at": "2024-04-18T23:37:04.430253Z"}], "thread_id": "Thread-3", "execution_time": 0.03042912483215332, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.jira_source.stg_jira__resolution_tmp", "compiled": true, "compiled_code": "select * \nfrom \"postgres\".\"jira_source_integration_tests_302\".\"resolution\"", "relation_name": "\"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__resolution_tmp\""}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.415586Z", "completed_at": "2024-04-18T23:37:04.426867Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.431015Z", "completed_at": "2024-04-18T23:37:04.431018Z"}], "thread_id": "Thread-4", "execution_time": 0.03077983856201172, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.jira_source.stg_jira__sprint_tmp", "compiled": true, "compiled_code": "\n\nselect * \nfrom \"postgres\".\"jira_source_integration_tests_302\".\"sprint\"", "relation_name": "\"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__sprint_tmp\""}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.418906Z", "completed_at": "2024-04-18T23:37:04.427431Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.431981Z", "completed_at": "2024-04-18T23:37:04.431984Z"}], "thread_id": "Thread-6", "execution_time": 0.03131699562072754, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.jira_source.stg_jira__status_category_tmp", "compiled": true, "compiled_code": "select * \nfrom \"postgres\".\"jira_source_integration_tests_302\".\"status_category\"", "relation_name": "\"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__status_category_tmp\""}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.421113Z", "completed_at": "2024-04-18T23:37:04.427760Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.432240Z", "completed_at": "2024-04-18T23:37:04.432243Z"}], "thread_id": "Thread-7", "execution_time": 0.03152608871459961, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.jira_source.stg_jira__status_tmp", "compiled": true, "compiled_code": "select * \nfrom \"postgres\".\"jira_source_integration_tests_302\".\"status\"", "relation_name": "\"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__status_tmp\""}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.423610Z", "completed_at": "2024-04-18T23:37:04.429015Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.434228Z", "completed_at": "2024-04-18T23:37:04.434231Z"}], "thread_id": "Thread-8", "execution_time": 0.03009819984436035, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.jira_source.stg_jira__user_tmp", "compiled": true, "compiled_code": "select * \nfrom \"postgres\".\"jira_source_integration_tests_302\".\"user\"", "relation_name": "\"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__user_tmp\""}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.444750Z", "completed_at": "2024-04-18T23:37:04.445823Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.456804Z", "completed_at": "2024-04-18T23:37:04.456808Z"}], "thread_id": "Thread-2", "execution_time": 0.02126002311706543, "adapter_response": {}, "message": null, "failures": null, "unique_id": "seed.jira_source_integration_tests.comment", "compiled": null, "compiled_code": null, "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.446520Z", "completed_at": "2024-04-18T23:37:04.448222Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.457723Z", "completed_at": "2024-04-18T23:37:04.457726Z"}], "thread_id": "Thread-5", "execution_time": 0.021569013595581055, "adapter_response": {}, "message": null, "failures": null, "unique_id": "seed.jira_source_integration_tests.component", "compiled": null, "compiled_code": null, "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.448498Z", "completed_at": "2024-04-18T23:37:04.449478Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.457971Z", "completed_at": "2024-04-18T23:37:04.457974Z"}], "thread_id": "Thread-3", "execution_time": 0.0216519832611084, "adapter_response": {}, "message": null, "failures": null, "unique_id": "seed.jira_source_integration_tests.epic", "compiled": null, "compiled_code": null, "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.449786Z", "completed_at": "2024-04-18T23:37:04.450800Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.458223Z", "completed_at": "2024-04-18T23:37:04.458226Z"}], "thread_id": "Thread-4", "execution_time": 0.021528959274291992, "adapter_response": {}, "message": null, "failures": null, "unique_id": "seed.jira_source_integration_tests.field", "compiled": null, "compiled_code": null, "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.451549Z", "completed_at": "2024-04-18T23:37:04.452544Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.459164Z", "completed_at": "2024-04-18T23:37:04.459167Z"}], "thread_id": "Thread-6", "execution_time": 0.021633148193359375, "adapter_response": {}, "message": null, "failures": null, "unique_id": "seed.jira_source_integration_tests.field_option", "compiled": null, "compiled_code": null, "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.452844Z", "completed_at": "2024-04-18T23:37:04.453816Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.459419Z", "completed_at": "2024-04-18T23:37:04.459423Z"}], "thread_id": "Thread-7", "execution_time": 0.021782875061035156, "adapter_response": {}, "message": null, "failures": null, "unique_id": "seed.jira_source_integration_tests.issue", "compiled": null, "compiled_code": null, "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.441512Z", "completed_at": "2024-04-18T23:37:04.454058Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.459676Z", "completed_at": "2024-04-18T23:37:04.459678Z"}], "thread_id": "Thread-1", "execution_time": 0.026563167572021484, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.jira_source.stg_jira__version_tmp", "compiled": true, "compiled_code": "\n\nselect * \nfrom \"postgres\".\"jira_source_integration_tests_302\".\"version\"", "relation_name": "\"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__version_tmp\""}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.454960Z", "completed_at": "2024-04-18T23:37:04.456546Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.461317Z", "completed_at": "2024-04-18T23:37:04.461320Z"}], "thread_id": "Thread-8", "execution_time": 0.019868135452270508, "adapter_response": {}, "message": null, "failures": null, "unique_id": "seed.jira_source_integration_tests.issue_field_history", "compiled": null, "compiled_code": null, "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.469209Z", "completed_at": "2024-04-18T23:37:04.470421Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.481874Z", "completed_at": "2024-04-18T23:37:04.481879Z"}], "thread_id": "Thread-2", "execution_time": 0.01935291290283203, "adapter_response": {}, "message": null, "failures": null, "unique_id": "seed.jira_source_integration_tests.issue_link", "compiled": null, "compiled_code": null, "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.471309Z", "completed_at": "2024-04-18T23:37:04.472308Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.482753Z", "completed_at": "2024-04-18T23:37:04.482756Z"}], "thread_id": "Thread-5", "execution_time": 0.019479036331176758, "adapter_response": {}, "message": null, "failures": null, "unique_id": "seed.jira_source_integration_tests.issue_multiselect_history", "compiled": null, "compiled_code": null, "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.472571Z", "completed_at": "2024-04-18T23:37:04.473548Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.483017Z", "completed_at": "2024-04-18T23:37:04.483020Z"}], "thread_id": "Thread-3", "execution_time": 0.019516944885253906, "adapter_response": {}, "message": null, "failures": null, "unique_id": "seed.jira_source_integration_tests.issue_type", "compiled": null, "compiled_code": null, "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.473805Z", "completed_at": "2024-04-18T23:37:04.474777Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.483268Z", "completed_at": "2024-04-18T23:37:04.483271Z"}], "thread_id": "Thread-4", "execution_time": 0.01961207389831543, "adapter_response": {}, "message": null, "failures": null, "unique_id": "seed.jira_source_integration_tests.priority", "compiled": null, "compiled_code": null, "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.475451Z", "completed_at": "2024-04-18T23:37:04.476468Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.484225Z", "completed_at": "2024-04-18T23:37:04.484229Z"}], "thread_id": "Thread-6", "execution_time": 0.019778728485107422, "adapter_response": {}, "message": null, "failures": null, "unique_id": "seed.jira_source_integration_tests.project", "compiled": null, "compiled_code": null, "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.476731Z", "completed_at": "2024-04-18T23:37:04.478401Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.484465Z", "completed_at": "2024-04-18T23:37:04.484468Z"}], "thread_id": "Thread-7", "execution_time": 0.019958019256591797, "adapter_response": {}, "message": null, "failures": null, "unique_id": "seed.jira_source_integration_tests.project_board", "compiled": null, "compiled_code": null, "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.478673Z", "completed_at": "2024-04-18T23:37:04.479690Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.484706Z", "completed_at": "2024-04-18T23:37:04.484709Z"}], "thread_id": "Thread-1", "execution_time": 0.020098209381103516, "adapter_response": {}, "message": null, "failures": null, "unique_id": "seed.jira_source_integration_tests.project_category", "compiled": null, "compiled_code": null, "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.480594Z", "completed_at": "2024-04-18T23:37:04.481589Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.486335Z", "completed_at": "2024-04-18T23:37:04.486338Z"}], "thread_id": "Thread-8", "execution_time": 0.020341157913208008, "adapter_response": {}, "message": null, "failures": null, "unique_id": "seed.jira_source_integration_tests.resolution", "compiled": null, "compiled_code": null, "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.494079Z", "completed_at": "2024-04-18T23:37:04.495294Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.514801Z", "completed_at": "2024-04-18T23:37:04.514806Z"}], "thread_id": "Thread-2", "execution_time": 0.027379989624023438, "adapter_response": {}, "message": null, "failures": null, "unique_id": "seed.jira_source_integration_tests.sprint", "compiled": null, "compiled_code": null, "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.496197Z", "completed_at": "2024-04-18T23:37:04.497214Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.515496Z", "completed_at": "2024-04-18T23:37:04.515499Z"}], "thread_id": "Thread-5", "execution_time": 0.02726292610168457, "adapter_response": {}, "message": null, "failures": null, "unique_id": "seed.jira_source_integration_tests.status", "compiled": null, "compiled_code": null, "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.497470Z", "completed_at": "2024-04-18T23:37:04.498452Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.515966Z", "completed_at": "2024-04-18T23:37:04.515970Z"}], "thread_id": "Thread-3", "execution_time": 0.02756214141845703, "adapter_response": {}, "message": null, "failures": null, "unique_id": "seed.jira_source_integration_tests.status_category", "compiled": null, "compiled_code": null, "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.498703Z", "completed_at": "2024-04-18T23:37:04.500412Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.516251Z", "completed_at": "2024-04-18T23:37:04.516254Z"}], "thread_id": "Thread-4", "execution_time": 0.027675151824951172, "adapter_response": {}, "message": null, "failures": null, "unique_id": "seed.jira_source_integration_tests.user", "compiled": null, "compiled_code": null, "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.501087Z", "completed_at": "2024-04-18T23:37:04.502072Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.517206Z", "completed_at": "2024-04-18T23:37:04.517210Z"}], "thread_id": "Thread-6", "execution_time": 0.027776718139648438, "adapter_response": {}, "message": null, "failures": null, "unique_id": "seed.jira_source_integration_tests.user_group", "compiled": null, "compiled_code": null, "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.502327Z", "completed_at": "2024-04-18T23:37:04.503297Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:04.517452Z", "completed_at": "2024-04-18T23:37:04.517455Z"}], "thread_id": "Thread-7", "execution_time": 0.027937889099121094, "adapter_response": {}, "message": null, "failures": null, "unique_id": "seed.jira_source_integration_tests.version", "compiled": null, "compiled_code": null, "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.510404Z", "completed_at": "2024-04-18T23:37:05.089718Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.090481Z", "completed_at": "2024-04-18T23:37:05.090490Z"}], "thread_id": "Thread-8", "execution_time": 0.6451327800750732, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.jira_source.stg_jira__component", "compiled": true, "compiled_code": "\n\nwith base as (\n\n    select * \n    from \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__component_tmp\"\n),\n\nfields as (\n\n    select\n        \n    \n    \n    _fivetran_synced\n    \n as \n    \n    _fivetran_synced\n    \n, \n    \n    \n    description\n    \n as \n    \n    description\n    \n, \n    \n    \n    id\n    \n as \n    \n    id\n    \n, \n    \n    \n    name\n    \n as \n    \n    name\n    \n, \n    \n    \n    project_id\n    \n as \n    \n    project_id\n    \n\n\n\n    from base\n),\n\nfinal as (\n    \n    select \n        description as component_description,\n        id as component_id,\n        name as component_name,\n        project_id,\n        _fivetran_synced\n    from fields\n)\n\nselect * \nfrom final", "relation_name": "\"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__component\""}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.565119Z", "completed_at": "2024-04-18T23:37:05.165039Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.185105Z", "completed_at": "2024-04-18T23:37:05.185114Z"}], "thread_id": "Thread-4", "execution_time": 0.7454409599304199, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.jira_source.stg_jira__issue_link", "compiled": true, "compiled_code": "with base as (\n\n    select * \n    from \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__issue_link_tmp\"\n),\n\nfields as (\n\n    select\n        \n    \n    \n    _fivetran_synced\n    \n as \n    \n    _fivetran_synced\n    \n, \n    \n    \n    issue_id\n    \n as \n    \n    issue_id\n    \n, \n    \n    \n    related_issue_id\n    \n as \n    \n    related_issue_id\n    \n, \n    \n    \n    relationship\n    \n as \n    \n    relationship\n    \n\n\n\n    from base\n),\n\nfinal as (\n    \n    select \n        issue_id,\n        related_issue_id,\n        relationship,\n        _fivetran_synced \n    from fields\n)\n\nselect * \nfrom final", "relation_name": "\"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__issue_link\""}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.573635Z", "completed_at": "2024-04-18T23:37:05.217572Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.220176Z", "completed_at": "2024-04-18T23:37:05.220182Z"}], "thread_id": "Thread-7", "execution_time": 0.7463040351867676, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.jira_source.stg_jira__issue", "compiled": true, "compiled_code": "with base as (\n    \n    select * \n    from \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__issue_tmp\"\n    where not coalesce(_fivetran_deleted, false)\n),\n\nfields as (\n\n    select \n        \n    \n    \n    _fivetran_deleted\n    \n as \n    \n    _fivetran_deleted\n    \n, \n    \n    \n    _fivetran_synced\n    \n as \n    \n    _fivetran_synced\n    \n, \n    \n    \n    _original_estimate\n    \n as \n    \n    _original_estimate\n    \n, \n    \n    \n    _remaining_estimate\n    \n as \n    \n    _remaining_estimate\n    \n, \n    \n    \n    _time_spent\n    \n as \n    \n    _time_spent\n    \n, \n    \n    \n    assignee\n    \n as \n    \n    assignee\n    \n, \n    \n    \n    created\n    \n as \n    \n    created\n    \n, \n    \n    \n    creator\n    \n as \n    \n    creator\n    \n, \n    \n    \n    description\n    \n as \n    \n    description\n    \n, \n    \n    \n    due_date\n    \n as \n    \n    due_date\n    \n, \n    \n    \n    environment\n    \n as \n    \n    environment\n    \n, \n    \n    \n    id\n    \n as \n    \n    id\n    \n, \n    \n    \n    issue_type\n    \n as \n    \n    issue_type\n    \n, \n    \n    \n    key\n    \n as \n    \n    key\n    \n, \n    \n    \n    original_estimate\n    \n as \n    \n    original_estimate\n    \n, \n    \n    \n    parent_id\n    \n as \n    \n    parent_id\n    \n, \n    \n    \n    priority\n    \n as \n    \n    priority\n    \n, \n    \n    \n    project\n    \n as \n    \n    project\n    \n, \n    \n    \n    remaining_estimate\n    \n as \n    \n    remaining_estimate\n    \n, \n    \n    \n    reporter\n    \n as \n    \n    reporter\n    \n, \n    \n    \n    resolution\n    \n as \n    \n    resolution\n    \n, \n    \n    \n    resolved\n    \n as \n    \n    resolved\n    \n, \n    \n    \n    status\n    \n as \n    \n    status\n    \n, \n    \n    \n    status_category_changed\n    \n as \n    \n    status_category_changed\n    \n, \n    \n    \n    summary\n    \n as \n    \n    summary\n    \n, \n    \n    \n    time_spent\n    \n as \n    \n    time_spent\n    \n, \n    \n    \n    updated\n    \n as \n    \n    updated\n    \n, \n    \n    \n    work_ratio\n    \n as \n    \n    work_ratio\n    \n\n\n\n    from base\n),\n\nfinal as (\n\n    select\n        coalesce(original_estimate, _original_estimate) as original_estimate_seconds,\n        coalesce(remaining_estimate, _remaining_estimate) as remaining_estimate_seconds,\n        coalesce(time_spent, _time_spent) as time_spent_seconds,\n        assignee as assignee_user_id,\n        cast(created as timestamp) as created_at,\n        cast(resolved  as timestamp) as resolved_at,\n        creator as creator_user_id,\n        description as issue_description,\n        due_date,\n        environment,\n        id as issue_id,\n        issue_type as issue_type_id,\n        key as issue_key,\n        parent_id as parent_issue_id,\n        priority as priority_id,\n        project as project_id,\n        reporter as reporter_user_id,\n        resolution as resolution_id,\n        status as status_id,\n        cast(status_category_changed as timestamp) as status_changed_at,\n        summary as issue_name,\n        cast(updated as timestamp) as updated_at,\n        work_ratio,\n        _fivetran_synced\n    from fields\n)\n\nselect * \nfrom final", "relation_name": "\"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__issue\""}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.525155Z", "completed_at": "2024-04-18T23:37:05.218366Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.221136Z", "completed_at": "2024-04-18T23:37:05.221139Z"}], "thread_id": "Thread-2", "execution_time": 0.7575678825378418, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.jira_source.stg_jira__issue_field_history", "compiled": true, "compiled_code": "with base as (\n\n    select * \n    from \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__issue_field_history_tmp\"\n),\n\nfields as (\n\n    select\n        \n    \n    \n    _fivetran_synced\n    \n as \n    \n    _fivetran_synced\n    \n, \n    \n    \n    field_id\n    \n as \n    \n    field_id\n    \n, \n    \n    \n    issue_id\n    \n as \n    \n    issue_id\n    \n, \n    \n    \n    value\n    \n as \n    \n    value\n    \n, \n    \n    \n    time\n    \n as \n    \n    time\n    \n\n\n\n    from base\n),\n\nfinal as (\n    \n    select \n        cast(field_id as TEXT) as field_id,\n        issue_id,\n        cast(time as timestamp)\n         as updated_at,\n        value as field_value,\n        _fivetran_synced\n    from fields\n)\n\nselect * \nfrom final", "relation_name": "\"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__issue_field_history\""}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.503565Z", "completed_at": "2024-04-18T23:37:05.217870Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.220676Z", "completed_at": "2024-04-18T23:37:05.220679Z"}], "thread_id": "Thread-1", "execution_time": 0.787229061126709, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.jira_source.stg_jira__comment", "compiled": true, "compiled_code": "with base as (\n\n    select * \n    from \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__comment_tmp\"\n),\n\nfields as (\n\n    select\n        \n    \n    \n    _fivetran_synced\n    \n as \n    \n    _fivetran_synced\n    \n, \n    \n    \n    author_id\n    \n as \n    \n    author_id\n    \n, \n    \n    \n    body\n    \n as \n    \n    body\n    \n, \n    \n    \n    created\n    \n as \n    \n    created\n    \n, \n    \n    \n    id\n    \n as \n    \n    id\n    \n, \n    \n    \n    is_public\n    \n as \n    \n    is_public\n    \n, \n    \n    \n    issue_id\n    \n as \n    \n    issue_id\n    \n, \n    \n    \n    update_author_id\n    \n as \n    \n    update_author_id\n    \n, \n    \n    \n    updated\n    \n as \n    \n    updated\n    \n\n\n\n    from base\n),\n\nfinal as (\n    \n    select \n        author_id as author_user_id,\n        body,\n        cast(created as timestamp) as created_at,\n        id as comment_id,\n        issue_id,\n        is_public,\n        update_author_id as last_update_user_id,\n        cast(updated as timestamp) as last_updated_at,\n        _fivetran_synced\n    from fields\n)\n\nselect * \nfrom final", "relation_name": "\"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__comment\""}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.568916Z", "completed_at": "2024-04-18T23:37:05.219429Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.222305Z", "completed_at": "2024-04-18T23:37:05.222308Z"}], "thread_id": "Thread-6", "execution_time": 0.7568681240081787, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.jira_source.stg_jira__issue_multiselect_history", "compiled": true, "compiled_code": "with base as (\n\n    select * \n    from \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__issue_multiselect_history_tmp\"\n),\n\nfields as (\n\n    select\n        \n    \n    \n    _fivetran_id\n    \n as \n    \n    _fivetran_id\n    \n, \n    \n    \n    _fivetran_synced\n    \n as \n    \n    _fivetran_synced\n    \n, \n    \n    \n    field_id\n    \n as \n    \n    field_id\n    \n, \n    \n    \n    issue_id\n    \n as \n    \n    issue_id\n    \n, \n    \n    \n    value\n    \n as \n    \n    value\n    \n, \n    \n    \n    time\n    \n as \n    \n    time\n    \n\n\n\n    from base\n),\n\nfinal as (\n    \n    select \n        _fivetran_id,\n        cast(field_id as TEXT) as field_id,\n        issue_id,\n        \n        cast(time as timestamp)\n         as updated_at,\n        value as field_value,\n        _fivetran_synced\n    from fields\n)\n\nselect * \nfrom final", "relation_name": "\"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__issue_multiselect_history\""}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.534303Z", "completed_at": "2024-04-18T23:37:05.218937Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.221826Z", "completed_at": "2024-04-18T23:37:05.221830Z"}], "thread_id": "Thread-3", "execution_time": 0.7586450576782227, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.jira_source.stg_jira__field", "compiled": true, "compiled_code": "with base as (\n\n    select * \n    from \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__field_tmp\"\n),\n\nfields as (\n\n    select\n        \n    \n    \n    _fivetran_synced\n    \n as \n    \n    _fivetran_synced\n    \n, \n    \n    \n    id\n    \n as \n    \n    id\n    \n, \n    \n    \n    is_array\n    \n as \n    \n    is_array\n    \n, \n    \n    \n    is_custom\n    \n as \n    \n    is_custom\n    \n, \n    \n    \n    name\n    \n as \n    \n    name\n    \n\n\n\n    from base\n),\n\nfinal as (\n    \n    select \n        cast(id as TEXT) as field_id,\n        is_array,\n        is_custom,\n        name as field_name,\n        _fivetran_synced\n    from fields\n)\n\nselect * \nfrom final", "relation_name": "\"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__field\""}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:04.530235Z", "completed_at": "2024-04-18T23:37:05.219918Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.222780Z", "completed_at": "2024-04-18T23:37:05.222782Z"}], "thread_id": "Thread-5", "execution_time": 0.7595460414886475, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.jira_source.stg_jira__field_option", "compiled": true, "compiled_code": "with base as (\n\n    select * \n    from \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__field_option_tmp\"\n),\n\nfields as (\n\n    select\n        \n    \n    \n    id\n    \n as \n    \n    id\n    \n, \n    cast(null as integer) as \n    \n    parent_id\n    \n , \n    \n    \n    _fivetran_synced\n    \n as \n    \n    _fivetran_synced\n    \n, \n    \n    \n    name\n    \n as \n    \n    name\n    \n\n\n\n    from base\n),\n\nfinal as (\n    \n    select \n        id as field_id,\n        parent_id as parent_field_id,\n        name as field_option_name\n    from fields\n)\n\nselect * \nfrom final", "relation_name": "\"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__field_option\""}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:05.141138Z", "completed_at": "2024-04-18T23:37:05.602758Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.603797Z", "completed_at": "2024-04-18T23:37:05.603809Z"}], "thread_id": "Thread-8", "execution_time": 0.5122230052947998, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.jira_source.stg_jira__issue_type", "compiled": true, "compiled_code": "with base as (\n\n    select * from \n    \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__issue_type_tmp\"\n),\n\nfields as (\n\n    select \n        \n    \n    \n    _fivetran_synced\n    \n as \n    \n    _fivetran_synced\n    \n, \n    \n    \n    description\n    \n as \n    \n    description\n    \n, \n    \n    \n    id\n    \n as \n    \n    id\n    \n, \n    \n    \n    name\n    \n as \n    \n    name\n    \n, \n    \n    \n    subtask\n    \n as \n    \n    subtask\n    \n\n\n\n    from base\n),\n\nfinal as (\n\n    select\n        description,\n        id as issue_type_id,\n        name as issue_type_name,\n        subtask as is_subtask,\n        _fivetran_synced\n    from fields\n)\n\nselect * \nfrom final", "relation_name": "\"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__issue_type\""}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:05.271730Z", "completed_at": "2024-04-18T23:37:05.736200Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.737079Z", "completed_at": "2024-04-18T23:37:05.737089Z"}], "thread_id": "Thread-4", "execution_time": 0.5353028774261475, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.jira_source.stg_jira__priority", "compiled": true, "compiled_code": "\n\nwith base as (\n\n    select * \n    from \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__priority_tmp\"\n),\n\nfields as (\n\n    select\n        \n    \n    \n    _fivetran_synced\n    \n as \n    \n    _fivetran_synced\n    \n, \n    \n    \n    description\n    \n as \n    \n    description\n    \n, \n    \n    \n    id\n    \n as \n    \n    id\n    \n, \n    \n    \n    name\n    \n as \n    \n    name\n    \n\n\n\n    from base\n),\n\nfinal as (\n    \n    select \n        description as priority_description,\n        id as priority_id,\n        name as priority_name,\n        _fivetran_synced\n    from fields\n)\n\nselect * \nfrom final", "relation_name": "\"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__priority\""}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:05.842190Z", "completed_at": "2024-04-18T23:37:05.853483Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.854556Z", "completed_at": "2024-04-18T23:37:05.854559Z"}], "thread_id": "Thread-4", "execution_time": 0.01699686050415039, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.jira_source.not_null_stg_jira__component_component_id.2f017ad5ad", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect component_id\nfrom \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__component\"\nwhere component_id is null\n\n\n", "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:05.856314Z", "completed_at": "2024-04-18T23:37:05.860757Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.861274Z", "completed_at": "2024-04-18T23:37:05.861278Z"}], "thread_id": "Thread-4", "execution_time": 0.006023883819580078, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.jira_source.unique_stg_jira__component_component_id.1773ebe913", "compiled": true, "compiled_code": "\n    \n    \n\nselect\n    component_id as unique_field,\n    count(*) as n_records\n\nfrom \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__component\"\nwhere component_id is not null\ngroup by component_id\nhaving count(*) > 1\n\n\n", "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:05.862914Z", "completed_at": "2024-04-18T23:37:05.866013Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.866494Z", "completed_at": "2024-04-18T23:37:05.866498Z"}], "thread_id": "Thread-4", "execution_time": 0.004594087600708008, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.jira_source.not_null_stg_jira__issue_issue_id.13a4fbe132", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect issue_id\nfrom \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__issue\"\nwhere issue_id is null\n\n\n", "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:05.868051Z", "completed_at": "2024-04-18T23:37:05.871414Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.871892Z", "completed_at": "2024-04-18T23:37:05.871896Z"}], "thread_id": "Thread-4", "execution_time": 0.004833221435546875, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.jira_source.unique_stg_jira__issue_issue_id.7bb0ee7230", "compiled": true, "compiled_code": "\n    \n    \n\nselect\n    issue_id as unique_field,\n    count(*) as n_records\n\nfrom \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__issue\"\nwhere issue_id is not null\ngroup by issue_id\nhaving count(*) > 1\n\n\n", "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:05.873445Z", "completed_at": "2024-04-18T23:37:05.880034Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.880539Z", "completed_at": "2024-04-18T23:37:05.880546Z"}], "thread_id": "Thread-4", "execution_time": 0.008096933364868164, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.jira_source.dbt_utils_unique_combination_of_columns_stg_jira__issue_field_history_field_id__issue_id__updated_at.c01ecbb82c", "compiled": true, "compiled_code": "\n\n\n\n\n\nwith validation_errors as (\n\n    select\n        field_id, issue_id, updated_at\n    from \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__issue_field_history\"\n    group by field_id, issue_id, updated_at\n    having count(*) > 1\n\n)\n\nselect *\nfrom validation_errors\n\n\n", "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:05.882122Z", "completed_at": "2024-04-18T23:37:05.884980Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.885446Z", "completed_at": "2024-04-18T23:37:05.885450Z"}], "thread_id": "Thread-4", "execution_time": 0.004306793212890625, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.jira_source.not_null_stg_jira__comment_comment_id.5dec28a22a", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect comment_id\nfrom \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__comment\"\nwhere comment_id is null\n\n\n", "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:05.886986Z", "completed_at": "2024-04-18T23:37:05.889800Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.890264Z", "completed_at": "2024-04-18T23:37:05.890267Z"}], "thread_id": "Thread-4", "execution_time": 0.004236936569213867, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.jira_source.unique_stg_jira__comment_comment_id.004104fac5", "compiled": true, "compiled_code": "\n    \n    \n\nselect\n    comment_id as unique_field,\n    count(*) as n_records\n\nfrom \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__comment\"\nwhere comment_id is not null\ngroup by comment_id\nhaving count(*) > 1\n\n\n", "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:05.283712Z", "completed_at": "2024-04-18T23:37:05.837032Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.841368Z", "completed_at": "2024-04-18T23:37:05.841373Z"}], "thread_id": "Thread-7", "execution_time": 0.617551326751709, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.jira_source.stg_jira__project", "compiled": true, "compiled_code": "with base as (\n    \n    select *\n    from \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__project_tmp\"\n),\n\nfields as (\n\n    select\n        \n    \n    \n    _fivetran_synced\n    \n as \n    \n    _fivetran_synced\n    \n, \n    \n    \n    description\n    \n as \n    \n    description\n    \n, \n    \n    \n    id\n    \n as \n    \n    id\n    \n, \n    \n    \n    key\n    \n as \n    \n    key\n    \n, \n    \n    \n    lead_id\n    \n as \n    \n    lead_id\n    \n, \n    \n    \n    name\n    \n as \n    \n    name\n    \n, \n    \n    \n    permission_scheme_id\n    \n as \n    \n    permission_scheme_id\n    \n, \n    \n    \n    project_category_id\n    \n as \n    \n    project_category_id\n    \n\n\n\n    from base\n\n),\n\nfinal as (\n\n    select \n        description as project_description,\n        id as project_id,\n        key as project_key,\n        lead_id as project_lead_user_id,\n        name as project_name,\n        project_category_id,\n        permission_scheme_id,\n        _fivetran_synced\n    from fields\n)\n\nselect * \nfrom final", "relation_name": "\"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__project\""}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:05.891802Z", "completed_at": "2024-04-18T23:37:05.895381Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.897073Z", "completed_at": "2024-04-18T23:37:05.897077Z"}], "thread_id": "Thread-4", "execution_time": 0.006844043731689453, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.jira_source.dbt_utils_unique_combination_of_columns_stg_jira__issue_multiselect_history__fivetran_id__updated_at.4dd2d1c5a8", "compiled": true, "compiled_code": "\n\n\n\n\n\nwith validation_errors as (\n\n    select\n        _fivetran_id, updated_at\n    from \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__issue_multiselect_history\"\n    group by _fivetran_id, updated_at\n    having count(*) > 1\n\n)\n\nselect *\nfrom validation_errors\n\n\n", "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:05.301757Z", "completed_at": "2024-04-18T23:37:05.838959Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.851263Z", "completed_at": "2024-04-18T23:37:05.851267Z"}], "thread_id": "Thread-6", "execution_time": 0.6083462238311768, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.jira_source.stg_jira__status_category", "compiled": true, "compiled_code": "with base as (\n\n    select * \n    from \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__status_category_tmp\"\n),\n\nfields as (\n\n    select\n        \n    \n    \n    _fivetran_synced\n    \n as \n    \n    _fivetran_synced\n    \n, \n    \n    \n    id\n    \n as \n    \n    id\n    \n, \n    \n    \n    name\n    \n as \n    \n    name\n    \n\n\n\n    from base\n),\n\nfinal as (\n    \n    select \n        id as status_category_id,\n        name as status_category_name\n    from fields\n)\n\nselect * \nfrom final", "relation_name": "\"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__status_category\""}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:05.292957Z", "completed_at": "2024-04-18T23:37:05.838561Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.850690Z", "completed_at": "2024-04-18T23:37:05.850696Z"}], "thread_id": "Thread-2", "execution_time": 0.6164999008178711, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.jira_source.stg_jira__resolution", "compiled": true, "compiled_code": "with base as (\n\n    select * \n    from \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__resolution_tmp\"\n),\n\nfields as (\n\n    select\n        \n    \n    \n    _fivetran_synced\n    \n as \n    \n    _fivetran_synced\n    \n, \n    \n    \n    description\n    \n as \n    \n    description\n    \n, \n    \n    \n    id\n    \n as \n    \n    id\n    \n, \n    \n    \n    name\n    \n as \n    \n    name\n    \n\n\n\n    from base\n),\n\nfinal as (\n    \n    select \n        description as resolution_description,\n        id as resolution_id,\n        name as resolution_name,\n        _fivetran_synced\n    from fields\n)\n\nselect * \nfrom final", "relation_name": "\"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__resolution\""}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:05.296953Z", "completed_at": "2024-04-18T23:37:05.840773Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.852658Z", "completed_at": "2024-04-18T23:37:05.852661Z"}], "thread_id": "Thread-1", "execution_time": 0.6167600154876709, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.jira_source.stg_jira__sprint", "compiled": true, "compiled_code": "\n\nwith base as (\n\n    select * \n    from \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__sprint_tmp\"\n),\n\nfields as (\n\n    select\n        \n    \n    \n    _fivetran_synced\n    \n as \n    \n    _fivetran_synced\n    \n, \n    \n    \n    board_id\n    \n as \n    \n    board_id\n    \n, \n    \n    \n    complete_date\n    \n as \n    \n    complete_date\n    \n, \n    \n    \n    end_date\n    \n as \n    \n    end_date\n    \n, \n    \n    \n    id\n    \n as \n    \n    id\n    \n, \n    \n    \n    name\n    \n as \n    \n    name\n    \n, \n    \n    \n    start_date\n    \n as \n    \n    start_date\n    \n\n\n\n    from base\n),\n\nfinal as (\n    \n    select \n        id as sprint_id,\n        name as sprint_name,\n        board_id,\n        cast(complete_date as timestamp) as completed_at,\n        cast(end_date as timestamp) as ended_at,\n        cast(start_date as timestamp) as started_at,\n        _fivetran_synced\n    from fields\n)\n\nselect * \nfrom final", "relation_name": "\"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__sprint\""}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:05.309592Z", "completed_at": "2024-04-18T23:37:05.839908Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.852105Z", "completed_at": "2024-04-18T23:37:05.852108Z"}], "thread_id": "Thread-5", "execution_time": 0.6157431602478027, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.jira_source.stg_jira__user", "compiled": true, "compiled_code": "with base as (\n\n    select * \n    from \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__user_tmp\"\n),\n\nfields as (\n\n    select\n        \n    \n    \n    _fivetran_synced\n    \n as \n    \n    _fivetran_synced\n    \n, \n    \n    \n    email\n    \n as \n    \n    email\n    \n, \n    \n    \n    id\n    \n as \n    \n    id\n    \n, \n    \n    \n    locale\n    \n as \n    \n    locale\n    \n, \n    \n    \n    name\n    \n as \n    \n    name\n    \n, \n    \n    \n    time_zone\n    \n as \n    \n    time_zone\n    \n, \n    \n    \n    username\n    \n as \n    \n    username\n    \n\n\n\n    from base\n),\n\nfinal as (\n\n    select \n        email,\n        id as user_id,\n        locale,\n        name as user_display_name,\n        time_zone,\n        username,\n        _fivetran_synced\n    from fields\n)\n\nselect * \nfrom final", "relation_name": "\"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__user\""}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:05.305491Z", "completed_at": "2024-04-18T23:37:05.839583Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.851813Z", "completed_at": "2024-04-18T23:37:05.851817Z"}], "thread_id": "Thread-3", "execution_time": 0.6165800094604492, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.jira_source.stg_jira__status", "compiled": true, "compiled_code": "with base as (\n\n    select * \n    from \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__status_tmp\"\n),\n\nfields as (\n\n    select\n        \n    \n    \n    _fivetran_synced\n    \n as \n    \n    _fivetran_synced\n    \n, \n    \n    \n    description\n    \n as \n    \n    description\n    \n, \n    \n    \n    id\n    \n as \n    \n    id\n    \n, \n    \n    \n    name\n    \n as \n    \n    name\n    \n, \n    \n    \n    status_category_id\n    \n as \n    \n    status_category_id\n    \n\n\n\n    from base\n),\n\nfinal as (\n\n    select\n        description as status_description,\n        id as status_id,\n        name as status_name,\n        status_category_id,\n        _fivetran_synced\n    from fields\n)\n\nselect * \nfrom final", "relation_name": "\"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__status\""}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:05.900515Z", "completed_at": "2024-04-18T23:37:05.911279Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.917640Z", "completed_at": "2024-04-18T23:37:05.917645Z"}], "thread_id": "Thread-7", "execution_time": 0.02451801300048828, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.jira_source.not_null_stg_jira__field_field_id.34424f1c2b", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect field_id\nfrom \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__field\"\nwhere field_id is null\n\n\n", "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:05.908404Z", "completed_at": "2024-04-18T23:37:05.916379Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.925209Z", "completed_at": "2024-04-18T23:37:05.925214Z"}], "thread_id": "Thread-4", "execution_time": 0.02814626693725586, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.jira_source.unique_stg_jira__field_field_id.df7b462fff", "compiled": true, "compiled_code": "\n    \n    \n\nselect\n    field_id as unique_field,\n    count(*) as n_records\n\nfrom \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__field\"\nwhere field_id is not null\ngroup by field_id\nhaving count(*) > 1\n\n\n", "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:05.912475Z", "completed_at": "2024-04-18T23:37:05.918148Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.931863Z", "completed_at": "2024-04-18T23:37:05.931867Z"}], "thread_id": "Thread-6", "execution_time": 0.029477834701538086, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.jira_source.not_null_stg_jira__field_option_field_id.1b1f37b358", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect field_id\nfrom \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__field_option\"\nwhere field_id is null\n\n\n", "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:05.919054Z", "completed_at": "2024-04-18T23:37:05.932205Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.936346Z", "completed_at": "2024-04-18T23:37:05.936349Z"}], "thread_id": "Thread-2", "execution_time": 0.025847196578979492, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.jira_source.not_null_stg_jira__issue_type_issue_type_id.57419fc343", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect issue_type_id\nfrom \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__issue_type\"\nwhere issue_type_id is null\n\n\n", "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:05.922325Z", "completed_at": "2024-04-18T23:37:05.932989Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.936979Z", "completed_at": "2024-04-18T23:37:05.936982Z"}], "thread_id": "Thread-1", "execution_time": 0.025864362716674805, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.jira_source.unique_stg_jira__issue_type_issue_type_id.a89d34aa41", "compiled": true, "compiled_code": "\n    \n    \n\nselect\n    issue_type_id as unique_field,\n    count(*) as n_records\n\nfrom \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__issue_type\"\nwhere issue_type_id is not null\ngroup by issue_type_id\nhaving count(*) > 1\n\n\n", "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:05.925468Z", "completed_at": "2024-04-18T23:37:05.933749Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.940038Z", "completed_at": "2024-04-18T23:37:05.940042Z"}], "thread_id": "Thread-5", "execution_time": 0.025722265243530273, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.jira_source.not_null_stg_jira__priority_priority_id.ec0c873363", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect priority_id\nfrom \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__priority\"\nwhere priority_id is null\n\n\n", "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:05.928918Z", "completed_at": "2024-04-18T23:37:05.934008Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.940313Z", "completed_at": "2024-04-18T23:37:05.940317Z"}], "thread_id": "Thread-3", "execution_time": 0.02583909034729004, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.jira_source.unique_stg_jira__priority_priority_id.79b77ea5d2", "compiled": true, "compiled_code": "\n    \n    \n\nselect\n    priority_id as unique_field,\n    count(*) as n_records\n\nfrom \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__priority\"\nwhere priority_id is not null\ngroup by priority_id\nhaving count(*) > 1\n\n\n", "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:05.937239Z", "completed_at": "2024-04-18T23:37:05.946396Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.952758Z", "completed_at": "2024-04-18T23:37:05.952763Z"}], "thread_id": "Thread-7", "execution_time": 0.024480819702148438, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.jira_source.not_null_stg_jira__project_project_id.996fe19522", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect project_id\nfrom \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__project\"\nwhere project_id is null\n\n\n", "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:05.943646Z", "completed_at": "2024-04-18T23:37:05.952220Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.966551Z", "completed_at": "2024-04-18T23:37:05.966557Z"}], "thread_id": "Thread-4", "execution_time": 0.03148913383483887, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.jira_source.unique_stg_jira__project_project_id.58d321d374", "compiled": true, "compiled_code": "\n    \n    \n\nselect\n    project_id as unique_field,\n    count(*) as n_records\n\nfrom \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__project\"\nwhere project_id is not null\ngroup by project_id\nhaving count(*) > 1\n\n\n", "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:05.947477Z", "completed_at": "2024-04-18T23:37:05.953257Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.967214Z", "completed_at": "2024-04-18T23:37:05.967218Z"}], "thread_id": "Thread-6", "execution_time": 0.028705120086669922, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.jira_source.not_null_stg_jira__status_category_status_category_id.7a89cdfcf0", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect status_category_id\nfrom \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__status_category\"\nwhere status_category_id is null\n\n\n", "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:05.958135Z", "completed_at": "2024-04-18T23:37:05.967651Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.971564Z", "completed_at": "2024-04-18T23:37:05.971568Z"}], "thread_id": "Thread-1", "execution_time": 0.025313138961791992, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.jira_source.not_null_stg_jira__resolution_resolution_id.1c04bac8a4", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect resolution_id\nfrom \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__resolution\"\nwhere resolution_id is null\n\n\n", "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:05.954164Z", "completed_at": "2024-04-18T23:37:05.968413Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.972427Z", "completed_at": "2024-04-18T23:37:05.972431Z"}], "thread_id": "Thread-2", "execution_time": 0.02698993682861328, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.jira_source.unique_stg_jira__status_category_status_category_id.99c869330a", "compiled": true, "compiled_code": "\n    \n    \n\nselect\n    status_category_id as unique_field,\n    count(*) as n_records\n\nfrom \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__status_category\"\nwhere status_category_id is not null\ngroup by status_category_id\nhaving count(*) > 1\n\n\n", "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:05.961130Z", "completed_at": "2024-04-18T23:37:05.969589Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.975924Z", "completed_at": "2024-04-18T23:37:05.975928Z"}], "thread_id": "Thread-5", "execution_time": 0.02666187286376953, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.jira_source.unique_stg_jira__resolution_resolution_id.b3acb37c87", "compiled": true, "compiled_code": "\n    \n    \n\nselect\n    resolution_id as unique_field,\n    count(*) as n_records\n\nfrom \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__resolution\"\nwhere resolution_id is not null\ngroup by resolution_id\nhaving count(*) > 1\n\n\n", "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:05.963865Z", "completed_at": "2024-04-18T23:37:05.970084Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:05.976696Z", "completed_at": "2024-04-18T23:37:05.976700Z"}], "thread_id": "Thread-3", "execution_time": 0.027033090591430664, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.jira_source.not_null_stg_jira__sprint_sprint_id.899b4b77d7", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect sprint_id\nfrom \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__sprint\"\nwhere sprint_id is null\n\n\n", "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:05.972693Z", "completed_at": "2024-04-18T23:37:05.981902Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:06.022262Z", "completed_at": "2024-04-18T23:37:06.022268Z"}], "thread_id": "Thread-7", "execution_time": 0.057470083236694336, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.jira_source.not_null_stg_jira__user_user_id.b2153f97d2", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect user_id\nfrom \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__user\"\nwhere user_id is null\n\n\n", "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:05.982428Z", "completed_at": "2024-04-18T23:37:06.025526Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:06.030043Z", "completed_at": "2024-04-18T23:37:06.030048Z"}], "thread_id": "Thread-6", "execution_time": 0.055726051330566406, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.jira_source.unique_stg_jira__user_user_id.a397e1a23f", "compiled": true, "compiled_code": "\n    \n    \n\nselect\n    user_id as unique_field,\n    count(*) as n_records\n\nfrom \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__user\"\nwhere user_id is not null\ngroup by user_id\nhaving count(*) > 1\n\n\n", "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:05.979181Z", "completed_at": "2024-04-18T23:37:06.025765Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:06.030329Z", "completed_at": "2024-04-18T23:37:06.030332Z"}], "thread_id": "Thread-4", "execution_time": 0.0597071647644043, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.jira_source.unique_stg_jira__sprint_sprint_id.8a73555fed", "compiled": true, "compiled_code": "\n    \n    \n\nselect\n    sprint_id as unique_field,\n    count(*) as n_records\n\nfrom \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__sprint\"\nwhere sprint_id is not null\ngroup by sprint_id\nhaving count(*) > 1\n\n\n", "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:06.022792Z", "completed_at": "2024-04-18T23:37:06.029770Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:06.032085Z", "completed_at": "2024-04-18T23:37:06.032089Z"}], "thread_id": "Thread-1", "execution_time": 0.012015104293823242, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.jira_source.not_null_stg_jira__status_status_id.b32a8a0d84", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect status_id\nfrom \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__status\"\nwhere status_id is null\n\n\n", "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:06.026514Z", "completed_at": "2024-04-18T23:37:06.030825Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:06.033150Z", "completed_at": "2024-04-18T23:37:06.033153Z"}], "thread_id": "Thread-2", "execution_time": 0.0120391845703125, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.jira_source.unique_stg_jira__status_status_id.0449241b95", "compiled": true, "compiled_code": "\n    \n    \n\nselect\n    status_id as unique_field,\n    count(*) as n_records\n\nfrom \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__status\"\nwhere status_id is not null\ngroup by status_id\nhaving count(*) > 1\n\n\n", "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:05.655498Z", "completed_at": "2024-04-18T23:37:06.114538Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:06.115185Z", "completed_at": "2024-04-18T23:37:06.115191Z"}], "thread_id": "Thread-8", "execution_time": 0.5075209140777588, "adapter_response": {}, "message": null, "failures": null, "unique_id": "model.jira_source.stg_jira__version", "compiled": true, "compiled_code": "\n\nwith base as (\n\n    select * \n    from \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__version_tmp\"\n),\n\nfields as (\n\n    select\n        \n    \n    \n    _fivetran_synced\n    \n as \n    \n    _fivetran_synced\n    \n, \n    \n    \n    archived\n    \n as \n    \n    archived\n    \n, \n    \n    \n    description\n    \n as \n    \n    description\n    \n, \n    \n    \n    id\n    \n as \n    \n    id\n    \n, \n    \n    \n    name\n    \n as \n    \n    name\n    \n, \n    \n    \n    overdue\n    \n as \n    \n    overdue\n    \n, \n    \n    \n    project_id\n    \n as \n    \n    project_id\n    \n, \n    \n    \n    release_date\n    \n as \n    \n    release_date\n    \n, \n    \n    \n    released\n    \n as \n    \n    released\n    \n, \n    \n    \n    start_date\n    \n as \n    \n    start_date\n    \n\n\n\n    from base\n),\n\nfinal as (\n    \n    select \n        archived as is_archived,\n        description,\n        id as version_id,\n        name as version_name,\n        overdue as is_overdue,\n        project_id,\n        cast(release_date as timestamp) as release_date,\n        released as is_released,\n        cast(start_date as timestamp) as start_date\n    from fields\n)\n\nselect * \nfrom final", "relation_name": "\"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__version\""}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:06.169272Z", "completed_at": "2024-04-18T23:37:06.173539Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:06.174172Z", "completed_at": "2024-04-18T23:37:06.174178Z"}], "thread_id": "Thread-3", "execution_time": 0.011062860488891602, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.jira_source.unique_stg_jira__version_version_id.08231bd017", "compiled": true, "compiled_code": "\n    \n    \n\nselect\n    version_id as unique_field,\n    count(*) as n_records\n\nfrom \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__version\"\nwhere version_id is not null\ngroup by version_id\nhaving count(*) > 1\n\n\n", "relation_name": null}, {"status": "success", "timing": [{"name": "compile", "started_at": "2024-04-18T23:37:06.164578Z", "completed_at": "2024-04-18T23:37:06.174495Z"}, {"name": "execute", "started_at": "2024-04-18T23:37:06.175829Z", "completed_at": "2024-04-18T23:37:06.175833Z"}], "thread_id": "Thread-5", "execution_time": 0.01368403434753418, "adapter_response": {}, "message": null, "failures": null, "unique_id": "test.jira_source.not_null_stg_jira__version_version_id.03877ce324", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect version_id\nfrom \"postgres\".\"jira_source_integration_tests_302_jira\".\"stg_jira__version\"\nwhere version_id is null\n\n\n", "relation_name": null}], "elapsed_time": 3.960796356201172, "args": {"empty_catalog": false, "warn_error_options": {"include": [], "exclude": []}, "defer": false, "log_level": "info", "invocation_command": "dbt docs generate", "vars": {}, "write_json": true, "log_file_max_bytes": 10485760, "log_format": "default", "partial_parse": true, "static_parser": true, "project_dir": "/Users/<USER>/Documents/dbt_packages/jira/dbt_jira_source/integration_tests", "compile": true, "print": true, "log_format_file": "debug", "use_colors": true, "strict_mode": false, "log_level_file": "debug", "version_check": true, "log_path": "/Users/<USER>/Documents/dbt_packages/jira/dbt_jira_source/integration_tests/logs", "which": "generate", "use_colors_file": true, "exclude": [], "enable_legacy_logger": false, "printer_width": 80, "partial_parse_file_diff": true, "introspect": true, "macro_debugging": false, "profiles_dir": "/Users/<USER>/.dbt", "show_resource_report": false, "indirect_selection": "eager", "select": [], "send_anonymous_usage_stats": true, "static": false, "favor_state": false, "populate_cache": true, "cache_selected_only": false, "quiet": false}}