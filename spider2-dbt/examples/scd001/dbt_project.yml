
# Name your project! Project names should contain only lowercase characters
# and underscores. A good package name should reflect your organization's
# name or the intended use of these models
name: 'dbt_scd'
version: '1.0.0'
config-version: 2

# This setting configures which "profile" dbt uses for this project.
profile: 'scd'

# These configurations specify where dbt should look for different types of files.
# The `model-paths` config, for example, states that models in this project can be
# found in the "models/" directory. You probably won't need to change these!
model-paths: ["models"]
analysis-paths: ["analyses"]
test-paths: ["tests"]
seed-paths: ["seeds"]
macro-paths: ["macros"]
snapshot-paths: ["snapshots"]

target-path: "target"  # directory which will store compiled SQL files
clean-targets:         # directories to be removed by `dbt clean`
  - "target"
  - "dbt_packages"


# Configuring models
# Full documentation: https://docs.getdbt.com/docs/configuring-models

# In this example config, we tell dbt to build all models in the example/ directory
# as tables. These settings can be overridden in the individual model files
# using the `{{ config(...) }}` macro.
models:
  +materialized: table

vars:
  # dbt_artifacts:
  #   dbt_artifacts_database: your_db # optional, default is your target database
  #   dbt_artifacts_schema: your_schema # optional, default is 'dbt_artifacts'
  #   dbt_artifacts_table: your_table # optional, default is 'artifacts'
  #   dbt_artifacts_results_table: your_table # optional, default is 'dbt_run_results'
  #   dbt_artifacts_result_nodes_table: your_table # optional, default is 'dbt_run_result_nodes'
  #   dbt_artifacts_manifest_nodes_table: your_table # optional, default is 'dbt_manifest_nodes'

 high_timestamp: '9999-12-31 23:59:59' # used in place of null for dbt valid_to
 current_records_only: false # switches all models to only latest records rather than all hist