
name: 'spark_utils_snowplow_integration_tests'
version: '1.0'
config-version: 2

profile: 'integration_tests'

source-paths: ["models"]
analysis-paths: ["analysis"] 
test-paths: ["tests"]
data-paths: ["data"]
macro-paths: ["macros"]

target-path: "target"  # directory which will store compiled SQL files
clean-targets:         # directories to be removed by `dbt clean`
    - "target"
    - "dbt_modules"

dispatch:
  - macro_namespace: dbt_utils
    search_order: ['spark_utils', 'dbt_utils']
  - macro_namespace: snowplow
    search_order: ['spark_utils', 'snowplow']

models:
  incremental_strategy: merge
  file_format: delta
  
  snowplow_integration_tests:
    pre:
      default:
        base_event:
          +enabled: false
    post:
      page_views:
        default:
          page_views_expected:
            +enabled: false
      sessions:
        default:
          sessions_expected:
            +enabled: false

vars:
  'snowplow:timezone': 'America/New_York'
  'snowplow:events': '{{ ref("base_event") }}'
  'snowplow:context:web_page': '{{ ref("base_web_page") }}'
  'snowplow:context:performance_timing': FALSE
  'snowplow:context:useragent': FALSE
  'snowplow:pass_through_columns': ['test_add_col']
    
seeds:
  quote_columns: false
  
  snowplow_integration_tests:
    event:
      +column_types:
        br_cookies: string
        br_features_director: string
        br_features_flash: string
        br_features_gears: string
        br_features_java: string
        br_features_pdf: string
        br_features_quicktime: string
        br_features_realplayer: string
        br_features_silverlight: string
        br_features_windowsmedia: string
        collector_tstamp: string
        derived_tstamp: string
        dvce_ismobile: string
    expected:
      snowplow_page_views_expected:
        +column_types:
          page_view_start: string
          page_view_end: string
      snowplow_sessions_expected:
        +column_types:
          session_start: string
          session_end: string
