version: 2

models:
  - name: daily_agg_reviews
    description: |
      This model aggregates the total number of reviews and their sentiment for each day. It is an incremental model that processes new data daily. 
      A surrogate key is generated based on the review sentiment and review date to ensure uniqueness. The model only processes reviews from the current date when running incrementally.

    refs:
      - name: fct_reviews

    columns:
      - name: REVIEW_TOTALS
        description: "The total count of reviews for a specific sentiment on a given date."

      - name: REVIEW_SENTIMENT
        description: "The sentiment of the review (e.g., positive, neutral, or negative)."

      - name: REVIEW_DATE
        description: "The date on which the review was submitted."

      - name: DATE_SENTIMENT_ID
        description: "A surrogate key generated from the review sentiment and the review date, ensuring uniqueness for each date-sentiment combination."

  - name: mom_agg_reviews
    description: |
      This model calculates the total number of reviews and their sentiment over a rolling 30-day window, providing month-over-month (MoM) comparison metrics. 
      The model aggregates review data over the past 30 days and calculates the percentage change in review totals compared to the previous month. 
      It uses a surrogate key generated from the aggregation date and review sentiment to ensure uniqueness.

    refs:
      - name: fct_reviews
      - name: dim_dates

    columns:
      - name: REVIEW_TOTALS
        description: "The total count of reviews for a specific sentiment during the 30-day aggregation window."

      - name: REVIEW_SENTIMENT
        description: "The sentiment of the review (e.g., positive, neutral, or negative)."

      - name: AGGREGATION_DATE
        description: "The aggregation date representing the date on which the 30-day rolling aggregation was calculated."

      - name: MOM
        description: "The month-over-month (MoM) percentage change in review totals compared to the previous 30 days."
      
      - name: DATE_SENTIMENT_ID
        description: "A surrogate key generated from the aggregation date and review sentiment, ensuring uniqueness for each date-sentiment combination."