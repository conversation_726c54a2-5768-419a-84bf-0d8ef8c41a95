version: 2

models:
  - name: stg_f1_dataset__drivers
    description: "This model transforms raw driver data for Formula 1, including driver details like name, nationality, date of birth, and calculates their full name and current age."

    refs:
      - name: drivers

    columns:
      - name: driver_id
        description: "Unique identifier for each driver."
        tests:
          - not_null
          - unique
      
      - name: driver_ref
        description: "Reference code used for the driver in the original dataset."
      
      - name: driver_number
        description: "The racing number assigned to the driver."
      
      - name: driver_code
        description: "The code abbreviation used to represent the driver in race classifications."
      
      - name: driver_first_name
        description: "The first name of the driver."
      
      - name: driver_last_name
        description: "The last name (surname) of the driver."
      
      - name: driver_date_of_birth
        description: "The date of birth of the driver."
      
      - name: driver_nationality
        description: "The nationality of the driver."
      
      - name: driver_url
        description: "The URL to the driver's official profile or Wikipedia page."

      - name: driver_full_name
        description: "The full name of the driver, generated by concatenating the first name and last name."

      - name: driver_current_age
        description: "The current age of the driver, calculated based on their date of birth."
