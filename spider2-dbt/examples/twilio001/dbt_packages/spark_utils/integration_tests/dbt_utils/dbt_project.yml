
name: 'spark_utils_dbt_utils_integration_tests'
version: '1.0'
config-version: 2

profile: 'integration_tests'

source-paths: ["models"]
analysis-paths: ["analysis"] 
test-paths: ["tests"]
data-paths: ["data"]
macro-paths: ["macros"]

target-path: "target"  # directory which will store compiled SQL files
clean-targets:         # directories to be removed by `dbt clean`
    - "target"
    - "dbt_modules"
    
dispatch:
  - macro_namespace: dbt_utils
    search_order:
      - spark_utils
      - dbt_utils_integration_tests
      - dbt_utils

seeds:
  dbt_utils_integration_tests:
    +file_format: delta

models:
  dbt_utils_integration_tests:
    +file_format: delta
    sql:
      # macro doesn't work for this integration test (schema pattern)
      test_get_relations_by_pattern:
        +enabled: false
      # integration test doesn't work
      test_groupby:
        +enabled: false
    schema_tests:
      # integration test doesn't work
      test_recency:
        +enabled: false
