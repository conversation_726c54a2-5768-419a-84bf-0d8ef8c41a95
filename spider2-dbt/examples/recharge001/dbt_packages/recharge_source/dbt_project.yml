
name: 'recharge_source'
version: '0.3.1'
config-version: 2
require-dbt-version: [">=1.3.0", "<2.0.0"]

vars:
  recharge_source:
    address: "{{ source('recharge','address') }}"
    address_discounts: "{{ source('recharge','address_discounts') }}"
    address_shipping_line: "{{ source('recharge','address_shipping_line') }}"
    charge: "{{ source('recharge','charge') }}"
    charge_discount: "{{ source('recharge','charge_discount') }}"
    charge_line_item: "{{ source('recharge','charge_line_item') }}"
    charge_order_attribute: "{{ source('recharge','charge_order_attribute') }}"
    charge_shipping_line: "{{ source('recharge','charge_shipping_line') }}"
    charge_tax_line: "{{ source('recharge','charge_tax_line') }}"
    checkout: "{{ source('recharge','checkout') }}"
    customer: "{{ source('recharge','customer') }}"
    discount: "{{ source('recharge','discount') }}"
    one_time_product: "{{ source('recharge','one_time_product') }}"
    order: "{{ source('recharge','order') }}"
    orders: "{{ source('recharge','orders') }}"
    order_line_item: "{{ source('recharge','order_line_item') }}"
    subscription: "{{ source('recharge','subscription') }}"
    subscription_history: "{{ source('recharge','subscription_history') }}"

    recharge__address_passthrough_columns: [] 
    recharge__charge_passthrough_columns: [] 
    recharge__charge_line_item_passthrough_columns: []
    recharge__checkout_passthrough_columns: [] 
    recharge__order_passthrough_columns: [] 
    recharge__order_line_passthrough_columns: [] 
    recharge__subscription_passthrough_columns: [] 
    recharge__subscription_history_passthrough_columns: []

models:
  recharge_source:
    tmp:
      materialized: view
    +materialized: table
    +schema: recharge_source