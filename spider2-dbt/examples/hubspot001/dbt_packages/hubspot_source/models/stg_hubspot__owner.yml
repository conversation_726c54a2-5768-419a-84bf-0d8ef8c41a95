version: 2

models:

  - name: stg_hubspot__owner
    description: Each record represents an owner/user in Hubspot.
    columns:
      - name: _fivetran_synced
        description: '{{ doc("_fivetran_synced") }}'
      - name: created_timestamp
        description: A timestamp for when the owner was created.
      - name: email_address
        description: The email address of the owner.
      - name: first_name
        description: The first name of the owner.
      - name: full_name
        description: Full name of the owner.
      - name: last_name
        description: The last name of the owner.
      - name: owner_id
        description: The ID of the owner.
      - name: portal_id
        description: '{{ doc("portal_id") }}'
      - name: owner_type
        description: The type of owner.
      - name: updated_timestamp
        description: A timestamp for when the owner was last updated.