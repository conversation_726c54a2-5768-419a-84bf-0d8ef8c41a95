version: 2

models:

  - name: hubspot__email_event_bounce
    description: Each record represents a BOUNCE email event.
    columns:
      - name: _fivetran_synced
        description: '{{ doc("_fivetran_synced") }}'

      - name: is_contact_deleted
        description: '{{ doc("is_deleted") }}'

      - name: bounce_category
        description: |
              The best-guess of the type of bounce encountered. 
              If an appropriate category couldn't be determined, this property is omitted. See below for the possible values. 
              Note that this is a derived value, and may be modified at any time to improve the accuracy of classification.

      - name: event_id
        description: The ID of the event.
        tests:
          - not_null
          - unique:
              config:
                where: "not coalesce(is_contact_deleted, false)"

      - name: returned_response
        description: The full response from the recipient's email server.

      - name: returned_status
        description: The status code returned from the recipient's email server.

      - name: created_timestamp
        description: The created timestamp of the event.

      - name: email_campaign_id
        description: The ID of the related email campaign.

      - name: recipient_email_address
        description: The email address of the contact related to the event.

      - name: email_send_timestamp
        description: The timestamp of the SENT event related to this event.

      - name: email_send_id
        description: The event ID which uniquely identifies the email message's SENT event. If not applicable, this property is omitted.

      - name: contact_id
        description: The ID of the related contact.
          
  - name: hubspot__email_event_clicks
    description: Each record represents a CLICK email event.
    columns:
      - name: _fivetran_synced
        description: '{{ doc("_fivetran_synced") }}'

      - name: is_contact_deleted
        description: '{{ doc("is_deleted") }}'

      - name: browser
        description: '{{ doc("email_event_browser") }}'

      - name: click_url
        description: The URL within the message that the recipient clicked.

      - name: event_id
        description: The ID of the event.
        tests:
          - not_null
          - unique:
              config:
                where: "not coalesce(is_contact_deleted, false)"

      - name: geo_location
        description: '{{ doc("email_event_location") }}'

      - name: ip_address
        description: '{{ doc("email_event_ip_address") }}'

      - name: referer_url
        description: The URL of the webpage that linked to the URL clicked. Whether this is provided, and what its value is, is determined by the recipient's email client.

      - name: user_agent
        description: '{{ doc("email_event_user_agent") }}'
        
      - name: created_timestamp
        description: The created timestamp of the event.

      - name: email_campaign_id
        description: The ID of the related email campaign.

      - name: recipient_email_address
        description: The email address of the contact related to the event.

      - name: email_send_timestamp
        description: The timestamp of the SENT event related to this event.

      - name: email_send_id
        description: The event ID which uniquely identifies the email message's SENT event. If not applicable, this property is omitted.

      - name: contact_id
        description: The ID of the related contact.
          
  - name: hubspot__email_event_deferred
    description: Each record represents a DEFERRED email event.
    columns:
      - name: _fivetran_synced
        description: '{{ doc("_fivetran_synced") }}'

      - name: is_contact_deleted
        description: '{{ doc("is_deleted") }}'

      - name: attempt_number
        description: The delivery attempt number.

      - name: event_id
        description: The ID of the event.
        tests:
          - not_null
          - unique:
              config:
                where: "not coalesce(is_contact_deleted, false)"

      - name: returned_response
        description: The full response from the recipient's email server.
        
      - name: created_timestamp
        description: The created timestamp of the event.

      - name: email_campaign_id
        description: The ID of the related email campaign.

      - name: recipient_email_address
        description: The email address of the contact related to the event.

      - name: email_send_timestamp
        description: The timestamp of the SENT event related to this event.

      - name: email_send_id
        description: The event ID which uniquely identifies the email message's SENT event. If not applicable, this property is omitted.

      - name: contact_id
        description: The ID of the related contact.
 
  - name: hubspot__email_event_delivered
    description: Each record represents a DELIVERED email event.
    columns:
      - name: _fivetran_synced
        description: '{{ doc("_fivetran_synced") }}'

      - name: is_contact_deleted
        description: '{{ doc("is_deleted") }}'

      - name: event_id
        description: The ID of the event.
        tests:
          - not_null
          - unique:
              config:
                where: "not coalesce(is_contact_deleted, false)"

      - name: returned_response
        description: The full response from the recipient's email server.

      - name: smtp_id
        description: An ID attached to the message by HubSpot.
        
      - name: created_timestamp
        description: The created timestamp of the event.

      - name: email_campaign_id
        description: The ID of the related email campaign.

      - name: recipient_email_address
        description: The email address of the contact related to the event.

      - name: email_send_timestamp
        description: The timestamp of the SENT event related to this event.

      - name: email_send_id
        description: The event ID which uniquely identifies the email message's SENT event. If not applicable, this property is omitted.

      - name: contact_id
        description: The ID of the related contact.
   
  - name: hubspot__email_event_dropped
    description: Each record represents a DROPPED email event.
    columns:
      - name: _fivetran_synced
        description: '{{ doc("_fivetran_synced") }}'

      - name: is_contact_deleted
        description: '{{ doc("is_deleted") }}'

      - name: bcc_emails
        description: The 'bcc' field of the email message.

      - name: cc_emails
        description: The 'cc' field of the email message.

      - name: drop_message
        description: The raw message describing why the email message was dropped. This will usually provide additional details beyond 'dropReason'.

      - name: drop_reason
        description: The reason why the email message was dropped. See below for the possible values.

      - name: email_subject
        description: The subject line of the email message.

      - name: event_id
        description: The ID of the event.
        tests:
          - not_null
          - unique:
              config:
                where: "not coalesce(is_contact_deleted, false)"

      - name: from_email
        description: The 'from' field of the email message.

      - name: reply_to_email
        description: The 'reply-to' field of the email message.
        
      - name: created_timestamp
        description: The created timestamp of the event.

      - name: email_campaign_id
        description: The ID of the related email campaign.

      - name: recipient_email_address
        description: The email address of the contact related to the event.

      - name: email_send_timestamp
        description: The timestamp of the SENT event related to this event.

      - name: email_send_id
        description: The event ID which uniquely identifies the email message's SENT event. If not applicable, this property is omitted.

      - name: contact_id
        description: The ID of the related contact.
 
  - name: hubspot__email_event_forward
    description: Each record represents a FORWARD email event.
    columns:
      - name: _fivetran_synced
        description: '{{ doc("_fivetran_synced") }}'

      - name: is_contact_deleted
        description: '{{ doc("is_deleted") }}'

      - name: browser
        description: '{{ doc("email_event_browser") }}'

      - name: event_id
        description: The ID of the event.
        tests:
          - not_null
          - unique:
              config:
                where: "not coalesce(is_contact_deleted, false)"

      - name: geo_location
        description: '{{ doc("email_event_location") }}'

      - name: ip_address
        description: '{{ doc("email_event_ip_address") }}'

      - name: user_agent
        description: '{{ doc("email_event_user_agent") }}'
        
      - name: created_timestamp
        description: The created timestamp of the event.

      - name: email_campaign_id
        description: The ID of the related email campaign.

      - name: recipient_email_address
        description: The email address of the contact related to the event.

      - name: email_send_timestamp
        description: The timestamp of the SENT event related to this event.

      - name: email_send_id
        description: The event ID which uniquely identifies the email message's SENT event. If not applicable, this property is omitted.

      - name: contact_id
        description: The ID of the related contact.

  - name: hubspot__email_event_opens
    description: Each record represents a OPEN email event.
    columns:
      - name: _fivetran_synced
        description: '{{ doc("_fivetran_synced") }}'

      - name: is_contact_deleted
        description: '{{ doc("is_deleted") }}'

      - name: browser
        description: '{{ doc("email_event_browser") }}'

      - name: duration_open
        description: If provided and nonzero, the approximate number of milliseconds the user had opened the email.

      - name: event_id
        description: The ID of the event.
        tests:
          - not_null
          - unique:
              config:
                where: "not coalesce(is_contact_deleted, false)"

      - name: geo_location
        description: '{{ doc("email_event_location") }}'

      - name: ip_address
        description: '{{ doc("email_event_ip_address") }}'

      - name: user_agent
        description: '{{ doc("email_event_user_agent") }}'
        
      - name: created_timestamp
        description: The created timestamp of the event.

      - name: email_campaign_id
        description: The ID of the related email campaign.

      - name: recipient_email_address
        description: The email address of the contact related to the event.

      - name: email_send_timestamp
        description: The timestamp of the SENT event related to this event.

      - name: email_send_id
        description: The event ID which uniquely identifies the email message's SENT event. If not applicable, this property is omitted.

      - name: contact_id
        description: The ID of the related contact.

  - name: hubspot__email_event_print
    description: Each record represents a PRINT email event.
    columns:
      - name: _fivetran_synced
        description: '{{ doc("_fivetran_synced") }}'

      - name: is_contact_deleted
        description: '{{ doc("is_deleted") }}'

      - name: browser
        description: '{{ doc("email_event_browser") }}'

      - name: event_id
        description: The ID of the event.
        tests:
          - not_null
          - unique:
              config:
                where: "not coalesce(is_contact_deleted, false)"

      - name: geo_location
        description: '{{ doc("email_event_location") }}'

      - name: ip_address
        description: '{{ doc("email_event_ip_address") }}'

      - name: user_agent
        description: '{{ doc("email_event_user_agent") }}'
        
      - name: created_timestamp
        description: The created timestamp of the event.

      - name: email_campaign_id
        description: The ID of the related email campaign.

      - name: recipient_email_address
        description: The email address of the contact related to the event.

      - name: email_send_timestamp
        description: The timestamp of the SENT event related to this event.

      - name: email_send_id
        description: The event ID which uniquely identifies the email message's SENT event. If not applicable, this property is omitted.

      - name: contact_id
        description: The ID of the related contact.

  - name: hubspot__email_event_sent
    description: Each record represents a SENT email event.
    columns:
      - name: _fivetran_synced
        description: '{{ doc("_fivetran_synced") }}'

      - name: is_contact_deleted
        description: '{{ doc("is_deleted") }}'

      - name: bcc_emails
        description: The 'cc' field of the email message.

      - name: cc_emails
        description: The 'bcc' field of the email message.

      - name: email_subject
        description: The subject line of the email message.

      - name: event_id
        description: The ID of the event.
        tests:
          - not_null
          - unique:
              config:
                where: "not coalesce(is_contact_deleted, false)"

      - name: from_email
        description: The 'from' field of the email message.

      - name: reply_to_email
        description: The 'reply-to' field of the email message.
        
      - name: created_timestamp
        description: The created timestamp of the event.

      - name: email_campaign_id
        description: The ID of the related email campaign.

      - name: recipient_email_address
        description: The email address of the contact related to the event.

      - name: email_send_timestamp
        description: The timestamp of the SENT event related to this event.

      - name: email_send_id
        description: The event ID which uniquely identifies the email message's SENT event. If not applicable, this property is omitted.

      - name: contact_id
        description: The ID of the related contact.

  - name: hubspot__email_event_spam_report
    description: Each record represents a SPAM_REPORT email event.
    columns:
      - name: _fivetran_synced
        description: '{{ doc("_fivetran_synced") }}'

      - name: is_contact_deleted
        description: '{{ doc("is_deleted") }}'

      - name: event_id
        description: The ID of the event.
        tests:
          - not_null
          - unique:
              config:
                where: "not coalesce(is_contact_deleted, false)"

      - name: ip_address
        description: '{{ doc("email_event_ip_address") }}'

      - name: user_agent
        description: '{{ doc("email_event_user_agent") }}'
        
      - name: created_timestamp
        description: The created timestamp of the event.

      - name: email_campaign_id
        description: The ID of the related email campaign.

      - name: recipient_email_address
        description: The email address of the contact related to the event.

      - name: email_send_timestamp
        description: The timestamp of the SENT event related to this event.

      - name: email_send_id
        description: The event ID which uniquely identifies the email message's SENT event. If not applicable, this property is omitted.

      - name: contact_id
        description: The ID of the related contact.

  - name: hubspot__email_event_status_change
    description: Each record represents a STATUS_CHANGE email event in Hubspot.
    columns:
      - name: _fivetran_synced
        description: '{{ doc("_fivetran_synced") }}'

      - name: is_contact_deleted
        description: '{{ doc("is_deleted") }}'

      - name: change_source
        description: The source of the subscription change. 

      - name: event_id
        description: The ID of the event.
        tests:
          - not_null
          - unique:
              config:
                where: "not coalesce(is_contact_deleted, false)"

      - name: is_bounced
        description: | 
              A HubSpot employee explicitly initiated the status change to block messages to the recipient. 
              (Note this usage has been deprecated in favor of dropping messages with a 'dropReason' of BLOCKED_ADDRESS.)

      - name: requested_by_email
        description: The email address of the person requesting the change on behalf of the recipient. If not applicable, this property is omitted.

      - name: subscription_status
        description: |
              The recipient's portal subscription status. 
              Note that if this is 'UNSUBSCRIBED', the property 'subscriptions' is not necessarily an empty array, nor are all 
              subscriptions contained in it necessarily going to have their statuses set to 'UNSUBSCRIBED'.)

      - name: subscriptions
        description: |
              An array of JSON objects representing the status of subscriptions for the recipient. 
              Each JSON subscription object is comprised of the properties: 'id', 'status'.
        
      - name: created_timestamp
        description: The created timestamp of the event.

      - name: email_campaign_id
        description: The ID of the related email campaign.

      - name: recipient_email_address
        description: The email address of the contact related to the event.

      - name: email_send_timestamp
        description: The timestamp of the SENT event related to this event.

      - name: email_send_id
        description: The event ID which uniquely identifies the email message's SENT event. If not applicable, this property is omitted.

      - name: contact_id
        description: The ID of the related contact.